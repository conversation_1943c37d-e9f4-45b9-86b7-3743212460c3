import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../bloc/sticker_bloc.dart';

class StickerPlaceholder extends StatelessWidget {
  const StickerPlaceholder({
    required this.stickerUrl,
    this.filteredColor = true,
    super.key,
  });

  final String stickerUrl;
  final bool filteredColor;

  @override
  Widget build(BuildContext context) {
    final thumbnailPath =
        context.read<StickerBloc>().state.pathOfThumbnail(stickerUrl);
    if (thumbnailPath == null) {
      return ui.AppShimmerEffect(
        child: Container(
          decoration: BoxDecoration(
            color: ui.AppColors.skeletonColor,
            borderRadius: BorderRadius.circular(8),
          ),
          constraints: BoxConstraints.expand(),
        ),
      );
    }

    if (filteredColor) {
      return ColorFiltered(
        colorFilter: ColorFilter.mode(
          Colors.grey,
          BlendMode.srcIn,
        ),
        child: Image.file(File(thumbnailPath)),
      );
    }
    return Image.file(File(thumbnailPath));
  }
}
