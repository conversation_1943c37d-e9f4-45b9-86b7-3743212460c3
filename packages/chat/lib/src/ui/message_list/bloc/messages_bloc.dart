import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/database/classes/reaction_data.dart';
import '../../../domain/usecase/message/load_messages_use_case.dart';
import '../../../domain/usecase/message/load_pinned_message_use_case.dart';
import '../../../domain/usecase/message/mark_as_read_use_case.dart';
import '../../../domain/usecase/message/pin_unpin_dm_message_use_case.dart';
import '../../../domain/usecase/message/pin_unpin_message_use_case.dart';
import '../../../domain/usecase/message/upsert_all_unpin_message_use_case.dart';

part 'messages_bloc.freezed.dart';
part 'messages_event.dart';
part 'messages_state.dart';

@injectable
class MessagesBloc extends Bloc<MessagesEvent, MessagesState> {
  MessagesBloc(
    this._listAllMessagesUseCase,
    this._loadMessagesUseCase,
    this._getChannelUseCase,
    this._deleteMessagesForMeUseCase,
    this._deleteMessagesForEveryOneUseCase,
    this._localDeleteMessagesUseCase,
    this._deleteOriginalMessageUseCase,
    this._markAsReadUseCase,
    this._messageRepository,
    this._updateLastSeenMessageUseCase,
    this._pinUnpinDMMessageUseCase,
    this._pinUnpinMessageUseCase,
    this._upsertPinUnPinMessageUseCase,
    this._upsertAllUnPinMessageUseCase,
    this._loadPinnedMessageUseCase,
  ) : super(MessagesState.initial()) {
    on<MessagesEventInitiate>(_onInitiate);
    on<MessagesEventLoadMore>(_onLoadMore);
    on<AddTempMessage>(_onAddTempMessage);
    on<AddMessage>(_onAddMessage);
    on<SaveTempMessage>(_onSaveTempMessage);
    on<UpdateMessage>(_onUpdateMessage);
    on<UpdateAttachment>(_onUpdateAttachment);
    on<UpdateMessageStatus>(_onUpdateMessageStatus);
    on<Sync>(_onSyncMessage);
    on<ClearAllMessageEvent>(_onClearAllMessage);
    on<Dispose>(_onDispose);
    on<OnDeleteMessageForMeEvent>(_onDeleteMessageForMe);
    on<OnDeleteMessageForEveryOneEvent>(_onDeleteMessageForEveryOne);
    on<MarkAsReadMessageEvent>(_onMarkAsRead);
    on<OnLocalDeleteMessageEvent>(_onLocalDeleteMessage);
    on<ResendTempMessage>(_onResendMessage);
    on<OnPinUnPinMessage>(_onPinUnPinMessage);
    on<pinUnPinUpdateMessage>(_onPinUnPinUpdateMessage);
    on<InitLoadPinUnPinMessage>(_onInitLoadPinUnPinMessage);
    on<OnLoadPinUnPinMessage>(_onLoadPinUnPinMessage);
    on<UpdateChannelIdEvent>(_onUpdateChanelId);
  }

  final ListAllMessagesUseCase _listAllMessagesUseCase;
  final LoadMessagesUseCase _loadMessagesUseCase;
  final LocalDeleteMessagesUseCase _localDeleteMessagesUseCase;
  final DeleteMessagesForMeUseCase _deleteMessagesForMeUseCase;
  final DeleteMessagesForEveryoneUseCase _deleteMessagesForEveryOneUseCase;
  final DeleteOriginalMessageUseCase _deleteOriginalMessageUseCase;
  final GetChannelUseCase _getChannelUseCase;
  final MarkAsReadUseCase _markAsReadUseCase;
  final UpdateLastSeenMessageUseCase _updateLastSeenMessageUseCase;

  final PinUnpinDMMessageUseCase _pinUnpinDMMessageUseCase;
  final PinUnpinMessageUseCase _pinUnpinMessageUseCase;
  final UpsertPinUnPinMessageUseCase _upsertPinUnPinMessageUseCase;
  final UpsertAllUnPinMessageUseCase _upsertAllUnPinMessageUseCase;
  final LoadPinnedMessageUseCase _loadPinnedMessageUseCase;

  final MessageRepository _messageRepository;

  final _appEventBus = GetIt.instance.get<AppEventBus>();
  StreamSubscription? _clearMessageEventSubscription;
  StreamSubscription? _pinMessageEventSubscription;
  StreamSubscription? _onAttachmentUpdateSubscription;

  bool _isInitiated = false;
  Channel? _channel;

  String? workspaceId;
  String? channelId;
  String? userId;

  bool _isSameChannel(String? wsId, String? chId, String? uId) {
    return (workspaceId == wsId && channelId == chId) || userId == uId;
  }

  bool isNullChannelInfo() {
    return workspaceId == null || channelId == null || userId == null;
  }

  Future<void> _onInitiate(
    MessagesEventInitiate event,
    Emitter<MessagesState> emit,
  ) async {
    if (isNullChannelInfo()) {
      workspaceId = event.workspaceId;
      channelId = event.channelId;
      userId = event.userId;
    }

    if (_isInitiated) return;
    emit(MessagesState.waiting());
    _channel = getChannel(
      workspaceId: event.workspaceId,
      channelId: event.channelId,
      userId: event.userId,
    );

    if (_channel == null) {
      return;
    }
    workspaceId = _channel!.workspaceId;
    channelId = _channel!.channelId;
    userId = _channel!.userId;

    _isInitiated = true;
    _listenPinMessageEvent(
      _channel!.workspaceId,
      _channel!.channelId,
      userId: event.userId,
    );
    _listenClearMessageEvent();
    _listenAttachmentUpdateEvent();
    final output = await _listAllMessagesUseCase.execute(
      ListAllMessagesInput(
        workspaceId: _channel?.workspaceId,
        channelId: _channel?.channelId,
        userId: event.userId,
        limit: event.limit,
      ),
    );
    if (output.messages.isNotEmpty) {
      emit(
        MessagesState.loaded(
          messages: output.messages,
          lastSeenMessageId: _channel?.lastSeenMessageId,
          hasNext: output.hasNext,
          nextPageToken: output.nextPageToken,
        ),
      );

      _handleSyncMessage(
        output,
        channelId: _channel?.channelId,
        workspaceId: _channel?.workspaceId,
        userId: event.userId,
      );
      return;
    }
    try {
      final loadMessageOutput = await _loadMessagesUseCase.execute(
        LoadMessagesInput(
          workspaceId: _channel?.workspaceId,
          channelId: _channel?.channelId,
          userId: event.userId,
          limit: event.limit,
        ),
      );

      emit(
        MessagesState.loaded(
          messages: loadMessageOutput.messages,
          lastSeenMessageId: _channel?.lastSeenMessageId,
          hasNext: loadMessageOutput.hasNext,
          nextPageToken: loadMessageOutput.nextPageToken,
        ),
      );

      _updateLastMessageIsFirstFlag(loadMessageOutput);
      await _messageRepository.insertAll(loadMessageOutput.messages);
      if (loadMessageOutput.messages.isEmpty) {
        _messageRepository.tagFirstMessage(
          workspaceId: _channel!.workspaceId,
          channelId: _channel!.channelId,
          messageId: loadMessageOutput.nextPageToken!,
        );
      }
    } catch (ex) {
      Log.e(ex);
    }
  }

  void _handleSyncMessage(
    ListAllMessagesOutput output, {
    String? channelId,
    String? workspaceId,
    String? userId,
  }) {
    final nonTempMessages = output.messages
        .where((message) => message.isTemp == false)
        .toList()
      ..sort((a, b) => b.createTime!.compareTo(a.createTime!));

    if (nonTempMessages.isNotEmpty) {
      final latestMessage = nonTempMessages.first;
      add(
        MessagesEvent.sync(
          workspaceId: workspaceId,
          channelId: channelId,
          userId: userId,
          prevPageToken: latestMessage.messageId,
        ),
      );
    }
  }

  Future<void> _onLoadMore(
    MessagesEventLoadMore event,
    Emitter<MessagesState> emit,
  ) async {
    try {
      final output = await _listAllMessagesUseCase.execute(
        ListAllMessagesInput(
          workspaceId: event.workspaceId,
          channelId: event.channelId,
          userId: event.userId,
          limit: event.limit,
          nextPageToken: event.nextPageToken,
        ),
      );

      if (output.messages.isNotEmpty) {
        emit(
          MessagesState.onLoadMore(
            messages: output.messages,
            hasNext: output.hasNext,
            nextPageToken: output.nextPageToken,
          ),
        );
        return;
      }

      final loadMessageOutput = await _loadMessagesUseCase.execute(
        LoadMessagesInput(
          workspaceId: event.workspaceId,
          channelId: event.channelId,
          userId: event.userId,
          nextPageToken: event.nextPageToken,
        ),
      );

      Channel? channel = getChannel(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      );

      emit(
        MessagesState.loaded(
          messages: loadMessageOutput.messages,
          lastSeenMessageId: channel?.lastSeenMessageId,
          hasNext: loadMessageOutput.hasNext,
          nextPageToken: loadMessageOutput.nextPageToken,
        ),
      );

      _updateLastMessageIsFirstFlag(loadMessageOutput);
      await _messageRepository.insertAll(loadMessageOutput.messages);
      if (!loadMessageOutput.hasNext) {
        _messageRepository.tagFirstMessage(
          workspaceId: event.workspaceId!,
          channelId: event.channelId!,
          messageId: loadMessageOutput.messages.isNotEmpty
              ? loadMessageOutput.messages.last.messageId
              : event.nextPageToken!,
        );
      }
    } catch (error) {
      Log.e(name: 'MessagesBloc._onLoadMore', error);
    }
  }

  void _updateLastMessageIsFirstFlag(LoadMessagesOutput output) {
    if (output.messages.isEmpty) return;

    output.messages.last.isFirstMessage = output.error ? false : output.hasNext;
  }

  void _onAddTempMessage(AddTempMessage event, Emitter<MessagesState> emit) {
    if (_isSameChannel(
      event.message.workspaceId,
      event.message.channelId,
      event.message.userId,
    )) {
      emit(MessagesState.addTempMessage(event.message));
    }
  }

  void _onAddMessage(AddMessage event, Emitter<MessagesState> emit) {
    if (_isSameChannel(
      event.message.workspaceId,
      event.message.channelId,
      event.message.userId,
    )) {
      emit(MessagesState.addMessage(event.message));
    }
  }

  void _onUpdateMessage(UpdateMessage event, Emitter<MessagesState> emit) {
    emit(MessagesState.updateMessage(event.message));
  }

  void _onResendMessage(ResendTempMessage event, Emitter<MessagesState> emit) {
    emit(MessagesState.resendMessage(event.message));
  }

  FutureOr<void> _onSyncMessage(
    Sync event,
    Emitter<MessagesState> emit,
  ) async {
    final loadMessageOutput = await _loadMessagesUseCase.execute(
      LoadMessagesInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
        prevPageToken: event.prevPageToken,
      ),
    );

    emit(
      MessagesState.synced(
        messages: loadMessageOutput.messages.reversed.toList(),
      ),
    );
    if (loadMessageOutput.messages.isEmpty) return;
    await _messageRepository.insertAll(loadMessageOutput.messages);
  }

  FutureOr<void> _onSaveTempMessage(
    SaveTempMessage event,
    Emitter<MessagesState> emit,
  ) async {
    await _messageRepository.insert(event.message);
  }

  Channel? getChannel({
    String? workspaceId,
    String? channelId,
    String? userId,
  }) {
    return _getChannelUseCase
        .execute(
          GetChannelInput(
            workspaceId: workspaceId,
            channelId: channelId,
            userId: userId,
          ),
        )
        .channel;
  }

  void _listenClearMessageEvent() {
    _clearMessageEventSubscription ??= _appEventBus
        .on<ClearMessageEvent>()
        .listen(onReceivedFromClearMessageEvent);
  }

  void _listenAttachmentUpdateEvent() {
    _onAttachmentUpdateSubscription ??= _appEventBus
        .on<OnUpdateAttachmentEvent>()
        .listen(onReceivedFromAttachmentUpdatedEvent);
  }

  void _listenPinMessageEvent(
    String workspaceId,
    String channelId, {
    String? userId,
  }) async {
    _pinMessageEventSubscription?.cancel();
    _pinMessageEventSubscription ??= _messageRepository
        .streamPinMessages(workspaceId: workspaceId, channelId: channelId)
        .listen((messages) {
      add(MessagesEvent.OnLoadPinUnPinMessage(messages));
    });
    final output = await _loadPinnedMessageUseCase.execute(
      LoadPinnedMessageInput(
        workspaceId: workspaceId,
        channelId: channelId,
        userId: userId,
      ),
    );
    if (output.messages != null) {
      if (output.messages!.isNotEmpty) {
        _upsertPinUnPinMessageUseCase.execute(
          UpsertPinUnPinMessageInput(
            workspaceId: output.messages!.first.workspaceId,
            channelId: output.messages!.first.channelId,
            messageId: output.messages!.first.messageId,
            status: output.messages!.first.isPinned,
            pinTime: output.messages!.first.pinTime,
          ),
        );
      } else {
        _upsertAllUnPinMessageUseCase.execute(
          UpsertAllUnPinMessageInput(
            workspaceId: workspaceId,
            channelId: channelId,
            userId: userId,
          ),
        );
      }
    }
  }

  Future<void> onReceivedFromClearMessageEvent(ClearMessageEvent event) async {
    add(
      MessagesEvent.clearAllMessageEvent(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
      ),
    );
  }

  Future<void> onReceivedFromAttachmentUpdatedEvent(
    OnUpdateAttachmentEvent event,
  ) async {
    Attachment attachment = Attachment.fromJson(event.data);
    final updatedMessage =
        await _messageRepository.updateAttachmentAndGetMessage(
      workspaceId: event.workspaceId,
      channelId: event.channelId,
      messageId: event.messageId,
      attachment: attachment,
    );

    add(MessagesEvent.updateMessage(updatedMessage!));
  }

  FutureOr<void> _onClearAllMessage(
    ClearAllMessageEvent event,
    Emitter<MessagesState> emit,
  ) async {
    emit(
      MessagesState.clearAllMessage(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
      ),
    );
  }

  FutureOr<void> _onDispose(
    Dispose event,
    Emitter<MessagesState> emit,
  ) {
    if (_clearMessageEventSubscription != null) {
      _clearMessageEventSubscription?.cancel();
      _clearMessageEventSubscription = null;
    }

    if (_onAttachmentUpdateSubscription != null) {
      _onAttachmentUpdateSubscription?.cancel();
      _onAttachmentUpdateSubscription = null;
    }
    if (_pinMessageEventSubscription != null) {
      _pinMessageEventSubscription?.cancel();
      _pinMessageEventSubscription = null;
    }
  }

  FutureOr<void> _onUpdateMessageStatus(
    UpdateMessageStatus event,
    Emitter<MessagesState> emit,
  ) {
    emit(
      MessagesState.updateMessageStatus(
        messageStatus: event.messageStatus,
        messageId: event.messageId,
        messageRef: event.messageRef,
      ),
    );
    _messageRepository.updateMessageStatus(
      workspaceId: event.workspaceId,
      channelId: event.channelId,
      messageId: event.messageId,
      ref: event.messageRef ?? '',
      messageStatus: event.messageStatus,
    );
  }

  FutureOr<void> _onUpdateAttachment(
    UpdateAttachment event,
    Emitter<MessagesState> emit,
  ) async {
    final newMessage = event.message;
    final oldMessage = _messageRepository.getMessageByIdOrRef(
      workspaceId: newMessage.workspaceId,
      channelId: newMessage.channelId,
      messageId: newMessage.messageId,
      ref: newMessage.ref!,
    );

    if (oldMessage == null) {
      for (var attachment in newMessage.mediaAttachments) {
        await _messageRepository.insertOrUpdateAttachment(
          workspaceId: newMessage.workspaceId,
          channelId: newMessage.channelId,
          messageId: newMessage.messageId,
          attachment: attachment,
        );
      }
      emit(MessagesState.updateMessage(newMessage));
      return;
    }

    final oldAttachmentsMap = <String, Attachment>{};
    for (var att in oldMessage.mediaAttachments) {
      final key = att.isTemp == true ? att.ref : att.attachmentId;
      if (key != null) {
        oldAttachmentsMap[key] = att;
      }
    }

    final newAttachmentsMap = <String, Attachment>{};
    for (var att in newMessage.mediaAttachments) {
      final key = att.isTemp == true ? att.ref : att.attachmentId;
      if (key != null) {
        newAttachmentsMap[key] = att;
      }
    }

    final attachmentsToInsertOrUpdate = <Attachment>[];

    for (var key in newAttachmentsMap.keys) {
      final newAtt = newAttachmentsMap[key];
      final oldAtt = oldAttachmentsMap[key];

      if (oldAtt == null) {
        attachmentsToInsertOrUpdate.add(newAtt!);
      } else {
        attachmentsToInsertOrUpdate.add(newAtt!);
      }
    }

    for (var attachment in attachmentsToInsertOrUpdate) {
      await _messageRepository.insertOrUpdateAttachment(
        workspaceId: newMessage.workspaceId,
        channelId: newMessage.channelId,
        messageId: newMessage.messageId,
        attachment: attachment,
      );
    }

    final attachmentsToRemove = oldAttachmentsMap.keys
        .where((key) => !newAttachmentsMap.containsKey(key))
        .map((key) => oldAttachmentsMap[key]!)
        .toList();

    for (var attachment in attachmentsToRemove) {
      _messageRepository.deleteAttachmentByDBId(attachment.id);
    }

    await _messageRepository.insert(newMessage);

    emit(MessagesState.updateMessage(newMessage));
  }

  FutureOr<void> _onDeleteMessageForMe(
    OnDeleteMessageForMeEvent event,
    Emitter<MessagesState> emit,
  ) async {
    emit(MessagesState.refreshDelete());
    var delete = await _deleteMessagesForMeUseCase.execute(
      DeleteMessagesForMeInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
        messageIds: event.messageIds ?? [],
      ),
    );
    if (delete.ok == true) {
      var workspaceId = event.workspaceId;
      var channelId = event.channelId;
      if (event.workspaceId == null || event.channelId == null) {
        var channel = getChannel(userId: event.userId);
        workspaceId = channel?.workspaceId;
        channelId = channel?.channelId;
      }
      AppEventBus.publish(
        DeleteMessageEvent(
          workspaceId: workspaceId!,
          channelId: channelId!,
          messageIds: event.messageIds ?? [],
        ),
      );
      await _localDeleteMessagesUseCase.execute(
        LocalDeleteMessagesInput(
          workspaceId: workspaceId,
          channelId: channelId,
          messageIds: event.messageIds ?? [],
        ),
      );
      _deleteOriginal(
        workspaceId: workspaceId,
        channelId: channelId,
        messageIds: event.messageIds ?? [],
      );
    }
    emit(MessagesState.deleteMessage(response: delete.ok));
  }

  FutureOr<void> _onDeleteMessageForEveryOne(
    OnDeleteMessageForEveryOneEvent event,
    Emitter<MessagesState> emit,
  ) async {
    emit(MessagesState.refreshDelete());
    var delete = await _deleteMessagesForEveryOneUseCase.execute(
      DeleteMessagesForEveryoneInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
        messageIds: event.messageIds ?? [],
      ),
    );
    if (delete.ok == true) {
      var workspaceId = event.workspaceId;
      var channelId = event.channelId;
      if (event.workspaceId == null || event.channelId == null) {
        var channel = getChannel(userId: event.userId);
        workspaceId = channel?.workspaceId;
        channelId = channel?.channelId;
      }
      AppEventBus.publish(
        DeleteMessageEvent(
          workspaceId: workspaceId!,
          channelId: channelId!,
          messageIds: event.messageIds ?? [],
        ),
      );
      await _localDeleteMessagesUseCase.execute(
        LocalDeleteMessagesInput(
          workspaceId: workspaceId,
          channelId: channelId,
          messageIds: event.messageIds ?? [],
        ),
      );
      _deleteOriginal(
        workspaceId: workspaceId,
        channelId: channelId,
        messageIds: event.messageIds ?? [],
      );
    }
    emit(MessagesState.deleteMessage(response: delete.ok));
  }

  void _onMarkAsRead(
    MarkAsReadMessageEvent event,
    Emitter<MessagesState> emit,
  ) async {
    _updateLastSeenMessageUseCase.execute(
      UpdateLastSeenMessageInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        lastSeenMessageId: event.messageId,
      ),
    );

    _markAsReadUseCase.execute(
      MarkAsReadInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
        messageId: event.messageId,
      ),
    );
  }

  FutureOr<void> _onLocalDeleteMessage(
    OnLocalDeleteMessageEvent event,
    Emitter<MessagesState> emit,
  ) async {
    var workspaceId = event.workspaceId;
    var channelId = event.channelId;
    if (event.userId != null) {
      var channel = getChannel(userId: event.userId);
      workspaceId = channel?.workspaceId;
      channelId = channel?.channelId;
    }
    AppEventBus.publish(
      DeleteMessageEvent(
        workspaceId: workspaceId!,
        channelId: channelId!,
        messageIds: event.messageIds ?? [],
      ),
    );
    await _localDeleteMessagesUseCase.execute(
      LocalDeleteMessagesInput(
        workspaceId: workspaceId,
        channelId: channelId,
        messageIds: event.messageIds ?? [],
      ),
    );
  }

  Future<void> _deleteOriginal({
    required String workspaceId,
    required String channelId,
    required List<String> messageIds,
  }) async {
    for (final messageId in messageIds) {
      final output = await _deleteOriginalMessageUseCase.execute(
        DeleteOriginalMessageInput(
          workspaceId: workspaceId,
          channelId: channelId,
          messageId: messageId,
        ),
      );
      for (final message in output.messages) {
        add(MessagesEvent.updateMessage(message));
      }
    }
  }

  FutureOr<void> _onPinUnPinMessage(
    OnPinUnPinMessage event,
    Emitter<MessagesState> emit,
  ) async {
    final output;
    if (event.userId != null) {
      output = await _pinUnpinDMMessageUseCase.execute(
        PinUnpinDMMessageInput(
          userId: event.userId!,
          messageId: event.messageId,
          status: event.status,
        ),
      );
    } else {
      output = await _pinUnpinMessageUseCase.execute(
        PinUnpinMessageInput(
          workspaceId: event.workspaceId,
          channelId: event.channelId,
          messageId: event.messageId,
          status: event.status,
        ),
      );
    }
    if (output.ok == true && (_channel == null || _channel?.isTemp == true)) {
      final upsertMessage = await _upsertPinUnPinMessageUseCase.execute(
        UpsertPinUnPinMessageInput(
          workspaceId: event.workspaceId,
          channelId: event.channelId,
          messageId: event.messageId,
          status: event.status,
          pinTime: TimeUtils.now(),
        ),
      );
      if (upsertMessage.message != null) {
        add(MessagesEvent.pinUnPinUpdateMessage(upsertMessage.message!));
      }
    }
  }

  void _onPinUnPinUpdateMessage(
    pinUnPinUpdateMessage event,
    Emitter<MessagesState> emit,
  ) {
    emit(MessagesState.refreshDelete());
    emit(MessagesState.pinUnPinUpdateMessage(event.message));
  }

  FutureOr<void> _onInitLoadPinUnPinMessage(
    InitLoadPinUnPinMessage event,
    Emitter<MessagesState> emit,
  ) async {
    Channel? channel = getChannel(
      workspaceId: event.workspaceId,
      channelId: event.channelId,
      userId: event.userId,
    );

    if (channel == null) {
      return;
    }

    _listenPinMessageEvent(
      channel.workspaceId,
      channel.channelId,
      userId: event.userId,
    );
  }

  void _onLoadPinUnPinMessage(
    OnLoadPinUnPinMessage event,
    Emitter<MessagesState> emit,
  ) {
    emit(MessagesState.loadPinUnPinUpdateMessage(event.messages));
  }

  void _onUpdateChanelId(
    UpdateChannelIdEvent event,
    Emitter<MessagesState> emit,
  ) {
    workspaceId = event.workspaceId;
    channelId = event.channelId;
  }
}
