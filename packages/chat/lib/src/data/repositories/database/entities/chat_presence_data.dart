import 'package:json_annotation/json_annotation.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';

import '../../../../common/di/di.dart';
import '../enums/chat_presence_state.dart';

part 'chat_presence_data.g.dart';

@JsonSerializable(explicitToJson: true)
class ChatPresenceData {
  ChatPresenceData({
    this.presenceState,
    this.lastUpdateInSeconds,
    this.lastUpdateTime,
  });

  ChatPresenceStateEnum? presenceState;
  int? lastUpdateInSeconds;
  String? lastUpdateTime;

  @JsonKey(includeFromJson: false, includeToJson: false)
  bool get isOnline => presenceState == ChatPresenceStateEnum.ONLINE;

  String getOnlineStatus({
    required bool use24Hour,
    ChatPresenceStateEnum? presenceStatus,
  }) {
    final appLocalizations = getIt<AppLocalizations>();

    if (isOnline || presenceStatus == ChatPresenceStateEnum.ONLINE) {
      return appLocalizations.online;
    }

    final lastTime = TimeUtils.parseUTCStringToDateTime(lastUpdateTime);
    if (lastTime == null) return ' ';

    final current = DateTime.now();
    final duration = current.difference(lastTime);

    if (duration.inSeconds < 60) {
      return appLocalizations.lastSeenJustNow;
    }

    if (duration.inMinutes < 60) {
      return appLocalizations.lastSeenMinuteAgo('${duration.inMinutes}');
    }

    final isSameDay = lastTime.isSameDay(current);

    if (duration.inHours < 24 && isSameDay) {
      return appLocalizations.lastSeenHourAgo('${duration.inHours}');
    }

    final yesterday = DateTime(current.year, current.month, current.day - 1);
    final isYesterday = lastTime.isSameDay(yesterday);

    if (isYesterday) {
      return appLocalizations.lastSeenYesterdayAtTime(
        '${lastTime.toStringTime(use24Hour: use24Hour)}',
      );
    }

    return appLocalizations.lastSeenDate(
      '${lastTime.toShortYMD(appLocalizations.localeName)}',
    );
  }

  factory ChatPresenceData.fromJson(Map<String, dynamic> json) =>
      _$ChatPresenceDataFromJson(json);

  Map<String, dynamic> toJson() => _$ChatPresenceDataToJson(this);
}
