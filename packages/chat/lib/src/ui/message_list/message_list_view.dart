import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:app_core/core.dart' hide Config;
import 'package:auto_route/auto_route.dart';
import 'package:dartx/dartx.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:scrollview_observer/scrollview_observer.dart';
import 'package:shared/shared.dart';
import 'package:sticker/sticker.dart' hide Config;
import 'package:visibility_detector/visibility_detector.dart';
import 'package:ziichat_ui/ziichat_ui.dart'
    hide InviteToChannelBottomSheet, MessageStatus;
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import '../../common/di/di.dart';
import '../../data/repositories/database/entities/channel_local_metadata.dart';
import '../../data/repositories/database/entities/translated_result.dart';
import '../../data/repositories/extensions/file_path_extensions.dart';
import '../../domain/event_bus/message/resend_attachment_event.dart';
import '../../domain/event_bus/message/show_quote_message_event.dart';
import '../../domain/event_bus/show_member_settings_event.dart';
import '../../utils/name_utils.dart';
import '../channel_info/bloc/channel_info_bloc.dart';
import '../channel_view/bloc/channel_view/channel_view_bloc.dart';
import '../channel_view/bloc/list_member/list_member_bloc.dart';
import '../friend_profile/user_profile_handler.dart';
import '../member_settings/member_settings_handler.dart';
import '../member_settings/model_member_setting_profile.dart';
import '../translate_to/bloc/translate_to_bloc.dart';
import 'action_card_state.dart';
import 'message_handler.dart';
import 'reaction_handler.dart';
import 'widgets/animated_message_item_widget.dart';
import 'widgets/message_view_factory.dart';

// Các sự kiện của Chat
class StandbyKeepMessageListEvent {
  final int count;
  final bool isRemove;

  StandbyKeepMessageListEvent({
    required this.count,
    required this.isRemove,
  });
}

class MessageListView extends StatefulWidget {
  const MessageListView(
      {required this.sendMessageHandler,
      required this.translateToHandler,
      super.key,
      this.onTapMessage,
      this.onChangeVisibility,
      this.workspaceId,
      this.channelId,
      this.userId,
      this.messageId,
      this.channel,
      this.isMessageRequestNotifier,
      this.goToDMMessage,
      this.goToViewAvatar,
      this.goToChannelInfo,
      this.mapCheckedMessage,
      this.isEnableClickBottom,
      this.isHiddenEditor,
      this.countItem,
      this.isChangeAppbar,
      this.metaData,
      this.translatedResultList,
      this.isHiddenBlockUser,
      this.onClickTakeChannelAvatarPhoto,
      this.onClickTapOpenGalleryAvatar,
      this.countNewValue,
      this.isChannelLoaded = false,
      this.hasPinned = false,
      this.hasMinimizeCall = false,
      this.isShowQuoteViewMessage = false,
      this.isCloseEditViewMessage = true,
      this.fromNotification,
      this.clearAllMessage});

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? messageId;
  final Channel? channel;
  final bool hasPinned;
  final bool hasMinimizeCall;
  final SendMessageHandler sendMessageHandler;
  final TranslateToHandler translateToHandler;
  final void Function(String messageId, String? userId)? onTapMessage;
  final void Function(
    int firstIndex,
    int lastIndex,
    Message firstItem,
    Message lastItem,
    Map<String, GlobalKey> timeIndicatorGlobalKeys,
    double paddingTop,
  )? onChangeVisibility;
  final ValueNotifier<bool>? isMessageRequestNotifier;
  final void Function(String userId)? goToDMMessage;
  final void Function(String avatarUrl)? goToViewAvatar;
  final VoidCallback? goToChannelInfo;
  final Map<String, Message>? mapCheckedMessage;
  final ValueNotifier<bool>? isEnableClickBottom;
  final ValueNotifier<bool>? isHiddenEditor;
  final ValueNotifier<int>? countItem;
  final ValueNotifier<int>? countNewValue;
  final ValueNotifier<bool>? isChangeAppbar;
  final ValueNotifier<bool>? isHiddenBlockUser;
  final void Function(Channel channel)? onClickTakeChannelAvatarPhoto;
  final void Function(Channel channel)? onClickTapOpenGalleryAvatar;
  final bool isChannelLoaded;
  final bool isShowQuoteViewMessage;
  final bool isCloseEditViewMessage;
  final bool? fromNotification;

  // _metadata
  final ChannelLocalMetadata? metaData;
  final List<TranslatedResult>? translatedResultList;
  final VoidCallback? clearAllMessage;

  @override
  State<MessageListView> createState() => MessageListViewState();
}

class MessageListViewState extends State<MessageListView>
    with
        AutoRouteAwareStateMixin,
        TickerProviderStateMixin,
        WidgetsBindingObserver {
  final PagingController<String?, Message> _pagingController = PagingController(
    firstPageKey: null,
    invisibleItemsThreshold: 20,
  );
  MessagesBloc? _messagesBloc;
  ListMemberBloc? _memberBloc;
  ListChatUserBloc? _userBloc;
  final Set<int> _visibleItems = {};
  Map<String, Member> _members = {};
  Map<String, ChatUser> _users = {};

  int _pageSize = 100;
  String? _nextPageToken;
  bool _onLoadingMore = false;
  bool _firstTimeScrolled = false;
  bool hasShowBottomSheetInvitation = false;
  final ValueNotifier<ActionCardState> _actionCardValueNotifier = ValueNotifier(
    ActionCardState(
      hasUserMessage: true,
      hasAvatar: true,
      hasAliasName: true,
      hasOtherMember: true,
    ),
  );
  String? _workspaceId;
  String? _channelId;
  String? _userId;
  String? _focusableMessageId;
  String? _animateMessageId;
  Channel? _channel;
  StreamSubscription? _showMemberSettingsSubscription;
  StreamSubscription? _updatedMessageSubscription;
  StreamSubscription? _clearMessageSubscription;
  StreamSubscription? _deleteMessageSubscription;
  StreamSubscription? _callCheckMessageSubscription;
  StreamSubscription? _cancelCheckMessageSubscription;
  StreamSubscription? _callPinUnPinMessageSubscription;
  StreamSubscription? _pinUnPinMessageUpdateSubscription;
  StreamSubscription? _clickPinnedMessageSubscription;
  ModelMemberSettingProfile? _myProfile;
  final _memberSettingsHandler = MemberSettingsHandler();
  bool _isInitiate = false;

  bool isChannel() => widget.userId == null;
  List<UserPrivateData> _listUserPrivateData = [];
  late ReactionHandler _reactionHandler;
  late StreamSubscription? _listenReportMessageEventSubscription;
  ValueNotifier<ui.ProcessStatus> processStatus = ValueNotifier(
    ui.ProcessStatus.loading,
  );
  late AppLocalizations appLocalizations;

  ValueNotifier<String> processContent = ValueNotifier("");
  final _messageReportHandler = MessageHandler();
  Map<String, ChatUser> _mapBlockUser = {};
  bool isOpenCheckBox = false;

  bool _unReadMessageIndicatorShowed = false;
  Timer? _unreadIndicatorTimer;
  String? _lastSeenMessageId;
  String? _lastMarkAsReadMessageId;
  final ValueNotifier<bool> _showUnreadIndicatorNotifier = ValueNotifier(false);
  bool _shouldUpdateIndicatorValue = true;
  Timer? _refreshIndicatorTimer;
  final List<String> _visibleMessages = [];
  BuildContext? _messageListCtx;

  ValueNotifier<int> get _countNewNotifier =>
      widget.countNewValue ?? ValueNotifier(0);
  ValueNotifier<bool> _showScrollToBottomNotifier = ValueNotifier(false);

  final _offsetScrollOver = 60;
  final _myUserId = Config.getInstance().activeSessionKey ?? '';
  late ui.MessageOptions messageOption = ui.MessageOptions.delete;
  late StreamSubscription? _resendMessage;
  late StreamSubscription? _resendAttachmentSubscription;
  late StreamSubscription? _listenBlockEventSubscription;
  late StreamSubscription? _onClickMessageNotificationSubscription;
  late ValueNotifier<bool> _isBlockUserReport = ValueNotifier(false);

  bool _isActive = true;

  bool _isRoleMember = true;
  late bool is24HourFormat;

  Map<String, GlobalKey> _timeIndicatorGlobalKeys = {};
  final _timeIndicatorHeight = 28;
  final pinMessageHeight = 50.h;
  final minimizeCallHeight = 42.h;
  final ListAnimationManager _animationManager = ListAnimationManager();
  late Message? _pinnedMessage;

  // User ids of messages
  Set<String> _setUserIds = {};

  Timer? _timerNotificationFocusMessage;

  // Declare for list observer
  late ListObserverController _observerController;
  late ChatScrollObserver _chatObserver;
  final ScrollController _scrollController = ScrollController();

  final _curve = Curves.easeInOutCirc;

  late bool? _fromNotification;

  bool _hasPageRequestListener = false;
  bool _messageSynced = false;

  bool _isAppPaused = false;
  List<Message> _incomingMessages = [];

  @override
  void initState() {
    super.initState();
    _fromNotification = widget.fromNotification;
    hasShowBottomSheetInvitation = widget.messageId != null;

    _isActive = true;
    _listenScrollMessages();
    _messagesBloc = context.read<MessagesBloc>();
    _memberBloc = context.read<ListMemberBloc>();
    _userBloc = context.read<ListChatUserBloc>();
    _showMemberSettingsSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ShowMemberSettingsEvent>()
        .listen(_onShowMemberSettings);
    _updatedMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<UpdateMessageEvent>()
        .listen(_onUpdateMessage);
    _deleteMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<DeleteMessageEvent>()
        .listen(_onDeleteMessage);
    _callCheckMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<CallCheckMessagesEvent>()
        .listen(_onCallCheckMessage);
    _cancelCheckMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<CancelAppBarChannelViewEvent>()
        .listen(_onCancelAppBarChannelView);
    _resendMessage = GetIt.instance
        .get<AppEventBus>()
        .on<ResendMessageEvent>()
        .listen(_handleResendMessageEvent);
    _resendAttachmentSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ResendAttachmentEvent>()
        .listen(_handleResendAttachmentEvent);
    _onClickMessageNotificationSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<OnClickMessageNotificationEvent>()
        .listen(_handleOnClickMessageNotificationEvent);
    _callPinUnPinMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<CallPinUnPinMessageEvent>()
        .listen(_onPinUnPinMessageEvent);
    _pinUnPinMessageUpdateSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<PinUnPinMessageUpdateEvent>()
        .listen(_onPinUnPinUpdateMessageEvent);
    _clickPinnedMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<OnClickPinnedMessageEvent>()
        .listen(_onClickPinnedMessageEvent);
    _messagesBloc!.add(
      MessagesEvent.initiate(
        workspaceId: widget.workspaceId,
        channelId: widget.channelId,
        userId: widget.userId,
        limit: _pageSize,
      ),
    );

    _initialize();
    setupListenReportMessageEventHandler();
    setupListenBlockUserEventHandler();
    setupListenClearMessageAllEventHandler();
    is24HourFormat = GetIt.instance.get<AppBloc>().state.is24HourFormat;
    getIt<UserInfoHandler>().setMessageBloc(_messagesBloc!);

    //region Setup chat observer
    _observerController = ListObserverController(controller: _scrollController)
      ..cacheJumpIndexOffset = false;

    _chatObserver = ChatScrollObserver(_observerController)
      ..fixedPositionOffset = 5
      ..toRebuildScrollViewCallback = () {};
    _chatObserver.onHandlePositionResultCallback = (result) {
      switch (result.type) {
        case ChatScrollObserverHandlePositionType.keepPosition:
          break;
        case ChatScrollObserverHandlePositionType.none:
          break;
      }
    };
    //endregion Setup chat observer

    if (StringUtils.isNullOrEmpty(_lastSeenMessageId)) {
      _lastSeenMessageId = widget.channel?.lastSeenMessageId;
    }
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _isAppPaused = false;
      _handleResumeIncomingMessages();
    }
    if (state == AppLifecycleState.paused) {
      _isAppPaused = true;
    }
  }

  Future<void> _handleResumeIncomingMessages() async {
    for (final msg in _incomingMessages) {
      _handleAddMessageState(context, msg);
      await Future.delayed(DurationUtils.ms50);
    }
    _incomingMessages.clear();
  }

  void _onUpdateMessageList(StandbyKeepMessageListEvent event) {
    _chatObserver.standby(
      mode: ChatScrollObserverHandleMode.normal,
      changeCount: event.count,
      isRemove: event.isRemove,
    );
  }

  //endregion Chat observer

  void _listenScrollMessages() {
    _scrollController.addListener(() {
      _handleFocusMessageItemState();
      _handleScrollToBottomIndicator();
    });
  }

  void _handleFocusMessageItemState() {
    if (_focusableMessageId.isNullOrEmpty) return;
    _focusableMessageId = '';
    _animateMessageId = '';
    _reRender();
  }

  void _handleScrollToBottomIndicator() {
    _showScrollToBottomNotifier.value =
        _scrollController.offset > _offsetScrollOver;
    _onChangeVisibility();
  }

  @override
  void didUpdateWidget(covariant MessageListView oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (_channel != widget.channel) {
      final memberMe = _members[Config.getInstance().activeSessionKey];
      final isRoleMember =
          MemberSettingsHandler.getRoleFromName(memberMe?.role) == Roles.member;

      final hasAvatar =
          (widget.channel?.avatar ?? '').isNotEmpty || isRoleMember;
      final hasOtherMember = (widget.channel?.totalMembers ?? 1) > 1;

      if ((_actionCardValueNotifier.value.hasOtherMember != hasOtherMember ||
              _actionCardValueNotifier.value.hasAvatar != hasAvatar) &&
          _actionCardValueNotifier.value.showActionCard) {
        _actionCardValueNotifier.value =
            _actionCardValueNotifier.value.copyWith(
          hasOtherMember: hasOtherMember,
          hasAvatar: hasAvatar,
        );
      }
    }

    if (widget.isChannelLoaded &&
        (widget.channel == null || widget.channel!.channelId.isEmpty)) {
      _pagingController.appendLastPage([]);
    }

    if (StringUtils.isNullOrEmpty(_channel?.workspaceId) &&
        StringUtils.isNullOrEmpty(_channel?.channelId)) {
      _initialize();
    }

    if (widget.channel != null &&
        StringUtils.isNullOrEmpty(_lastSeenMessageId)) {
      _lastSeenMessageId = widget.channel!.lastSeenMessageId;
      _handleScrollToInitialMessage();
    }
  }

  void _initialize() {
    _workspaceId = widget.workspaceId;
    _channelId = widget.channelId;
    _userId = widget.userId;
    _channel = widget.channel;
    _workspaceId = _channel?.workspaceId ?? widget.workspaceId;
    _channelId = _channel?.channelId ?? widget.channelId;

    if (!StringUtils.isNullOrEmpty(_workspaceId) &&
        !StringUtils.isNullOrEmpty(_channelId)) {
      _initBloc(_workspaceId!, _channelId!);
    }
    _reactionHandler = ReactionHandler.getInstance(
      channelId: _channelId,
      workspaceId: _workspaceId,
      userId: _userId,
    );
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _reactionHandler.init(context: context);
      _reactionHandler.addCallbackOnReact(
        (message) {
          _messagesBloc?.add(MessagesEvent.updateMessage(message));
        },
      );
    });
  }

  void _initBloc(String workspaceId, String channelId) {
    if (_isInitiate) return;

    _workspaceId = workspaceId;
    _channelId = channelId;

    _messagesBloc!.add(
      MessagesEvent.initiate(
        workspaceId: _workspaceId,
        channelId: _channelId,
        userId: _userId,
        limit: _pageSize,
      ),
    );

    _memberBloc?.add(
      ListMemberInit(
        workspaceId: _workspaceId!,
        channelId: _channelId!,
      ),
    );

    if (_userId != null) {
      _setUserIds = {_myUserId, _userId!};
      _userBloc?.add(ListChatUserInit(setIds: _setUserIds));
    }
    _isInitiate = true;
  }

  void _loadMore(String? nextPageToken) {
    _onLoadingMore = true;
    _messagesBloc!.add(
      MessagesEvent.loadMore(
        workspaceId: _workspaceId,
        channelId: _channelId,
        userId: _userId,
        limit: _pageSize,
        nextPageToken: _nextPageToken,
      ),
    );
  }

  @override
  void dispose() {
    _pagingController.dispose();
    _scrollController.dispose();
    _memberBloc!.add(ListMemberUnSubscription());
    _userBloc!.add(ListChatUserUnSubscription());
    _showMemberSettingsSubscription?.cancel();
    _updatedMessageSubscription?.cancel();
    _messagesBloc!.add(MessagesEvent.dispose());
    _callCheckMessageSubscription?.cancel();
    _cancelCheckMessageSubscription?.cancel();
    _actionCardValueNotifier.dispose();
    _listenReportMessageEventSubscription?.cancel();
    _deleteMessageSubscription?.cancel();
    _clearMessageSubscription?.cancel();
    _reactionHandler.dispose();
    _unreadIndicatorTimer?.cancel();
    _listenBlockEventSubscription?.cancel();
    _resendMessage?.cancel();
    _onClickMessageNotificationSubscription?.cancel();
    _callPinUnPinMessageSubscription?.cancel();
    _pinUnPinMessageUpdateSubscription?.cancel();
    _clickPinnedMessageSubscription?.cancel();
    _resendAttachmentSubscription?.cancel();
    _timerNotificationFocusMessage?.cancel();
    _fromNotification = false;
    _hasPageRequestListener = false;
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didPushNext() {
    _isActive = false;
  }

  @override
  void didPopNext() {
    _isActive = true;
    _channel = GetIt.instance
        .get<ChannelRepository>()
        .getChannel(workspaceId: _workspaceId!, channelId: _channelId!);
    setState(() {
      _lastSeenMessageId = _channel?.lastSeenMessageId;
    });
  }

  int _findMessageIndex(List<Message> items, String messageId, String? ref) {
    return items.indexWhere(
      (msg) =>
          msg.messageId == messageId ||
          (msg.ref != null && ref != null && msg.ref == ref),
    );
  }

  void _blocMessageListener(BuildContext context, MessagesState state) {
    state.when(
      initial: () => _handleInitialState(),
      waiting: () => _handleWaitingState(),
      loaded: (messages, lastSeenMessageId, hasNext, nextPageToken) =>
          _handleLoadedState(
        messages,
        lastSeenMessageId,
        hasNext,
        nextPageToken,
      ),
      onLoadMore: (messages, hasNext, nextPageToken) =>
          _handleOnLoadMoreState(messages, hasNext, nextPageToken),
      addTempMessage: (message) => _handleAddTempMessageState(message),
      addMessage: (message) => _handleAddMessageState(context, message),
      updateMessage: (message) => _handleUpdateMessageState(message),
      updateMessageStatus: (messageStatus, messageId, messageRef) =>
          _handleUpdateMessageStatusState(messageStatus, messageId, messageRef),
      synced: (messages) => _handleSyncedState(messages),
      error: (error) => _handleErrorState(context, error),
      clearAllMessage: (workspaceId, channelId) =>
          _handleClearAllMessageState(workspaceId, channelId),
      deleteMessage: (response) => _handleDeleteMessageState(context, response),
      resendMessage: (message) => _handleResendMessageState(message),
      refreshDelete: () => _handleRefreshDeleteState(),
      pinUnPinUpdateMessage: (message) =>
          _handlePinUnPinUpdateMessageState(message),
      loadPinUnPinUpdateMessage: (messages) =>
          _handleLoadPinUnPinUpdateMessageState(messages),
    );

    _reObserveUsers(
      _pagingController.itemList?.map((item) => item.userId).toSet() ?? {},
    );
  }

  void _handleInitialState() {
    _pagingController.refresh();
  }

  void _handleWaitingState() {
    // No action needed
  }

  void _handleLoadedState(
    List<Message> messages,
    String? lastSeenMessageId,
    bool hasNext,
    String? nextPageToken,
  ) {
    _markAnimated(messages);
    if (!mounted) return;
    _onLoadingMore = false;
    _nextPageToken = nextPageToken;

    // Handle duplicate first dm message
    if ((_pagingController.itemList?.isNotEmpty ?? false) &&
        _pagingController.nextPageKey == null &&
        messages.isNotEmpty) {
      _pagingController.itemList = null;
    }

    if (_pagingController.nextPageKey == null) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _scrollController.jumpTo(0.1);
      });
    }

    setState(() {
      if (hasNext) {
        _pagingController.appendPage(messages, _nextPageToken);
      } else {
        _pagingController.appendLastPage(messages);
        handleActionCardStateAfterSync();
      }
    });

    if (!_hasPageRequestListener) {
      _hasPageRequestListener = true;
      _pagingController.addPageRequestListener(_loadMore);
    }

    _updateLastReceiveMessage();
    _handleScrollToInitialMessage();
  }

  void _handleOnLoadMoreState(
    List<Message> messages,
    bool hasNext,
    String? nextPageToken,
  ) {
    _observerController.reattach();
    _onLoadingMore = false;
    _nextPageToken = nextPageToken;
    final items = List<Message>.from(_pagingController.itemList ?? []);
    final index =
        items.indexWhere((item) => item.messageId == messages.first.messageId);
    if (index != -1) {
      setState(() {
        updateItemsLoadMore(messages);
      });
    } else {
      setState(() {
        if (hasNext) {
          _pagingController.appendPage(messages, _nextPageToken);
        } else {
          _pagingController.appendLastPage(messages);
        }
      });
    }

    _updateLastReceiveMessage();

    if (_lastSeenMessageId.isNullOrEmpty && widget.messageId.isNullOrEmpty) {
      _firstTimeScrolled = true;
      _markAsReadMessage();
    }
  }

  void updateItemsLoadMore(List<Message> messages) {
    final items = List<Message>.from(_pagingController.itemList ?? []);

    final updatedMap = {for (var item in messages) item.messageId: item};

    final newList = items.map((item) {
      if (updatedMap.containsKey(item.id)) {
        return updatedMap[item.id]!; // dùng item đã được cập nhật
      }
      return item; // giữ nguyên nếu không có cập nhật
    }).toList();

    _pagingController.itemList = newList;
  }

  void _handleAddTempMessageState(Message message) {
    debugPrint(
      'MessageListViewState.addTempMessage: ${message.messageId} - ${message.ref}',
    );

    _onUpdateMessageList(
      StandbyKeepMessageListEvent(
        count: 1,
        isRemove: false,
      ),
    );

    setState(() {
      _pagingController.itemList?.insert(0, message);
    });

    if (message.messageViewType.value > 2 &&
        !_actionCardValueNotifier.value.hasUserMessage) {
      _actionCardValueNotifier.value =
          _actionCardValueNotifier.value.copyWith(hasUserMessage: true);
    }

    _showUnreadIndicatorNotifier.value = false;
    _shouldUpdateIndicatorValue = false;
    _refreshIndicatorTimer?.cancel();
    _refreshIndicatorTimer = Timer(DurationUtils.ms500, () {
      _shouldUpdateIndicatorValue = true;
    });

    // Scroll to bottom when sending message
    _scrollToBottom();
  }

  void _handleAddMessageState(BuildContext context, Message message) {
    debugPrint(
      'MessageListViewState.addMessage: ${message.messageId} - ${message.ref}',
    );
    if (_shouldInitiate()) {
      _initBloc(message.workspaceId, message.channelId);
      _initChannelBloc(context);
      return;
    }

    if (_isAppPaused) {
      _incomingMessages.add(message);
      return;
    }

    if (_pagingController.itemList == null) {
      _pagingController.itemList = [message];
    } else {
      final list = List<Message>.from(_pagingController.itemList!);
      final index = _findMessageIndex(list, message.messageId, message.ref);

      if (index != -1) {
        _handleUpdateMessageState(message);
        return;
      } else {
        _onUpdateMessageList(
          StandbyKeepMessageListEvent(count: 1, isRemove: false),
        );

        final insertIndex = findInsertIndex(list, message);

        setState(() {
          if (insertIndex == -1) {
            list.add(message);
          } else {
            list.insert(insertIndex, message);
          }
          _pagingController.itemList = list;
        });
      }
    }

    if (message.messageViewType.value > 2 &&
        !_actionCardValueNotifier.value.hasUserMessage) {
      _actionCardValueNotifier.value =
          _actionCardValueNotifier.value.copyWith(hasUserMessage: true);
    }

    _updateLastReceiveMessage();

    if (!_isActive) return;

    if (_scrollController.offset < _offsetScrollOver ||
        message.userId == _myUserId) {
      _markAsReadMessage();
      if (!_unReadMessageIndicatorShowed) {
        _lastSeenMessageId = _lastMarkAsReadMessageId;
      }
    }

    if (_channel?.isTemp == true) {
      _messagesBloc?.add(
        MessagesEvent.InitLoadPinUnPinMessage(
          workspaceId: isChannel() ? message.workspaceId : null,
          channelId: isChannel() ? message.channelId : null,
          userId: isChannel() ? null : widget.userId,
        ),
      );
      _updateChannel(message.workspaceId, message.channelId);
    }
  }

  void _handleUpdateMessageState(Message message) {
    debugPrint(
      'MessageListViewState.updateMessage: ${message.messageId} - ${message.ref}',
    );

    /// using when channelID and workspaceID null or empty
    if (_shouldInitiateChannel()) {
      _updateChannel(message.workspaceId, message.channelId);
    }

    if (message.messageViewType.value > 2 &&
        !_actionCardValueNotifier.value.hasUserMessage) {
      _actionCardValueNotifier.value =
          _actionCardValueNotifier.value.copyWith(hasUserMessage: true);
      _messagesBloc?.add(
        UpdateChannelIdEvent(
          workspaceId: message.workspaceId,
          channelId: message.channelId,
        ),
      );
    }

    final items = List<Message>.from(_pagingController.itemList ?? []);
    final index = _findMessageIndex(items, message.messageId, message.ref);

    if (index != -1) {
      final oldMessage = items[index];
      if (message != oldMessage) {
        _preserveLocalFilePaths(oldMessage, message);

        items[index] = message;
        _updateMessageList(items);

        if (message.userId == _myUserId && index == 0) {
          _markAsReadMessage();
        }
      }
    }

    _updateLastReceiveMessage();
  }

  /// Check and preserve filePath information from old attachments
  void _preserveLocalFilePaths(Message oldMessage, Message newMessage) {
    // Use extension to update filePath
    newMessage.preserveLocalFilePaths(oldMessage);
  }

  /// Update filePath from old mediaObject to new mediaObject if needed
  void _updateFilePathForMediaObject(
    MediaObject? oldMedia,
    MediaObject? newMedia,
  ) {
    // Use extension to update filePath
    MediaObjectFilePathExtension.updateFilePathForMediaObject(
      oldMedia,
      newMedia,
    );
  }

  /// Directly update raw fields of Attachment
  void _updateAttachmentRawFields(
    Attachment oldAttachment,
    Attachment newAttachment,
  ) {
    // Use extension to update raw fields
    newAttachment.updateRawFields(oldAttachment);
  }

  void _handleUpdateMessageStatusState(
    MessageStatus messageStatus,
    String messageId,
    String? messageRef,
  ) {
    final items = List<Message>.from(_pagingController.itemList ?? []);
    final index = _findMessageIndex(items, messageId, messageRef);

    if (index != -1) {
      final oldStatus = items[index].messageStatusRaw;
      final newStatus = messageStatus.rawValue();

      if (oldStatus != newStatus) {
        items[index].messageStatusRaw = newStatus;
        _updateMessageList(items);

        if (items[index].userId == _myUserId && index == 0) {
          _markAsReadMessage();
        }
      }
    }
  }

  void _handleSyncedState(List<Message> messages) {
    final items = List<Message>.from(_pagingController.itemList ?? []);

    for (final message in messages) {
      final index = _findMessageIndex(items, message.messageId, message.ref);

      if (index != -1) {
        items[index] = message;
        continue;
      }

      final insertIndex = findInsertIndex(items, message);

      if (insertIndex == -1) {
        items.add(message);
      } else {
        items.insert(insertIndex, message);
      }
    }
    _updateMessageList(items);
    _updateLastReceiveMessage();

    if (_lastSeenMessageId.isNullOrEmpty &&
        widget.messageId.isNullOrEmpty &&
        !_firstTimeScrolled) {
      _firstTimeScrolled = true;
      _markAsReadMessage();
    }

    _handleScrollToInitialMessage();
    _messageSynced = true;
    handleActionCardStateAfterSync();
  }

  void _handleErrorState(BuildContext context, String error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(error)),
    );
  }

  void _handleClearAllMessageState(String? workspaceId, String? channelId) {
    if (workspaceId == _channel?.workspaceId &&
        channelId == _channel?.channelId) {
      _pagingController.itemList?.clear();
      _showScrollToBottomNotifier.value = false;
      final memberMe = _members[Config.getInstance().activeSessionKey];
      final isRoleMember =
          MemberSettingsHandler.getRoleFromName(memberMe?.role) == Roles.member;

      final hasAvatar =
          (widget.channel?.avatar ?? '').isNotEmpty || isRoleMember;
      final hasOtherMember = (widget.channel?.totalMembers ?? 1) > 1;
      _actionCardValueNotifier.value = _actionCardValueNotifier.value.copyWith(
        hasUserMessage: false,
        hasAvatar: hasAvatar,
        hasOtherMember: hasOtherMember,
      );
      _onChangeVisibility();
    }
    setState(() {});
  }

  Future<void> _handleDeleteMessageState(
    BuildContext context,
    bool response,
  ) async {
    widget.isHiddenEditor?.value = false;
    widget.isChangeAppbar?.value = false;
    setState(() {
      isOpenCheckBox = false;
    });
    AppEventBus.publish(PopToChannelViewEvent());
    await Future.delayed(Duration(milliseconds: 100));
    if (response == false) {
      ui.DialogUtils.showErrorOccurredTranslateDialog(
        context,
        onOkClicked: () {
          AppEventBus.publish(PopToChannelViewEvent());
        },
      );
    }
  }

  void _handleResendMessageState(Message message) {
    _onMessageResend(message);
  }

  void _handleRefreshDeleteState() {
    // No action needed
  }

  void _handlePinUnPinUpdateMessageState(Message message) {
    _pinnedMessage = message;
    handlePinUnPinMessageUpdate(message);
  }

  void _handleLoadPinUnPinUpdateMessageState(List<Message> messages) {
    _pinnedMessage = messages.isEmpty ? null : messages.first;
    if (_pinnedMessage != null) {
      handlePinUnPinMessageUpdate(_pinnedMessage!);
    }
  }

  void handleActionCardStateAfterSync() {
    if (_pagingController.value.nextPageKey != null || !_messageSynced) return;
    final items = List<Message>.from(_pagingController.itemList ?? []);

    if (items.isEmpty) {
      _actionCardValueNotifier.value =
          _actionCardValueNotifier.value.copyWith(hasUserMessage: false);
      return;
    }

    final hasUserMessage = items.any((message) => !message.isSystemOrLocalType);
    _actionCardValueNotifier.value =
        _actionCardValueNotifier.value.copyWith(hasUserMessage: hasUserMessage);
  }

  int findInsertIndex(List<Message> list, Message message) {
    final time = message.createTime;
    if (time == null) return list.length;

    int left = 0;
    int right = list.length;

    while (left < right) {
      final mid = (left + right) >> 1;
      final midTime = list[mid].createTime;

      if (midTime == null || midTime.isBefore(time)) {
        right = mid;
      } else {
        left = mid + 1;
      }
    }

    return left;
  }

  void _updateMessageList(List<Message> items) {
    setState(() {
      if (_pagingController.itemList != null) {
        _pagingController.itemList!
          ..clear()
          ..addAll(items);
      } else {
        _pagingController.itemList = items;
      }
      _animateMessageId = '';
    });
  }

  /// Re-observes users by tracking changes in the user ID set.
  /// - Merges the current and previous user ID sets.
  /// - If there are changes, it triggers a user update event and re-renders the UI.
  /// - Updates the stored user ID set after processing.
  void _reObserveUsers(Set<String> setIds) {
    final mergedSetIds = setIds.union(_setUserIds);
    final isEqual = mergedSetIds.containsAll(_setUserIds) &&
        _setUserIds.containsAll(mergedSetIds);

    _setUserIds = mergedSetIds;
    if (!isEqual) {
      _userBloc?.add(ListChatUserInit(setIds: mergedSetIds));
    }
  }

  //Handle Scroll to message when click notification or exist lastSeenMessageId
  Future<void> _handleScrollToInitialMessage() async {
    if (_firstTimeScrolled) return;

    if (widget.messageId.isNotNullOrEmpty) {
      _scrollToInFocusableMessage();
      handleShowBottomSheetInvitationNotificationClick(widget.messageId!);
      return;
    }

    if (_lastSeenMessageId.isNotNullOrEmpty) {
      await animateToMessage(
        _lastSeenMessageId ?? '',
        itemPadding: 2,
        focus: false,
      );
      return;
    }

    // _firstTimeScrolled = true;
  }

  void _onMessageResend(Message message) {
    final items = List<Message>.from(_pagingController.itemList ?? []);
    items.removeWhere(
      (m) => m.ref == message.ref || m.messageId == message.messageId,
    );
    items.insert(0, message);
    _updateMessageList(items);
  }

  void _updateLastReceiveMessage() {
    final items = List<Message>.from(_pagingController.itemList ?? []);
    if (items.isEmpty) return;

    final messageHasReactIndex = items.indexWhere(
      (msg) => msg.isLastReceiveMessage,
    );

    final lastReceiveMessageIndex = items.indexWhere(
      (msg) =>
          msg.messageViewTypeRaw > 2 &&
          msg.userId != Config.getInstance().activeSessionKey!,
    );

    if (lastReceiveMessageIndex < 0 &&
        messageHasReactIndex == lastReceiveMessageIndex) return;

    if (messageHasReactIndex > -1) {
      items[messageHasReactIndex].isLastReceiveMessage = false;
    }

    items[lastReceiveMessageIndex].isLastReceiveMessage = true;
    _updateMessageList(items);
  }

  void _initChannelBloc(BuildContext context) {
    context.read<ChannelViewBloc>().add(
          OnInitChannelViewEvent(
            workspaceId: _workspaceId,
            channelId: _channelId,
            userId: _userId,
          ),
        );
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
        _reRender();
      },
    );
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      UserPrivateData userPrivateData = _listUserPrivateData.firstWhere(
        (user) => user.userId == userId,
      );
      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  void setupListenReportMessageEventHandler() {
    _listenReportMessageEventSubscription =
        GetIt.instance.get<AppEventBus>().on<ReportEvent>().listen(
              onReceivedListenReportMessageEvent,
            );
  }

  void onReceivedListenReportMessageEvent(event) async {
    if (event is CallReportMessageEvent) {
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }
      FocusScope.of(context).requestFocus(FocusNode());

      _messageReportHandler.showMessageReportBottomSheet(
        context,
        userId: event.userId,
        name: event.name,
        messageId: event.messageId,
        channelId: event.channelId,
        workspaceId: event.workspaceId,
      );
    }
  }

  void setupListenClearMessageAllEventHandler() {
    _clearMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ClearMessageEvent>()
        .listen(onReceivedClearMessageAllEvent);
  }

  void onReceivedClearMessageAllEvent(ClearMessageEvent event) async {
    if (event.channelId == _channel?.channelId &&
        event.workspaceId == _channel?.workspaceId) {
      _pagingController.itemList?.clear();
      widget.clearAllMessage?.call();
    }
  }

  void _blocReportMessageListener(
    BuildContext context,
    ReportMessageState state,
  ) {
    state.maybeWhen(
      initial: () {},
      showProcessDialog: () {},
      updateProcessDialog: (
        response,
        workspaceId,
        channelId,
        userId,
        name,
        isBlocked,
      ) {
        if (response) {
          Navigator.pop(context);
          _isBlockUserReport.value = isBlocked ?? false;
          if (isChannel()) {
            final memberMe = _members[MessageHandler.sessionKey];

            _messageReportHandler.showThankYouChannel1n(
              context,
              userId: userId,
              name: getAliasName(userId) != null ? getAliasName(userId) : name,
              workspaceId: workspaceId,
              channelId: channelId,
              isBlock: _isBlockUserReport,
              role: MemberSettingsHandler.getRoleFromName(memberMe?.role),
              goToChannelInfo: widget.goToChannelInfo,
            );
          } else {
            _messageReportHandler.showThankYouDmChannel(
              context,
              userId: userId,
              name: getAliasName(userId) != null ? getAliasName(userId) : name,
              isBlock: _isBlockUserReport,
            );
          }
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              popToChannelView();
            },
          );
        }
      },
      orElse: () {},
    );
  }

  void popToChannelView() {
    AppEventBus.publish(
      PopToChannelViewEvent(),
    );
  }

  void _onChannelInfoStateChanged(
    BuildContext context,
    ChannelInfoState state,
  ) {
    state.maybeWhen(
      channelAvatarChanged: (String? avatarPath) {
        setState(() {
          _channel?.avatar = avatarPath;
        });
      },
      orElse: () => {},
    );
  }

  void _blocBlockUserListener(BuildContext context, BlockUserState state) {
    state.maybeWhen(
      loadListBlockUser: (listBlockUser) {
        _mapBlockUser = {};
        listBlockUser?.forEach(
          (item) {
            _mapBlockUser.putIfAbsent(
              item.userId,
              () => ChatUser.fromJson(item.toJson()),
            );
          },
        );
      },
      updateProcessDialog: (response, bool? popOnlyMine) {
        if (response) {
          if (Navigator.canPop(context)) {
            _isBlockUserReport.value = true;
          }
        }
      },
      orElse: () {},
    );
  }

  void setupListenBlockUserEventHandler() {
    _listenBlockEventSubscription =
        GetIt.instance.get<AppEventBus>().on<ListenBlockUserEvent>().listen(
              onReceivedListenEvent,
            );
  }

  void onReceivedListenEvent(event) async {
    if (event is BlockEvent) {
      var user = ChatUser.fromJson(event.user);
      _mapBlockUser.putIfAbsent(user.userId, () => user);
      if (_users[user.userId] != null) {
        _users[user.userId]?.blocked = true;
      }
      _reRender();
    }
    if (event is UnBlockEvent) {
      _mapBlockUser.remove(event.userId);
      if (_users[event.userId] != null) {
        _users[event.userId]?.blocked = false;
      }
      _reRender();
    }
  }

  void _appBlocListener(
    BuildContext context,
    AppState state,
  ) {
    if (is24HourFormat != state.is24HourFormat) {
      setState(() {
        is24HourFormat = state.is24HourFormat;
      });
    }
  }

  GlobalKey _key = GlobalKey();

  @override
  Widget build(BuildContext context) {
    appLocalizations = AppLocalizations.of(context)!;

    // Calculate top padding when there is a pinned message
    final paddingTop = MediaQuery.of(context).padding.top;
    final paddingTopDynamic = paddingTop +
        (widget.hasPinned ? pinMessageHeight : 0) +
        (widget.hasMinimizeCall ? minimizeCallHeight : 0);

    return MultiBlocListener(
      listeners: [
        BlocListener<MessagesBloc, MessagesState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocMessageListener,
        ),
        BlocListener<ListMemberBloc, ListMemberState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocMemberListener,
        ),
        BlocListener<ListChatUserBloc, ListChatUserState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocChatUserListener,
        ),
        BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocUserPrivateListener,
        ),
        BlocListener<ReportMessageBloc, ReportMessageState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocReportMessageListener,
        ),
        BlocListener<BlockUserBloc, BlockUserState>(
          listenWhen: (prev, state) => prev != state,
          listener: _blocBlockUserListener,
        ),
        BlocListener<ChannelInfoBloc, ChannelInfoState>(
          listenWhen: (prev, state) => prev != state,
          listener: _onChannelInfoStateChanged,
        ),
        BlocListener<AppBloc, AppState>(
          listenWhen: (prev, state) => prev != state,
          listener: _appBlocListener,
        ),
      ],
      child: Stack(
        alignment: AlignmentDirectional.bottomCenter,
        fit: StackFit.expand,
        children: [
          Column(
            children: [
              Expanded(
                child: AppRawScrollBar(
                  interactive: false,
                  controller: _scrollController,
                  timeToFade: Duration(milliseconds: 1000),
                  padding: EdgeInsets.only(
                    right: 4.w,
                    left: 2.w,
                    top: paddingTop,
                  ),
                  thickness: 3.w,
                  fadeDuration: Duration(milliseconds: 1000),
                  child: ListViewObserver(
                    controller: _observerController,
                    sliverListContexts: () {
                      return [
                        if (_messageListCtx != null) _messageListCtx!,
                      ];
                    },
                    child: PagedListView<String?, Message>.separated(
                      key: _key,
                      reverse: true,
                      pagingController: _pagingController,
                      scrollController: _scrollController,
                      shrinkWrap: _chatObserver.isShrinkWrap,
                      physics: ChatObserverClampingScrollPhysics(
                        observer: _chatObserver,
                      ),
                      padding: EdgeInsets.only(
                        top: paddingTopDynamic,
                        bottom: _getPaddingBottom(),
                      ),
                      builderDelegate: PagedChildBuilderDelegate<Message>(
                        newPageProgressIndicatorBuilder: (context) =>
                            _buildSkeletonLoader(context),
                        firstPageProgressIndicatorBuilder: (context) =>
                            _buildSkeletonLoader(context),
                        itemBuilder: _buildMessageItem,
                        noItemsFoundIndicatorBuilder:
                            _noItemsFoundIndicatorBuilder,
                      ),
                      separatorBuilder: _buildSeparator,
                    ),
                  ),
                ),
              ),
              _buildActionCard(),
            ],
          ),
          _buildScrollToBottomWidget(),
        ],
      ),
    );
  }

  /// Calculate bottom padding when there is a block user message
  double _getPaddingBottom() {
    final bool isMessageRequest =
        widget.isMessageRequestNotifier?.value ?? false;
    final bool isHiddenBlockUser = widget.isHiddenBlockUser?.value ?? false;
    final bool closeEditPopupView = widget.isCloseEditViewMessage;
    final bool showQuotePopupView = widget.isShowQuoteViewMessage;
    final expandedPadding = 56.h + ((closeEditPopupView ? 0 : 2) * 14.sp);
    final defaultPadding = 4.h;

    if (isMessageRequest) {
      if (isHiddenBlockUser) {
        return showQuotePopupView || closeEditPopupView
            ? expandedPadding
            : defaultPadding;
      }
      return expandedPadding;
    }

    return showQuotePopupView || closeEditPopupView
        ? expandedPadding
        : defaultPadding;
  }

  Widget _buildScrollToBottomWidget() {
    return ValueListenableBuilder(
      valueListenable: _showScrollToBottomNotifier,
      builder: (context, show, _) {
        return ValueListenableBuilder(
          valueListenable: _countNewNotifier,
          builder: (context, countNew, _) {
            return Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: EdgeInsets.only(bottom: _getPaddingBottom()),
                child: ui.AppSizeTransition(
                  duration: 50,
                  child: !show
                      ? SizedBox.shrink()
                      : countNew > 0
                          ? JumpToBottomButtonContentWidget(
                              onPress: _scrollToFirstUnreadMessage,
                              count: countNew,
                            )
                          : Padding(
                              padding: const EdgeInsets.all(2.0),
                              child: JumpToBottomButtonWidget(
                                onPress: () => _scrollToBottom(),
                              ),
                            ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _scrollToFirstUnreadMessage() {
    // final index = _pagingController.itemList!.lastIndexWhere(
    //   (msg) => msg.messageId > _lastMarkAsReadMessageId!,
    // );
    _animateToMessageIndex(0, count: 1);
  }

  void _blocMemberListener(context, ListMemberState state) {
    state.when(
      loading: () {},
      loaded: (members) {
        _members = members;

        if (_members[_myUserId] != null) {
          _isRoleMember = MemberSettingsHandler.getRoleFromName(
                _members[_myUserId]!.role,
              ) ==
              ui.Roles.member;
        }
        _reObserveUsers(_members.keys.toSet());
        _loadMyProfile();
        setState(() {});
      },
      error: (error) => ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(error)),
      ),
    );
  }

  void _blocChatUserListener(context, ListChatUserState state) {
    state.when(
      loading: () {},
      loaded: (users) {
        _users = users;
        _reRender();
        _loadMyProfile();
      },
      error: (error) => ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(error)),
      ),
    );
  }

  Widget _buildSkeletonLoader(BuildContext context) {
    var skeletonView = [
      TextMessageSkeletonWidget(
        isDmChannel: !isChannel(),
        isSender: false,
        isLastMessage: false,
      ),
      TextSmallMessageSkeletonWidget(
        isDmChannel: !isChannel(),
        isSender: false,
        isLastMessage: false,
      ),
      ImageMessageSkeletonWidget(
        isDmChannel: !isChannel(),
        isSender: false,
        isLastMessage: true,
      ),
      TextMessageSkeletonWidget(
        isDmChannel: !isChannel(),
        isSender: true,
        isLastMessage: false,
      ),
      TextSmallMessageSkeletonWidget(
        isDmChannel: !isChannel(),
        isSender: true,
        isLastMessage: false,
      ),
      ImageMessageSkeletonWidget(
        isDmChannel: !isChannel(),
        isSender: true,
        isLastMessage: true,
      ),
    ];

    return SizedBox(
      height: skeletonView.length * 80.0,
      child: ListView(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        children: skeletonView,
      ),
    );
  }

  Widget _noItemsFoundIndicatorBuilder(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _actionCardValueNotifier.value =
          _actionCardValueNotifier.value.copyWith(hasUserMessage: false);
    });
    return SizedBox.shrink();
  }

  Widget _buildMessageItem(BuildContext _context, Message item, int index) {
    if (_messageListCtx != _context) {
      _messageListCtx = _context;
      _observerController.reattach();
      // Waiting for reattach to complete
      // WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      //   _chatObserver.observeSwitchShrinkWrap();
      // });
    }
    final isLastItem = index == _pagingController.itemList!.length - 1;

    var keys = [item.messageId, if (item.ref != null) item.ref!];

    var status = _animationManager.getAnimationStatus(keys);

    return status.animated
        ? _buildMessageItemWithTimeDivider(
            isLastItem,
            item,
            index,
          )
        : AnimatedMessageItemWidget(
            key: Key(status.key!),
            child: _buildMessageItemWithTimeDivider(isLastItem, item, index),
          );
  }

  Widget _buildMessageItemWithTimeDivider(
    bool isLastItem,
    Message item,
    int index,
  ) {
    return _needShowTimeDivider(isLastItem)
        ? Column(
            key: Key('Column_${item.messageId}'),
            children: [
              _buildTimeDivider(item.createTime!),
              _buildVisibilityDetector(index, item, isLastItem),
            ],
          )
        : _buildVisibilityDetector(index, item, isLastItem);
  }

  bool _needShowTimeDivider(bool isLastItem) => isLastItem && !_onLoadingMore;

  Widget _buildVisibilityDetector(int index, Message item, bool isLastItem) {
    Member? member = isChannel() ? _members[item.userId] : null;
    ChatUser? chatUser = isChannel() ? _users[item.userId] : null;
    chatUser?.aliasName = getAliasName(chatUser.userId);
    ui.MessageErrorReason? messageErrorReason = (isChannel()
            ? chatUser?.blocked == true
            : _mapBlockUser[widget.userId] != null)
        ? ui.MessageErrorReason.blocked
        : null;

    OriginalMessage? originalMessage = item.originalMessageRaw != null
        ? OriginalMessage.fromJson(jsonDecode(item.originalMessageRaw!))
        : null;

    TranslatedResult? translatedResult = _handelTranslateTo(item);
    final isBotChannel = item.userId == GlobalConfig.ZIICHAT_USER_ID;
    final messages = _pagingController.itemList!;

    final isInValidMessageType = [
      MessageViewType.loading.rawValue(),
      MessageViewType.system.rawValue(),
      MessageViewType.systemTime.rawValue(),
    ];

    bool shouldShowMessageCreateTime(int index) {
      if (isInValidMessageType.contains(messages[index].messageViewTypeRaw))
        return false;

      // this is last message
      if (index == 0) return true;

      final currentMessage = messages[index];
      final nextMessage = messages[index - 1];

      // next message is a system message
      if (isInValidMessageType.contains(nextMessage.messageViewTypeRaw))
        return true;

      // next message different userId or different date
      return currentMessage.userId != nextMessage.userId ||
          !DateUtils.isSameDay(
            currentMessage.createTime,
            nextMessage.createTime,
          );
    }

    bool shouldShowUserName(int index) {
      if (isInValidMessageType.contains(messages[index].messageViewTypeRaw))
        return false;

      // this is oldest message
      if (index == messages.length - 1) return true;

      final currentMessage = messages[index];
      final previousMessage = messages[index + 1];

      // previous message is a system message
      if (isInValidMessageType.contains(previousMessage.messageViewTypeRaw))
        return true;

      // previous message different userId or different date
      return previousMessage.userId != currentMessage.userId ||
          !DateUtils.isSameDay(
            currentMessage.createTime,
            previousMessage.createTime,
          );
    }

    bool handleHiddenPin() {
      var memberMe = _members[Config.getInstance().activeSessionKey ?? ''];
      ui.Roles? role = MemberSettingsHandler.getRoleFromName(memberMe?.role);
      switch (role) {
        case null:
          return true;
        case ui.Roles.owner || ui.Roles.admin:
          return false;
        case ui.Roles.member:
          return true;
      }
    }

    final isLastSenderMessage =
        item.userId == Config.getInstance().activeSessionKey && index == 0;

    return VisibilityDetector(
      key: Key('visibility_${item.messageId}'),
      onVisibilityChanged: (VisibilityInfo info) {
        _updateVisibleItems(index, item, info.visibleFraction > 0);
      },
      child: MessageViewFactory.buildMessageView(
        isLastMessage: isLastSenderMessage,
        message: item,
        translatedResult: translatedResult,
        member: member,
        user: chatUser,
        isOpenCheckbox: isOpenCheckBox,
        isBotChannel: isBotChannel,
        onCheckBoxButtonTap: _onCheckBoxButtonTap,
        isCheckedMessage:
            widget.mapCheckedMessage?[item.messageId] != null ? true : false,
        recipientId: widget.userId,
        quoteMessage: _createQuoteMessage(originalMessage),
        onQuoteMessageClicked: (messageItem) async {
          if (originalMessage != null && originalMessage.messageId != null) {
            await _onQuoteMessageClicked(messageItem, originalMessage);
          }
        },
        messageErrorReason: messageErrorReason,
        onTranslateMessageClicked: (messageItem) =>
            _onTranslateMessageClicked(item),
        onButtonTranslateClick: _onButtonTranslateClick,
        is24HourFormat: is24HourFormat,
        isHighlighted: _focusableMessageId == item.messageId,
        shouldAnimate: _animateMessageId == item.messageId,
        isShowCreateTime: shouldShowMessageCreateTime(index),
        isShowAvatar: shouldShowMessageCreateTime(index),
        isShowName: shouldShowUserName(index),
        isHiddenPin: isChannel() ? handleHiddenPin() : false,
      ),
    );
  }

  QuoteMessage? _createQuoteMessage(
    OriginalMessage? originalMessage,
  ) {
    ChatUser? chatUser = _users[originalMessage?.userId];

    // Load user info in case the quote user is removed from the channel
    // and the user info cannot be retrieved.
    if (chatUser == null && originalMessage?.userId != null) {
      _setUserIds.add(originalMessage!.userId!);
      _userBloc?.add(ListChatUserInit(setIds: _setUserIds));
      GetIt.instance.get<GetChatUserUseCase>().execute(
            GetChatUserInput(userId: originalMessage.userId!),
          );
    }

    chatUser?.aliasName = getAliasName(chatUser.userId);
    return originalMessage != null
        ? QuoteMessage(
            originalUsername: NameUtils.parseName(
                  chatUser,
                  _members[originalMessage.userId],
                ) ??
                '',
            originalAvatarUrl: UrlUtils.parseAvatar(
              _users[originalMessage.userId]?.profile?.avatar,
            ),
            originalContent: TranslateContentUtils.translateContent(
              originalMessage.content ?? '',
              originalMessage.contentArguments ?? [],
            ),
            isDeletedMessage: originalMessage.messageId == null,
          )
        : null;
  }

  void _onTranslateMessageClicked(Message message) {
    Navigator.pop(context);
    final translatedResult = widget.translatedResultList
        ?.where((item) => item.messageId == message.messageId)
        .firstOrNull;
    widget.translateToHandler.onMessageClickTranslateTo(
      context: context,
      metaData: widget.metaData,
      message: message,
      translatedResult: translatedResult,
    );
  }

  void _onButtonTranslateClick(MessageItem messageItem) {
    if (messageItem.translateStatus == TranslateStatus.success) {
      final messageTranslateResult = widget.translatedResultList
          ?.where((item) => item.messageId == messageItem.messageId)
          .firstOrNull;

      if (messageTranslateResult == null) return;

      final isShowTranslateResult =
          messageTranslateResult.isShowTranslateResult ? false : true;

      final newResult = messageTranslateResult.copyWith(
        isShowTranslateResult: isShowTranslateResult,
      );

      widget.translateToHandler.translateToBloc.add(
        InsertOrUpdateTranslatedResultEvent(result: newResult),
      );
    } else if (messageItem.translateStatus == TranslateStatus.noSupport) {
      ui.DialogUtils.showCanNotTranslateMessageDialog(
        context,
        onOkClicked: () {
          Navigator.pop(context);
        },
      );
    }
  }

  Future<void> _onQuoteMessageClicked(
    ui.MessageItem messageItem,
    OriginalMessage originalMessage,
  ) async {
    var enableScroll = await animateToMessage(
      originalMessage.messageId!,
      focus: true,
    );
    if (!enableScroll) {
      AppEventBus.publish(
        ShowQuoteMessageEvent(
          messageId: originalMessage.messageId!,
        ),
      );
    }
  }

  TranslatedResult? _handelTranslateTo(Message message) {
    final metaData = widget.metaData;
    final transResultList = widget.translatedResultList;
    final translateToHandler = widget.translateToHandler;
    final isTranslated = transResultList != null &&
        transResultList
            .where((msg) => msg.messageId == message.messageId)
            .isNotEmpty;

    if (isTranslated) {
      final currentTranslate = transResultList
          .where((msg) => msg.messageId == message.messageId)
          .first;

      if (currentTranslate.originalContent == message.content) {
        return currentTranslate;
      } else {
        // handle if user edit content, must translate again with targetLanguage
        return translateToHandler.translateMessageToLanguage(
          message,
          currentTranslate.targetLanguage ?? "",
        );
      }
    }

    if (metaData == null ||
        (message.messageViewType != MessageViewType.text &&
            message.messageViewType != MessageViewType.link)) return null;

    final transFromMsgId = metaData.translateFromMessageId ?? '';
    //TODO case clear all messages
    // final transToLanguage = metaData.translateToLanguage ?? '';
    // if (transFromMsgId.isEmpty || transToLanguage.isEmpty) return null;
    if (message.messageId > transFromMsgId) {
      return translateToHandler.translateMessageToLanguage(
        message,
        metaData.translateToLanguage ?? "",
      );
    }
    return null;
  }

  void _updateVisibleItems(int index, Message message, bool isVisible) {
    if (isVisible) {
      _visibleItems.add(index);
      _visibleMessages.add(message.messageId);
      if (!message.isTemp) {
        _markAsReadMessage(message.messageId);
      }
    } else {
      _visibleItems.remove(index);
      _visibleMessages.remove(message.messageId);
    }
    _onChangeVisibility();
  }

  void _onChangeVisibility() {
    if (!mounted) return;

    final paddingTop = MediaQuery.of(context).padding.top +
        (Platform.isIOS ? kMinInteractiveDimensionCupertino : kToolbarHeight) +
        (widget.hasPinned ? pinMessageHeight : 0) +
        (widget.hasMinimizeCall ? minimizeCallHeight : 0);
    final visibleItems =
        _filterVisibleItemsWithPaddingTop(_visibleItems, paddingTop);
    if (visibleItems.isNotEmpty && widget.onChangeVisibility != null) {
      final firstIndex = visibleItems.max()!;
      final lastIndex = visibleItems.min()!;
      final firstItem = _pagingController.itemList![firstIndex];
      final lastItem = _pagingController.itemList![lastIndex];

      widget.onChangeVisibility!(
        firstIndex,
        lastIndex,
        firstItem,
        lastItem,
        _timeIndicatorGlobalKeys,
        paddingTop,
      );
    }
  }

  Set<int> _filterVisibleItemsWithPaddingTop(
    Set<int> visibleItems,
    double paddingTop,
  ) {
    final newVisibleItems = Set<int>.from(visibleItems);
    final firstIndex = newVisibleItems.max();
    if (firstIndex == null) return newVisibleItems;
    final itemInfo = _observerController.findChildInfo(
      index: firstIndex,
      sliverContext: _messageListCtx,
    );
    if (itemInfo != null &&
        itemInfo.renderObject.localToGlobal(Offset.zero).dy +
                (itemInfo.renderObject.size.height + _timeIndicatorHeight) <
            paddingTop) {
      newVisibleItems.remove(firstIndex);
      return _filterVisibleItemsWithPaddingTop(newVisibleItems, paddingTop);
    }
    return newVisibleItems;
  }

  Widget _buildSeparator(BuildContext context, int index) {
    return Column(
      children: [
        _handleDateTimeIndicator(context, index),
        _handleUnreadMessageIndicator(context, index),
      ],
    );
  }

  Widget _handleDateTimeIndicator(BuildContext context, int index) {
    final currentMessage = _pagingController.itemList![index];
    final nextMessage = index + 1 < _pagingController.itemList!.length
        ? _pagingController.itemList![index + 1]
        : null;

    if (nextMessage != null &&
        !currentMessage.createTime!.isSameDay(nextMessage.createTime!)) {
      return _buildTimeDivider(currentMessage.createTime!);
    }

    return const SizedBox.shrink();
  }

  Widget _buildTimeDivider(DateTime date) {
    final stringTime = date.toLocaleTime(
      Localizations.localeOf(context).toLanguageTag(),
    );
    if (_timeIndicatorGlobalKeys[stringTime] == null) {
      _timeIndicatorGlobalKeys[stringTime] = GlobalKey();
    }
    return TimeIndicator(
      key: _timeIndicatorGlobalKeys[stringTime],
      time: stringTime,
    );
  }

  String? _getFirstMessageUnread() {
    if (_lastSeenMessageId == null) {
      return null;
    }
    final unreadMessages = _pagingController.itemList!.where(
      (msg) =>
          msg.messageId > _lastSeenMessageId! &&
          (msg.messageStatus == MessageStatus.SUCCESS ||
              msg.messageStatus == MessageStatus.UNRECOGNIZED) &&
          msg.messageViewTypeRaw > 2,
    );
    if (unreadMessages.isEmpty) {
      return null;
    }
    return unreadMessages.last.messageId;
  }

  Widget _handleUnreadMessageIndicator(BuildContext context, int index) {
    final currentMessage = _pagingController.itemList![index];
    final firstUnreadMessageId = _getFirstMessageUnread();
    if (firstUnreadMessageId != null &&
        currentMessage.messageId == firstUnreadMessageId &&
        currentMessage.userId != Config.getInstance().activeSessionKey!) {
      if (_shouldUpdateIndicatorValue)
        _showUnreadIndicatorNotifier.value = true;
      return ValueListenableBuilder(
        valueListenable: _showUnreadIndicatorNotifier,
        builder: (context, showIndicator, _) {
          if (showIndicator) {
            _unReadMessageIndicatorShowed = true;
            return ui.UnreadMessageWidget();
          }
          _unReadMessageIndicatorShowed = false;
          _lastSeenMessageId = _lastMarkAsReadMessageId;
          return const SizedBox.shrink();
        },
      );
    }

    // Hide unread indicator when scrolled to bottom
    /*if (index == 0 &&
        _lastMarkAsReadMessageId != null &&
        _lastMarkAsReadMessageId == currentMessage.messageId) {
      _lastSeenMessageId = _lastMarkAsReadMessageId;
    }*/

    return const SizedBox.shrink();
  }

  Widget _buildActionCard() {
    return ValueListenableBuilder(
      valueListenable: _actionCardValueNotifier,
      builder: (context, actionCardValue, _) {
        Widget widgetChild = Container();

        if (_channelId != null &&
            _workspaceId != null &&
            _userId == null &&
            actionCardValue.showActionCard) {
          widgetChild = ActionCardChannel1N(
            onClickWave: _sentWaveSticker,
            onAddChannelAvatar: _onAddChannelAvatar,
            onInviteToChannel: () {
              BottomSheetUtil.showDefaultBottomSheet(
                context: context,
                child: InviteToChannelBottomSheet(
                  channelId: _channelId!,
                  workspaceId: _workspaceId!,
                ),
              );
            },
            hadAvatar: actionCardValue.hasAvatar,
            hasOtherMember: actionCardValue.hasOtherMember,
          );
        }
        if (_userId != null && actionCardValue.showActionCard) {
          final hasAliasName = getAliasName(_userId) != null ? true : false;
          widgetChild = ActionCardChannel11(
            onClickWave: _sentWaveSticker,
            onClickAddAliasName: () {
              UserProfileHandler.showBottomSheetAliasName(
                context,
                widget.userId!,
                aliasName: getAliasName(_userId),
                onDone: () {
                  _actionCardValueNotifier.value =
                      _actionCardValueNotifier.value.copyWith(hasAvatar: true);
                  Navigator.pop(context);
                },
              );
            },
            hasAliasName: hasAliasName,
          );
        }
        return AnimatedSwitcher(
          duration: DurationUtils.ms300,
          reverseDuration: DurationUtils.ms300,
          transitionBuilder: (Widget child, Animation<double> animation) {
            return SizeTransition(
              sizeFactor: animation,
              child: child,
              axisAlignment: -1,
            );
          },
          child: widgetChild,
        );
      },
    );
  }

  void _onAddChannelAvatar() {
    ui.ActionSheetUtil.showSetAvatarChannel1NlActionSheet(
      context,
      isRoleMember: _isRoleMember,
      onTapOpenGallery: _onTapOpenGallery,
      onTapTakePhoto: _onTapTakeChannelAvatarPhoto,
      onTapCancel: () {
        Navigator.of(context).pop();
      },
      hasAvatar: _hasAvatar(),
      onTapViewAvatar: () => {},
      onTapRemove: () => {},
    );
  }

  bool _hasAvatar() =>
      ui.StringUtil.stringIsNotEmpty(_channel?.avatar) || _isRoleMember;

  Future<void> _onTapTakeChannelAvatarPhoto() async {
    // Check if the user is in a meeting room
    if (getIt<MeetingHandler>().hasJoinedMeetingRoom(context: context)) return;

    final isGranted = await PermissionUtils.requestCameraPermission(context);
    if (isGranted) {
      Navigator.of(context).pop();
      widget.onClickTakeChannelAvatarPhoto?.call(_channel!);
    }
  }

  Future<void> _onTapOpenGallery() async {
    final isGranted = Platform.isIOS
        ? await PermissionUtils.requestImagePermission(context)
        : true;
    if (isGranted) {
      Navigator.of(context).pop();
      widget.onClickTapOpenGalleryAvatar?.call(_channel!);
    }
  }

  void _sentWaveSticker() {
    widget.sendMessageHandler.sendStickerMessage(
      getIt<StickerBloc>().getStickerHello()!,
    );
  }

  void _loadMyProfile() {
    final myId = Config.getInstance().activeSessionKey ?? '';
    final memberMe = _members[myId];
    final userMe = _users[myId];
    if (memberMe != null && userMe != null) {
      _myProfile = ModelMemberSettingProfile(
        userId: myId,
        channelId: memberMe.channelId,
        workspaceId: memberMe.workspaceId,
        username: userMe.username ?? '',
        aliasName: userMe.aliasName,
        displayName: userMe.profile?.displayName,
        nickname: memberMe.nickname,
        role: MemberSettingsHandler.getRoleFromName(memberMe.role),
        avatarUrl: UrlUtils.parseAvatar(userMe.profile?.avatar),
        isOnline: true,
      );
    }
  }

  void _onShowMemberSettings(ShowMemberSettingsEvent event) {
    final member = _members[event.userId];
    final user = _users[event.userId];
    if (user == null) {
      return;
    }
    final memberProfile = ModelMemberSettingProfile(
      userId: event.userId,
      channelId: member?.channelId,
      workspaceId: member?.workspaceId,
      username: user.username ?? '',
      aliasName: user.aliasName,
      displayName: user.profile?.displayName,
      nickname: member?.nickname,
      role: MemberSettingsHandler.getRoleFromName(member?.role),
      avatarUrl: UrlUtils.parseAvatar(user.profile?.avatar),
      isOnline: user.presenceData?.isOnline ?? false,
    );

    if (_myProfile == null) {
      _loadMyProfile();
    }

    _memberSettingsHandler.showMemberSettings(
      context: context,
      myProfile: _myProfile!,
      memberProfile: memberProfile,
      goToDMMessage: widget.goToDMMessage,
      goToViewAvatar: widget.goToViewAvatar,
      sourcePage: MemberSettingSourcePage.channelView,
    );
  }

  bool _shouldInitiate() {
    return ((_workspaceId == null && _channelId == null));
  }

  bool _shouldInitiateChannel() {
    return _channel == null ||
        _channel!.channelId.isEmpty ||
        _channel!.workspaceId.isEmpty ||
        !ULIDUtils.isValidUlid(_channel!.channelId);
  }

  void _updateChannel(String workspaceId, String channelId) {
    _channel = GetIt.instance.get<ChannelRepository>().getChannel(
          workspaceId: workspaceId,
          channelId: channelId,
        );
    _workspaceId = _channel?.workspaceId;
    _channelId = _channel?.channelId;
  }

  void _onUpdateMessage(UpdateMessageEvent event) {
    if (event.message.channelId == _channelId &&
        event.message.workspaceId == _workspaceId) {
      _messagesBloc?.add(MessagesEvent.updateMessage(event.message));
    }
  }

  void _onDeleteMessage(DeleteMessageEvent event) {
    if (ULIDUtils.isValidUlid(_channelId!)) {
      if (event.channelId != _channelId && event.workspaceId != _workspaceId)
        return;
    }
    final List<String> messageIds = event.messageIds;
    final items = List<Message>.from(_pagingController.itemList ?? []);

    items.removeWhere((item) {
      if (messageIds.contains(item.messageId)) {
        widget.mapCheckedMessage?.remove(item.messageId);
        int index = items.indexWhere(
          (item) => messageIds.contains(item.messageId),
        );
        _visibleItems.remove(index);
      }
      return messageIds.contains(item.messageId);
    });
    _incomingMessages.removeWhere((item) {
      return messageIds.contains(item.messageId);
    });

    if (onlySystemMessage(items)) {
      final memberMe = _members[Config.getInstance().activeSessionKey];
      final isRoleMember =
          MemberSettingsHandler.getRoleFromName(memberMe?.role) == Roles.member;

      final hasAvatar =
          (widget.channel?.avatar ?? '').isNotEmpty || isRoleMember;
      final hasOtherMember = (widget.channel?.totalMembers ?? 1) > 1;
      _actionCardValueNotifier.value = _actionCardValueNotifier.value.copyWith(
        hasUserMessage: false,
        hasAvatar: hasAvatar,
        hasOtherMember: hasOtherMember,
      );
    }
    _onUpdateMessageList(
      StandbyKeepMessageListEvent(
        count: messageIds.length,
        isRemove: true,
      ),
    );
    _updateMessageList(items);
    widget.isEnableClickBottom?.value = widget.mapCheckedMessage!.isNotEmpty;
    widget.countItem?.value = widget.mapCheckedMessage!.length;
  }

  void _onCallCheckMessage(CallCheckMessagesEvent event) {
    final messages = List<Message>.from(_pagingController.itemList ?? []);
    if (ULIDUtils.isValidUlid(_channelId!)) {
      if (_channelId != event.channelId) return;
    }

    widget.mapCheckedMessage?.clear();
    popToChannelView();
    if (event.isForward == true) {
      messageOption = ui.MessageOptions.forward;
    }
    if (event.isDelete == true) {
      messageOption = ui.MessageOptions.delete;
    }

    Message message =
        messages.where((item) => item.messageId == event.messageId).first;
    setState(() {
      if (!(message.isTemp == true && event.isForward == true)) {
        widget.mapCheckedMessage?[event.messageId] = message;
      }
      isOpenCheckBox = true;
      widget.isEnableClickBottom?.value = widget.mapCheckedMessage!.isNotEmpty;
      widget.countItem?.value = widget.mapCheckedMessage!.length;
    });
  }

  void _onCancelAppBarChannelView(CancelAppBarChannelViewEvent event) {
    if (event.isCancel == true) {
      widget.isHiddenEditor?.value = false;
      widget.isChangeAppbar?.value = false;
      setState(() {
        isOpenCheckBox = false;
      });
    }
    if (event.isForward == true) {
      showSuccessForward(second: 2);
    }
  }

  bool onlySystemMessage(List<Message> messages) {
    return messages
            .filter((item) =>
                item.messageViewTypeRaw != MessageViewType.system.value)
            .toList()
            .length ==
        0;
  }

  void showSuccessForward({int? second}) {
    SnackBarOverlayHelper().showSnackBar(
      widgetBuilder: (T) {
        return ui.SnackBarUtilV2.showForwarded(
          context,
          appLocalizations: GetIt.instance.get<AppLocalizations>(),
          duration: Duration(seconds: second ?? 3),
          onClose: () {
            AppEventBus.publish(PopToChannelViewEvent());
          },
        );
      },
    );
  }

  void _onCheckBoxButtonTap(MessageItem messageItem) {
    setState(() {
      var value = widget.mapCheckedMessage?[messageItem.messageId];
      if (value != null) {
        widget.mapCheckedMessage?.remove(messageItem.messageId);
      } else {
        final messages = List<Message>.from(_pagingController.itemList ?? []);
        Message message = messages
            .where((item) => item.messageId == messageItem.messageId)
            .first;
        if (!(message.isTemp == true &&
                messageOption == ui.MessageOptions.forward ||
            (messageOption == ui.MessageOptions.forward &&
                message.mediaAttachments.isNotEmpty &&
                message.mediaAttachments.first.sticker?.stickerId ==
                    GlobalConfig.STICKER_POKE_ID))) {
          widget.mapCheckedMessage?[messageItem.messageId] = message;
        }
      }
      widget.isEnableClickBottom?.value = widget.mapCheckedMessage!.isNotEmpty;
      widget.countItem?.value = widget.mapCheckedMessage!.length;
    });
  }

  void _handleResendMessageEvent(ResendMessageEvent event) {
    final items = List<Message>.from(_pagingController.itemList ?? []);

    Message? message = items.firstOrNullWhere((item) {
      return item.messageId == event.messageId;
    });

    if (message == null) return;

    _onMessageResend(message);

    ResendMessageHandler.resendMessage(widget.sendMessageHandler, message);
  }

  void _handleResendAttachmentEvent(ResendAttachmentEvent event) {
    if (_pagingController.itemList == null) return;
    Message? message = _pagingController.itemList!.firstOrNullWhere((item) {
      return item.messageId == event.attachment.message.target?.messageId;
    });

    if (message == null) return;

    ResendMessageHandler.resendAttachment(
      widget.sendMessageHandler,
      event.attachment,
    );
  }

  void _markAsReadMessage([String? messageId]) {
    if (!_firstTimeScrolled) return;

    if (_pagingController.itemList!.isEmpty ||
        _lastMarkAsReadMessageId ==
            _pagingController.itemList!.first.messageId ||
        (messageId != null &&
            _lastMarkAsReadMessageId != null &&
            _lastMarkAsReadMessageId! >= messageId)) {
      return;
    }
    if (messageId != null &&
        (_pagingController.itemList!
                .firstOrNullWhere(
                  (msg) => msg.messageId == messageId,
                )
                ?.isTemp ??
            true)) {
      return;
    }
    if (messageId == null &&
        _pagingController.itemList!.isNotEmpty &&
        _pagingController.itemList!.first.isTemp) {
      return;
    }
    _lastMarkAsReadMessageId =
        messageId ?? _pagingController.itemList!.first.messageId;

    _messagesBloc?.add(
      MessagesEvent.MarkAsReadMessageEvent(
        workspaceId: _workspaceId,
        channelId: _channelId,
        userId: _userId,
        messageId: _lastMarkAsReadMessageId!,
      ),
    );
  }

  Future<bool> animateToMessage(
    String messageId, {
    int itemPadding = 0,
    bool foreScroll = true,
    bool focus = false,
    bool onClickNotification = false,
  }) async {
    final messageIndex = _pagingController.itemList!.indexWhere(
      (msg) => msg.messageId == messageId,
    );

    if (messageIndex == -1) {
      if (onClickNotification || _fromNotification == true) {
        _timerNotificationFocusMessage = Timer(Duration(milliseconds: 500), () {
          animateToMessage(
            messageId,
            itemPadding: itemPadding,
            focus: focus,
            onClickNotification: onClickNotification,
          );
          _timerNotificationFocusMessage?.cancel();
        });
      }
      return false;
    }
    _timerNotificationFocusMessage?.cancel();
    _fromNotification = false;

    if (messageIndex > 0) {
      await _animateToMessageIndex(
        messageIndex,
        itemPadding: itemPadding,
        duration: DurationUtils.ms500,
      );
    }

    _firstTimeScrolled = true;
    if (focus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _markAsReadMessage(messageId);
        Future.delayed(
          messageIndex >= 0 ? DurationUtils.ms50 : Duration.zero,
          () => _handleFocusMessage(messageId),
        );
      });
    }

    return true;
  }

  Future<void> _animateToMessageIndex(
    int messageIndex, {
    int count = 0,
    int itemPadding = 0,
    bool foreScroll = true,
    Duration duration = const Duration(
      milliseconds: 500,
    ),
  }) async {
    if (messageIndex < 0) return;

    if (_pagingController.itemList!.isNotEmpty &&
        _visibleMessages.contains(
          _pagingController.itemList![messageIndex].messageId,
        ) &&
        !foreScroll) {
      if (!_firstTimeScrolled) {
        _firstTimeScrolled = true;
        _markAsReadMessage(_visibleMessages.max());
      }
      return;
    }

    if (count == 0) {
      if (_scrollController.hasClients) {
        await _scrollController.animateTo(
          _scrollController.offset + 1,
          duration: DurationUtils.ms100,
          curve: _curve,
        );
      }
      return _animateToMessageIndex(
        messageIndex,
        count: count + 1,
        itemPadding: itemPadding,
      );
    }

    if (!mounted) return;
    await _scrollMessageToMiddle(messageIndex);

    if (!_firstTimeScrolled) {
      _firstTimeScrolled = true;
      _markAsReadMessage(_visibleMessages.max());
    }
  }

  /// Scrolls to a specific message index while applying padding adjustments.
  ///
  /// - [messageIndex]: The index of the target message.
  /// - [itemPadding]: The number of items to pad before the target message.
  /// - [duration]: The animation duration for scrolling.
  ///
  /// Ensures smooth scrolling and proper padding based on context constraints.
  Future<void> scrollToIndexWithMessagePadding(
    int messageIndex,
    int itemPadding,
    Duration duration,
  ) async {
    await _observerController.animateTo(
      sliverContext: _messageListCtx,
      index: isLastMessage(messageIndex, length: 5)
          ? _scrollController.position.maxScrollExtent.toInt()
          : messageIndex - itemPadding < 0
              ? 0
              : messageIndex - itemPadding,
      duration: duration,
      curve: _curve,
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top,
        bottom: _getPaddingBottom(),
      ),
    );
  }

  /// Scrolls the given [messageIndex] to the center of the screen.
  ///
  /// - Ensures the message is properly observed before scrolling.
  /// - Prevents scrolling if the message is already fully visible.
  /// - Applies necessary padding adjustments based on screen constraints.
  /// - Uses an offset calculation to align the target message in view.
  Future<void> _scrollMessageToMiddle(int messageIndex) async {
    final result = await _observerController.dispatchOnceObserve(
      sliverContext: _messageListCtx,
      isDependObserveCallback: false,
      isForce: true,
    );
    final observeResult = result.observeAllResult[_messageListCtx];
    if (observeResult is! ListViewObserveModel) {
      return;
    }

    // Do not scroll while the item is displayed on the screen.
    // final resultMap = observeResult.displayingChildModelMap;
    // final displayPercentage = resultMap[messageIndex]?.displayPercentage ?? 0;
    // if (displayPercentage == 1) return;

    final paddingBottom = MediaQuery.of(context).padding.top +
        (widget.hasPinned ? pinMessageHeight : 0) +
        (widget.hasMinimizeCall ? minimizeCallHeight : 0);

    _observerController.jumpTo(
      index: messageIndex,
      sliverContext: _messageListCtx,
      alignment: 0,
      offset: (targetOffset) {
        var _obj = ObserverUtils.findRenderObject(_messageListCtx);
        if (_obj == null || _obj is! RenderSliver) return 0;
        return (_obj.geometry?.paintExtent ?? 0) * 0.3;
      },
      padding: EdgeInsets.only(bottom: paddingBottom),
    );
  }

  bool isLastMessage(int messageIndex, {int? length}) {
    return messageIndex != -1 &&
        messageIndex >=
            List.of(_pagingController.itemList ?? []).length - (length ?? 1);
  }

  void _scrollToBottom() {
    _scrollController.animateTo(
      0,
      duration: DurationUtils.ms500,
      curve: _curve,
    );
  }

  void _scrollToInFocusableMessage() {
    if (widget.messageId.isNullOrEmpty) return;

    animateToMessage(widget.messageId ?? '', itemPadding: 2, focus: true);
  }

  void _handleFocusMessage(String messageId) {
    _focusableMessageId = messageId;
    _animateMessageId = messageId;
    _reRender();
    Future.delayed(DurationUtils.ms500, () {
      _animateMessageId = '';
      _isAnimating = false;

      _reRender();
    });
  }

  void handleShowBottomSheetInvitationNotificationClick(String messageId) {
    if (!hasShowBottomSheetInvitation) return;

    final items = _pagingController.itemList;
    if (items == null) {
      hasShowBottomSheetInvitation = true;
      return;
    }

    Message? itemMessage;
    for (final message in items) {
      if (message.messageId == messageId) {
        itemMessage = message;
        break;
      }
    }

    /// hide bottom sheet has shown before
    popToChannelView();
    if (itemMessage == null || !itemMessage.isInvitation) return;

    final embed = itemMessage.embed?.first;
    final invitationLink = embed?.invitationData?.invitationLink;

    if (invitationLink.isNotNullOrEmpty) {
      AppEventBus.publish(
        OnLinkClickedEvent(
          link: invitationLink!,
          workspaceId: itemMessage.workspaceId,
          channelId: itemMessage.channelId,
          userId: itemMessage.userId,
          messageId: itemMessage.messageId,
        ),
      );
    }

    hasShowBottomSheetInvitation = false;
  }

  void _reRender() {
    setState(() {});
  }

  void _markAnimated(List<Message> messages) {
    _animationManager.markAnimated(
      messages.map((message) => message.messageId).toList(),
    );
  }

  void _handleOnClickMessageNotificationEvent(
    OnClickMessageNotificationEvent event,
  ) {
    hasShowBottomSheetInvitation = (event.messageId != null);

    if (event.messageId == null) return;
    animateToMessage(
      event.messageId!,
      itemPadding: 2,
      focus: true,
      onClickNotification: true,
    );
    if (event.workspaceId != _workspaceId && event.channelId != _channelId)
      return;
    handleShowBottomSheetInvitationNotificationClick(event.messageId!);
  }

  void _onPinUnPinMessageEvent(CallPinUnPinMessageEvent event) async {
    popToChannelView();
    await Future.delayed(Duration(milliseconds: 500));
    if (_pinnedMessage != null) {
      return event.status
          ? ui.ActionSheetUtil.showUpdatePinActionSheet(
              context,
              onTapCancel: () {
                popToChannelView();
              },
              onClickPin: () {
                popToChannelView();
                blocPinUnPinMessage(
                  workspaceId: event.workspaceId,
                  channelId: event.channelId,
                  isChannel: event.isChannel,
                  messageId: event.messageId,
                  status: event.status,
                );
              },
            )
          : ui.ActionSheetUtil.showUnpinActionSheet(
              context,
              onTapCancel: () {
                popToChannelView();
              },
              onClickUnpin: () {
                popToChannelView();
                blocPinUnPinMessage(
                  workspaceId: event.workspaceId,
                  channelId: event.channelId,
                  isChannel: event.isChannel,
                  messageId: event.messageId,
                  status: event.status,
                );
              },
            );
    }

    /// when have not pin message
    blocPinUnPinMessage(
      workspaceId: event.workspaceId,
      channelId: event.channelId,
      isChannel: event.isChannel,
      messageId: event.messageId,
      status: event.status,
    );
  }

  void blocPinUnPinMessage({
    required String workspaceId,
    required String channelId,
    required bool isChannel,
    required String messageId,
    required bool status,
  }) {
    _messagesBloc?.add(
      MessagesEvent.onPinUnPinMessage(
        workspaceId: workspaceId,
        channelId: channelId,
        userId: isChannel ? null : _userId,
        messageId: messageId,
        status: status,
      ),
    );
  }

  void _onPinUnPinUpdateMessageEvent(PinUnPinMessageUpdateEvent event) {
    if (event.workspaceId != _workspaceId && event.channelId != _channelId)
      return;

    final items = List<Message>.from(_pagingController.itemList ?? []);
    Message? message = items.firstOrNullWhere(
      (message) => message.messageId == event.messageId,
    );
    if (message == null) return;
    message.isPinned = event.isPinned;
    message.pinTime = event.pinTime;
    _messagesBloc?.add(MessagesEvent.pinUnPinUpdateMessage(message));
  }

  bool _isAnimating = false;

  void _onClickPinnedMessageEvent(OnClickPinnedMessageEvent event) async {
    if (_isAnimating) return;
    _isAnimating = true;

    if ((event.workspaceId != _workspaceId && event.channelId != _channelId) ||
        event.messageId == null) return;
    bool click = await animateToMessage(
      event.messageId!,
      itemPadding: 2,
      focus: true,
      foreScroll: true,
    );

    if (!click) {
      _isAnimating = false;
      return AppEventBus.publish(
        OnShowBottomSheetPinnedMessageEvent(
          workspaceId: event.workspaceId,
          channelId: event.channelId,
          userId: event.userId,
          messageId: event.messageId,
        ),
      );
    }
  }

  void handlePinUnPinMessageUpdate(Message message) {
    if (_shouldInitiateChannel()) {
      _updateChannel(message.workspaceId, message.channelId);
    }
    final items = List<Message>.from(_pagingController.itemList ?? []);
    final index = _findMessageIndex(items, message.messageId, message.ref);
    if (index != -1) {
      for (int i = 0; i < items.length; i++) {
        if (i == index) {
          items[index] = message;
        } else {
          items[i].isPinned = false;
        }
      }
      _updateMessageList(items);

      if (message.userId == _myUserId && index == 0) {
        _markAsReadMessage();
        // _lastSeenMessageId = _lastMarkAsReadMessageId;
      }
    }
    _updateLastReceiveMessage();
  }
}
