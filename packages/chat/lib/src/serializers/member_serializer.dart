import 'dart:convert';

import 'package:shared/shared.dart';

import '../data/repositories/database/entities/member.dart';

class MemberSerializer {
  static Member? serializeFromJson({
    required Map<String, dynamic> data,
  }) {
    final responseMember = ResponseMember.fromJson(data);

    final member = Member(
      workspaceId: responseMember.workspaceId,
      channelId: responseMember.channelId,
      userId: responseMember.userId,
      nickname: responseMember.nickname,
      role: responseMember.role,
      rolesRaw: responseMember.roles != null
          ? jsonEncode(
              responseMember.roles!.map((role) => role.toJson()).toList(),
            )
          : null,
      createTime: responseMember.createTime,
      updateTime: responseMember.updateTime,
    );

    return member;
  }
}
