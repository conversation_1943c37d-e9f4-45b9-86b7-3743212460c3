import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:app_core/core.dart' as core;
import 'package:app_core/core.dart';
import 'package:async/async.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared/shared.dart';
import 'package:upload_manager/upload_manager.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../common/di/di.dart';
import '../../../data/repositories/database/entities/user.dart';
import '../../../data/repositories/database/enums/user_badge_enum.dart';
import '../../bloc/me_profile/me_profile_bloc.dart';
import 'me_profile_interface.dart';

class MeProfilePage extends StatefulWidget {
  const MeProfilePage({
    required this.interface,
    required this.isLoadingNewAvatar,
    this.updateAvatarUser,
    super.key,
  });

  final MeProfileInterface interface;
  final ValueNotifier<bool> isLoadingNewAvatar;

  final void Function(UploadFile file)? updateAvatarUser;

  @override
  State<MeProfilePage> createState() => _MeProfilePageState();
}

class _MeProfilePageState extends BasePageState<MeProfilePage, MeProfileBloc>
    implements ui.PersonalProfileInterface, ui.BlockedUsersPageInterface {
  final _appEventBus = GetIt.instance.get<AppEventBus>();
  late StreamSubscription _subscription;

  User? _user;
  ValueNotifier<String> _name = ValueNotifier('');
  int badgeEnum = 0;
  ui.UserBadgeType? userBadgeType;
  ValueNotifier<bool> _hasVisitedProfile = ValueNotifier(false);

  late MeProfileBloc _meProfileBloc;

  late SettingNotificationBloc _settingNotificationBloc;

  @override
  void initState() {
    _meProfileBloc = bloc;
    _settingNotificationBloc = getIt<SettingNotificationBloc>();
    bloc.add(InitiateMeProfileEvent());
    Future.delayed(DurationUtils.ms100, () {
      bloc.add(ListenVisitedProfileEvent());
    });
    Future.delayed(DurationUtils.ms200, () {
      bloc.add(CheckDifferentVisitedProfileEvent());
    });

    _subscription = StreamGroup.merge([
      _appEventBus.on<ChooseCoverEvent>(),
      _appEventBus.on<ChooseAvatarEvent>(),
    ]).listen((event) async {
      switch (event.runtimeType) {
        case ChooseCoverEvent:
          await _handleChooseCoverEvent(event as ChooseCoverEvent);
          break;

        case ChooseAvatarEvent:
          await _handleChooseAvatarEvent(event as ChooseAvatarEvent);
          break;

        default:
          _showErrorOccurredDialog(
            context,
            "Unhandled event: ${event.runtimeType}",
          );
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    _subscription.cancel();
    _meProfileBloc.add(DisposeEvent());
    super.dispose();
  }

  void _blocSettingNotification(
    BuildContext context,
    SettingNotificationState state,
  ) {
    state.maybeWhen(
      turnOffGlobalNotification: (response) {
        if (response == true) {
          _user?.globalNotificationStatus = false;
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
        _meProfileBloc.add(RefreshEvent());
      },
      turnOnGlobalNotification: (response) {
        if (response == true) {
          _user?.globalNotificationStatus = true;
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
        _meProfileBloc.add(RefreshEvent());
      },
      orElse: () {},
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    return Scaffold(
      body: MultiBlocProvider(
        providers: [
          BlocProvider<MeProfileBloc>.value(value: _meProfileBloc),
          BlocProvider<SettingNotificationBloc>.value(
            value: _settingNotificationBloc,
          ),
        ],
        child: MultiBlocListener(
          listeners: [
            BlocListener<SettingNotificationBloc, SettingNotificationState>(
              listenWhen: (prev, state) => prev != state,
              listener: _blocSettingNotification,
            ),
          ],
          child: BlocListener<MeProfileBloc, MeProfileState>(
            listener: _listener,
            child: BlocBuilder<MeProfileBloc, MeProfileState>(
              buildWhen: (prev, current) => current != prev,
              builder: (context, state) {
                return state.maybeWhen(
                  initial: () {
                    return ui.AppScaffold(
                      backgroundColor:
                          Theme.of(context).scaffoldBackgroundColor,
                      systemUIBottomColor: Colors.transparent,
                      systemUIColor: Colors.transparent,
                      hasSafeArea: false,
                      body: Center(
                        child: ui.AppCircularProgressIndicator(),
                      ),
                    );
                  },
                  loaded: (user) {
                    _user = user;
                    _name.value =
                        _user?.profile?.displayName ?? _user?.username ?? '';
                    badgeEnum = _user?.profile?.userBadgeType ?? 0;
                    userBadgeType =
                        UserBadgeEnumExtension.getEnumByValue(badgeEnum)
                            .toUserBadgeType();

                    return ui.PersonalProfilePage(
                      interface: this,
                      name: _name,
                      badgeType: userBadgeType,
                    );
                  },
                  meProfileCoverChanged: (cover) {
                    LoadingOverlayHelper.hideLoading(context);
                    _user?.profile?.cover = cover;
                    return ui.PersonalProfilePage(
                      interface: this,
                      name: _name,
                      badgeType: userBadgeType,
                    );
                  },
                  meProfileAvatarChanged: (avatarPath) {
                    LoadingOverlayHelper.hideLoading(context);
                    _user?.profile?.avatar = avatarPath;
                    return ui.PersonalProfilePage(
                      interface: this,
                      name: _name,
                      badgeType: userBadgeType,
                    );
                  },
                  meProfileShowError: (errorMessage) {
                    return ui.PersonalProfilePage(
                      interface: this,
                      name: _name,
                      badgeType: userBadgeType,
                    );
                  },
                  setDisplayName: (displayName) {
                    _name.value = displayName;
                    return ui.PersonalProfilePage(
                      interface: this,
                      name: _name,
                      badgeType: userBadgeType,
                    );
                  },
                  orElse: () {
                    return ui.PersonalProfilePage(
                      interface: this,
                      name: _name,
                      badgeType: userBadgeType,
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _showErrorOccurredDialog(BuildContext context, String? errorMessage) {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      ui.DialogUtils.showErrorOccurredTranslateDialog(
        onOkClicked: () => {Navigator.of(context).pop()},
        context,
      );
    });
  }

  @override
  void onClickCamera() {
    _showSetCoverPhotoDmChannelActionSheet();
  }

  @override
  void onClickNotification() {
    widget.interface.onClickNotification();
  }

  @override
  String avatarPath() {
    return UrlUtils.parseAvatar(_user?.profile?.avatar);
  }

  @override
  String coverPath() {
    return UrlUtils.parseAvatar(_user?.profile?.cover);
  }

  @override
  bool isOnline() {
    return _user?.presenceData?.isOnline ?? false;
  }

  @override
  String statusEmoji() {
    return _user?.statusData?.status ?? '';
  }

  @override
  String statusText() {
    return _user?.statusData?.content ?? '';
  }

  @override
  int statusDuration() {
    return _user?.statusData?.expireAfterTime?.value ?? 0;
  }

  @override
  String username() {
    return _user?.username ?? '';
  }

  @override
  void onClickEditProfile() {
    widget.interface.onClickEditProfile(_user!);
  }

  @override
  void onClickShareProfile() {
    if (_user != null) {
      widget.interface.onClickShareProfile(_user!);
    }
  }

  @override
  void onCLickAddDisplayName() {
    if (_name.value.isEmpty) {
      ui.BottomSheetUtil.showSetDisplayNameBottomSheet(
        context: context,
        onPressedCancel: () {
          Navigator.of(context).pop();
        },
        onPressedDone: (newDisplayname) {
          _meProfileBloc.add(SetDisplayNameEvent(displayName: newDisplayname));
          Navigator.of(context).pop();
        },
        displayName: () {
          return _name.value;
        },
      );
    }
  }

  @override
  void onCLickAddProfileAvatar() {
    if (isFinishAddProfileAvatar().value) return;
    _showSetProfileAvatarActionSheet();
  }

  @override
  void onCLickAddVideoAvatar() {
    Log.d('onCLickAddVideoAvatar');
  }

  @override
  bool showNotification() {
    return _user?.globalNotificationStatus ?? true;
  }

  @override
  void updateNotification({required bool notification}) {
    Log.e(notification, name: 'updateNotification');
    _settingNotificationBloc
        .add(OnTurnOnOffGlobalNotificationEvent(isTurnOn: notification));
    _user?.globalNotificationStatus = notification;
  }

  @override
  void onClickAppearance() {
    widget.interface.onClickAppearance();
  }

  @override
  void onClickLanguage() {
    widget.interface.onClickLanguage();
  }

  @override
  void onClickPrivacyAndSecurity() {
    widget.interface.onClickPrivacyAndSecurity(_user!);
  }

  @override
  void onClickBlockedUser() {
    widget.interface.onClickListBlockedUser();
  }

  @override
  bool isFinishedOverview() {
    return (_user?.profile?.avatar ?? '').isNotEmpty &&
        (_user?.profile?.displayName ?? '').isNotEmpty;
  }

  @override
  List<String> emojiList() {
    return [];
  }

  @override
  void onUpdateEmojiList(List<String> newEmojiList) {}

  @override
  ValueNotifier<bool> isFinishAddDisplayName() {
    return ValueNotifier((_user?.profile?.displayName ?? '').isNotEmpty);
  }

  @override
  ValueNotifier<bool> isFinishAddProfileAvatar() {
    return ValueNotifier((_user?.profile?.avatar ?? '').isNotEmpty);
  }

  @override
  ValueNotifier<bool> isFinishAddVideoAvatar() {
    /// hide video avatar because feature pending
    return ValueNotifier(false);
  }

  Future<void> _onClickAcceptLogOut() async {
    isAuthenticated.value = false;
    Navigator.of(context).pop();
    getIt<AppBloc>().add(AppLogout(onSuccess: () {}));
    await widget.interface.onLogoutSuccess();
  }

  void _onClickCancelLogOut() {
    Navigator.of(context).pop();
  }

  @override
  void onClickCalls() {}

  @override
  void onClickLogOut() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      ui.ActionSheetUtil.showLogOutActionSheet(
        context,
        onClickLogOut: _onClickAcceptLogOut,
        onClickCancel: _onClickCancelLogOut,
        isDismissible: false,
      );
    });
  }

  @override
  void onClickUpdateStatus() {
    if (_user?.statusData != null) {
      _updateMyStatus();
    } else {
      widget.interface.onAddStatus(
        onAddStatusSuccessful: (_, __, ___) {
          AppEventBus.publish(UpdatedMeStatusEvent());
        },
      );
    }
  }

  @override
  void onClickAcceptLogOut(BuildContext context) {
    // TODO: implement onClickAcceptLogOut
  }

  @override
  void onClickCancelLogOut(BuildContext context) {
    // TODO: implement onClickCancelLogOut
  }

  @override
  Uint8List? avatarData() {
    return null;
  }

  @override
  void onProfileClicked() {
    _showSetCoverPhotoDmChannelActionSheet();
  }

  Future<void> _handleChooseCoverEvent(ChooseCoverEvent event) async {
    if (event.avatarType != AvatarType.me) return;
    final cover = await XFile(event.filePath);

    UploadFile coverUploadFile = UploadFile(
      path: cover.path,
      name: cover.name,
      size: await cover.length(),
    );
    LoadingOverlayHelper.showLoading(context);
    if (_hasCoverPhoto()) {
      _meProfileBloc.add(
        ChangeCoverMeProfileEvent(
          cover: coverUploadFile,
        ),
      );
    } else {
      _meProfileBloc.add(
        AddCoverMeProfileEvent(
          cover: coverUploadFile,
        ),
      );
    }
  }

  Future<void> _handleChooseAvatarEvent(ChooseAvatarEvent event) async {
    if (event.avatarType != AvatarType.me) return;
    final avatar = await XFile(event.filePath);

    UploadFile avatarUploadFile = UploadFile(
      path: avatar.path,
      name: avatar.name,
      size: await avatar.length(),
    );
    widget.isLoadingNewAvatar.value = true;
    widget.updateAvatarUser?.call(avatarUploadFile);
  }

  void _showSetCoverPhotoDmChannelActionSheet() {
    ui.ActionSheetUtil.showSetCoverPhotoDmChannelActionSheet(
      context,
      hasCovePhoto: _hasCoverPhoto(),
      onTapOpenGallery: _onTapOpenGallery,
      onTapTakePhoto: _onTapTakePhoto,
      onTapCancel: () {
        Navigator.of(context).pop();
      },
      onTapViewAvatar: () {
        Navigator.of(context).pop();
        widget.interface.onTapFullScreenImage(coverPath());
      },
      onTapRemove: _onTapRemove,
    );
  }

  void _showSetProfileAvatarActionSheet() {
    ui.ActionSheetUtil.showSetAvatarDmChannelActionSheet(
      context,
      hasAvatar: isFinishAddProfileAvatar().value,
      onTapOpenGallery: _onTapOpenGalleryAvatar,
      onTapTakePhoto: _onTapTakePhotoAvatar,
      onTapTakeVideo: () {
        //TODO: onTapTakeVideo
      },
      onTapCancel: () {
        Navigator.of(context).pop();
      },
      onTapViewAvatar: () {
        Navigator.of(context).pop();

        widget.interface.onTapFullScreenImage(avatarPath());
      },
      onTapRemove: _onTapRemoveAvatar,
      allowAddFrame: false,
      onTapAvatarFrame: () {},
    );
  }

  Future<void> _onTapOpenGalleryAvatar() async {
    final isGranted = Platform.isIOS
        ? await PermissionUtils.requestImagePermission(context)
        : true;
    if (isGranted) {
      Navigator.of(context).pop();
      if (_user != null) {
        widget.interface.onOpenGalleryAvatar(_user!);
      }
    }
  }

  bool _hasCoverPhoto() => (_user?.profile?.cover ?? '').isNotEmpty;

  Future<void> _onTapOpenGallery() async {
    final isGranted = Platform.isIOS
        ? await PermissionUtils.requestImagePermission(context)
        : true;
    if (isGranted) {
      Navigator.of(context).pop();
      if (_user != null) {
        widget.interface.onClickTapOpenGallery(_user!);
      }
    }
  }

  Future<void> _onTapTakePhoto() async {
    // Check if the user is in a meeting room
    if (getIt<MeetingHandler>().hasJoinedMeetingRoom(context: context)) return;

    final isGranted = await PermissionUtils.requestCameraPermission(context);
    if (isGranted) {
      Navigator.of(context).pop();
      if (_user != null) {
        widget.interface.onClickTakePhoto(_user!);
      }
    }
  }

  Future<void> _onTapTakePhotoAvatar() async {
    // Check if the user is in a meeting room
    if (getIt<MeetingHandler>().hasJoinedMeetingRoom(context: context)) return;

    final isGranted = await PermissionUtils.requestCameraPermission(context);
    if (isGranted) {
      Navigator.of(context).pop();
      if (_user != null) {
        widget.interface.onClickTakeAvatarPhoto(_user!);
      }
    }
  }

  void _onTapRemove() {
    if (coverPath().isEmpty) return;
    Navigator.of(context).pop();
    LoadingOverlayHelper.showLoading(context);
    _meProfileBloc.add(DeleteCoverMeProfileEvent());
  }

  void _onTapRemoveAvatar() {
    if (avatarPath().isEmpty) return;
    Navigator.of(context).pop();
    LoadingOverlayHelper.showLoading(context);
    _meProfileBloc.add(DeleteAvatarMeProfileEvent());
  }

  Future<Uint8List?> getImageBytesFromImageProvider(
    ImageProvider imageProvider,
  ) async {
    final Completer<ImageInfo> completer = Completer<ImageInfo>();

    final ImageStream stream =
        imageProvider.resolve(const ImageConfiguration());
    final listener = ImageStreamListener(
      (ImageInfo info, _) {
        completer.complete(info);
      },
      onError: (dynamic error, StackTrace? stackTrace) {
        completer.completeError(error, stackTrace);
      },
    );

    stream.addListener(listener);

    try {
      final ImageInfo imageInfo = await completer.future;
      final ByteData? byteData =
          await imageInfo.image.toByteData(format: ImageByteFormat.png);
      return byteData?.buffer.asUint8List();
    } finally {
      stream.removeListener(listener);
    }
  }

  @override
  void onAvatarClicked() {
    _showSetProfileAvatarActionSheet();
  }

  void _listener(BuildContext context, MeProfileState state) {
    state.when(
      initial: () {},
      loaded: (user) {},
      meProfileCoverChanged: (cover) {},
      meProfileAvatarChanged: (avatarPath) {
        widget.isLoadingNewAvatar.value = false;
      },
      meProfileShowError: (errorMessage) {
        LoadingOverlayHelper.hideLoading(context);
        _showErrorOccurredDialog(context, errorMessage);
      },
      setDisplayName: (String displayName) {},
      hasVisitedProfileEvent: (bool? hasVisited) {
        _hasVisitedProfile.value = hasVisited ?? false;
        AppEventBus.publish(
          HasNotificationEvent(hasNotification: _hasVisitedProfile.value),
        );
      },
      refresh: () {},
    );
  }

  @override
  List<ui.FriendItem> blockedUsersList() {
    // TODO: implement blockedUsersList
    throw UnimplementedError();
  }

  @override
  bool displaySkeleton() {
    // TODO: implement displaySkeleton
    return false;
  }

  @override
  bool isGotIt() {
    // TODO: implement isGotIt
    throw UnimplementedError();
  }

  @override
  void onClickBack() {
    // TODO: implement onClickBack
  }

  @override
  void onClickGotIt() {
    // TODO: implement onClickGotIt
  }

  @override
  void onClickUnBlockedUser(ui.FriendItem friendItem) {
    // TODO: implement onClickUnBlockedUser
  }

  @override
  void onClickUserItem(ui.FriendItem friendItem) {
    // TODO: implement onClickUserItem
  }

  @override
  ValueNotifier<bool> hasNotification() {
    return _hasVisitedProfile;
  }

  @override
  ValueNotifier<bool> isLoadingNewAvatar() {
    return widget.isLoadingNewAvatar;
  }

  @override
  void onStatusBallonClicked() {
    final bottomSheetUserStatus = ui.BottomSheetUserStatus(
      emoji: statusEmoji(),
      content: statusText(),
      avatarUrl: avatarPath(),
      name: _name.value,
      username: '@${username()}',
      isMyStatus: true,
      badgeType: userBadgeType,
    );

    ui.BottomSheetUtil.showBottomSheetUserStatus(
      context,
      bottomSheetUserStatus: bottomSheetUserStatus,
      onEditStatusButtonClicked: (BottomSheetUserStatus) {
        Navigator.of(context).pop();
        _updateMyStatus();
      },
      onDeleteButtonClicked: (BottomSheetUserStatus) {
        widget.interface.onDeleteStatus(
          onDeleteStatusSuccessful: () {
            AppEventBus.publish(UpdatedMeStatusEvent());
          },
        );
      },
    );
  }

  void _updateMyStatus() {
    widget.interface.onUpdateStatus(
      emoji: statusEmoji(),
      content: statusText(),
      keepStatusDuration: _user?.statusData?.expireAfterTime?.value ?? 0,
      onUpdateStatusSuccessful: (_, __, ___) {
        AppEventBus.publish(UpdatedMeStatusEvent());
      },
    );
  }

  @override
  void onClickMessagesAndCalls() {}
}

class ViewAvatarAppbarWidgetImplementation
    extends ui.ViewAvatarAppbarWidgetInterface {
  ViewAvatarAppbarWidgetImplementation({
    required this.context,
    required this.onSaveToGallery,
  });

  final BuildContext context;
  final VoidCallback onSaveToGallery;

  @override
  void onBackButtonClicked() {
    Navigator.pop(context);
  }

  @override
  void onDownloadButtonClicked() {
    onSaveToGallery();
  }
}
