import 'package:json_annotation/json_annotation.dart';
import 'package:shared/shared.dart';

import 'audio_metadata.dart';
import 'file_metadata.dart';
import 'layout_metadata.dart';

part 'media_object.g.dart';

@JsonSerializable(explicitToJson: true)
class MediaObject {
  @JsonKey(name: 'fileId')
  String? fileId;

  @JsonKey(name: 'attachmentType')
  int? attachmentType;

  @JsonKey(name: 'fileUrl')
  String? fileUrl;

  @JsonKey(includeToJson: false, includeFromJson: false)
  String? get correctFileUrl {
    if (fileMetadata?.filename != null && fileUrl != null) {
      final newPathSegments = List<String>.from(fileUrl!.split('/'));
      newPathSegments.last = fileMetadata!.filename!;
      return UrlUtils.parseCDNUrl(newPathSegments.join('/'));
    }
    return UrlUtils.parseCDNUrl(fileUrl);
  }

  @<PERSON><PERSON><PERSON>ey(name: 'filePath')
  String? filePath;

  @Json<PERSON>ey(name: 'fileMetadata')
  FileMetadata? fileMetadata;

  @Json<PERSON>ey(name: 'fileStatus')
  int? fileStatus;

  @Json<PERSON>ey(name: 'thumbnailUrl')
  String? thumbnailUrl;

  @JsonKey(name: 'thumbnailMetadata')
  FileMetadata? thumbnailMetadata;

  @JsonKey(name: 'layoutMetadata')
  LayoutMetadata? layoutMetadata;

  @JsonKey(name: 'audioMetadata')
  AudioMetadata? audioMetadata;

  @JsonKey(name: 'fileRef')
  String? fileRef;

  @JsonKey(name: 'attachmentId')
  String? attachmentId;

  @JsonKey(name: 'blob')
  String? blob;

  @JsonKey(name: 'collectionId')
  String? collectionId;

  MediaObject({
    this.fileId,
    this.attachmentType,
    this.fileUrl,
    this.filePath,
    this.fileMetadata,
    this.fileStatus,
    this.thumbnailUrl,
    this.thumbnailMetadata,
    this.layoutMetadata,
    this.audioMetadata,
    this.fileRef,
    this.attachmentId,
    this.blob,
    this.collectionId,
  });

  factory MediaObject.fromJson(Map<String, dynamic> json) =>
      _$MediaObjectFromJson(json);

  Map<String, dynamic> toJson() => _$MediaObjectToJson(this);

  factory MediaObject.nullObject() =>
      MediaObject(fileId: '', attachmentId: '', fileRef: '');

  MediaObject copyWith({
    String? fileId,
    int? attachmentType,
    String? fileUrl,
    String? filePath,
    FileMetadata? fileMetadata,
    int? fileStatus,
    String? thumbnailUrl,
    FileMetadata? thumbnailMetadata,
    LayoutMetadata? layoutMetadata,
    AudioMetadata? audioMetadata,
    String? fileRef,
    String? attachmentId,
    String? blob,
    String? collectionId,
  }) {
    return MediaObject(
      fileId: fileId ?? this.fileId,
      attachmentType: attachmentType ?? this.attachmentType,
      fileUrl: fileUrl ?? this.fileUrl,
      filePath: filePath ?? this.filePath,
      fileMetadata: fileMetadata ?? this.fileMetadata,
      fileStatus: fileStatus ?? this.fileStatus,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      thumbnailMetadata: thumbnailMetadata ?? this.thumbnailMetadata,
      layoutMetadata: layoutMetadata ?? this.layoutMetadata,
      audioMetadata: audioMetadata ?? this.audioMetadata,
      fileRef: fileRef ?? this.fileRef,
      attachmentId: attachmentId ?? this.attachmentId,
      blob: blob ?? this.blob,
      collectionId: collectionId ?? this.collectionId,
    );
  }

  String? getPathFromCache(String? messageRef) {
    if (messageRef == null) return filePath;

    if (fileMetadata?.filename == null || messageRef.isEmpty) return null;

    return '${FileUtils.cacheDirPath}/$filePath';
  }
}
