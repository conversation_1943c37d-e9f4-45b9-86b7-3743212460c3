import 'package:app_core/core.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../data/repositories/database/entities/user.dart';
import '../../data/repositories/user_repository.dart';

@Injectable()
class GetMeUseCase extends BaseSyncUseCase<GetMeInput, GetMeOutput> {
  const GetMeUseCase(this._repository);

  final UserRepository _repository;

  @protected
  @override
  GetMeOutput buildUseCase(GetMeInput input) {
    final user =
        _repository.getUser(Config.getInstance().activeSessionKey ?? '');

    return GetMeOutput(user: user);
  }
}

class GetMeInput extends BaseInput {
  const GetMeInput();
}

class GetMeOutput extends BaseOutput {
  const GetMeOutput({required this.user});

  final User? user;
}
