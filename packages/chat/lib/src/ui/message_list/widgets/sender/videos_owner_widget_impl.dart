import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../../chat.dart';
import '../../../../data/repositories/database/enums/message_status.dart' as m;
import '../../../../data/repositories/extensions/message_extension.dart';
import '../../../../domain/event_bus/message/show_fullscreen_event.dart';
import '../base/base_sender_widget.dart';

class VideosOwnerWidgetImpl extends BaseSenderWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isHiddenPin;
  final bool isLastMessage;

  VideosOwnerWidgetImpl({
    required super.messageItem,
    required this.isOpenCheckBox,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    this.onCheckBoxButtonTap,
    this.isShowCreateTime = false,
    this.isHiddenPin = false,
    this.isLastMessage = false,
    super.key,
    required super.message,
  });

  @override
  Widget build(BuildContext context) {
    if (message.mediaAttachments.isEmpty) {
      //TODO: handle file not found view
      return const SizedBox();
    }

    final defaultAttachments = message.mediaAttachments.map((attachment) {
      final video = attachment.video ?? MediaObject.nullObject();
      return VideoAttachment(
        attachmentId:
            video.attachmentId ?? video.fileId ?? RandomUtils.randomUlId(),
        attachmentUrl: UrlUtils.parseCDNUrl(video.thumbnailUrl),
        attachmentPath: video.filePath,
        duration: Duration(seconds: video.fileMetadata?.duration ?? 0),
      );
    }).toList();

    return FutureBuilder<List<VideoAttachment>>(
      future: _parseVideoAttachments(),
      builder: (context, snapshot) {
        final videoAttachments = snapshot.data ?? defaultAttachments;

        final isHideOptionResend =
            message.messageStatus == m.MessageStatus.FAILURE &&
                videoAttachments.length != message.mediaAttachments.length;

        return VideosMessageSenderWidget(
          isLastMessage: isLastMessage,
          messageItem: messageItem,
          onQuote: onQuote,
          isHiddenPin: isHiddenPin,
          isShowCreateTime: isShowCreateTime,
          onDeleteMessages: onDeleteMessages,
          onForward: onForward,
          onPinMessage: onPinMessage,
          onUnPinMessage: onUnPinMessage,
          onMessageItemClicked: onMessageItemClicked,
          onListReactionClicked: onListReactionClicked,
          onEmojiClicked: onEmojiClicked,
          emojiList: message.emojiList,
          videoAttachments: videoAttachments,
          onVideoClicked: (MessageItem messageItem, String attachmentId) {
            AppEventBus.publish(
              ShowFullscreenEvent(
                messageId: messageItem.messageId,
                workspaceId: message.workspaceId,
                channelId: message.channelId,
              ),
            );
          },
          onDownload: (MessageItem messageItem) => onDownload(context),
          onResend: onResendMessage,
          onDiscard: onDiscardMessage,
          isOpenCheckBox: isOpenCheckBox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          isHideOptionResend: isHideOptionResend,
          isHideOptionCopy: true,
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
        );
      },
    );
  }

  /// Parse video attachments and get thumbnail paths from cache for preview display
  /// Optimized for video preview - only loads thumbnail path, not video file path
  Future<List<VideoAttachment>> _parseVideoAttachments() async {
    if (message.mediaAttachments.isEmpty) return [];

    final futures = message.mediaAttachments.map((attachment) async {
      final video = attachment.video ?? MediaObject.nullObject();

      // Get thumbnail file path from cache using FileUtils
      // For video preview, we only need thumbnail path, not the actual video file
      final videoPath = await FileUtils.getVideoPathFromFileRef(
            messageRef: message.ref!,
            fileRef: video.fileRef ?? '',
          ) ??
          video.filePath;

      return VideoAttachment(
        attachmentId:
            video.attachmentId ?? video.fileId ?? RandomUtils.randomUlId(),
        attachmentUrl: UrlUtils.parseCDNUrl(video.thumbnailUrl),
        attachmentPath:
            videoPath, // Use thumbnail path as attachment path for preview
        duration: Duration(seconds: video.fileMetadata?.duration ?? 0),
      );
    });

    return await Future.wait(futures);
  }
}
