import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:shared/shared.dart';

import '../domain/handler/notification/notification_translation_handler.dart';

part 'notification_data.g.dart';

@JsonSerializable(explicitToJson: true)
class NotificationData {
  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? username;
  final String? messageId;
  final String? dmId;
  final String? channelName;
  final String? channelAvatar;
  final String? avatar;
  final String? body;
  final String? title;
  final String? contentLocale;
  final String? routingKey;
  final String? taskId;
  @ContentArgumentsConverter()
  final List<String>? contentArguments;

  NotificationData({
    this.workspaceId,
    this.channelId,
    this.userId,
    this.username,
    this.messageId,
    this.dmId,
    this.channelName,
    this.channelAvatar,
    this.avatar,
    this.body,
    this.title,
    this.contentLocale,
    this.routingKey,
    this.taskId,
    this.contentArguments,
  });

  factory NotificationData.fromJson(Map<String, dynamic> json) {
    NotificationData data = _$NotificationDataFromJson(json);
    return data;
  }

  Map<String, dynamic> toJson() => _$NotificationDataToJson(this);

  String getKey() {
    return hasChannelViewAction() ? (messageId ?? '') : (userId ?? '');
  }

  bool isDm() {
    return dmId?.isNotEmpty ?? false;
  }

  bool hasChannelName() {
    return channelName?.isNotEmpty ?? false;
  }

  bool hasReaction() {
    return title == 'REACTED_YOUR_MESSAGE';
  }

  bool hasChannelViewAction() {
    return routingKey == 'CHANNEL_VIEW';
  }

  bool hasUserViewViewAction() {
    return routingKey == 'USER_PROFILE_VIEW';
  }

  bool hasUserViewProfile() {
    return title == 'USER_VIEWED_PROFILE_EVENT';
  }

  bool hasNotificationFromZiiChat() {
    return title == 'USER_VIEWED_PROFILE_EVENT' ||
        title == 'INCOMING_FRIEND_REQUEST_CREATED_EVENT' ||
        title == 'OUTGOING_FRIEND_REQUEST_ACCEPTED_EVENT';
  }

  bool isDownload() {
    return taskId != null && taskId != "";
  }

  String? parseChannelAvatar() {
    return channelAvatar?.isNotEmpty == true ? channelAvatar : null;
  }

  String? parseAvatar(String? avatar) {
    return avatar?.isNotEmpty == true ? avatar : null;
  }

  Future<String> getBody() async {
    return await NotificationTranslationHandler.getTranslation(
      contentArguments ?? [],
      body ?? '',
    );
  }

  Future<String> getReplacementContent() async {
    var message = await getBody();
    if (hasUserViewProfile()) {
      return message;
    }
    if (username?.isNotEmpty == true) {
      message = message
          .replaceAll('${username!}: ', '')
          .replaceAll('@${username!}: ', '')
          .replaceAll('@${username!} ', '');
    }
    return message;
  }
}

extension NotificationDataExtensions on NotificationData {
  bool hasValidAvatar() {
    return avatar?.isNotEmpty ?? false;
  }

  bool hasValidChannelAvatar() {
    return channelAvatar?.isNotEmpty ?? false;
  }

  String? getValidAvatar() {
    if (hasValidAvatar()) return UrlUtils.parseAvatar(avatar);
    if (hasValidChannelAvatar()) return UrlUtils.parseAvatar(channelAvatar);
    return null;
  }

  bool hasValidChannelName() {
    return channelName?.isNotEmpty ?? false;
  }
}

class ContentArgumentsConverter
    implements JsonConverter<List<String>?, dynamic> {
  const ContentArgumentsConverter();

  @override
  List<String>? fromJson(dynamic json) {
    if (json == null) {
      return [];
    }
    if (json is String) {
      try {
        final parsed = jsonDecode(json);
        if (parsed is List) {
          return parsed.map((e) => e.toString()).toList();
        }
      } catch (_) {
        return [];
      }
    } else if (json is List) {
      return json.map((e) => e.toString()).toList();
    }
    return [];
  }

  @override
  dynamic toJson(List<String>? object) => object;
}
