part of 'invite_to_channel_bloc.dart';

@freezed
sealed class InviteToChannelState extends BaseBlocState
    with _$InviteToChannelState {
  const InviteToChannelState._();

  factory InviteToChannelState.initial({
    @Default([]) List<String> memberIDs,
  }) = _Initial;

  factory InviteToChannelState.userIDsInvitedUpdated({
    @Default([]) List<String> userIDsInvited,
  }) = _UserIDsInvitedUpdated;

  factory InviteToChannelState.searchingStatusChanged({
    required bool isSearching,
  }) = SearchingStatusChanged;

  factory InviteToChannelState.searchTextChanged({
    required String keyword,
  }) = SearchTextChanged;
}

extension InviteToChannelStateX on InviteToChannelState {
  T maybeWhen<T>({
    T Function(List<String> memberIDs)? initial,
    T Function(List<String> userIDsInvited)? userIDsInvitedUpdated,
    T Function(bool isSearching)? searchingStatusChanged,
    T Function(String keyword)? searchTextChanged,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is _Initial && initial != null) {
      return initial(state.memberIDs);
    }
    if (state is _UserIDsInvitedUpdated && userIDsInvitedUpdated != null) {
      return userIDsInvitedUpdated(state.userIDsInvited);
    }
    if (state is SearchingStatusChanged && searchingStatusChanged != null) {
      return searchingStatusChanged(state.isSearching);
    }
    if (state is SearchTextChanged && searchTextChanged != null) {
      return searchTextChanged(state.keyword);
    }

    return orElse();
  }

  T when<T>({
    required T Function(List<String> memberIDs) initial,
    required T Function(List<String> userIDsInvited) userIDsInvitedUpdated,
    required T Function(bool isSearching) searchingStatusChanged,
    required T Function(String keyword) searchTextChanged,
  }) {
    final state = this;

    if (state is _Initial) return initial(state.memberIDs);
    if (state is _UserIDsInvitedUpdated)
      return userIDsInvitedUpdated(state.userIDsInvited);
    if (state is SearchingStatusChanged)
      return searchingStatusChanged(state.isSearching);
    if (state is SearchTextChanged) return searchTextChanged(state.keyword);

    throw StateError('Unhandled state: $state');
  }
}
