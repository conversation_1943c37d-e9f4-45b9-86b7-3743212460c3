import '../../../../../../shared.dart';

class DialogUnavailableEvent extends DialogEvent {
  DialogUnavailableEvent({
    this.workspaceId,
    this.channelId,
    this.userId,
    super.source = BaseEvent.LOCAL_SOURCE,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;

  @override
  Map<String, dynamic> toJson() => {
        "workspaceId": workspaceId,
        "channelId": channelId,
        "userId": userId,
      };
}
