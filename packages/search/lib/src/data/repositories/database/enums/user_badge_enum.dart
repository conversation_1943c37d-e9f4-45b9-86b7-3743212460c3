import 'package:json_annotation/json_annotation.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

enum UserBadgeEnum {
  @JsonValue(0)
  DEFAULT(0),
  @JsonValue(1)
  BLUE(1),
  @JsonValue(2)
  GRAY(2),
  @JsonValue(3)
  YELLOW(3);

  final int value;

  int rawValue() => value;

  const UserBadgeEnum(this.value);
}

extension UserBadgeEnumExtension on UserBadgeEnum {
  static UserBadgeEnum getEnumByValue(int? rawValue) {
    return UserBadgeEnum.values.firstWhere(
      (e) => e.value == rawValue,
      orElse: () => UserBadgeEnum.DEFAULT,
    );
  }

  static int toRawValue(UserBadgeEnum type) {
    return type.rawValue();
  }
}

extension UserBadgeMapper on UserBadgeEnum {
  ui.UserBadgeType toUserBadgeType() {
    switch (this) {
      case UserBadgeEnum.BLUE:
        return ui.UserBadgeType.blue;
      case UserBadgeEnum.YELLOW:
        return ui.UserBadgeType.yellow;
      case UserBadgeEnum.GRAY:
        return ui.UserBadgeType.gray;
      default:
        return ui.UserBadgeType.unknown;
    }
  }
}
