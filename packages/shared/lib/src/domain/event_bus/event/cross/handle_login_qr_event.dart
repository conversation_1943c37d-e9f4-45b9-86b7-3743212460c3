import '../../../../../shared.dart';

class HandleLoginQREvent extends BaseEvent {
  HandleLoginQREvent({
    required String id,
    required this.qrValue,
    String source = BaseEvent.LOCAL_SOURCE,
  }) : super(id: id, source: source);

  final String qrValue;

  @override
  Map<String, dynamic> toJson() => {'qrValue': qrValue};
}

class FinishLoginQREvent extends BaseEvent {
  FinishLoginQREvent({
    required String id,
    String source = BaseEvent.LOCAL_SOURCE,
  }) : super(id: id, source: source);

  @override
  Map<String, dynamic> toJson() => {'id': id};
}
