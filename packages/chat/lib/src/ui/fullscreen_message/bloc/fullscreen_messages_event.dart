part of 'fullscreen_messages_bloc.dart';

@freezed
sealed class FullscreenMessagesEvent extends BaseBlocEvent
    with _$FullscreenMessagesEvent {
  const FullscreenMessagesEvent._();
  factory FullscreenMessagesEvent.initiate({
    String? workspaceId,
    String? channelId,
    String? userId,
    @Default(50) int limit,
  }) = FullscreenMessagesEventInitiate;

  factory FullscreenMessagesEvent.loadMore({
    String? workspaceId,
    String? channelId,
    String? userId,
    @Default(50) int limit,
    String? nextPageToken,
  }) = FullscreenMessagesEventLoadMore;

  factory FullscreenMessagesEvent.sync({
    String? workspaceId,
    String? channelId,
    String? userId,
    required String prevPageToken,
  }) = Sync;

  factory FullscreenMessagesEvent.addTempMessage(Message message) =
      AddTempMessage;

  factory FullscreenMessagesEvent.addMessage(Message message) = AddMessage;

  factory FullscreenMessagesEvent.updateMessage(Message message) =
      UpdateMessage;

  factory FullscreenMessagesEvent.updateMessages(List<Message> messages) =
      UpdateMessages;

  factory FullscreenMessagesEvent.addMessageReaction(
    ReactionData reaction,
  ) = AddMessageReaction;

  factory FullscreenMessagesEvent.revokeMessageReaction(
    ReactionData reaction,
  ) = RevokeMessageReaction;

  factory FullscreenMessagesEvent.saveTempMessage(Message message) =
      SaveTempMessage;

  factory FullscreenMessagesEvent.clearAllMessageEvent({
    String? workspaceId,
    String? channelId,
  }) = ClearAllMessageEvent;

  factory FullscreenMessagesEvent.clearMessageUnSubscription() =
      ClearMessageUnSubscription;

  factory FullscreenMessagesEvent.MarkAsReadMessageEvent({
    required String messageId,
    String? workspaceId,
    String? channelId,
    String? userId,
  }) = MarkAsReadFullScreenMessageEvent;

  factory FullscreenMessagesEvent.OnDeleteMessageForMeEvent({
    String? workspaceId,
    String? channelId,
    String? userId,
    List<String>? messageIds,
  }) = OnDeleteMessageForMeEvent;

  factory FullscreenMessagesEvent.OnDeleteMessageForEveryOneEvent({
    String? workspaceId,
    String? channelId,
    String? userId,
    List<String>? messageIds,
  }) = OnDeleteMessageForEveryOneEvent;

  factory FullscreenMessagesEvent.OnLocalDeleteMessageEvent({
    String? workspaceId,
    String? channelId,
    String? userId,
    List<String>? messageIds,
  }) = OnLocalDeleteMessageEvent;

  factory FullscreenMessagesEvent.OnLoadPinUnPinMessage(
    List<Message> messages,
  ) = OnLoadPinUnPinMessage;
}
