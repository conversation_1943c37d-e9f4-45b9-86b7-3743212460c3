import 'package:json_annotation/json_annotation.dart';

import 'response_channel.dart';
import 'response_channel_metadata.dart';
import 'response_member.dart';
import 'response_message.dart';
import 'response_user.dart';

part 'api_data.g.dart';

@JsonSerializable(explicitToJson: true)
class APIData {
  final ResponseMessage? message;
  final ResponseChannel? channel;
  final ResponseChannelMetadata? channelMetadata;
  final ResponseUser? user;
  final ResponseMember? member;

  APIData({
    this.message,
    this.channel,
    this.channelMetadata,
    this.user,
    this.member,
  });

  factory APIData.fromJson(Map<String, dynamic> json) =>
      _$APIDataFromJson(json);

  Map<String, dynamic> toJson() => _$APIDataToJson(this);
}
