{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:5755151949977715663", "lastPropertyId": "21:2068", "name": "User", "properties": [{"id": "1:184280995837690141", "name": "id", "type": 6, "flags": 129}, {"id": "2:2001", "name": "<PERSON><PERSON><PERSON>", "type": 9, "flags": 2048, "indexId": "1:6345687588363375349"}, {"id": "3:2002", "name": "userId", "type": 9}, {"id": "4:2003", "name": "username", "type": 9}, {"id": "5:2006", "name": "createTime", "type": 9}, {"id": "6:2007", "name": "updateTime", "type": 9}, {"id": "7:2008", "name": "userType", "type": 6}, {"id": "8:2010", "name": "userConnectLink", "type": 9}, {"id": "9:2011", "name": "mediaPermissionSetting", "type": 6}, {"id": "10:2012", "name": "dbPresenceData", "type": 9}, {"id": "11:2013", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "12:2014", "name": "dbProfile", "type": 9}, {"id": "13:2015", "name": "dbFriendData", "type": 9}, {"id": "14:2016", "name": "dbStatusData", "type": 9}, {"id": "15:2017", "name": "blocked", "type": 1}, {"id": "16:2018", "name": "globalNotificationStatus", "type": 1}, {"id": "17:2019", "name": "dbSetting", "type": 9}, {"id": "19:2066", "name": "partial", "type": 1}, {"id": "20:2067", "name": "sipCredentials", "type": 9}, {"id": "21:2068", "name": "sipAddress", "type": 9}], "relations": []}, {"id": "3:3618113809786688771", "lastPropertyId": "9:9008", "name": "Friend", "properties": [{"id": "1:7074415731501838283", "name": "id", "type": 6, "flags": 129}, {"id": "2:9001", "name": "friendId", "type": 9}, {"id": "3:9002", "name": "<PERSON><PERSON><PERSON>", "type": 9, "flags": 2048, "indexId": "4:7086368752148849495"}, {"id": "4:9003", "name": "userId", "type": 9}, {"id": "5:9004", "name": "requestedFromUserId", "type": 9}, {"id": "6:9005", "name": "requestedToUserId", "type": 9}, {"id": "7:9006", "name": "statusRow", "type": 6}, {"id": "8:9007", "name": "createTime", "type": 9}, {"id": "9:9008", "name": "updateTime", "type": 9}], "relations": []}, {"id": "4:3114277235513527030", "lastPropertyId": "15:13013", "name": "CallLogPrivateData", "properties": [{"id": "1:4317574616181321934", "name": "id", "type": 6, "flags": 129}, {"id": "2:13001", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "3:13002", "name": "callId", "type": 9}, {"id": "4:13003", "name": "callerId", "type": 9}, {"id": "5:13004", "name": "calleeId", "type": 9}, {"id": "6:13005", "name": "callState", "type": 6}, {"id": "7:13006", "name": "endedReason", "type": 6}, {"id": "8:13007", "name": "callTimeInSeconds", "type": 6}, {"id": "9:13008", "name": "isOutgoing", "type": 1}, {"id": "10:13009", "name": "readTime", "type": 9}, {"id": "11:13010", "name": "endedTime", "type": 9}, {"id": "12:5509627311458594822", "name": "privateDataId", "type": 11, "flags": 520, "indexId": "5:8079029435172453924", "relationTarget": "PrivateData"}, {"id": "13:13011", "name": "source", "type": 9}, {"id": "14:13012", "name": "version", "type": 6}, {"id": "15:13013", "name": "createTime", "type": 9}], "relations": []}, {"id": "5:4052016978606803828", "lastPropertyId": "10:12008", "name": "ChannelPrivateData", "properties": [{"id": "1:8075413873946152164", "name": "id", "type": 6, "flags": 129}, {"id": "2:12001", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "3:12002", "name": "channelId", "type": 9}, {"id": "4:12003", "name": "source", "type": 9}, {"id": "5:12004", "name": "version", "type": 6}, {"id": "6:12005", "name": "unreadCount", "type": 6}, {"id": "7:12006", "name": "lastSeenMessageId", "type": 9}, {"id": "8:6176837994190987624", "name": "privateDataId", "type": 11, "flags": 520, "indexId": "6:5283156962127133860", "relationTarget": "PrivateData"}, {"id": "9:12007", "name": "pinned", "type": 1}, {"id": "10:12008", "name": "sort", "type": 6}], "relations": []}, {"id": "6:1583717143655302177", "lastPropertyId": "4:14003", "name": "PrivateData", "properties": [{"id": "1:1098343080712589812", "name": "id", "type": 6, "flags": 129}, {"id": "2:14001", "name": "userId", "type": 9}, {"id": "3:14002", "name": "createTime", "type": 9}, {"id": "4:14003", "name": "updateTime", "type": 9}], "relations": []}, {"id": "7:9140086481094213094", "lastPropertyId": "9:11007", "name": "UserPrivateData", "properties": [{"id": "1:7603814540121263554", "name": "id", "type": 6, "flags": 129}, {"id": "2:11001", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "3:11002", "name": "userId", "type": 9}, {"id": "4:11003", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "5:11004", "name": "version", "type": 6}, {"id": "6:11005", "name": "source", "type": 9}, {"id": "7:11006", "name": "dmId", "type": 9}, {"id": "8:8304908658350062370", "name": "privateDataId", "type": 11, "flags": 520, "indexId": "7:3597460472721819595", "relationTarget": "PrivateData"}, {"id": "9:11007", "name": "blocked", "type": 1}], "relations": []}, {"id": "8:5645682955682627175", "lastPropertyId": "8:10026", "name": "VisitedProfile", "properties": [{"id": "1:7658544207544203718", "name": "id", "type": 6, "flags": 129}, {"id": "2:10020", "name": "<PERSON><PERSON><PERSON>", "type": 9, "flags": 2048, "indexId": "8:4650411032046952314"}, {"id": "3:10021", "name": "userId", "type": 9}, {"id": "4:10022", "name": "isRead", "type": 1}, {"id": "5:10023", "name": "createTime", "type": 9}, {"id": "6:10024", "name": "updateTime", "type": 9}], "relations": []}], "lastEntityId": "8:5645682955682627175", "lastIndexId": "8:4650411032046952314", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [3000056928217729461], "retiredIndexUids": [8985066871704480404], "retiredPropertyUids": [6681425491816379943, 3001, 3002, 3003, 3004, 3005, 3006, 10025, 10026, 2020], "retiredRelationUids": [], "version": 1}