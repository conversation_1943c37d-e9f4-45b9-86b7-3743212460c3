import 'dart:io';

import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../../chat.dart';
import '../../../../data/repositories/extensions/message_extension.dart';
import '../../../../domain/event_bus/message/show_fullscreen_event.dart';
import '../base/base_receiver_widget.dart';

class ImagesWidgetImpl extends BaseReceiverWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isFocusable;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isShowAvatar;
  final bool isShowName;
  final bool isHiddenPin;

  ImagesWidgetImpl({
    required this.isOpenCheckBox,
    this.isShowCreateTime = false,
    this.onCheckBoxButtonTap,
    this.isShowAvatar = false,
    this.isShowName = false,
    this.isHiddenPin = false,
    required this.isCheckedMessage,
    required this.isFocusable,
    required super.messageItem,
    super.key,
    required super.message,
  });

  @override
  Widget build(BuildContext context) {
    if (message.mediaAttachments.isEmpty) {
      //TODO: handle file not found view
      return const SizedBox();
    }

    final imageAttachments = message.mediaAttachments.map(
      (attachment) {
        final photo = attachment.photo ?? MediaObject.nullObject();
        return ImageAttachment(
          attachmentId:
              photo.attachmentId ?? photo.fileId ?? RandomUtils.randomUlId(),
          attachmentUrl: UrlUtils.parseCDNUrl(photo.fileUrl),
          width: photo.fileMetadata?.dimensions?.width?.toDouble() ?? 0,
          height: photo.fileMetadata?.dimensions?.height?.toDouble() ?? 0,
        );
      },
    ).toList();

    int findAttachmentIndex(
      List<ImageAttachment> attachments,
      String targetId,
    ) {
      return attachments
          .indexWhere((attachment) => attachment.attachmentId == targetId);
    }

    return ImagesMessageReceiverWidget(
      interface: this,
      messageItem: messageItem,
      isShowCreateTime: isShowCreateTime,
      isHiddenPin: isHiddenPin,
      isHideOptionCopy: imageAttachments.length > 1 || Platform.isAndroid,
      onQuote: onQuote,
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      isShowName: isShowName,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      emojiList: message.emojiList,
      isShowAvatar: isShowAvatar,
      onReport: onReport,
      imageAttachments: imageAttachments,
      quickReact: hasQuickReaction,
      onImageClicked: (MessageItem messageItem, String attachmentId) {
        final attachmentIndex =
            findAttachmentIndex(imageAttachments, attachmentId);

        AppEventBus.publish(
          ShowFullscreenEvent(
            messageId: messageItem.messageId,
            attachmentIndex: attachmentIndex,
            workspaceId: message.workspaceId,
            channelId: message.channelId,
          ),
        );
      },
      onDownload: (MessageItem messageItem) => onDownload(context),
      onCopy: (MessageItem messageItem) => onCopy(context, messageItem),
      isOpenCheckBox: isOpenCheckBox,
      isCheckedMessage: isCheckedMessage,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      isHighlighted: isFocusable,
    );
  }

  @override
  void onMessageItemClicked(MessageItem messageItem) {}
}
