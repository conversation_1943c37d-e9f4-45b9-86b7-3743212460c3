import 'package:json_annotation/json_annotation.dart';
import 'package:shared/shared.dart';

part 'call_user.g.dart';

@JsonSerializable()
class CallUser {
  const CallUser({
    required this.userId,
    required this.username,
    this.aliasName,
    this.displayName,
    this.avatar,
    this.decoratedAvatar,
    this.videoAvatar,
  });

  final String userId;
  final String username;
  final String? displayName;
  final String? aliasName;
  final String? avatar;
  final String? decoratedAvatar;
  final String? videoAvatar;

  @JsonKey(includeFromJson: false, includeToJson: false)
  String get name {
    if (!StringUtils.isNullOrEmpty(aliasName)) {
      return aliasName!;
    }
    if (!StringUtils.isNullOrEmpty(displayName)) {
      return displayName!;
    }
    return username;
  }

  factory CallUser.fromJson(Map<String, dynamic> json) =>
      _$CallUserFromJson(json);

  Map<String, dynamic> toJson() => _$CallUserToJson(this);
}
