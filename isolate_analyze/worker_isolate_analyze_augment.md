# Phân tích chi tiết Worker Isolate - worker_isolate.dart

## Tổng quan chức năng

File `worker_isolate.dart` là một thành phần cốt lõi trong hệ thống xử lý tác vụ nền (background tasks) của ứng dụng Flutter. Nó cung cấp hai cơ chế chính để xử lý các tác vụ:

1. **Worker Isolate**: Xử lý tác vụ trong isolate riêng biệt
2. **Workmanager**: Xử lý tác vụ nền khi ứng dụng không hoạt động

## Kiến trúc và luồng xử lý

### 1. Entry Points chính

#### `callbackDispatcher()` - Entry point cho Workmanager
- **Mục đích**: <PERSON><PERSON><PERSON><PERSON> gọi bởi Workmanager khi có tác vụ nền cần xử lý
- **Luồng xử lý**:
  1. Khởi tạo Flutter binding
  2. Khởi tạo các service cần thiết (API client, FileStore client)
  3. Xử lý tác vụ composite "process_pending_tasks" hoặc tác vụ đơn lẻ
  4. <PERSON>try tác vụ thất bại trên iOS
- **Đặc điểm**: Hoạt động độc lập, không cần main isolate

#### `workerIsolateEntryPoint()` - Entry point cho Worker Isolate
- **Mục đích**: Được gọi khi tạo worker isolate mới
- **Luồng xử lý**:
  1. Thiết lập communication ports với main isolate
  2. Khởi tạo shared variables (token, metadata, clients)
  3. Lắng nghe và xử lý các yêu cầu tác vụ
  4. Gửi heartbeat định kỳ để duy trì kết nối
- **Đặc điểm**: Hoạt động liên tục, giao tiếp với main isolate

### 2. Cơ chế xử lý tác vụ

#### Task Execution Flow
```
executeTask() → executeTaskLogic() → Specific Handler → Result Processing
```

#### Các loại tác vụ được hỗ trợ:
1. **sendMessage**: Gửi tin nhắn văn bản
2. **editMessage**: Chỉnh sửa tin nhắn
3. **sendQuoteMessage**: Gửi tin nhắn trích dẫn
4. **sendForwardMessage**: Chuyển tiếp tin nhắn
5. **compressVideoMessage**: Nén video
6. **compressAndUploadVideoMessage**: Nén và upload video
7. **uploadFile**: Upload file
8. **compressAndUploadImages**: Nén và upload hình ảnh

### 3. Xử lý Media và File

#### Video Processing Pipeline
```
Compress Video → Upload Video → Upload Thumbnail → Send Message
```

#### Image Processing Pipeline
```
Compress Images → Upload Images → Send/Update Message → Process Next Batch
```

#### File Upload Pipeline
```
Upload File → Create Media Object → Send Message
```

## Các phương thức quan trọng

### 1. `executeTaskLogic()` - Core task execution
- **Chức năng**: Điều phối xử lý các loại tác vụ khác nhau
- **Input**: Task name, session key, input data, task registration port
- **Output**: Boolean success status
- **Đặc điểm**:
  - Kiểm tra timeout dựa trên creationTime
  - Xử lý network errors một cách đặc biệt (silent retry)
  - Cập nhật trạng thái tác vụ sau khi hoàn thành

### 2. `_handleCompressAndUploadImages()` - Image processing
- **Chức năng**: Xử lý nén và upload hình ảnh theo batch
- **Luồng xử lý**:
  1. Xử lý batch đầu tiên (currentBash = 0)
  2. Gửi tin nhắn với hình ảnh đầu tiên
  3. Xử lý các batch tiếp theo (currentBash > 0)
  4. Cập nhật tin nhắn với hình ảnh bổ sung
- **Đặc điểm**: Hỗ trợ xử lý nhiều hình ảnh trong một tin nhắn

### 3. `_handleCompressAndUploadVideoMessage()` - Video processing
- **Chức năng**: Xử lý toàn bộ pipeline video
- **Các bước**:
  1. Nén video với `_compressVideo()`
  2. Upload video với `_uploadVideo()`
  3. Upload thumbnail với `_uploadThumbnail()`
  4. Gửi tin nhắn với `_sendVideoMessage()`
- **Timeout**: 15 phút cho toàn bộ quá trình

### 4. `_handleUploadAndSendFileMessage()` - File upload and send
- **Chức năng**: Upload file và gửi tin nhắn trong một luồng
- **Các bước**:
  1. Upload file với `_uploadFile()`
  2. Tạo media object
  3. Gửi tin nhắn với `_sendFileMessage()`
- **Timeout**: 10 phút cho toàn bộ quá trình

## Cơ chế Error Handling

### 1. Network Error Handling
- **Đặc điểm**: Xử lý đặc biệt để cho phép silent retry
- **Cách thức**:
  - Không hiển thị lỗi cho user
  - Giữ tin nhắn ở trạng thái PENDING
  - Cho phép retry khi có mạng

### 2. Timeout Handling
- **Timeout levels**:
  - Task level: Dựa trên loại tác vụ
  - Operation level: Cho từng thao tác cụ thể
  - Global level: Từ GlobalConfig.sendTimeoutDuration

### 3. Retry Mechanism
- **File operations**: 3 lần retry với exponential backoff
- **Network operations**: Sử dụng RetryManager
- **Video compression**: 3 lần retry với delay tăng dần

## Task Registration và Recovery

### 1. Task Registration
- **Cơ chế**: Sử dụng `_registerTaskWithResilientIsolate()`
- **Routing**:
  - Qua taskRegistrationPort nếu có
  - Trực tiếp với Workmanager nếu không có port

### 2. Task Recovery
- **SharedPreferencesStore**: Lưu trữ tác vụ để recovery
- **Result persistence**: Lưu kết quả cho các tác vụ quan trọng
- **Acknowledgment system**: Đảm bảo kết quả được nhận

## Logging và Debugging

### 1. Structured Logging
- **Format**: `[DEBUG][TaskID:xxx][MessageRef:xxx] Message`
- **Levels**: Task debug, method debug, error logging
- **Tracking**: Chi tiết từng bước xử lý

### 2. Performance Monitoring
- **Timing**: Đo thời gian cho từng operation
- **Resource tracking**: File size, compression ratio
- **Progress tracking**: Batch processing progress

## Tối ưu hóa và Performance

### 1. Memory Management
- **File streaming**: Không load toàn bộ file vào memory
- **Batch processing**: Xử lý hình ảnh theo batch
- **Resource cleanup**: Cleanup sau mỗi operation

### 2. Concurrency Control
- **Task deduplication**: Tránh xử lý trùng lặp
- **Processing tracking**: Theo dõi tác vụ đang xử lý
- **Heartbeat mechanism**: Duy trì kết nối isolate

### 3. Network Optimization
- **Connection reuse**: Sử dụng lại API client
- **Retry strategy**: Smart retry cho network errors
- **Timeout tuning**: Timeout phù hợp cho từng loại tác vụ

## Chi tiết các phương thức chính

### 1. `callbackDispatcher()` (Lines 61-233)
```dart
@pragma('vm:entry-point')
void callbackDispatcher() async
```
- **Chức năng**: Entry point cho Workmanager background tasks
- **Luồng xử lý chi tiết**:
  1. **Initialization**: Khởi tạo WidgetsFlutterBinding và services
  2. **Task Processing**: Xử lý "process_pending_tasks" hoặc single task
  3. **Batch Processing**: Sắp xếp tasks theo priority và thời gian tạo
  4. **Result Handling**: Xóa task thành công, retry task thất bại
- **Đặc điểm quan trọng**:
  - Hoạt động độc lập với main isolate
  - Hỗ trợ composite task processing
  - Retry mechanism cho iOS

### 2. `workerIsolateEntryPoint()` (Lines 253-660)
```dart
@pragma('vm:entry-point')
void workerIsolateEntryPoint(List<dynamic> args) async
```
- **Chức năng**: Entry point cho worker isolate
- **Communication Setup**:
  - `responsePort`: Gửi kết quả về main isolate
  - `ackPort`: Nhận acknowledgment
  - `taskRegistrationPort`: Đăng ký task mới
- **State Management**:
  - `_registeredTaskIds`: Theo dõi tasks đã đăng ký
  - `_processingTaskIds`: Theo dõi tasks đang xử lý
  - `pendingAcks`: Quản lý acknowledgments
- **Initialization Flow**:
  1. Nhận init data từ main isolate hoặc IsolateTaskService
  2. Khởi tạo shared clients (API, FileStore, RetryManager)
  3. Thiết lập TaskHandlerRegistry
- **Task Processing**:
  - Duplicate detection và prevention
  - Async task execution với Future()
  - Heartbeat mechanism (30 seconds interval)

### 3. `executeTaskLogic()` (Lines 1093-1458)
```dart
Future<bool> executeTaskLogic(
  String name,
  String sessionKey,
  Map<String, dynamic> inputData,
  SendPort? taskRegistrationPort,
) async
```
- **Chức năng**: Core logic xử lý tất cả loại tasks
- **Timeout Checking**:
  - Kiểm tra `creationTime` với `GlobalConfig.sendTimeoutDuration`
  - Early termination cho tasks quá hạn
- **Task Routing**:
  - Switch case dựa trên `TaskNameEnum`
  - Delegate đến specific handlers
- **Error Handling**:
  - `FileNotFoundException`: Xử lý file không tồn tại
  - `SendMessageTimeoutException`: Xử lý timeout
  - `DioException`: Xử lý network errors đặc biệt
- **Network Error Strategy**:
  - Silent failure cho network errors
  - Giữ message ở PENDING state
  - Không hiển thị error cho user

### 4. `_handleCompressAndUploadImages()` (Lines 1479-2095)
```dart
Future<bool> _handleCompressAndUploadImages(
  Map<String, dynamic> inputData,
  IsolateApiClient apiClient,
  FilestoreClientV2 fileStoreClient,
  String folderPath,
  SendPort? taskRegistrationPort,
) async
```
- **Chức năng**: Xử lý batch image compression và upload
- **First Batch Processing** (currentBash = 0):
  1. Compress và upload first image
  2. Gửi message với first image
  3. Lưu messageId cho subsequent batches
  4. Process remaining images recursively
- **Subsequent Batch Processing** (currentBash > 0):
  1. Process multiple images concurrently
  2. Update message với new attachments
  3. Handle attachment errors gracefully
- **Error Handling**:
  - Individual image failures không block toàn bộ batch
  - Attachment status tracking
  - Graceful degradation

### 5. `_handleCompressAndUploadVideoMessage()` (Lines 3714-4068)
```dart
Future<void> _handleCompressAndUploadVideoMessage(
  Map<String, dynamic> inputData,
  IsolateApiClient apiClient,
  FilestoreClientV2 fileStoreClient,
  String folderPath,
) async
```
- **Chức năng**: Complete video processing pipeline
- **Processing Steps**:
  1. **Video Compression**: `_compressVideo()` với timeout 5 phút
  2. **Video Upload**: `_uploadVideo()` với timeout 8 phút
  3. **Thumbnail Upload**: `_uploadThumbnail()` với timeout ngắn hơn
  4. **Message Sending**: `_sendVideoMessage()` với timeout 3 phút
- **Total Timeout**: 15 phút cho toàn bộ pipeline
- **Error Recovery**:
  - Mỗi step có retry mechanism riêng
  - Thumbnail failure không block video sending
  - Comprehensive error logging và result saving

### 6. `_handleUploadAndSendFileMessage()` (Lines 2493-2944)
```dart
Future<void> _handleUploadAndSendFileMessage(
  Map<String, dynamic> inputData,
  IsolateApiClient apiClient,
  FilestoreClientV2 fileStoreClient,
  String folderPath,
) async
```
- **Chức năng**: Single-flow file upload và message sending
- **Processing Flow**:
  1. **File Upload**: `_uploadFile()` với timeout 5 phút
  2. **Media Object Creation**: Tạo WorkerMediaObject
  3. **Message Sending**: `_sendFileMessage()` với timeout 5 phút
- **File Type Support**:
  - Voice messages (uploadType = 4)
  - General files (uploadType = 5)
  - Automatic attachment type detection
- **Comprehensive Logging**: Chi tiết checkpoint tracking

## Utility Methods và Helper Functions

### 1. `_saveSendMessageResult()` (Lines 2113-2198)
```dart
Future<void> _saveSendMessageResult({
  required String msgRef,
  String? msgId,
  String? workspaceId,
  String? channelId,
  String? userId,
  Map<String, AttachmentStatusEnum>? mapAttachmentStatus,
  String? errorReason,
  bool forceStore = false,
}) async
```
- **Chức năng**: Lưu kết quả gửi message để UI update
- **Network Error Handling**: Không lưu error reason cho network errors
- **Storage Strategy**: Chỉ lưu khi cần thiết (forceStore hoặc có error)

### 2. `_registerTaskWithResilientIsolate()` (Lines 5314-5410)
```dart
Future<void> _registerTaskWithResilientIsolate({
  required String taskId,
  required String taskName,
  String? tag,
  required SendPort? taskRegistrationPort,
  required Map<String, dynamic> inputData,
  // ... other parameters
}) async
```
- **Chức năng**: Đăng ký task với main isolate hoặc Workmanager
- **Routing Logic**:
  - Nếu có `taskRegistrationPort`: Gửi qua port
  - Nếu không có: Đăng ký trực tiếp với Workmanager
- **Timeout Adjustment**: Sử dụng `_getTaskTimeout()` để điều chỉnh

### 3. `_getTaskTimeout()` (Lines 5283-5299)
```dart
int _getTaskTimeout(String taskName, int defaultTimeout) {
  final globalTimeout = GlobalConfig.sendTimeoutDuration.inMilliseconds;

  if (taskName == TaskNameEnum.compressVideoMessage.value ||
      taskName == TaskNameEnum.compressAndUploadVideoMessage.value) {
    return math.max(globalTimeout, 10 * 60 * 1000); // 10 minutes
  } else if (taskName == TaskNameEnum.uploadFile.value) {
    return math.max(globalTimeout, 5 * 60 * 1000); // 5 minutes
  }

  return globalTimeout;
}
```
- **Chức năng**: Điều chỉnh timeout dựa trên loại task
- **Video tasks**: Tối thiểu 10 phút
- **Upload tasks**: Tối thiểu 5 phút
- **Other tasks**: Sử dụng GlobalConfig.sendTimeoutDuration

### 4. `persistUnacknowledgedResult()` (Lines 5231-5271)
```dart
Future<void> persistUnacknowledgedResult(
  String taskId,
  bool success,
  SendPort ackPort,
) async
```
- **Chức năng**: Lưu kết quả khi không nhận được acknowledgment
- **Recovery Mechanism**: Đảm bảo không mất kết quả quan trọng
- **Message Tasks**: Force store kết quả cho message tasks

## Patterns và Best Practices

### 1. Error Handling Patterns

#### Network Error Pattern
```dart
if (e is DioException && e.isNetworkError) {
  // Silent failure - không hiển thị error cho user
  // Giữ message ở PENDING state để retry sau
  success = true; // Pretend success để tránh UI error
}
```

#### Timeout Pattern
```dart
final completer = Completer<ResultType>();
final timer = Timer(timeout, () {
  if (!completer.isCompleted) {
    completer.completeError(TimeoutException('Operation timeout'));
  }
});

// Execute operation
Future(() async {
  try {
    final result = await operation();
    if (!completer.isCompleted) {
      completer.complete(result);
    }
  } catch (e) {
    if (!completer.isCompleted) {
      completer.completeError(e);
    }
  }
});

return await completer.future.whenComplete(() => timer.cancel());
```

#### Retry Pattern
```dart
int retryCount = 0;
while (retryCount <= maxRetries) {
  try {
    final result = await operation();
    return result;
  } catch (e) {
    retryCount++;
    if (retryCount > maxRetries) return null;
    await Future.delayed(Duration(seconds: 2 * retryCount)); // Exponential backoff
  }
}
```

### 2. Logging Patterns

#### Structured Logging
```dart
RILogger.printClassMethodDebug(
  'WorkerIsolate',
  'methodName',
  '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Message with context',
);
```

#### Performance Tracking
```dart
final stopwatch = Stopwatch()..start();
// ... operation
final duration = stopwatch.elapsedMilliseconds;
RILogger.printClassMethodDebug(
  'WorkerIsolate',
  'methodName',
  'Operation completed in ${duration}ms',
);
```

### 3. Resource Management Patterns

#### File Validation
```dart
final file = File(filePath);
if (!file.existsSync()) {
  throw FileNotFoundException(message: 'File not exists: $filePath');
}
```

#### Completer Management
```dart
final completer = Completer<void>();
Timer? timer;

try {
  timer = Timer(timeout, () {
    if (!completer.isCompleted) {
      completer.complete();
    }
  });

  // ... processing

} finally {
  timer?.cancel();
  if (!completer.isCompleted) {
    completer.complete();
  }
}
```

## Điểm mạnh của thiết kế hiện tại

### 1. Robustness
- **Dual execution model**: Worker Isolate + Workmanager đảm bảo tasks được xử lý
- **Comprehensive error handling**: Network errors, timeouts, file errors
- **Recovery mechanisms**: Task persistence, acknowledgment system
- **Graceful degradation**: Thumbnail failures không block video sending

### 2. Performance
- **Batch processing**: Xử lý nhiều images hiệu quả
- **Concurrent operations**: Parallel processing trong limits
- **Resource management**: Proper cleanup và memory management
- **Timeout optimization**: Task-specific timeouts

### 3. Maintainability
- **Structured logging**: Chi tiết tracking và debugging
- **Modular design**: Separate handlers cho từng task type
- **Clear separation**: Business logic tách biệt với infrastructure
- **Comprehensive documentation**: Inline comments và method docs

## Các vấn đề và điểm cần cải thiện

### 1. Code Complexity
- **File size**: 5691 lines - quá lớn cho một file
- **Method length**: Một số methods quá dài (>200 lines)
- **Nested logic**: Deep nesting trong error handling
- **Duplicate code**: Retry patterns lặp lại nhiều nơi

### 2. Error Handling
- **Inconsistent patterns**: Một số nơi dùng try-catch, nơi khác dùng completer
- **Silent failures**: Network errors được handle silent có thể gây confusion
- **Error recovery**: Không có mechanism để recover từ partial failures

### 3. Performance Issues
- **Memory usage**: Có thể optimize memory usage cho large files
- **Concurrent limits**: Không có explicit concurrency control
- **Resource leaks**: Potential timer leaks nếu exception xảy ra

### 4. Testing Challenges
- **Tight coupling**: Hard to unit test do dependencies
- **Async complexity**: Complex async flows khó test
- **Isolate testing**: Khó mock isolate communication

## Đề xuất cải tiến

### 1. Refactoring Structure
```dart
// Tách thành các files nhỏ hơn
worker_isolate_entry_points.dart     // Entry points
worker_isolate_task_executor.dart    // Core execution logic
worker_isolate_media_handlers.dart   // Media processing
worker_isolate_error_handlers.dart   // Error handling
worker_isolate_utils.dart           // Utilities
```

### 2. Improved Error Handling
```dart
// Unified error handling strategy
abstract class TaskErrorHandler {
  Future<TaskResult> handleError(Exception error, TaskContext context);
}

class NetworkErrorHandler extends TaskErrorHandler {
  @override
  Future<TaskResult> handleError(Exception error, TaskContext context) {
    // Consistent network error handling
  }
}
```

### 3. Better Resource Management
```dart
// Resource manager với automatic cleanup
class ResourceManager {
  final List<Timer> _timers = [];
  final List<Completer> _completers = [];

  void addTimer(Timer timer) => _timers.add(timer);
  void addCompleter(Completer completer) => _completers.add(completer);

  void cleanup() {
    _timers.forEach((timer) => timer.cancel());
    _completers.where((c) => !c.isCompleted).forEach((c) => c.complete());
  }
}
```

### 4. Enhanced Monitoring
```dart
// Performance monitoring và metrics
class TaskMetrics {
  static void recordTaskDuration(String taskName, Duration duration);
  static void recordTaskSuccess(String taskName);
  static void recordTaskFailure(String taskName, String reason);
  static Map<String, dynamic> getMetrics();
}
```

### 5. Configuration Management
```dart
// Centralized configuration
class WorkerIsolateConfig {
  static const Duration defaultTimeout = Duration(minutes: 5);
  static const Duration videoTimeout = Duration(minutes: 10);
  static const int maxRetries = 3;
  static const Duration heartbeatInterval = Duration(seconds: 30);
}
```

## Kết luận

File `worker_isolate.dart` là một thành phần phức tạp và quan trọng trong hệ thống, đảm nhận vai trò xử lý tất cả các tác vụ nền. Thiết kế hiện tại có nhiều điểm mạnh về robustness và functionality, nhưng cần được refactor để cải thiện maintainability và testability.

Các cải tiến được đề xuất sẽ giúp:
- **Giảm complexity**: Tách file lớn thành modules nhỏ hơn
- **Tăng reliability**: Unified error handling và better resource management
- **Cải thiện performance**: Better monitoring và optimization
- **Dễ maintain hơn**: Clear structure và consistent patterns
- **Dễ test hơn**: Loose coupling và dependency injection

Việc implement các cải tiến này nên được thực hiện từng bước để đảm bảo không ảnh hưởng đến functionality hiện tại.

---

## Phân tích Error Handling Issues trong TaskNameEnum.compressAndUploadVideoMessage

### Tổng quan Flow xử lý

Flow xử lý `compressAndUploadVideoMessage` bao gồm:
1. **executeTaskLogic()** (lines 1236-1254) - Entry point
2. **_handleCompressAndUploadVideoMessage()** (lines 3714-4068) - Main handler
3. **Helper methods**:
   - `_compressVideo()` (lines 2346-2492)
   - `_uploadVideo()` (lines 3340-3483)
   - `_uploadThumbnail()` (lines 3484-3576)
   - `_sendVideoMessage()` (lines 3591-3696)

### Các điểm có vấn đề về Error Handling

#### 1. **executeTaskLogic() - Missing error handling cho video task**

**Line 1236-1254**: Case `TaskNameEnum.compressAndUploadVideoMessage`
```dart
case TaskNameEnum.compressAndUploadVideoMessage:
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    'executeTaskLogic',
    '[DEBUG][TaskID:$taskId][TaskName:$taskName] About to call _handleCompressAndUploadVideoMessage',
  );
  await _handleCompressAndUploadVideoMessage(
    inputData,
    apiClient,
    fileStoreClient,
    folderPath,
  );
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    'executeTaskLogic',
    '[DEBUG][TaskID:$taskId][TaskName:$taskName] _handleCompressAndUploadVideoMessage completed successfully',
  );
  success = true;
  break;
```

**🚨 VẤN ĐỀ**:
- Không có try-catch wrapper cho `_handleCompressAndUploadVideoMessage()`
- Nếu method này throw exception, sẽ không được handle properly
- Khác với `uploadFile` case có try-catch (lines 1274-1308)

**TODO**:
```dart
//TODO: Add try-catch wrapper around _handleCompressAndUploadVideoMessage call
// Similar to uploadFile case (lines 1274-1308) to ensure exceptions are caught
// and WorkerSendResultHandler.handleError is called appropriately
```

#### 2. **_handleCompressAndUploadVideoMessage() - Multiple early returns without error handling**

**Line 3795**: Early return after compression failure
```dart
if (compressOutput == null) {
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_handleCompressAndUploadVideoMessage',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Video compression failed after retries, aborting task',
  );

  // Save error result to ensure UI is updated correctly
  await _saveSendMessageResult(
    msgRef: messageRef,
    workspaceId: input.workspaceId,
    channelId: input.channelId,
    userId: input.userId,
    errorReason: "Video compression failed after retries",
    forceStore: true, // Ensure result is always saved
  );

  return; // ❌ Early return without calling WorkerSendResultHandler.handleError
}
```

**🚨 VẤN ĐỀ**:
- Early return không gọi `WorkerSendResultHandler.handleError`
- Chỉ save result nhưng không notify error handler

**TODO**:
```dart
//TODO: Add WorkerSendResultHandler.handleError call here
// Should call WorkerSendResultHandler.handleError(ref: taskId, error: compressionError)
// before return to ensure proper error notification to UI
```

**Line 3853**: Early return after upload failure
```dart
if (uploadOutput == null) {
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_handleCompressAndUploadVideoMessage',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Video upload failed after retries, aborting task',
  );
  return; // ❌ Early return without error handling
}
```

**🚨 VẤN ĐỀ**:
- Early return không save error result
- Không gọi `WorkerSendResultHandler.handleError`
- UI sẽ không biết task đã fail

**TODO**:
```dart
//TODO: Add WorkerSendResultHandler.handleError call here
// Should save error result and call WorkerSendResultHandler.handleError
// Similar to compression failure handling above (lines 3786-3793)
```

**Line 3930**: Early return after send message failure
```dart
if (sendOutput == null) {
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_handleCompressAndUploadVideoMessage',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Failed to send video message after retries, aborting task',
  );
  return; // ❌ Early return without error handling
}
```

**🚨 VẤN ĐỀ**:
- Early return không save error result
- Không gọi `WorkerSendResultHandler.handleError`

**TODO**:
```dart
//TODO: Add WorkerSendResultHandler.handleError call here
// Should save error result and call WorkerSendResultHandler.handleError
// to notify UI about send message failure
```

#### 3. **_compressVideo() - Silent failure return**

**Line 2367**: Early return for missing file
```dart
if (!File(input.file.path).existsSync()) {
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_compressVideo',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] ERROR: Source file does not exist at path: ${input.file.path}',
  );
  return null; // ❌ Silent failure
}
```

**🚨 VẤN ĐỀ**:
- Return null mà không throw exception
- Caller không biết lý do fail cụ thể
- Không consistent với FileNotFoundException pattern

**TODO**:
```dart
//TODO: Throw FileNotFoundException instead of returning null
// Should throw FileNotFoundException(message: 'File not exists: ${input.file.path}')
// to be consistent with other file validation patterns
```