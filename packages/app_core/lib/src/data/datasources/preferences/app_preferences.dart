import 'package:encrypted_shared_preferences/encrypted_shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferenceKeys {
  const SharedPreferenceKeys._();

  static const accessToken = 'accessToken';
  static const isAuthenticated = 'isAuthenticated';
  static const themeMode = 'themeMode';
  static const languageCode = 'languageCode';
  static const firstTimeInitApp = 'firstTimeInitApp';
  static const sessionMigrated = 'sessionMigrated';
  static const firstTimeOpenAppAfterMigration =
      'firstTimeOpenAppAfterMigration';
}

@LazySingleton()
class AppPreferences {
  AppPreferences(this._sharedPreference)
      : _encryptedSharedPreferences =
            EncryptedSharedPreferences(prefs: _sharedPreference);

  final SharedPreferences _sharedPreference;
  final EncryptedSharedPreferences _encryptedSharedPreferences;

  Future<String> get accessToken {
    return _encryptedSharedPreferences
        .getString(SharedPreferenceKeys.accessToken);
  }

  Future<void> saveAccessToken(String token) async {
    await _encryptedSharedPreferences.setString(
      SharedPreferenceKeys.accessToken,
      token,
    );
  }

  bool get isAuthenticated {
    return _sharedPreference.getBool(SharedPreferenceKeys.isAuthenticated) ??
        false;
  }

  Future<bool> saveIsAuthenticated(bool isAuthenticated) {
    return _sharedPreference.setBool(
      SharedPreferenceKeys.isAuthenticated,
      isAuthenticated,
    );
  }

  ThemeMode get getThemeMode {
    final themeMode =
        _sharedPreference.getString(SharedPreferenceKeys.themeMode);

    return ThemeMode.values
        .firstWhere((s) => s.name == themeMode, orElse: () => ThemeMode.system);
  }

  Future<bool> saveIsDarkMode(ThemeMode themeMode) {
    return _sharedPreference.setString(
      SharedPreferenceKeys.themeMode,
      themeMode.name,
    );
  }

  String? get languageCode {
    return _sharedPreference.getString(SharedPreferenceKeys.languageCode);
  }

  Future<bool> saveLocale(Locale locale) {
    return _sharedPreference.setString(
      SharedPreferenceKeys.languageCode,
      locale.languageCode,
    );
  }

  bool get isFirstTimeInitApp {
    return _sharedPreference.getBool(SharedPreferenceKeys.firstTimeInitApp) ??
        true;
  }

  Future<void> setFirstTimeInitApp(bool b) async {
    await _sharedPreference.setBool(
      SharedPreferenceKeys.firstTimeInitApp,
      b,
    );
  }

  Future<void> setSessionMigrated(bool b) async {
    await _sharedPreference.setBool(
      SharedPreferenceKeys.sessionMigrated,
      b,
    );
  }

  bool get isSessionMigrated {
    return _sharedPreference.getBool(SharedPreferenceKeys.sessionMigrated) ??
        false;
  }

  String? getDownloadPath(String taskId) {
    final path = _sharedPreference.getString('DOWNLOAD_OUTPUT_${taskId}');
    return path;
  }

  Future<void> setFirstTimeOpenAppAfterMigration(bool b) async {
    await _sharedPreference.setBool(
      SharedPreferenceKeys.firstTimeOpenAppAfterMigration,
      b,
    );
  }

  Future<bool?> isFirstTimeOpenAppAfterMigration() async {
    return await _sharedPreference
        .getBool(SharedPreferenceKeys.firstTimeOpenAppAfterMigration);
  }
}
