import 'package:json_annotation/json_annotation.dart';

import 'response_restrict_saving_content.dart';

part 'response_privacy_settings.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponsePrivacySettings {
  final ResponseRestrictSavingContent restrictSavingContent;

  ResponsePrivacySettings({required this.restrictSavingContent});

  factory ResponsePrivacySettings.fromJson(Map<String, dynamic> json) =>
      _$ResponsePrivacySettingsFromJson(json);

  Map<String, dynamic> toJson() => _$ResponsePrivacySettingsToJson(this);
}
