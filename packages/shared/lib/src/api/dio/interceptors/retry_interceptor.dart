import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../../../../shared.dart';

class RetryInterceptor extends Interceptor {
  RetryInterceptor({
    required this.dio,
    this.maxRetries = 3,
    this.retryIntervalMs = 1000,
    this.retryStatusCodes = const [500, 502, 503, 504],
  });

  final Dio dio;
  final int maxRetries;
  final int retryIntervalMs;
  final List<int> retryStatusCodes;

  @override
  Future<void> onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    // If the error is already a RetryException, pass it through
    if (err is RetryException) {
      debugPrint(
        'RetryInterceptor: Received RetryException: ${err.retryReason}',
      );
      handler.next(err);
      return;
    }

    // Handle specific network errors and convert them to RetryException with specific reasons
    final retryReason = _getRetryReason(err);
    if (retryReason != null) {
      debugPrint(
        'RetryInterceptor: Detected error that should retry: $retryReason',
      );
      handler.next(
        RetryException.withReason(
          retryReason,
          requestOptions: err.requestOptions,
          response: err.response,
          type: err.type,
          error: err.error,
        ),
      );
      return;
    }

    Log.api(
      'Request errorType: ${err.type}',
      requestData: err.requestOptions.data,
      responseData: err.response?.data,
      statusCode: err.response?.statusCode,
    );

    if (_shouldRetry(err)) {
      final retries = (err.requestOptions.extra['retries'] ?? 0) as int;
      if (retries < maxRetries) {
        await _retryRequest(err, handler, retries);
        return;
      }
    }

    handler.next(err);
  }

  Future<void> _retryRequest(
    DioException err,
    ErrorInterceptorHandler handler,
    int retries,
  ) async {
    final delay = retryIntervalMs * (retries + 1);
    Log.api('Retry attempt #${retries + 1} '
        '\nfor ${err.requestOptions.method} ${err.requestOptions.path} '
        '\nafter $delay ms');

    await Future<void>.delayed(Duration(milliseconds: delay));
    err.requestOptions.extra['retries'] = retries + 1;

    try {
      final response = await dio.request<Response<dynamic>>(
        err.requestOptions.path,
        data: err.requestOptions.data,
        queryParameters: err.requestOptions.queryParameters,
        options: Options(
          method: err.requestOptions.method,
          headers: err.requestOptions.headers,
          extra: err.requestOptions.extra,
          responseType: err.requestOptions.responseType,
          contentType: err.requestOptions.contentType,
          receiveDataWhenStatusError:
              err.requestOptions.receiveDataWhenStatusError,
          followRedirects: err.requestOptions.followRedirects,
          maxRedirects: err.requestOptions.maxRedirects,
          requestEncoder: err.requestOptions.requestEncoder,
          responseDecoder: err.requestOptions.responseDecoder,
        ),
      );
      handler.resolve(response);
    } on DioException catch (e) {
      Log.api(
        'Retry attempt failed: ${err.requestOptions.path}',
        requestData: e.requestOptions.data,
        responseData: e.response?.data,
        statusCode: e.response?.statusCode,
      );
      handler.next(e);
    } catch (e) {
      Log.e('Unexpected error during retry: $e');
      handler.next(
        DioException(
          requestOptions: err.requestOptions,
          error: e,
          type: DioExceptionType.unknown,
        ),
      );
    }
  }

  /// Determines if a request should be retried based on the error type and response
  ///
  /// Retry is appropriate for:
  /// - Network-related errors (timeouts, connection issues)
  /// - Certain server errors (5xx status codes)
  /// - Specific error messages indicating temporary issues
  bool _shouldRetry(DioException err) {
    // Never retry canceled requests
    if (err.type == DioExceptionType.cancel) return false;

    // Don't retry client errors (4xx) except specific cases
    if (err.type == DioExceptionType.badResponse) {
      final statusCode = err.response?.statusCode;

      // Retry server errors (5xx)
      if (retryStatusCodes.contains(statusCode)) return true;

      // Retry specific 4xx errors that might be temporary
      if (statusCode == 429) {
        // Too Many Requests - retry after backing off
        return true;
      }

      // Check response body for specific error messages that indicate retry is appropriate
      final responseData = err.response?.data;
      if (responseData is Map<String, dynamic>) {
        final errorMessage = responseData['message'] as String?;
        if (errorMessage != null) {
          // Retry if error message indicates a temporary server issue
          final temporaryErrorKeywords = [
            'timeout',
            'overloaded',
            'temporary',
            'maintenance',
            'retry',
            'try again',
            'unavailable',
          ];

          if (temporaryErrorKeywords
              .any((keyword) => errorMessage.toLowerCase().contains(keyword))) {
            return true;
          }
        }
      }

      // Don't retry other client errors
      return false;
    }

    // Retry all network-related errors
    final retryableErrorTypes = [
      DioExceptionType.connectionTimeout,
      DioExceptionType.sendTimeout,
      DioExceptionType.receiveTimeout,
      DioExceptionType.connectionError,
      DioExceptionType
          .unknown, // Sometimes network errors are reported as unknown
    ];

    if (retryableErrorTypes.contains(err.type)) {
      // For unknown errors, check if it's network-related
      if (err.type == DioExceptionType.unknown) {
        final errorMessage = err.error?.toString() ?? '';
        final networkErrorKeywords = [
          'network',
          'internet',
          'connection',
          'socket',
          'host',
          'dns',
          'timeout',
          'reset',
          'refused',
          'unreachable',
        ];

        return networkErrorKeywords
            .any((keyword) => errorMessage.toLowerCase().contains(keyword));
      }

      return true;
    }

    return false;
  }

  /// Determines the specific reason why a request needs to be retried
  ///
  /// Returns null if retry is not needed
  /// Returns a specific reason if retry is needed
  String? _getRetryReason(DioException err) {
    // Check for timeout error types
    if (err.type == DioExceptionType.connectionTimeout) {
      return 'Connection timeout';
    }
    if (err.type == DioExceptionType.sendTimeout) {
      return 'Send timeout';
    }
    if (err.type == DioExceptionType.receiveTimeout) {
      return 'Receive timeout';
    }

    // Check for connection errors
    if (err.type == DioExceptionType.connectionError) {
      return 'Connection error';
    }

    // Check for unknown errors related to network issues
    if (err.type == DioExceptionType.unknown && err.error != null) {
      final errorString = err.error.toString().toLowerCase();

      // Check for specific network issues
      if (errorString.contains('network') || errorString.contains('internet')) {
        return 'Network connectivity issue';
      }
      if (errorString.contains('connection') ||
          errorString.contains('socket')) {
        return 'Connection issue';
      }
      if (errorString.contains('host') || errorString.contains('dns')) {
        return 'DNS resolution issue';
      }
      if (errorString.contains('timeout')) {
        return 'Operation timeout';
      }
      if (errorString.contains('reset') || errorString.contains('refused')) {
        return 'Connection refused or reset';
      }
      if (errorString.contains('unreachable')) {
        return 'Host unreachable';
      }
      if (errorString.contains('certificate') ||
          errorString.contains('ssl') ||
          errorString.contains('handshake')) {
        return 'SSL/Certificate issue';
      }
    }

    // Check for specific HTTP status codes
    if (err.type == DioExceptionType.badResponse) {
      final statusCode = err.response?.statusCode;
      final responseData = err.response?.data;

      // Server errors (5xx)
      if (statusCode != null && statusCode >= 500 && statusCode < 600) {
        // Special handling for 503 with "no healthy upstream"
        if (statusCode == 503) {
          // Always retry 503 Service Unavailable as it's a temporary server issue
          // but provide more specific reasons when possible

          // Check for "no healthy upstream" in response data
          if (responseData is String) {
            if (responseData.contains('no healthy upstream')) {
              return 'Server unavailable: no healthy upstream (503)';
            }
            // Check for other common error messages in string response
            final lowerResponse = responseData.toLowerCase();
            if (lowerResponse.contains('unavailable') ||
                lowerResponse.contains('maintenance') ||
                lowerResponse.contains('overloaded')) {
              return 'Service unavailable: $responseData (503)';
            }
          }

          // Check for structured error messages
          if (responseData is Map<String, dynamic>) {
            final message = responseData['message'] as String?;
            if (message != null) {
              if (message.toLowerCase().contains('unavailable') ||
                  message.toLowerCase().contains('maintenance') ||
                  message.toLowerCase().contains('overloaded')) {
                return 'Service unavailable: $message (503)';
              }
            }
          }

          // Default reason for 503 when response format is unknown
          return 'Service unavailable (503)';
        }

        return 'Server error: $statusCode';
      }

      // Specific client errors that can be retried
      if (statusCode == 429) {
        return 'Rate limited (429 Too Many Requests)';
      }
      if (statusCode == 408) {
        return 'Request timeout (408)';
      }
    }

    return null;
  }
}
