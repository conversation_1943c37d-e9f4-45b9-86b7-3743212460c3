import 'package:dio/dio.dart';
import 'package:shared/shared.dart';

class IsolateApiClient {
  late final Dio _dio;
  static final DEBUG = GlobalConfig.enableLogInterceptor;

  IsolateApiClient({
    required String? baseUrl,
    required Map<String, String> header,
    required String apiAuthToken,
  }) {
    _dio = Dio(
      BaseOptions(
        baseUrl: baseUrl ?? '',
        headers: {
          'Content-Type': 'application/json',
          'x-session-token': apiAuthToken,
        }..addAll(header),
        sendTimeout: GlobalConfig.sendTimeoutDuration,
        receiveTimeout: GlobalConfig.receiveTimeoutDuration,
      ),
    );

    _dio.interceptors.add(
      LogInterceptor(
        request: DEBUG,
        requestBody: DEBUG,
        responseBody: DEBUG,
        responseHeader: DEBUG,
        error: DEBUG,
      ),
    );
  }

  Future<Response> get(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParams,
  }) async {
    try {
      return await _dio.get(
        endpoint,
        options: Options(headers: headers),
        queryParameters: queryParams,
      );
    } on DioException catch (e) {
      print('GET request error: $e');
      rethrow;
    }
  }

  Future<Response> post(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? body,
  }) async {
    try {
      return await _dio.post(
        endpoint,
        data: body,
        options: Options(headers: headers),
        cancelToken: CancelToken(),
      );
    } on DioException catch (e) {
      print('POST request error: $e');
      rethrow;
    }
  }

  Future<Response> put(
    String endpoint, {
    Map<String, String>? headers,
    Map<String, dynamic>? body,
  }) async {
    try {
      return await _dio.put(
        endpoint,
        data: body,
        options: Options(headers: headers),
      );
    } on DioException catch (e) {
      print('PUT request error: $e');
      rethrow;
    }
  }

  Future<Response> delete(
    String endpoint, {
    Map<String, String>? headers,
  }) async {
    try {
      return await _dio.delete(
        endpoint,
        options: Options(headers: headers),
      );
    } on DioException catch (e) {
      print('DELETE request error: $e');
      rethrow;
    }
  }
}
