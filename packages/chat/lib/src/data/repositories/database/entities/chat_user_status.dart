import 'package:json_annotation/json_annotation.dart';

import '../enums/chat_user_status_expire.dart';

part 'chat_user_status.g.dart';

@JsonSerializable(explicitToJson: true)
class ChatUserStatus {
  ChatUserStatus({
    this.status,
    this.content,
    this.expireAfterTime,
    this.createTime,
    this.updateTime,
    this.endTime,
  });

  String? status;
  String? content;
  ChatUserStatusExpireAfterTimeEnum? expireAfterTime;
  String? createTime;
  String? updateTime;
  String? endTime;

  factory ChatUserStatus.fromJson(Map<String, dynamic> json) =>
      _$ChatUserStatusFromJson(json);

  Map<String, dynamic> toJson() => _$ChatUserStatusToJson(this);
}
