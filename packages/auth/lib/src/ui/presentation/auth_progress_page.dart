import 'dart:io';

import 'package:auth_api/auth_api.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:passkeys/authenticator.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../auth.dart';
import '../../utils/cloud_kit_utils.dart';
import 'create_account_step_extended.dart';

class AuthProgressPage extends StatefulWidget {
  const AuthProgressPage(
    this.username,
    this.reqChallenge,
    this.request,
    this.hashCash,
    this.onPop,
    this.onPopBack,
    this.onHideDialog,
  );

  final String username;
  final String reqChallenge;
  final Object request;
  final Object hashCash;
  final VoidCallback onPop;
  final VoidCallback onPopBack;
  final VoidCallback onHideDialog;

  static const int totalLoginStep = 4;
  static const int totalLoginQRStep = 6;
  static const int totalRegisterStep = 4;

  @override
  _AuthProgressPageState createState() => _AuthProgressPageState();
}

class _AuthProgressPageState extends State<AuthProgressPage>
    with WidgetsBindingObserver
    implements ui.AuthProgressInterface {
  final List<AuthenticationStepExtended> _steps = [];
  late String _username;
  bool canPop = true;
  late AuthBloc _authBloc;
  late AppLocalizations _localizations;
  late bool _isLogin = widget.request is V3LoginRequestUserKey;
  late bool _isLoginQR = false;
  int _progress = 1;
  bool _showRetry = false;
  bool _showCancel = true;
  bool _onSuccess = false;
  bool _bottomSheetQRShowed = false;
  bool _bottomSheetShowed = false;
  bool _dialogShowed = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    _username = widget.username;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.paused) {
      _handleErrorCurrentStep('User cancelled the selector');
      context.read<AuthBloc>().add(
            OnAuthErrorEvent(errorMessage: 'User cancelled the selector'),
          );
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _authBloc = context.read<AuthBloc>();
    _localizations = AppLocalizations.of(context)!;

    return PopScope(
      canPop: (_isLogin || _isLoginQR) && canPop,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) {
          return;
        }

        if (!(_isLogin || _isLoginQR) && canPop) {
          _showConfirmCancelRegisterFlow();
          return;
        }
      },
      child: BlocBuilder<AuthBloc, AuthState>(
        buildWhen: (prev, state) => prev != state,
        builder: (context, state) {
          _handleSteps(state);
          return ui.AuthProgressPage(
            isLogin: _isLogin || _isLoginQR,
            interface: this,
          );
        },
      ),
    );
  }

  void _handleSteps(AuthState state) {
    state.maybeWhen(
      initial: () {
        _initializeSteps();
        _updateProgress();
      },
      onProcessRegisterRequest: _handleProcessingRegisterRequest,
      onProcessLoginRequest: _handleProcessingLoginRequest,
      loginQRRequest: _handleLoginQRRequest,
      loginQRGenerated: _handleLoginQRGenerated,
      loginQRCanceled: _handleLoginQRCanceled,
      usernameInvalid: _onUsernameInvalid,
      usernameRequestByOtherUser: _onUsernameRequestByOtherUser,
      usernameDeleted: _onUsernameDeleted,
      error: _handleErrorCurrentStep,
      retryStep: _onRetryStep,
      success: (_, __) {
        _handleSuccess();
      },
      orElse: () {},
    );
  }

  void _initializeSteps() {
    if (_steps.isNotEmpty) return;

    _steps.addAll([
      AuthenticationStepExtended(
        stepContent: _localizations.usingUsername(widget.username),
        stepStatus: StepStatus.success,
        boldContent: _username,
        id: AuthStepId.usingUsername,
      ),
      AuthenticationStepExtended(
        stepContent: _isLogin
            ? _localizations.authenticatingYourAccount
            : _localizations.securingYourAccountWithPhoneAuthentication,
        stepStatus: StepStatus.loading,
        id: _isLogin
            ? AuthStepId.loginAuthenticationYourAccount
            : AuthStepId.registerSecuringYourAccountWithPhoneAuthentication,
      ),
    ]);
  }

  void _handleSuccess() {
    canPop = false;
    _hideQRBottomSheet();
    _updateStepStatus(StepStatus.success);
    _addStepIfNotExist(
      AuthenticationStepExtended(
        stepContent: _localizations.finishing,
        stepStatus: StepStatus.success,
        id: AuthStepId.finnish,
      ),
    );
    _updateProgress();
    _showCancel = false;
    _onSuccess = true;
  }

  void _handleLoginQRRequest() {
    _isLogin = false;
    _isLoginQR = true;
    _updateStepStatus(StepStatus.success);
    _updateProgress();
    _addStepIfNotExist(
      AuthenticationStepExtended(
        stepContent: _localizations.generatingAnAuthenticationQrCode,
        stepStatus: StepStatus.loading,
        id: AuthStepId.loginGeneratingAnAuthenticationQRCode,
      ),
    );
  }

  void _handleLoginQRGenerated(String qrCode) {
    _updateStepStatus(StepStatus.success);
    _addStepIfNotExist(
      AuthenticationStepExtended(
        stepContent: _localizations.verifyingYourRequestFromAnotherDevice,
        stepStatus: StepStatus.loading,
        id: AuthStepId.loginVerifyingFromAnotherDevice,
      ),
    );
    _updateProgress();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted && !_bottomSheetQRShowed) return;

      _bottomSheetQRShowed = true;

      ui.BottomSheetUtil.showLoginQRBottomSheet(
        isDismissible: false,
        enableDrag: false,
        context: context,
        getQRView: () {
          return _getQRView(qrCode);
        },
        onCancel: _dismissQRBottomSheet,
        onClose: _dismissQRBottomSheet,
        onLearnMoreClicked: () {},
      );
    });
  }

  Widget _getQRView(String qrCode) {
    final cardTheme = Theme.of(this.context).cardTheme.color;
    final isDarkMode = Theme.of(this.context).brightness == Brightness.dark;
    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.white : cardTheme,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: EdgeInsets.all(10),
      child: ui.QRView(
        qrData: qrCode,
        color: Colors.black,
        image: AppAssets.iconZiichatQR,
      ),
    );
  }

  void _hideQRBottomSheet() {
    if (!_bottomSheetQRShowed) return;

    _bottomSheetQRShowed = false;

    widget.onPop.call();
  }

  void _dismissQRBottomSheet() {
    if (!_bottomSheetQRShowed) return;

    _bottomSheetQRShowed = false;
    _authBloc.add(CancelQRLoginFlowEvent());

    widget.onPop.call();
  }

  void _handleLoginQRCanceled() {
    _dismissQRBottomSheet();
    _setErrorCurrentStep();
    _showRetry = true;
  }

  void _setErrorCurrentStep() {
    if (_steps.length == 0) return;

    _steps[_steps.length - 1] =
        (_steps[_steps.length - 1]).copyWith(stepStatus: StepStatus.failed);
  }

  void _updateProgress() {
    final currentStep =
        _steps.where((step) => step.stepStatus == StepStatus.success).length;
    final totalStep = _getTotalStep();
    _progress = (currentStep * 100) ~/ totalStep;
  }

  int _getTotalStep() {
    if (_isLoginQR) return AuthProgressPage.totalLoginQRStep;

    if (!_isLogin) AuthProgressPage.totalRegisterStep;

    return AuthProgressPage.totalLoginStep;
  }

  void _updateStepStatus(StepStatus status) {
    for (var i = 0; i < _steps.length; i++) {
      _steps[i] = (_steps[i]).copyWith(stepStatus: status);
    }
  }

  void _addStepIfNotExist(AuthenticationStepExtended step) {
    if (!_steps.map((step) => step.id).contains(step.id)) _steps.add(step);
  }

  void Function(BuildContext context)? triggerShowDialog;

  @override
  String getUserName() => _username;

  @override
  void onBack() {
    if (_onSuccess) return;

    if (_isLogin || _isLoginQR) {
      widget.onPopBack.call();
      return;
    }
    if (canPop) {
      _showConfirmCancelRegisterFlow();
    }
  }

  @override
  void onCancel() {
    if (!_showRetry) return;
    if (_isLogin || _isLoginQR) {
      widget.onPopBack.call();
      return;
    }

    _showConfirmCancelRegisterFlow();
  }

  void _showConfirmCancelRegisterFlow() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      ui.DialogUtils.showConfirmationDialog(
        context,
        onFirstAction: _onNotCancelRegister,
        onSecondAction: _onCancelRegister,
      );
    });
  }

  void _onCancelRegister(BuildContext dialogContext) async {
    widget.onHideDialog.call();
    _authBloc.add(AuthEventConfirmCancelRegisterEvent());
    widget.onPopBack.call();
  }

  void _onNotCancelRegister(BuildContext dialogContext) {
    widget.onHideDialog.call();
  }

  @override
  void onRetry() async {
    if (widget.request is V3RegisterRequestUserKey) {
      if (await _needShowSignInAppleIdRequest()) return;
    }

    _handleRetryAuthentication();
  }

  @override
  List<AuthenticationStep> getSteps() => _steps;

  @override
  int getProgress() => _progress > 100 ? 100 : _progress;

  @override
  bool showCancel() => _showCancel;

  @override
  bool showRetry() => _showRetry;

  void _handleProcessingRegisterRequest() {
    canPop = false;
    _updateStepStatus(StepStatus.success);
    _addStepIfNotExist(
      AuthenticationStepExtended(
        stepContent: _localizations.processingYourRegistrationRequest,
        stepStatus: StepStatus.loading,
        id: AuthStepId.registerProcessingRegisterRequest,
      ),
    );
    _showCancel = false;
    _updateProgress();
  }

  void _handleProcessingLoginRequest() {
    _updateStepStatus(StepStatus.success);
    _addStepIfNotExist(
      AuthenticationStepExtended(
        stepContent: _localizations.processingYourLoginRequest,
        stepStatus: StepStatus.loading,
        id: AuthStepId.loginProcessingLoginRequest,
      ),
    );
    _showCancel = false;
    _updateProgress();
  }

  void _handleErrorCurrentStep(String error) {
    canPop = true;
    widget.onPop.call();
    _setErrorCurrentStep();
    _showRetry = true;
    _updateProgress();
  }

  void _handleRetryAuthentication() async {
    if (await canAuthenticate()) {
      final failureStep =
          _steps.where((step) => step.stepStatus == ui.StepStatus.failed).first;

      _authBloc.add(RetryAuthEvent(stepId: failureStep.id));
      return;
    }

    await _showAuthenticateNoSupportDialog();
  }

  Future<void> _showAuthenticateNoSupportDialog() async {
    DialogUtils.showAuthenticationNotSupportDialog(
      context,
      onOpenSettings: () {
        PasskeyAuthenticator().goToSettings();
      },
      onCancel: () {
        widget.onHideDialog.call();
      },
      isIos: Platform.isIOS,
      hasFaceId: await hasFaceId(),
    );
  }

  void _onRetryStep(AuthStepId stepId) {
    _showRetry = false;

    _updateStepStatusById(stepId, ui.StepStatus.loading);

    _updateProgress();
  }

  void _updateStepStatusById(AuthStepId stepId, ui.StepStatus status) {
    final stepIndex = _steps.indexWhere((step) => step.id == stepId);

    if (stepIndex != -1) {
      _steps[stepIndex] = _steps[stepIndex].copyWith(stepStatus: status);
    }
  }

  void _onUsernameInvalid() {
    _setErrorCurrentStep();
    _showRetry = true;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted || _dialogShowed) return;
      _dialogShowed = true;
      ui.DialogUtils.showInvalidUsernameDialog(
        context,
        onFirstAction: (BuildContext dialogContext) {
          _hideDialog();
        },
      );
    });
  }

  void _onUsernameRequestByOtherUser() {
    _setErrorCurrentStep();
    _showRetry = true;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted || _dialogShowed) return;
      _dialogShowed = true;
      ui.DialogUtils.showTemporaryDialog(
        context,
        onFirstAction: (BuildContext dialogContext) {
          _hideDialog();
        },
      );
    });
  }

  void _onUsernameDeleted() {
    _setErrorCurrentStep();
    _showRetry = true;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted || _dialogShowed) return;
      _dialogShowed = true;
      ui.DialogUtils.showAccountWasDeletedDialog(
        context,
        onFirstAction: (BuildContext dialogContext) {
          _hideDialog();
        },
      );
    });
  }

  Future<bool> _needShowSignInAppleIdRequest() async {
    if (!Platform.isIOS) return Future.value(false);

    if (await CloudKitUtils.isIcloudLogin()) {
      return Future.value(false);
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted || _bottomSheetShowed) return;
      _bottomSheetShowed = true;
      ui.BottomSheetUtil.showSignInAppleIdRequestFLoatingBottomsheet(
        context: context,
        isDismissible: false,
        enableDrag: false,
        onClose: () {
          _bottomSheetShowed = false;
          widget.onPop.call();
        },
        onSheetClose: () {
          _bottomSheetShowed = false;
        },
        onTurnOn: () {
          widget.onPop.call();
          PasskeyAuthenticator().goToSettings();
        },
        onLearnMore: () {},
      );
    });

    return Future.value(true);
  }

  void _hideDialog() {
    if (!_dialogShowed) return;

    _dialogShowed = false;

    widget.onPop.call();
  }
}
