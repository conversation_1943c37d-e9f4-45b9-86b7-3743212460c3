import 'dart:convert';

import 'package:app_core/core.dart';
import 'package:built_collection/built_collection.dart';
import 'package:channel_api/channel_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class CreateChannelUseCase
    extends BaseFutureUseCase<CreateChannelInput, CreateChannelOutput> {
  CreateChannelUseCase(
    this._handleIncludesDataUseCase,
  );

  final HandleIncludesDataUseCase _handleIncludesDataUseCase;

  @override
  Future<CreateChannelOutput> buildUseCase(CreateChannelInput input) async {
    final requestBody = V3CreateChannelRequestBuilder()
      ..workspaceId = '0'
      ..name = input.name
      ..avatarPath = input.avatarURL;
    if (input.userIDsInvited?.isNotEmpty ?? false) {
      requestBody.userIds =
          BuiltList<String>.from(input.userIDsInvited!).toBuilder();
    }
    final response = await ChannelClient().instance.createChannel(
          body: requestBody.build(),
        );

    if (response.data?.ok ?? false) {
      final json = jsonDecode(
        standardSerializers.toJson(
          V3CreateChannelResponse.serializer,
          response.data,
        ),
      );

      var apiResponse = APIResponse.fromJson(json);

      final channel = ChannelSerializer.serializeFromJson(
        data: apiResponse.data!.channel!.toJson(),
        metadata: apiResponse.data!.channelMetadata?.toJson(),
        includes: apiResponse.includes!.toJson(),
      );
      List<Message> messages = [];
      if (apiResponse.includes != null) {
        final includesDataOutput = await _handleIncludesDataUseCase
            .execute(HandleIncludesDataInput(includes: apiResponse.includes!));
        messages = includesDataOutput.messages;
      }
      return CreateChannelOutput(
        channel: channel,
        messages: messages,
      );
    }
    return CreateChannelOutput(
      error: response.data?.error,
    );
  }
}

class CreateChannelInput extends BaseInput {
  CreateChannelInput({
    required this.name,
    this.userIDsInvited,
    this.avatarURL,
  });

  final String name;
  final String? avatarURL;
  final List<String>? userIDsInvited;
}

class CreateChannelOutput extends BaseOutput {
  CreateChannelOutput({
    this.channel,
    this.messages,
    this.error,
  });

  final Channel? channel;

  final List<Message>? messages;
  final V3Error? error;
}
