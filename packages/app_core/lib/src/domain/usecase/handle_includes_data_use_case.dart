import 'package:chat/chat.dart';
import 'package:injectable/injectable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';

import '../../serializer/includes_serializer.dart';

part 'handle_includes_data_use_case.g.dart';

@Injectable()
class HandleIncludesDataUseCase extends BaseFutureUseCase<
    HandleIncludesDataInput, HandleIncludesDataOutput> {
  HandleIncludesDataUseCase(
    this._channelRepository,
    this._messageRepository,
    this._memberRepository,
    this._friendRepository,
    this._userRepository,
    this._chatUserRepository,
  );

  final ChannelRepository _channelRepository;
  final MessageRepository _messageRepository;
  final MemberRepository _memberRepository;
  final FriendRepository _friendRepository;
  final UserRepository _userRepository;
  final ChatUserRepository _chatUserRepository;

  @override
  Future<HandleIncludesDataOutput> buildUseCase(
    HandleIncludesDataInput input,
  ) async {
    final include = IncludesSerializer(input.includes);

    // Extract data from includes (synchronous operations)
    final extractedData = _extractDataFromIncludes(include);

    // Prepare futures for database operations (parallel execution)
    final List<Future<void>> insertOperations = [];

    if (extractedData.channels.isNotEmpty) {
      insertOperations
          .add(_channelRepository.insertAll(extractedData.channels));
    }

    if (extractedData.members.isNotEmpty) {
      insertOperations.add(_memberRepository.insertAll(extractedData.members));
    }

    if (extractedData.messages.isNotEmpty) {
      insertOperations
          .add(_messageRepository.insertAll(extractedData.messages));
    }

    if (extractedData.friends.isNotEmpty) {
      insertOperations.add(_friendRepository.insertAll(extractedData.friends));
    }

    if (extractedData.users.isNotEmpty) {
      // Users and ChatUsers can be inserted in parallel
      insertOperations.add(_userRepository.insertAll(extractedData.users));
      insertOperations
          .add(_chatUserRepository.insertAll(extractedData.chatUsers));
    }

    // Execute all database operations in parallel
    await Future.wait(insertOperations);

    _log(
      extractedData.users,
      extractedData.channels,
      extractedData.members,
      extractedData.messages,
      extractedData.friends,
    );

    return HandleIncludesDataOutput(
      users: extractedData.users,
      channels: extractedData.channels,
      members: extractedData.members,
      messages: extractedData.messages,
      friends: extractedData.friends,
    );
  }

  /// Extract all data from includes in a single pass
  _ExtractedData _extractDataFromIncludes(IncludesSerializer include) {
    final List<User> users = [];
    final List<ChatUser> chatUsers = [];
    final List<Channel> channels = [];
    final List<Member> members = [];
    final List<Message> messages = [];
    final List<Friend> friends = [];

    // Single user
    if (include.hasUser) {
      users.add(include.getUser()!);
    }

    // Multiple users
    if (include.hasUsers) {
      users.addAll(include.getUsers());
      chatUsers.addAll(include.getChatUsers());
    }

    // Single channel
    if (include.hasChannel) {
      channels.add(include.getChannel()!);
    }

    // Multiple channels
    if (include.hasChannels) {
      channels.addAll(include.getChannels());
    }

    // Single member
    if (include.hasMember) {
      members.add(include.getMember()!);
    }

    // Multiple members
    if (include.hasMembers) {
      members.addAll(include.getMembers());
    }

    // Single message
    if (include.hasMessage) {
      messages.add(include.getMessage()!);
    }

    // Multiple messages
    if (include.hasMessages) {
      messages.addAll(include.getMessages());
    }

    // Single friend
    if (include.hasFriend) {
      friends.add(include.getFriend()!);
    }

    // Multiple friends
    if (include.hasFriends) {
      friends.addAll(include.getFriends());
    }

    return _ExtractedData(
      users: users,
      chatUsers: chatUsers,
      channels: channels,
      members: members,
      messages: messages,
      friends: friends,
    );
  }

  void _log(
    List<User> users,
    List<Channel> channels,
    List<Member> members,
    List<Message> messages,
    List<Friend> friends,
  ) {
    if (!GlobalConfig.enableLogHandleIncludesData) return;

    Log.d(
      name: 'HandleIncludesDataUseCase',
      'Processed:\n'
      '  Users: ${users.length}\n'
      '  Channels: ${channels.length}\n'
      '  Members: ${members.length}\n'
      '  Messages: ${messages.length}\n'
      '  Friends: ${friends.length}',
    );
  }
}

/// Helper class to hold extracted data
class _ExtractedData {
  const _ExtractedData({
    required this.users,
    required this.chatUsers,
    required this.channels,
    required this.members,
    required this.messages,
    required this.friends,
  });

  final List<User> users;
  final List<ChatUser> chatUsers;
  final List<Channel> channels;
  final List<Member> members;
  final List<Message> messages;
  final List<Friend> friends;
}

@JsonSerializable(explicitToJson: true)
class HandleIncludesDataInput extends BaseInput {
  HandleIncludesDataInput({
    required this.includes,
  });

  final ResponseIncludes includes;

  factory HandleIncludesDataInput.fromJson(Map<String, dynamic> json) =>
      _$HandleIncludesDataInputFromJson(json);

  Map<String, dynamic> toJson() => _$HandleIncludesDataInputToJson(this);
}

@JsonSerializable(explicitToJson: true)
class HandleIncludesDataOutput extends BaseOutput {
  HandleIncludesDataOutput({
    required this.users,
    required this.channels,
    required this.members,
    required this.messages,
    required this.friends,
  });

  final List<User> users;
  final List<Channel> channels;
  final List<Member> members;
  final List<Message> messages;
  final List<Friend> friends;

  factory HandleIncludesDataOutput.fromJson(Map<String, dynamic> json) =>
      _$HandleIncludesDataOutputFromJson(json);

  Map<String, dynamic> toJson() => _$HandleIncludesDataOutputToJson(this);

  get all => (users, channels, messages, members, friends);
}
