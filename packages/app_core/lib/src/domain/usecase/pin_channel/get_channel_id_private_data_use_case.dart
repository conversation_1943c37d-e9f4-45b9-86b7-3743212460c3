import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';

part 'get_channel_id_private_data_use_case.freezed.dart';

@Injectable()
class GetChannelIdPrivateDataUseCase extends BaseFutureUseCase<
    GetChannelIdPrivateDataUseCaseInput, GetChannelIdPrivateDataUseCaseOutput> {
  const GetChannelIdPrivateDataUseCase(this._repository);

  final PrivateDataRepository _repository;

  @protected
  @override
  Future<GetChannelIdPrivateDataUseCaseOutput> buildUseCase(
    GetChannelIdPrivateDataUseCaseInput input,
  ) async {
    try {
      final channels = _repository.getChannel(input.channelId);

      if (channels != null) {
        return GetChannelIdPrivateDataUseCaseOutput(data: channels);
      }
      return GetChannelIdPrivateDataUseCaseOutput(data: null);
    } on Exception catch (_) {
      return GetChannelIdPrivateDataUseCaseOutput(data: null);
    }
  }
}

@freezed
sealed class GetChannelIdPrivateDataUseCaseInput extends BaseInput
    with _$GetChannelIdPrivateDataUseCaseInput {
  const GetChannelIdPrivateDataUseCaseInput._();
  factory GetChannelIdPrivateDataUseCaseInput(String channelId) =
      _GetChannelIdPrivateDataUseCaseInput;
}

@freezed
sealed class GetChannelIdPrivateDataUseCaseOutput extends BaseOutput
    with _$GetChannelIdPrivateDataUseCaseOutput {
  const GetChannelIdPrivateDataUseCaseOutput._();
  factory GetChannelIdPrivateDataUseCaseOutput({
    @Default(null) ChannelPrivateData? data,
  }) = _GetChannelIdPrivateDataUseCaseOutput;
}
