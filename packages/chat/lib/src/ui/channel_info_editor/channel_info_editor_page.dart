import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:app_core/core.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import '../../common/di/di.dart';
import 'bloc/channel_info_editor_bloc.dart';
import 'widgets/set_channel_name_bottom_sheet.dart';

class ChannelInfoEditorPage extends StatefulWidget {
  const ChannelInfoEditorPage({
    required this.workspaceId,
    required this.channelId,
    required this.interface,
    required this.channel,
    required this.isRoleMember,
    super.key,
    this.onTapTakePhoto,
    this.onTapOpenGallery,
    required this.isLoadingNewAvatar,
    this.initIsLoadingNewAvatar,
  });

  final String channelId;
  final String workspaceId;
  final Channel channel;
  final bool isRoleMember;
  final ChannelInfoEditorInterface interface;

  final VoidCallback? onTapTakePhoto;
  final VoidCallback? onTapOpenGallery;
  final ValueNotifier<bool> isLoadingNewAvatar;
  final VoidCallback? initIsLoadingNewAvatar;

  @override
  State<ChannelInfoEditorPage> createState() => _ChannelInfoEditorPageState();
}

class _ChannelInfoEditorPageState
    extends BasePageState<ChannelInfoEditorPage, ChannelInfoEditorBloc>
    implements ui.EditChannelInfoPageInterface {
  late final ChannelInfoEditorBloc _channelInfoEditorBloc;
  StreamSubscription? _changeAvatarScubscription;

  late Channel _channel;

  String _channelName = '';

  @override
  void initState() {
    bloc.add(
      InitiateChannelInfoEditorEvent(
        workspaceId: widget.workspaceId,
        channelId: widget.channelId,
      ),
    );
    widget.initIsLoadingNewAvatar?.call();
    _channelInfoEditorBloc = getIt<ChannelInfoEditorBloc>();

    _changeAvatarScubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ChooseAvatarEvent>()
        .listen(_onUpdateLoadingNewAvatar);

    super.initState();
  }

  void _onUpdateLoadingNewAvatar(ChooseAvatarEvent event) {
    widget.isLoadingNewAvatar.value = true;
  }

  @override
  void dispose() {
    _changeAvatarScubscription?.cancel();
    _channelInfoEditorBloc.close();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocBuilder<ChannelInfoEditorBloc, ChannelInfoEditorState>(
      builder: (BuildContext context, ChannelInfoEditorState state) {
        return state.maybeWhen(
          initial: () {
            return Center(child: ui.AppCircularProgressIndicator());
          },
          loaded: (channel) {
            _channel = channel;
            _channelName = _channel.name ?? '';
            return ui.EditChannelInfoPage(
              interface: this,
            );
          },
          changeChannelName: (channelName) {
            _channelName = channelName;
            return ui.EditChannelInfoPage(
              interface: this,
            );
          },
          orElse: () {
            return ui.EditChannelInfoPage(
              interface: this,
            );
          },
        );
      },
    );
  }

  @override
  String getNameChannel() {
    return _channelName;
  }

  @override
  void onSetProfileAvatarCardPressed(BuildContext context) {
    if (widget.isLoadingNewAvatar.value) return;
    _showSetAvatarChannel1NlActionSheet(context);
  }

  @override
  void onProfileAvatarPressed(BuildContext context) {
    _showSetAvatarChannel1NlActionSheet(context);
  }

  void _showSetAvatarChannel1NlActionSheet(BuildContext context) {
    if (widget.isLoadingNewAvatar.value) return;
    ui.ActionSheetUtil.showSetAvatarChannel1NlActionSheet(
      context,
      isRoleMember: widget.isRoleMember,
      onTapOpenGallery: _onTapOpenGallery,
      onTapTakePhoto: _onTapTakeChannelAvatarPhoto,
      onTapCancel: () {
        Navigator.of(context).pop();
      },
      hasAvatar: _hasAvatar(),
      onTapViewAvatar: _onTapViewAvatar,
      onTapRemove: _onTapRemoveAvatar,
    );
  }

  @override
  void onBackPressed() {
    Navigator.of(context).pop();
  }

  @override
  Uint8List? avatarData() {
    return null;
  }

  @override
  void onDisplayNameCardPressed(BuildContext context) {
    ui.BottomSheetUtil.showDefaultBottomSheet(
      context: context,
      child: SetChannelNameBottomSheet(
        currentName: _channelName,
        onNameChanged: (newName) {
          bloc.add(
            ChangeChannelNameEvent(
              workspaceId: widget.workspaceId,
              channelId: widget.channelId,
              channelName: newName,
            ),
          );
        },
      ),
    );
  }

  @override
  String getUrlChannelAvatar() {
    return _channel.fullAvatarUrl;
  }

  Future<void> _onTapOpenGallery() async {
    final isGranted = Platform.isIOS
        ? await PermissionUtils.requestImagePermission(context)
        : true;
    if (isGranted) {
      Navigator.of(context).pop();
      widget.interface.onClickTapOpenGalleryAvatar(_channel);
    }
  }

  Future<void> _onTapTakeChannelAvatarPhoto() async {
    // Check if the user is in a meeting room
    if (getIt<MeetingHandler>().hasJoinedMeetingRoom(context: context)) return;

    final isGranted = await PermissionUtils.requestCameraPermission(context);
    if (isGranted) {
      Navigator.of(context).pop();
      widget.interface.onClickTakeChannelAvatarPhoto(_channel);
    }
  }

  bool _hasAvatar() => ui.StringUtil.stringIsNotEmpty(_channel.avatar);

  void _onTapViewAvatar() {
    if (_channel.fullAvatarUrl.isEmpty) return;
    Navigator.of(context).pop();
    widget.interface.onGoToViewImagePage(_channel.fullAvatarUrl);
  }

  void _onTapRemoveAvatar() {
    if (!_hasAvatar()) return;
    _channelInfoEditorBloc.add(
      DeleteChannelAvatarEditEvent(
        workspaceId: _channel.workspaceId,
        channelId: _channel.channelId,
      ),
    );
    Navigator.of(context).pop();
  }

  @override
  ValueNotifier<bool> isLoadingNewAvatar() {
    return widget.isLoadingNewAvatar;
  }
}
