part of '../channels_page.dart';

class MessageRequestView extends StatelessWidget {
  const MessageRequestView({
    required this.onTap,
    super.key,
  });

  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: <PERSON><PERSON><PERSON><PERSON><MessageRequestBloc, MessageRequestState>(
        buildWhen: (prev, current) =>
            current.channels.length != prev.channels.length,
        builder: (context, state) {
          final messageRequestCount = state.channels.length;
          if (messageRequestCount > 0) {
            return ui.ItemChannelMessageRequestWidget(
              countNew: messageRequestCount,
              onItemChannelMessageRequestWidgetClicked: onTap,
            );
          }

          return SizedBox.shrink();
        },
      ),
    );
  }
}
