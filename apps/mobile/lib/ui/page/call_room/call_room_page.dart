import 'package:auto_route/auto_route.dart';
import 'package:call/call.dart' as call;
import 'package:flutter/material.dart';

@RoutePage()
class CallRoomPage extends StatefulWidget {
  const CallRoomPage({required this.args, super.key});

  final call.RoomPageArgs args;

  @override
  State<CallRoomPage> createState() => _CallRoomPageState();
}

class _CallRoomPageState extends State<CallRoomPage> {
  final _key = Key("ROOM_PAGE_KEY");

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: call.RoomPage(
        key: _key,
        args: widget.args,
        onBack: onBack,
      ),
    );
  }

  void onBack() {
    context.maybePop();
  }
}
