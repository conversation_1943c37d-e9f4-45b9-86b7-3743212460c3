import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

part 'load_chat_user_use_case.freezed.dart';

@Injectable()
class LoadChatUserUseCase
    extends BaseSyncUseCase<LoadChatUserInput, LoadChatUserOutput> {
  const LoadChatUserUseCase(this._chatUserRepository);

  final ChatUserRepository _chatUserRepository;

  @protected
  @override
  LoadChatUserOutput buildUseCase(
    LoadChatUserInput input,
  ) {
    final user = _chatUserRepository.getUser(input.userId);
    return LoadChatUserOutput(user: user);
  }
}

@freezed
sealed class LoadChatUserInput extends BaseInput with _$LoadChatUserInput {
  const LoadChatUserInput._();
  factory LoadChatUserInput({required String userId}) = _LoadChatUserInput;
}

@freezed
sealed class LoadChatUserOutput extends BaseOutput with _$LoadChatUserOutput {
  const LoadChatUserOutput._();
  factory LoadChatUserOutput({
    @Default(null) ChatUser? user,
  }) = _LoadChatUserOutput;
}
