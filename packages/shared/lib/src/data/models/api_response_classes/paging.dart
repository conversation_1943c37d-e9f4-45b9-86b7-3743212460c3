import 'package:json_annotation/json_annotation.dart';

part 'paging.g.dart';

@JsonSerializable(explicitToJson: true)
class Paging {
  final bool hasNext;
  final bool hasPrev;
  final String? prevPageToken;

  Paging({required this.hasNext, required this.hasPrev, this.prevPageToken});

  factory Paging.fromJson(Map<String, dynamic> json) => _$PagingFromJson(json);

  Map<String, dynamic> toJson() => _$PagingToJson(this);
}
