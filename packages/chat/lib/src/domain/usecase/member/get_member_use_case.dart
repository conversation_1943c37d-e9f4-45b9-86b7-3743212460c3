import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

part 'get_member_use_case.freezed.dart';

@Injectable()
class GetMemberUseCase
    extends BaseFutureUseCase<GetMemberInput, GetMemberOutput> {
  const GetMemberUseCase(this._repository);
  final MemberRepository _repository;

  @override
  Future<GetMemberOutput> buildUseCase(
    GetMemberInput input,
  ) async {
    final member = _repository.get(
      workspaceId: input.workspaceId,
      channelId: input.channelId,
      userId: input.userId,
    );

    return GetMemberOutput(member: member);
  }
}

@freezed
sealed class GetMemberInput extends BaseInput with _$GetMemberInput {
  const GetMemberInput._();
  factory GetMemberInput({
    required String workspaceId,
    required String channelId,
    required String userId,
  }) = _GetMemberInput;
}

@freezed
sealed class GetMemberOutput extends BaseOutput with _$GetMemberOutput {
  const GetMemberOutput._();
  factory GetMemberOutput({
    required Member? member,
  }) = _GetMemberOutput;
}
