name: sticker
description: ZiiChat Sticker package
version: 0.1.0+1
publish_to: none
environment:
  sdk: ^3.5.0
  flutter: ^3.24.0
dependencies:
  bloc: ^9.0.0
  equatable: ^2.0.7
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.0
  get_it: ^8.0.3
  injectable: ^2.5.0
  app_core:
    path: ../app_core
  shared:
    path: ../shared
  objectbox:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: objectbox
      ref: v4.1.0
  objectbox_flutter_libs:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: flutter_libs
      ref: v4.1.0
  path_provider: ^2.1.5
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  shared_preferences: ^2.5.3
  visibility_detector: ^0.4.0+2
  localization_client:
    git:
      url: **************:ziichatlabs/ziichat-flutter-i18n.git
      ref: main
  sticker_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/sticker_api
  ziichat_lottie:
    git:
      url: **************:ziichatlabs/flutter-lottie-widget.git
      ref: v0.0.4
  ziichat_ui:
    git:
      url: **************:ziichatlabs/ziichat-flutter-ui.git
      ref: release/0.56.0
  infinite_scroll_pagination: ^4.0.0
dev_dependencies:
  build_runner: ^2.4.15
  flutter_test:
    sdk: flutter
  freezed: ^3.0.6
  injectable_generator: ^2.7.0
  mocktail: ^1.0.4
  very_good_analysis: ^7.0.0
  objectbox_generator:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: generator
      ref: v4.1.0
  json_serializable: ^6.9.1
flutter:
  uses-material-design: true
objectbox:
  output_dir: src/data/repositories/database/generated
dependency_overrides:
  analyzer: 7.3.0
