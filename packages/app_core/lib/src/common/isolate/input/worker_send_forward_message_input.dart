import 'package:json_annotation/json_annotation.dart';

import 'worker_send_message_base.dart';

part 'worker_send_forward_message_input.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class WorkerSendForwardMessageInput extends WorkerSendMessageInputBase {
  final List<String> originalMessageIds;

  WorkerSendForwardMessageInput({
    required this.originalMessageIds,
    required super.workspaceId,
    required super.channelId,
    required super.userId,
    required super.ref,
    required super.taskName,
    required super.attachmentType,
    required super.creationTime,
    super.isRefTask,
  });

  factory WorkerSendForwardMessageInput.fromJson(Map<String, dynamic> json) =>
      _$WorkerSendForwardMessageInputFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$WorkerSendForwardMessageInputToJson(this);

  Map<String, dynamic> toRequest() {
    return isDm()
        ? {
            'userId': userId,
            'originalMessageIds': originalMessageIds,
          }
        : {
            'workspaceId': workspaceId,
            'channelId': channelId,
            'originalMessageIds': originalMessageIds,
          };
  }

  @override
  WorkerSendForwardMessageInput copyWith({
    List<String>? originalMessageIds,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? ref,
    String? taskName,
    int? attachmentType,
    DateTime? creationTime,
    bool? isRefTask,
  }) {
    return WorkerSendForwardMessageInput(
      originalMessageIds: originalMessageIds ?? this.originalMessageIds,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      userId: userId ?? this.userId,
      ref: ref ?? this.ref,
      taskName: taskName ?? this.taskName,
      attachmentType: attachmentType ?? this.attachmentType,
      creationTime: creationTime ?? this.creationTime,
      isRefTask: isRefTask ?? this.isRefTask,
    );
  }
}
