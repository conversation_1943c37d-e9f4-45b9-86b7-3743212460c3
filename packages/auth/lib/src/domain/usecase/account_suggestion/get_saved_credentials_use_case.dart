import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:passkeys/authenticator.dart';
import 'package:passkeys/types.dart';
import 'package:shared/shared.dart';

@Injectable()
class GetSavedCredentialsUseCase extends BaseFutureUseCase<
    GetSavedCredentialsInput, GetSavedCredentialsOutput> {
  GetSavedCredentialsUseCase();

  @override
  Future<GetSavedCredentialsOutput> buildUseCase(
    GetSavedCredentialsInput input,
  ) async {
    try {
      final response = await _getSavedCredentials(input);

      return GetSavedCredentialsOutput(
        ok: true,
        loginRequest: response,
      );
    } on PasskeyAuthCancelledException catch (e) {
      return GetSavedCredentialsOutput(
        ok: false,
        exception: AppPasskeyAuthCancelledException(
          code: e.code,
          message: e.message,
          details: e.details,
        ),
      );
    } on NoCredentialsAvailableException {
      return GetSavedCredentialsOutput(
        ok: false,
        exception: AppNoCredentialsAvailableException(),
      );
    } on UnhandledAuthenticatorException catch (e) {
      return GetSavedCredentialsOutput(
        ok: false,
        exception: AppUnhandledAuthenticatorException(
          e.code,
          e.message,
          e.details,
        ),
      );
    } on SyncAccountNotAvailableException {
      return GetSavedCredentialsOutput(
        ok: false,
        exception: AppNoCredentialsAvailableException(),
      );
    } catch (e) {
      return GetSavedCredentialsOutput(
        ok: false,
        exception: AppUnhandledAuthenticatorException(
          'UNKNOWN',
          e.toString(),
          null,
        ),
      );
    }
  }

  Future<V3LoginWithSuggestUserKeyRequest> _getSavedCredentials(
    GetSavedCredentialsInput input,
  ) async {
    final PasskeyAuthenticator passkeyAuthenticator = PasskeyAuthenticator();

    // final loginResult = await passkeyAuthenticator.getSavedCredential(
    final loginResult = await passkeyAuthenticator.authenticate(
      AuthenticateRequestType(
        relyingPartyId: input.credentialRequestOptions.rpId ?? '',
        challenge: input.credentialRequestOptions.challenge!,
        timeout: input.credentialRequestOptions.timeout ?? 60000,
        userVerification:
            input.credentialRequestOptions.userVerification ?? 'preferred',
        allowCredentials: [],
        mediation: MediationType.Required,
        preferImmediatelyAvailableCredentials: true,
      ),
    );

    final assertionResponse = CommonAssertionResponseBuilder()
      ..clientDataJSON = loginResult.clientDataJSON
      ..authenticatorData = loginResult.authenticatorData
      ..signature = loginResult.signature
      ..userHandle = loginResult.userHandle
      ..build();

    final assertion = CommonAssertionResultBuilder()
      ..id = loginResult.id
      ..rawId = loginResult.rawId
      ..type = 'public-key'
      ..response = assertionResponse;

    final loginWithUserKeyRequest = V3LoginWithSuggestUserKeyRequestBuilder()
      ..assertion = assertion
      ..reqId = input.reqId
      ..reqVerifier = input.reqVerifier;

    return loginWithUserKeyRequest.build();
  }
}

class GetSavedCredentialsInput extends BaseInput {
  GetSavedCredentialsInput({
    required this.credentialRequestOptions,
    required this.reqId,
    required this.reqVerifier,
  });

  final CommonPublicKeyCredentialRequestOptions credentialRequestOptions;
  final String reqId;
  final String reqVerifier;
}

class GetSavedCredentialsOutput extends BaseOutput {
  GetSavedCredentialsOutput({
    required this.ok,
    this.loginRequest,
    this.exception,
  });

  final bool ok;
  final V3LoginWithSuggestUserKeyRequest? loginRequest;
  final AppAuthenticatorException? exception;
}
