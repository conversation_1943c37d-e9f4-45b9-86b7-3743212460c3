import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';

import '../../data/repositories/cached/channel_modification_cache.dart';
import '../../data/repositories/cached/chat_friend_modification_cache.dart';
import '../../data/repositories/cached/chat_user_modification_cache.dart';
import '../../data/repositories/cached/member_modification_cache.dart';
import '../../data/repositories/cached/message_modification_cache.dart';
import '../../data/repositories/database/database.dart';
import '../../data/repositories/database/generated/objectbox.g.dart';
import 'di.config.dart';

final GetIt getIt = GetIt.instance;

@injectableInit
Future<void> configureInjection() async {
  final dir = await getApplicationSupportDirectory();

  final chatStore =
      await ChatStore(getObjectBoxModel(), directory: '${dir.path}/chat_store');
  ChatDatabase(chatStore);
  getIt
    ..registerSingleton<ChatStore>(chatStore)
    ..registerSingleton<ChannelModificationCache>(ChannelModificationCache())
    ..registerSingleton<MemberModificationCache>(MemberModificationCache())
    ..registerSingleton<MessageModificationCache>(MessageModificationCache())
    ..registerSingleton<ChatUserModificationCache>(ChatUserModificationCache())
    ..registerSingleton<ChatFriendModificationCache>(
      ChatFriendModificationCache(),
    )
    ..init();
}
