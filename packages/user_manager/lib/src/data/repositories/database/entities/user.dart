import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

import '../../../../../user_manager.dart';
import 'presence_data.dart';
import 'profile.dart';
import 'user_setting.dart';

part 'user.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class User {
  User({
    required this.sessionKey,
    required this.userId,
    this.username,
    this.createTime,
    this.updateTime,
    this.userType,
    this.userConnectLink,
    this.mediaPermissionSetting,
    this.presenceData,
    this.aliasName,
    this.profile,
    this.globalNotificationStatus,
    this.partial = false,
    this.sipCredentials,
    this.sipAddress,
  });

  User.mock()
      : id = 1,
        sessionKey = 'mockSessionKey',
        userId = 'mockUserId',
        username = 'mockUsername',
        createTime = 'mockCreateTime',
        updateTime = 'mockUpdateTime',
        userType = 0,
        userConnectLink = 'mockUserConnectLink',
        mediaPermissionSetting = 0,
        presenceData = null,
        sipCredentials = 'mockSipCredentials',
        sipAddress = 'mockSipAddress',
        aliasName = 'mockAliasName';

  @Id(assignable: true)
  @JsonKey(defaultValue: 0)
  int id = 0;

  @Index()
  @Property(uid: 2001)
  String sessionKey;

  @Property(uid: 2002)
  String userId;

  @Property(uid: 2003)
  String? username;

  @Property(uid: 2006)
  String? createTime;

  @Property(uid: 2007)
  String? updateTime;

  @Property(uid: 2008)
  int? userType;

  @Property(uid: 2010)
  String? userConnectLink;

  @Property(uid: 2011)
  int? mediaPermissionSetting;

  @Property(uid: 2012)
  String? get dbPresenceData {
    if (presenceData == null) {
      return null;
    }
    return jsonEncode(presenceData!.toJson());
  }

  set dbPresenceData(String? value) {
    if (value != null) {
      presenceData = PresenceData.fromJson(jsonDecode(value));
    }
  }

  @Property(uid: 2013)
  String? aliasName;

  @Transient()
  Profile? profile;

  @Transient()
  PresenceData? presenceData;

  @Property(uid: 2014)
  @JsonKey(includeFromJson: false, includeToJson: false)
  String? get dbProfile {
    if (profile == null) {
      return null;
    }
    return jsonEncode(profile!.toJson());
  }

  set dbProfile(String? value) {
    if (value != null) {
      profile = Profile.fromJson(jsonDecode(value));
    }
  }

  @Transient()
  FriendData? friendData;

  @Property(uid: 2015)
  @JsonKey(includeFromJson: false, includeToJson: false)
  String? get dbFriendData {
    if (friendData == null) {
      return null;
    }
    return jsonEncode(friendData!.toJson());
  }

  set dbFriendData(String? value) {
    if (value != null) {
      friendData = FriendData.fromJson(jsonDecode(value));
    }
  }

  @Transient()
  UserStatus? statusData;

  @Property(uid: 2016)
  @JsonKey(includeFromJson: false, includeToJson: false)
  String? get dbStatusData {
    if (statusData == null) {
      return null;
    }
    return jsonEncode(statusData!.toJson());
  }

  set dbStatusData(String? value) {
    if (value != null) {
      statusData = UserStatus.fromJson(jsonDecode(value));
    }
  }

  @Property(uid: 2017)
  bool? blocked;

  @Property(uid: 2018)
  bool? globalNotificationStatus;

  @Property(uid: 2066)
  bool? partial;

  @Transient()
  UserSetting? setting;

  @Property(uid: 2019)
  @JsonKey(includeFromJson: false, includeToJson: false)
  String? get dbSetting {
    if (setting == null) {
      return null;
    }
    return jsonEncode(setting!.toJson());
  }

  @Property(uid: 2067)
  String? sipCredentials;

  @Property(uid: 2068)
  String? sipAddress;

  set dbSetting(String? value) {
    if (value != null) {
      setting = UserSetting.fromJson(jsonDecode(value));
    }
  }

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  Map<String, dynamic> toJson() => _$UserToJson(this);
}
