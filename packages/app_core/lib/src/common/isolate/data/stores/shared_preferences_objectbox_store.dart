import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:synchronized/synchronized.dart';

import '../../../../data/repositories/database/database.dart';
import '../../../../data/repositories/database/entities/task_entity.dart';
import '../../../../data/repositories/database/entities/task_result_entity.dart';
import '../../../../data/repositories/database/entities/worker_metadata_entity.dart';
import '../../../../data/repositories/database/generated/objectbox.g.dart';
import '../../../di/di.dart';
import '../../resilient_isolate.dart';

/// Store class for managing tasks and results using ObjectBox
class SharedPreferencesObjectBoxStore {
  static const bool _enableLog = false;
  static const bool enableTaskBoxDebugLog = true;

  /// Config để bật/tắt debug stream cho TaskBox
  static const bool enableTaskBoxStreamDebug = true;

  /// Stream subscription để lắng nghe thay đổi của TaskBox
  static StreamSubscription<Query<TaskEntity>>? _taskBoxStreamSubscription;

  static void _log(String method, String message, {bool isError = false}) {
    if (kDebugMode && (enableTaskBoxDebugLog || _enableLog)) {
      final logPrefix = isError ? '[ERROR]' : '[INFO]';
      final timestamp = DateTime.now().toIso8601String();
      print(
        '$logPrefix [$timestamp] SharedPreferencesObjectBoxStore.$method: $message',
      );
    }
  }

  static Store? _store;
  static Box<TaskEntity>? _taskBox;
  static Box<TaskResultEntity>? _resultBox;
  static Box<WorkerMetadataEntity>? _metadataBox;
  static final Lock _initLock = Lock();

  /// Đảm bảo ObjectBox được khởi tạo trước khi sử dụng
  static Future<void> ensureInitialized() async {
    return _initLock.synchronized(() async {
      if (_store != null) return;

      try {
        _log('ensureInitialized', 'Getting TasksStore from GetIt');

        // Lấy TasksStore instance từ GetIt thay vì tạo mới
        try {
          _store = getIt.get<TasksStore>();
          _log('ensureInitialized', 'Successfully got TasksStore from GetIt');
        } catch (e) {
          _log(
            'ensureInitialized',
            'TasksStore not found in GetIt, attaching to existing store: $e',
          );

          // Fallback: attach to existing TasksStore (đã được mở trong main isolate)
          final docsDir = await getApplicationSupportDirectory();
          final storeDir = Directory('${docsDir.path}/task_store');

          if (!storeDir.existsSync()) {
            storeDir.createSync(recursive: true);
          }

          _log(
            'ensureInitialized',
            'Attaching to existing TasksStore with directory: ${storeDir.path}',
          );
          _store = Store.attach(getObjectBoxModel(), storeDir.path);
        }

        _taskBox = Box<TaskEntity>(_store!);
        _resultBox = Box<TaskResultEntity>(_store!);
        _metadataBox = Box<WorkerMetadataEntity>(_store!);

        _log('ensureInitialized', 'TasksStore initialized successfully');

        // Migrate dữ liệu từ SharedPreferences
        await _migrateFromSharedPreferences();

        // Khởi tạo stream để lắng nghe thay đổi của TaskBox
        _initTaskBoxStream();
      } catch (e) {
        // Cleanup on error
        if (_store != null && !getIt.isRegistered<TasksStore>()) {
          _store?.close();
        }
        _store = null;
        _taskBox = null;
        _resultBox = null;
        _metadataBox = null;

        rethrow;
      }
    });
  }

  static void _initTaskBoxStream() {
    if (!enableTaskBoxStreamDebug || _taskBox == null) return;

    // Hủy subscription cũ nếu có
    _disposeTaskBoxStream();

    try {
      _taskBoxStreamSubscription =
          _taskBox!.query().watch(triggerImmediately: true).listen((
        Query<TaskEntity> query,
      ) {
        final tasks = query.find();
        _log(
          '_taskBoxStream',
          'TaskBox changed: ${tasks.length} tasks | ' +
              'IDs: ${tasks.map(
                    (t) =>
                        '\n${t.taskId} - ${TaskStatus.values[t.statusValue]}',
                  ).join(', ')}',
        );
      });
    } catch (e) {
      _log(
        '_initTaskBoxStream',
        'Error initializing TaskBox stream: $e',
        isError: true,
      );
    }
  }

  /// Hủy stream lắng nghe thay đổi của TaskBox
  static void _disposeTaskBoxStream() {
    _taskBoxStreamSubscription?.cancel();
    _taskBoxStreamSubscription = null;
    _log('_disposeTaskBoxStream', 'TaskBox stream disposed');
  }

  /// Giải phóng tài nguyên khi không còn sử dụng
  static Future<void> dispose() async {
    return _initLock.synchronized(() async {
      try {
        _log('dispose', 'Disposing TasksStore resources');

        // Hủy stream lắng nghe thay đổi của TaskBox
        _disposeTaskBoxStream();

        // Chỉ close store nếu nó không được quản lý bởi GetIt
        if (_store != null && !getIt.isRegistered<TasksStore>()) {
          _store?.close();
        }

        _store = null;
        _taskBox = null;
        _resultBox = null;
        _metadataBox = null;
        _log('dispose', 'TasksStore resources disposed successfully');
      } catch (e) {
        _log(
          'dispose',
          'Error disposing TasksStore resources: $e',
          isError: true,
        );
      }
    });
  }

  /// Migrate dữ liệu từ SharedPreferences
  static Future<void> _migrateFromSharedPreferences() async {
    final prefs = await SharedPreferences.getInstance();

    // Kiểm tra xem đã migrate chưa
    if (prefs.getBool('objectbox_migrated') == true) return;

    // Lấy tất cả các task key từ SharedPreferences
    final taskKeys = prefs.getKeys().where((key) => key.startsWith('ri_task_'));
    _log(
      '_migrateFromSharedPreferences',
      'Found ${taskKeys.length} task keys to migrate',
    );

    // Migrate task data
    for (final key in taskKeys) {
      try {
        final taskJson = prefs.getString(key);
        if (taskJson != null) {
          final taskMap = jsonDecode(taskJson);
          final task = TaskModel.fromJson(taskMap);
          await saveTask(task);
          _log('_migrateFromSharedPreferences', 'Migrated task: ${task.id}');
        }
      } catch (e) {
        _log(
          '_migrateFromSharedPreferences',
          'Error migrating task $key: $e',
          isError: true,
        );
      }
    }

    // Lấy tất cả các result key từ SharedPreferences
    final resultKeys = prefs.getKeys().where(
          (key) => key.startsWith('ri_result_'),
        );
    _log(
      '_migrateFromSharedPreferences',
      'Found ${resultKeys.length} result keys to migrate',
    );

    // Migrate results data
    for (final key in resultKeys) {
      try {
        final resultJson = prefs.getString(key);
        if (resultJson != null) {
          final resultId = key.substring('ri_result_'.length);
          await saveTaskResult(resultId, resultJson);
          _log('_migrateFromSharedPreferences', 'Migrated result: $resultId');
        }
      } catch (e) {
        _log(
          '_migrateFromSharedPreferences',
          'Error migrating result $key: $e',
          isError: true,
        );
      }
    }

    // Đánh dấu đã migrate xong
    await prefs.setBool('objectbox_migrated', true);
    _log('_migrateFromSharedPreferences', 'Migration completed successfully');
  }

  /// Lưu task vào ObjectBox
  static Future<bool> saveTask(TaskModel task) async {
    await ensureInitialized();
    try {
      // Kiểm tra xem task đã tồn tại chưa
      final query = _taskBox!.query(TaskEntity_.taskId.equals(task.id)).build();
      final existingTasks = query.find();
      query.close();

      final taskEntity = TaskEntity.fromTaskModel(task);

      if (existingTasks.isNotEmpty) {
        // Update id
        taskEntity.id = existingTasks.first.id;
      }

      _taskBox!.put(taskEntity);
      return true;
    } catch (e) {
      _log('saveTask', 'Error saving task: $e', isError: true);
      return false;
    }
  }

  /// Kiểm tra xem task đã tồn tại chưa
  static Future<bool> taskExists(String taskId) async {
    await ensureInitialized();
    try {
      final query = _taskBox!.query(TaskEntity_.taskId.equals(taskId)).build();
      final count = query.count();
      query.close();
      return count > 0;
    } catch (e) {
      _log('taskExists', 'Error checking if task exists: $e', isError: true);
      return false;
    }
  }

  /// Load task từ ObjectBox
  static Future<TaskModel?> loadTask(String taskId) async {
    await ensureInitialized();
    try {
      final query = _taskBox!.query(TaskEntity_.taskId.equals(taskId)).build();
      final tasks = query.find();
      query.close();

      if (tasks.isEmpty) {
        return null;
      }

      return tasks.first.toTaskModel();
    } catch (e) {
      _log('loadTask', 'Error loading task: $e', isError: true);
      return null;
    }
  }

  /// Load tất cả task từ ObjectBox
  static Future<List<TaskModel>> loadAllTasks() async {
    await ensureInitialized();
    try {
      final tasks = _taskBox!.getAll();
      return tasks.map((entity) => entity.toTaskModel()).toList();
    } catch (e) {
      _log('loadAllTasks', 'Error loading all tasks: $e', isError: true);
      return [];
    }
  }

  /// Cập nhật task trong ObjectBox
  static Future<bool> updateTask(TaskModel task) => saveTask(task);

  /// Cập nhật trạng thái task trong ObjectBox
  static Future<bool> updateTaskStatus(String taskId, TaskStatus status) async {
    final task = await loadTask(taskId);
    if (task == null) {
      _log('updateTaskStatus', 'Task not found: $taskId');
      return false;
    }
    _log(
      'updateTaskStatus',
      'Updating task $taskId status from ${task.status} to $status',
    );
    return saveTask(task.copyWith(status: status));
  }

  /// Kiểm tra xem task đã unrecoverable chưa
  static Future<bool> isUnrecoverable(String taskId) async {
    final task = await loadTask(taskId);
    if (task == null) {
      _log('isUnrecoverable', 'Task not found: $taskId');
      return false;
    }

    final isUnrecoverable = task.status == TaskStatus.unrecoverable;
    _log(
      'isUnrecoverable',
      'Task $taskId is ${isUnrecoverable ? "unrecoverable" : "recoverable"}',
    );

    return isUnrecoverable;
  }

  /// Kiểm tra xem task đã gửi chưa
  static Future<bool> isSent(String taskId) async {
    return await loadTaskResult(taskId) != null;
  }

  /// Xóa task khỏi ObjectBox
  static Future<bool> deleteTask(String taskId) async {
    await ensureInitialized();
    try {
      final query = _taskBox!.query(TaskEntity_.taskId.equals(taskId)).build();
      final tasks = query.find();
      query.close();

      if (tasks.isEmpty) {
        return true;
      }

      _taskBox!.remove(tasks.first.id);
      _log('deleteTask', 'Successfully delete task: $taskId');
      return true;
    } catch (e) {
      _log('deleteTask', 'Error deleting task: $e', isError: true);
      return false;
    }
  }

  /// Xóa tất cả task khỏi ObjectBox
  static Future<bool> clearAllTasks() async {
    await ensureInitialized();
    try {
      _taskBox!.removeAll();
      return true;
    } catch (e) {
      _log('clearAllTasks', 'Error clearing all tasks: $e', isError: true);
      return false;
    }
  }

  /// Lưu kết quả task vào ObjectBox
  static Future<bool> saveTaskResult(String resultId, String resultJson) async {
    await ensureInitialized();
    try {
      // Kiểm tra xem kết quả đã tồn tại chưa
      final query = _resultBox!
          .query(
            TaskResultEntity_.resultId.equals(resultId),
          )
          .build();
      final existingResults = query.find();
      query.close();

      final resultEntity = TaskResultEntity(
        resultId: resultId,
        resultJson: resultJson,
      );

      if (existingResults.isNotEmpty) {
        // Update id
        resultEntity.id = existingResults.first.id;
      }

      _resultBox!.put(resultEntity);
      return true;
    } catch (e) {
      _log('saveTaskResult', 'Error saving task result: $e', isError: true);
      return false;
    }
  }

  /// Load kết quả task từ ObjectBox
  static Future<String?> loadTaskResult(String resultId) async {
    await ensureInitialized();
    try {
      final query = _resultBox!
          .query(
            TaskResultEntity_.resultId.equals(resultId),
          )
          .build();
      final results = query.find();
      query.close();

      if (results.isEmpty) {
        return null;
      }

      return results.first.resultJson;
    } catch (e) {
      _log('loadTaskResult', 'Error loading task result: $e', isError: true);
      return null;
    }
  }

  /// Xóa kết quả task khỏi ObjectBox
  static Future<bool> deleteTaskResult(String resultId) async {
    await ensureInitialized();
    try {
      final query = _resultBox!
          .query(
            TaskResultEntity_.resultId.equals(resultId),
          )
          .build();
      final results = query.find();
      query.close();

      if (results.isEmpty) {
        return true; // Kết quả không tồn tại, coi như đã xóa thành công
      }

      _resultBox!.remove(results.first.id);
      return true;
    } catch (e) {
      _log('deleteTaskResult', 'Error deleting task result: $e', isError: true);
      return false;
    }
  }

  /// Xóa tất cả kết quả message khỏi ObjectBox
  static Future<bool> clearAllMessageResults() async {
    await ensureInitialized();
    try {
      _resultBox!.removeAll();
      return true;
    } catch (e) {
      _log(
        'clearAllMessageResults',
        'Error clearing message results: $e',
        isError: true,
      );
      return false;
    }
  }

  /// Xóa tất cả kết quả message (alias của clearAllMessageResults)
  static Future<bool> deleteAllMessageResults() => clearAllMessageResults();

  /// Lấy ExecutorType từ WorkerMetadataEntity
  /// Nếu không có bản ghi nào, sẽ tạo bản ghi mới với ExecutorType.worker mặc định
  static Future<ExecutorType> getExecutorType() async {
    await ensureInitialized();
    try {
      // Lấy bản ghi đầu tiên hoặc tạo mới nếu chưa có
      final metadata = await _getOrCreateWorkerMetadata();
      return metadata.executorType;
    } catch (e) {
      _log('getExecutorType', 'Error getting executor type: $e', isError: true);
      return ExecutorType.worker; // Trả về giá trị mặc định
    }
  }

  static Future<bool> setExecutorType(ExecutorType executorType) async {
    await ensureInitialized();
    try {
      final metadata = await _getOrCreateWorkerMetadata();

      metadata.executorType = executorType;

      _metadataBox!.put(metadata);
      _log(
        'setExecutorType',
        'Successfully updated executor type to: $executorType',
      );
      return true;
    } catch (e) {
      _log('setExecutorType', 'Error setting executor type: $e', isError: true);
      return false;
    }
  }

  static Future<WorkerMetadataEntity> _getOrCreateWorkerMetadata() async {
    await ensureInitialized();

    final allMetadata = _metadataBox!.getAll();

    if (allMetadata.isNotEmpty) {
      return allMetadata.first;
    } else {
      final newMetadata = WorkerMetadataEntity(
        apiHost: '',
        fileStoreHost: '',
        activeSessionKey: '',
        headerJson: '{}',
        executorType: ExecutorType.worker,
      );
      newMetadata.id = 1;

      _metadataBox!.put(newMetadata);
      _log(
        '_getOrCreateWorkerMetadata',
        'Created new WorkerMetadataEntity with default values',
      );

      return newMetadata;
    }
  }
}
