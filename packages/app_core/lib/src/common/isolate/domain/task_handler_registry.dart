import 'package:filestore_sdk/core/implementations/filestore_client_v2.dart';

import '../../../../core.dart';
import '../../../data/source/api/client/isolate_api_client.dart';
import '../resilient_isolate.dart';

class TaskHandlerRegistry {
  static FilestoreClientV2? fileStoreClient;
  static IsolateApiClient? apiClient;
  static RetryManager? retryManager;
  static ExecutorType? executorType;

  static void setSharedClients(
    FilestoreClientV2 fs,
    IsolateApiClient api,
    RetryManager rm,
    ExecutorType e,
  ) {
    fileStoreClient = fs;
    apiClient = api;
    retryManager = rm;
    executorType = e;
  }
}
