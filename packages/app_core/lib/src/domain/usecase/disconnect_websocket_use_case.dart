import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../websockets/websocket_manager.dart';

@Injectable()
class DisconnectWebsocketUseCase extends BaseFutureUseCase<
    DisconnectWebsocketInput, DisconnectWebsocketOutput> {
  DisconnectWebsocketUseCase(this._webSocketManager);

  final WebSocketManager _webSocketManager;

  @override
  Future<DisconnectWebsocketOutput> buildUseCase(
    DisconnectWebsocketInput input,
  ) async {
    _webSocketManager.forceClose();
    return DisconnectWebsocketOutput();
  }
}

class DisconnectWebsocketInput extends BaseInput {
  DisconnectWebsocketInput();
}

class DisconnectWebsocketOutput extends BaseOutput {
  DisconnectWebsocketOutput();
}
