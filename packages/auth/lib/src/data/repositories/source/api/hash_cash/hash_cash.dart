import 'dart:convert';

import 'package:crypto/crypto.dart';

class HashCash {
  HashCash(this.hash, this.difficulty) : pattern = ''.padLeft(difficulty, '0');
  final String pattern;
  final String hash;
  final int difficulty;
  int? nonce;

  bool _checkNonce(int nonce) {
    final bytes = utf8.encode('$hash$nonce');
    final digest = sha256.convert(bytes);
    final sha256hex = digest.toString();
    return sha256hex.substring(0, difficulty) == pattern;
  }

  void solveProofOfWork() {
    var nonceTemp = 0;
    while (!_checkNonce(nonceTemp)) {
      nonceTemp++;
    }
    nonce = nonceTemp;
  }

  String getHash() {
    return hash;
  }

  int getDifficulty() {
    return difficulty;
  }

  int? getNonce() {
    return nonce;
  }

  Map<String, String> getHashCashMetadata() {
    return {
      'x-pow-d': getDifficulty().toString(),
      'x-pow-ch': getHash(),
      'x-pow-nonce': getNonce()?.toString() ?? '',
    };
  }
}
