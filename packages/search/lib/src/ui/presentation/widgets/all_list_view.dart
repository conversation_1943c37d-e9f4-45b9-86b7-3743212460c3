import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:localization_client/localization_client.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../data/repositories/database/entities/search.dart';

class AllListView extends StatelessWidget {
  const AllListView({
    required this.listUsers,
    required this.listChannels,
    required this.onTapSearchItem,
    required this.listUserPrivateData,
    super.key,
  });

  final List<Search> listUsers;
  final List<Search> listChannels;
  final void Function(Search item) onTapSearchItem;
  final List<UserPrivateData> listUserPrivateData;

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      UserPrivateData userPrivateData =
          this.listUserPrivateData.firstWhere((user) => user.userId == userId);
      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;
    final paddingBottom = MediaQuery.of(context).padding.bottom;
    return CustomScrollView(
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      slivers: [
        if (listUsers.isNotEmpty)
          SliverToBoxAdapter(
            child: Container(
              color: ui.AppColors.gray40.withValues(alpha: 0.1),
              padding: EdgeInsets.symmetric(horizontal: 15, vertical: 4),
              child: ui.SearchPartialTitleWidget(
                text: appLocalizations.users.toUpperCase(),
              ),
            ),
          ),
        if (listUsers.isNotEmpty)
          SliverPadding(
            padding: EdgeInsets.only(
              bottom: listChannels.isNotEmpty ? 0 : paddingBottom,
            ),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                childCount: listUsers.length,
                (BuildContext context, int index) {
                  final model = ui.UserSearchModel(
                    userId: listUsers[index].userId!,
                    userName: listUsers[index].username,
                    name: getAliasName(listUsers[index].userId!) ??
                        listUsers[index].searchResultName,
                    avatarUrl: UrlUtils.parseAvatar(listUsers[index].avatar),
                  );
                  return ui.SearchResultWidget(
                    model: model,
                    onTap: () {
                      onTapSearchItem(listUsers[index]);
                    },
                    isShowStatus: false,
                  );
                },
              ),
            ),
          ),
        if (listChannels.isNotEmpty)
          SliverToBoxAdapter(
            child: Container(
              color: ui.AppColors.gray40.withValues(alpha: 0.1),
              padding: EdgeInsets.symmetric(horizontal: 15, vertical: 4),
              child: ui.SearchPartialTitleWidget(
                text: appLocalizations.channels.toUpperCase(),
              ),
            ),
          ),
        if (listChannels.isNotEmpty)
          SliverPadding(
            padding: EdgeInsets.only(bottom: paddingBottom),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                childCount: listChannels.length,
                (BuildContext context, int index) {
                  final model = ui.ChannelSearchModel(
                    channelId: listChannels[index].channelId!,
                    workspaceId: listChannels[index].workspaceId!,
                    name: listChannels[index].searchResultName,
                    avatarUrl: UrlUtils.parseAvatar(listChannels[index].avatar),
                  );
                  return ui.SearchResultWidget(
                    model: model,
                    onTap: () {
                      onTapSearchItem(listChannels[index]);
                    },
                  );
                },
              ),
            ),
          ),
      ],
    );
  }
}
