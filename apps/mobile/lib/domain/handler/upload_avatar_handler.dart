import 'package:chat/chat.dart';
import 'package:filestore_sdk/core/implementations/upload_file.dart';
import 'package:filestore_sdk/core/utils/constants/enums.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:upload_manager/upload_manager.dart';
import 'package:user_manager/user_manager.dart';

@LazySingleton()
class UploadAvatarHandler {
  UploadAvatarHandler(
    this._updateChannelAvatarUseCase,
    this._uploadImageHandler,
    this._updateUserAvatarUseCase,
    this._userRepository,
  );

  final UpdateChannelAvatarUseCase _updateChannelAvatarUseCase;
  final UploadImageHandler _uploadImageHandler;
  final UpdateUserAvatarUseCase _updateUserAvatarUseCase;
  final UserRepository _userRepository;
  Map<String, ValueNotifier<bool>> _mapHasUpdateAvatar = {};
  ValueNotifier<bool> _isLoadingNewAvatar = ValueNotifier(false);

  Future<void> onUpdateAvatar(
    UploadFile avatar, {
    String? workspaceId,
    String? channelId,
    String? userId,
    required Function(bool result) onSuccessUpdate,
  }) async {
    _mapHasUpdateAvatar[userId == null ? channelId! : userId] =
        _isLoadingNewAvatar;
    try {
      await _handleUpload(
        avatar,
        (String fileUrl) async {
          final result = (userId == null)
              ? await _updateAvatarChannelPath(
                  workspaceId!,
                  channelId!,
                  fileUrl,
                )
              : await _updateAvatarUserPath(fileUrl, userId);

          onSuccessUpdate(result);
        },
      );
    } catch (error) {
      _mapHasUpdateAvatar.remove(userId == null ? channelId! : userId);
    }
  }

  Future<bool> _handleUpload(
    UploadFile file,
    Future<void> Function(String fileUrl) onSuccessHandler,
  ) async {
    try {
      final result = await _uploadImageHandler.handleUpload(
        file: file,
        onSuccess: (UpFile file, String fileUrl) async {
          return await onSuccessHandler(fileUrl);
        },
        onError: _error,
      );

      return result.success;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _updateAvatarChannelPath(
    String workspaceId,
    String channelId,
    String avatarPath,
  ) async {
    final UpdateChannelAvatarOutput output =
        await _updateChannelAvatarUseCase.execute(
      UpdateChannelAvatarInput(
        workspaceId: workspaceId,
        channelId: channelId,
        avatarPath: avatarPath,
      ),
    );

    return output.success;
  }

  Future<bool> _updateAvatarUserPath(String avatarPath, String userId) async {
    final UpdateUserAvatarOutput output =
        await _updateUserAvatarUseCase.execute(
      UpdateUserAvatarInput(
        avatarPath: avatarPath,
      ),
    );

    if (output.success) {
      final avatarPath = output.avatarPath!;
      _userRepository.updateAvatar(
        userId,
        avatarPath,
      );
      return true;
    } else {
      return false;
    }
  }

  bool _error(UpFile file, ErrorCode errorCode, String message) {
    switch (errorCode) {
      case ErrorCode.noInternet:
        break;
      case ErrorCode.uploadError:
        break;
      default:
    }
    return false;
  }

  bool hasUpdateAvatar({
    String? channelId,
    String? userId,
  }) {
    return _mapHasUpdateAvatar[userId == null ? channelId! : userId] != null;
  }

  ValueNotifier<bool> get isLoadingNewAvatar => _isLoadingNewAvatar;

  void removeHasUpdateAvatar({
    String? channelId,
    String? userId,
  }) {
    _mapHasUpdateAvatar.remove(userId == null ? channelId! : userId);
  }
}
