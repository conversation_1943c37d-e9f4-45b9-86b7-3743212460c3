// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

class BetaFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAK-iX0hlrP5-Pm4wu6AWUiGiUasbMRSTs',
    appId: '1:540761922266:android:143c63ec6c18f560e663e3',
    messagingSenderId: '540761922266',
    projectId: 'zii-sandbox',
    storageBucket: 'zii-sandbox.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCoQB-SdIWosrcjxZr5fq4mlj90LdzX0Pk',
    appId: '1:540761922266:ios:1ac26c4b2a40c2b3e663e3',
    messagingSenderId: '540761922266',
    projectId: 'zii-sandbox',
    storageBucket: 'zii-sandbox.firebasestorage.app',
    androidClientId:
        '540761922266-4q841264q6gj2cml4jhe0pktvp3u517b.apps.googleusercontent.com',
    iosBundleId: 'com.ziichat.ios.media.flutter',
  );
}
