import 'package:json_annotation/json_annotation.dart';

part 'mention_identification.g.dart';

@JsonSerializable(explicitToJson: true)
class MentionIdentification {
  @Json<PERSON>ey(name: 'mention')
  String? mention;

  @J<PERSON><PERSON>ey(name: 'userId')
  String? userId;

  MentionIdentification({
    this.mention,
    this.userId,
  });

  factory MentionIdentification.fromJson(Map<String, dynamic> json) =>
      _$MentionIdentificationFromJson(json);

  Map<String, dynamic> toJson() => _$MentionIdentificationToJson(this);
}
