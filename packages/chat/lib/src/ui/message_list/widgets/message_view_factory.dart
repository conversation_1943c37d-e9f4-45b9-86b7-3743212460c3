import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../../chat.dart';
import '../../../data/repositories/database/entities/translated_result.dart';
import '../../../data/repositories/database/enums/translated_status_enum.dart';
import '../../../data/repositories/extensions/message_extension.dart';
import '../message_translate_state.dart';
import 'receiver/file_widget_impl.dart';
import 'receiver/images_widget_impl.dart';
import 'receiver/invitation_widget_impl.dart';
import 'receiver/link_widget_impl.dart';
import 'receiver/location_widget_impl.dart';
import 'receiver/sticker_widget_impl.dart';
import 'receiver/text_widget_impl.dart';
import 'receiver/videos_widget_impl.dart';
import 'receiver/zii_short_widget_impl.dart';
import 'receiver/ziivoice_widget_impl.dart';
import 'sender/file_owner_widget_impl.dart';
import 'sender/images_owner_widget_impl.dart';
import 'sender/invitation_owner_widget_impl.dart';
import 'sender/link_owner_widget_impl.dart';
import 'sender/location_owner_widget_impl.dart';
import 'sender/sticker_owner_widget_impl.dart';
import 'sender/text_owner_widget_impl.dart';
import 'sender/videos_owner_widget_impl.dart';
import 'sender/zii_short_owner_widget_impl.dart';
import 'sender/ziivoice_owner_widget_impl.dart';

class MessageViewFactory {
  static Widget buildMessageView({
    required Message message,
    TranslatedResult? translatedResult,
    required Member? member,
    required ChatUser? user,
    required String? recipientId,
    required ui.QuoteMessage? quoteMessage,
    required bool isOpenCheckbox,
    required bool isCheckedMessage,
    required bool is24HourFormat,
    void Function(ui.MessageItem messageItem)? onCheckBoxButtonTap,
    void Function(ui.MessageItem messageItem)? onQuoteMessageClicked,
    void Function(ui.MessageItem messageItem)? onTranslateMessageClicked,
    void Function(ui.MessageItem messageItem)? onButtonTranslateClick,
    ui.MessageErrorReason? messageErrorReason,
    bool isBotChannel = false,
    required bool isShowCreateTime,
    required bool isShowAvatar,
    required bool isHighlighted,
    required bool shouldAnimate,
    required bool isShowName,
    bool isHiddenPin = false,
    bool isLastMessage = false,
  }) {
    final AppLocalizations appLocalizations = GetIt.I.get<AppLocalizations>();
    if (message.content?.contains('@all') ?? false) {
      message = message.copyWith(
        content: message.content?.replaceAll(
          '@all',
          '@${appLocalizations.all}',
        ),
      );
    }

    switch (message.messageViewType) {
      case MessageViewType.text:
      case MessageViewType.link:
        return buildTranslatableMessageWidget(
          message: message,
          translatedResult: translatedResult,
          member: member,
          user: user,
          recipientId: recipientId,
          quoteMessage: quoteMessage,
          isOpenCheckbox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          is24HourFormat: is24HourFormat,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          onQuoteMessageClicked: onQuoteMessageClicked,
          onTranslateMessageClicked: onTranslateMessageClicked,
          onButtonTranslateClick: onButtonTranslateClick,
          isBotChannel: isBotChannel,
          isShowCreateTime: isShowCreateTime,
          isShowAvatar: isShowAvatar,
          isShowName: isShowName,
          shouldAnimate: shouldAnimate,
          isHiddenPin: isHiddenPin,
          isHighlighted: isHighlighted,
        );
      default:
        final widgetBuilder = _messageBuilders[message.messageViewType];
        return widgetBuilder != null
            ? widgetBuilder(
                message,
                member,
                user,
                recipientId,
                quoteMessage,
                isOpenCheckbox,
                isCheckedMessage,
                onCheckBoxButtonTap,
                onQuoteMessageClicked,
                messageErrorReason,
                isBotChannel,
                is24HourFormat,
                isHighlighted,
                shouldAnimate,
                isShowCreateTime,
                isShowAvatar,
                isShowName,
                isHiddenPin,
                isLastMessage,
              )
            : _buildUnknownView(message);
    }
  }

  static Widget buildTranslatableMessageWidget({
    required Message message,
    TranslatedResult? translatedResult,
    required Member? member,
    required ChatUser? user,
    required String? recipientId,
    required ui.QuoteMessage? quoteMessage,
    required bool isOpenCheckbox,
    required bool isCheckedMessage,
    required bool is24HourFormat,
    void Function(ui.MessageItem messageItem)? onCheckBoxButtonTap,
    void Function(ui.MessageItem messageItem)? onQuoteMessageClicked,
    void Function(ui.MessageItem messageItem)? onTranslateMessageClicked,
    void Function(ui.MessageItem messageItem)? onButtonTranslateClick,
    bool isBotChannel = false,
    required bool isShowCreateTime,
    required bool isShowAvatar,
    required bool isShowName,
    bool isHiddenPin = false,
    bool isHighlighted = false,
    bool shouldAnimate = false,
    bool isLastMessage = false,
  }) {
    const defaultTranslatedContent = '';

    late final ui.MessageItem messageItem;

    MessagesTranslateState? currentMessageState = null;
    if (translatedResult == null) {
      messageItem = message.createMessageItem(
        member: member,
        user: user,
        recipientId: recipientId,
        hasEditedMessage: message.editTime != null,
        use24HourFormat: is24HourFormat,
      );
    } else {
      currentMessageState =
          getTranslateStatuses(translatedResult, message.messageId);

      messageItem = message.createMessageItem(
        member: member,
        user: user,
        recipientId: recipientId,
        hasEditedMessage: message.editTime != null,
        hasTranslate: currentMessageState.hasTranslate,
        translateStatus: currentMessageState.translateStatus,
        translateButtonStatus: currentMessageState.translateButtonStatus,
        use24HourFormat: is24HourFormat,
      );
    }

    final hasShimmerEffect =
        currentMessageState?.translateStatus == ui.TranslateStatus.translating;
    final translatedContent =
        translatedResult?.translatedContent ?? defaultTranslatedContent;
    final messageContent = translatedContent.isEmpty ||
            currentMessageState?.translateButtonStatus ==
                ui.TranslateButtonStatus.inActive
        ? message.argsContent()!
        : translatedContent;

    return message.messageViewType == MessageViewType.text
        ? TextWidgetImpl(
            key: Key(message.messageId),
            messageItem: messageItem,
            message: message,
            quoteMessage: quoteMessage,
            messageContent: messageContent,
            isOpenCheckBox: isOpenCheckbox,
            isCheckedMessage: isCheckedMessage,
            isHiddenPin: isHiddenPin,
            isShowAvatar: isShowAvatar,
            isShowName: isShowName,
            isBotChannel: isBotChannel,
            isHighlighted: isHighlighted,
            shouldAnimate: shouldAnimate,
            isShowCreateTime: isShowCreateTime,
            hasShimmerEffect: hasShimmerEffect,
            onCheckBoxButtonTap: onCheckBoxButtonTap,
            onQuoteMessageClicked: onQuoteMessageClicked,
            onTranslateMessageClicked: onTranslateMessageClicked,
            onButtonTranslateClick: onButtonTranslateClick,
          )
        : LinkWidgetImpl(
            key: Key(message.messageId),
            message: message,
            messageContent: messageContent,
            messageItem: messageItem,
            quoteMessage: quoteMessage,
            isShowAvatar: isShowAvatar,
            isShowCreateTime: isShowCreateTime,
            isOpenCheckBox: isOpenCheckbox,
            isCheckedMessage: isCheckedMessage,
            isBotChannel: isBotChannel,
            isShowName: isShowName,
            isHiddenPin: isHiddenPin,
            isHighlighted: isHighlighted,
            shouldAnimate: shouldAnimate,
            hasShimmerEffect: hasShimmerEffect,
            onQuoteMessageClicked: onQuoteMessageClicked,
            onCheckBoxButtonTap: onCheckBoxButtonTap,
            onTranslateMessageClicked: onTranslateMessageClicked,
            onButtonTranslateClick: onButtonTranslateClick,
          );
  }

  static MessagesTranslateState getTranslateStatuses(
    TranslatedResult translatedResult,
    String messageId,
  ) {
    final TranslatedStatusEnum translationStatus =
        TranslatedStatusEnum.getEnumByValue(translatedResult.statusRaw);

    final translateButtonStatus = translatedResult.isShowTranslateResult
        ? ui.TranslateButtonStatus.active
        : ui.TranslateButtonStatus.inActive;

    switch (translationStatus) {
      case TranslatedStatusEnum.TRANSLATING:
        return MessagesTranslateState(
          messageId: messageId,
          translateStatus: ui.TranslateStatus.translating,
          translateButtonStatus: ui.TranslateButtonStatus.inActive,
          hasTranslate: false,
        );
      case TranslatedStatusEnum.SUCCESS:
        return MessagesTranslateState(
          messageId: messageId,
          translateStatus: ui.TranslateStatus.success,
          translateButtonStatus: translateButtonStatus,
          hasTranslate: true,
        );
      case TranslatedStatusEnum.NO_SUPPORT:
      case TranslatedStatusEnum.UNDETECTABLE:
        return MessagesTranslateState(
          messageId: messageId,
          translateStatus: ui.TranslateStatus.noSupport,
          translateButtonStatus: ui.TranslateButtonStatus.error,
          hasTranslate: true,
        );
      default:
        return MessagesTranslateState(
          messageId: messageId,
          translateStatus: ui.TranslateStatus.unKnown, // Default state
          translateButtonStatus: ui.TranslateButtonStatus.inActive,
          hasTranslate: false,
        );
    }
  }

  static final Map<
      MessageViewType,
      Widget Function(
        Message message,
        Member? member,
        ChatUser? user,
        String? recipientId,
        ui.QuoteMessage? quoteMessage,
        bool isOpenCheckbox,
        bool isCheckedMessage,
        void Function(ui.MessageItem messageItem)? onCheckBoxButtonTap,
        void Function(ui.MessageItem messageItem)? onQuoteMessageClicked,
        ui.MessageErrorReason? messageErrorReason,
        bool? isBotChannel,
        bool is24HourFormat,
        bool isHighlighted,
        bool shouldAnimate,
        bool isShowCreateTime,
        bool isShowAvatar,
        bool isShowName,
        bool isHiddenPin,
        bool isLastMessage,
      )> _messageBuilders = {
    MessageViewType.textOwner: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        TextSenderWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          isHiddenPin: isHiddenPin,
          isShowCreateTime: isShowCreateTime,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            hasEditedMessage: message.editTime != null,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          quoteMessage: quoteMessage,
          onQuoteMessageClicked: onQuoteMessageClicked,
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
          isLastMessage: isLastMessage,
        ),
    MessageViewType.linkOwner: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        LinkOwnerWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          isShowCreateTime: isShowCreateTime,
          isHiddenPin: isHiddenPin,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            hasEditedMessage: message.editTime != null,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          quoteMessage: quoteMessage,
          onQuoteMessageClicked: onQuoteMessageClicked,
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
          isLastMessage: isLastMessage,
        ),
    MessageViewType.sticker: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        StickerWidgetImpl(
          key: Key(message.messageId),
          isShowCreateTime: isShowCreateTime,
          messageContent: message.argsContent()!,
          message: message,
          isShowName: isShowName,
          isShowAvatar: isShowAvatar,
          isOpenCheckBox: isOpenCheckbox,
          isHiddenPin: isHiddenPin,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isFocusable: isHighlighted,
        ),
    MessageViewType.stickerOwner: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        StickerSenderWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isHiddenPin: isHiddenPin,
          isShowCreateTime: isShowCreateTime,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
          isLastMessage: isLastMessage,
        ),
    MessageViewType.invitation: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        InvitationWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isShowName: isShowName,
          isShowAvatar: isShowAvatar,
          isHiddenPin: isHiddenPin,
          isShowCreateTime: isShowCreateTime,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          quoteMessage: quoteMessage,
          isBotChannel: isBotChannel ?? false,
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
        ),
    MessageViewType.invitationOwner: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        InvitationOwnerWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isHiddenPin: isHiddenPin,
          isShowCreateTime: isShowCreateTime,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            hasEditedMessage: message.editTime != null,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          quoteMessage: quoteMessage,
          onQuoteMessageClicked: onQuoteMessageClicked,
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
          isLastMessage: isLastMessage,
        ),
    MessageViewType.location: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        LocationWidgetImpl(
          key: Key(message.messageId),
          isShowAvatar: isShowAvatar,
          message: message,
          isHiddenPin: isHiddenPin,
          isShowName: isShowName,
          isOpenCheckBox: isOpenCheckbox,
          isShowCreateTime: isShowCreateTime,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
        ),
    MessageViewType.locationOwner: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        LocationOwnerWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isShowCreateTime: isShowCreateTime,
          isOpenCheckBox: isOpenCheckbox,
          isHiddenPin: isHiddenPin,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
          isLastMessage: isLastMessage,
        ),
    MessageViewType.file: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        FileWidgetImpl(
          isShouldAnimate: shouldAnimate,
          key: Key(message.messageId),
          message: message,
          isHiddenPin: isHiddenPin,
          isShowName: isShowName,
          isShowAvatar: isShowAvatar,
          isShowCreateTime: isShowCreateTime,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isFocusable: isHighlighted,
        ),
    MessageViewType.fileOwner: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        FileOwnerWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isHiddenPin: isHiddenPin,
          isShowCreateTime: isShowCreateTime,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
          isLastMessage: isLastMessage,
        ),
    MessageViewType.images: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        ImagesWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isHiddenPin: isHiddenPin,
          isShowName: isShowName,
          isShowAvatar: isShowAvatar,
          isShowCreateTime: isShowCreateTime,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isFocusable: isHighlighted,
        ),
    MessageViewType.imagesOwner: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        ImagesOwnerWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isHiddenPin: isHiddenPin,
          isShowCreateTime: isShowCreateTime,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
          isLastMessage: isLastMessage,
        ),
    MessageViewType.video: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        VideosWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isShowName: isShowName,
          isHiddenPin: isHiddenPin,
          isShowAvatar: isShowAvatar,
          isShowCreateTime: isShowCreateTime,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isFocusable: isHighlighted,
        ),
    MessageViewType.videoOwner: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        VideosOwnerWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isShowCreateTime: isShowCreateTime,
          isOpenCheckBox: isOpenCheckbox,
          isHiddenPin: isHiddenPin,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
          isLastMessage: isLastMessage,
        ),
    MessageViewType.ziiShort: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        ZiiShortWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isShowName: isShowName,
          isShowAvatar: isShowAvatar,
          isShowCreateTime: isShowCreateTime,
          isHiddenPin: isHiddenPin,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isFocusable: isHighlighted,
        ),
    MessageViewType.ziiShortsOwner: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        ZiiShortOwnerWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isShowCreateTime: isShowCreateTime,
          isOpenCheckBox: isOpenCheckbox,
          isHiddenPin: isHiddenPin,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
          isLastMessage: isLastMessage,
        ),
    MessageViewType.ziiVoice: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        ZiiVoiceWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isShowAvatar: isShowAvatar,
          isShowName: isShowName,
          isShowCreateTime: isShowCreateTime,
          isHiddenPin: isHiddenPin,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
        ),
    MessageViewType.ziiVoiceOwner: (
      message,
      member,
      user,
      recipientId,
      quoteMessage,
      isOpenCheckbox,
      isCheckedMessage,
      onCheckBoxButtonTap,
      onQuoteMessageClicked,
      messageErrorReason,
      isBotChannel,
      is24HourFormat,
      isHighlighted,
      shouldAnimate,
      isShowCreateTime,
      isShowAvatar,
      isShowName,
      isHiddenPin,
      isLastMessage,
    ) =>
        ZiiVoiceOwnerWidgetImpl(
          key: Key(message.messageId),
          message: message,
          isOpenCheckBox: isOpenCheckbox,
          isCheckedMessage: isCheckedMessage,
          isHiddenPin: isHiddenPin,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          isShowCreateTime: isShowCreateTime,
          messageItem: message.createMessageItem(
            member: member,
            user: user,
            recipientId: recipientId,
            messageErrorReason: messageErrorReason,
            use24HourFormat: is24HourFormat,
          ),
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
          isLastMessage: isLastMessage,
        ),
  };

  static Widget _buildUnknownView(Message message) {
    if (message.content!.contains(GlobalConfig.CONTENT_PIN_MESSAGE)) {
      return ui.PinSystemMessage(
        name: message.contentArguments?.first ?? '',
      );
    }
    if (message.content!.contains(GlobalConfig.CONTENT_UNPIN_MESSAGE)) {
      return ui.UnpinSystemMessage(
        name: message.contentArguments?.first ?? '',
      );
    }
    if (message.content!.contains(GlobalConfig.CONTENT_START_A_CALL)) {
      return ui.StartCallSystemMessage(
        name: message.contentArguments?.first ?? '',
      );
    }
    return ui.SystemMessageContainer(
      messageContent: message.argsContent() ?? '',
    );
  }
}
