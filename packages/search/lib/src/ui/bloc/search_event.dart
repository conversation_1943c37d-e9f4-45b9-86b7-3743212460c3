part of 'search_bloc.dart';

abstract class SearchEvent extends BaseBloc<PERSON>vent {
  const SearchEvent();
}

class InitiateSearchEvent extends SearchEvent {
  const InitiateSearchEvent();
}

class SearchAllEvent extends SearchEvent {
  final String keyword;

  const SearchAllEvent({required this.keyword});
}

class SearchUserEvent extends SearchEvent {
  final String keyword;
  final String nextPageToken;

  const SearchUserEvent({
    required this.keyword,
    this.nextPageToken = '1',
  });
}

class SearchChannelEvent extends SearchEvent {
  final String keyword;
  final String nextPageToken;

  const SearchChannelEvent({
    required this.keyword,
    this.nextPageToken = '1',
  });
}

class RemoveHistoryEvent extends SearchEvent {
  final String keyword;

  const RemoveHistoryEvent({required this.keyword});
}

class SaveHistoryEvent extends SearchEvent {
  final String keyword;

  const SaveHistoryEvent({required this.keyword});
}

class SaveRecentSearchEvent extends SearchEvent {
  final Search model;

  const SaveRecentSearchEvent({required this.model});
}

class ChangeCurrentRecentSegmentEvent extends SearchEvent {
  final int index;

  const ChangeCurrentRecentSegmentEvent({required this.index});
}

class CheckUserEvent extends SearchEvent {
  final BuildContext context;
  final String userId;
  final VoidCallback onUserExists;

  const CheckUserEvent({
    required this.context,
    required this.userId,
    required this.onUserExists,
  });
}

class CheckChannelEvent extends SearchEvent {
  final BuildContext context;
  final String channelId;
  final String workspaceId;
  final VoidCallback onChannelExists;

  const CheckChannelEvent({
    required this.context,
    required this.channelId,
    required this.workspaceId,
    required this.onChannelExists,
  });
}

class GetSuggestedFriendsEvent extends SearchEvent {
  final String? nextPageToken;
  final SuggestionType suggestionType;

  const GetSuggestedFriendsEvent({
    this.nextPageToken,
    this.suggestionType = SuggestionType.channel,
  });
}

class OnInitUserPrivateDataEvent extends SearchEvent {
  const OnInitUserPrivateDataEvent();
}
