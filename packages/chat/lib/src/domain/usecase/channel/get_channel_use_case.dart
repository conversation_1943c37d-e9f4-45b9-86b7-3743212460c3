import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/channel_repository.dart';
import '../../../data/repositories/database/entities/channel.dart';

@Injectable()
class GetChannelUseCase
    extends BaseSyncUseCase<GetChannelInput, GetChannelOutput> {
  GetChannelUseCase(
    this._channelRepository,
  );

  final ChannelRepository _channelRepository;

  @override
  GetChannelOutput buildUseCase(GetChannelInput input) {
    Channel? channel = (input.workspaceId != null && input.channelId != null)
        ? _channelRepository.getChannel(
            workspaceId: input.workspaceId!,
            channelId: input.channelId!,
          )
        : _channelRepository.getDMChannel(recipientId: input.userId!);

    return GetChannelOutput(channel: channel);
  }
}

class GetChannelInput extends BaseInput {
  GetChannelInput({
    this.workspaceId,
    this.channelId,
    this.userId,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
}

class GetChannelOutput extends BaseOutput {
  GetChannelOutput({
    required this.channel,
  });

  final Channel? channel;
}
