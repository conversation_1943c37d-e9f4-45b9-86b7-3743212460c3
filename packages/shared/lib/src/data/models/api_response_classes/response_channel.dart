import 'package:json_annotation/json_annotation.dart';

import 'response_channel_metadata.dart';
import 'response_premium_settings.dart';
import 'response_privacy_settings.dart';

part 'response_channel.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseChannel {
  final String workspaceId;
  final String channelId;
  final String userId;
  final String? name;
  final String? avatar;
  final bool? isPrivate;
  final int? type;
  final int? dmStatus;
  final String? invitationLink;
  final int? totalMembers;
  final String? createTime;
  final String? updateTime;
  final List<String>? participantIds;
  final ResponseChannelMetadata? channelMetadata;
  final ResponsePrivacySettings? privacySettings;
  final ResponsePremiumSettings? premiumSettings;

  ResponseChannel({
    required this.workspaceId,
    required this.channelId,
    required this.userId,
    this.name,
    this.avatar,
    this.isPrivate,
    this.type,
    this.dmStatus,
    this.invitationLink,
    this.totalMembers,
    this.createTime,
    this.updateTime,
    this.participantIds,
    this.channelMetadata,
    this.privacySettings,
    this.premiumSettings,
  });

  factory ResponseChannel.fromJson(Map<String, dynamic> json) =>
      _$ResponseChannelFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseChannelToJson(this);
}
