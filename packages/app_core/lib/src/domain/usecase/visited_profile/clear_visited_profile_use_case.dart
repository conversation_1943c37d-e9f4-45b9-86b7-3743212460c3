import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';

part 'clear_visited_profile_use_case.freezed.dart';

@Injectable()
class ClearVisitedProfileUseCase extends BaseFutureUseCase<
    ClearVisitedProfileUseCaseInput, ClearVisitedProfileUseCaseOutput> {
  const ClearVisitedProfileUseCase();

  @protected
  @override
  Future<ClearVisitedProfileUseCaseOutput> buildUseCase(
    ClearVisitedProfileUseCaseInput input,
  ) async {
    try {
      final result = await UserProfileClient()
          .instance
          .clearUserVisitedProfileNotifications();

      return ClearVisitedProfileUseCaseOutput(
        response: result.data?.ok ?? false,
      );
    } on Exception catch (_) {
      return ClearVisitedProfileUseCaseOutput(response: false);
    }
  }
}

@freezed
sealed class ClearVisitedProfileUseCaseInput extends BaseInput
    with _$ClearVisitedProfileUseCaseInput {
  const ClearVisitedProfileUseCaseInput._();
  factory ClearVisitedProfileUseCaseInput() = _ClearVisitedProfileUseCaseInput;
}

@freezed
sealed class ClearVisitedProfileUseCaseOutput extends BaseOutput
    with _$ClearVisitedProfileUseCaseOutput {
  const ClearVisitedProfileUseCaseOutput._();
  factory ClearVisitedProfileUseCaseOutput({bool? response}) =
      _ClearVisitedProfileUseCaseOutput;
}
