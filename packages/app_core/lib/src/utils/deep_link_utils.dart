import 'package:shared/shared.dart';

enum DeepLinkType { invite, qrAuth, send, connect }

class DeepLinkUtils {
  static final Map<DeepLinkType, String> _patterns = {
    DeepLinkType.invite: EnvConfig.getInvitationHost,
    DeepLinkType.qrAuth: EnvConfig.getQrAuthHost,
    DeepLinkType.send: EnvConfig.getSendLink,
    DeepLinkType.connect: EnvConfig.getScanToConnectHost,
  };

  static bool isValid(String url) {
    return _patterns.values.any((pattern) => RegExp(pattern).hasMatch(url));
  }

  static bool isOfType(String url, DeepLinkType type) {
    final pattern = _patterns[type];

    if (pattern == null) return false;

    return url.startsWith(pattern);
  }

  static String? extractValue(String url, DeepLinkType type) {
    final typeSegment = '${type.name}/';

    final typeIndex = url.indexOf(typeSegment);
    if (typeIndex == -1) {
      return null;
    }

    final startIndex = typeIndex + typeSegment.length;
    if (startIndex >= url.length) {
      return null;
    }

    final value = url.substring(startIndex);

    return value;
  }

  static bool isInviteLink(String url) => isOfType(url, DeepLinkType.invite);

  static bool isQrAuthLink(String url) => isOfType(url, DeepLinkType.qrAuth);

  static bool isSendLink(String url) => isOfType(url, DeepLinkType.send);

  static bool isConnectLink(String url) => isOfType(url, DeepLinkType.connect);

  static String? getInviteValue(String url) =>
      extractValue(url, DeepLinkType.invite);

  static String? getQrAuthValue(String url) =>
      extractValue(url, DeepLinkType.qrAuth);

  static String? getSendValue(String url) =>
      extractValue(url, DeepLinkType.send);

  static String? getConnectValue(String url) =>
      extractValue(url, DeepLinkType.connect);
}
