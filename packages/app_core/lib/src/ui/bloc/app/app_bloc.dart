import 'dart:async';

import 'package:auth/auth.dart';
import 'package:chat/chat.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:locale_plus/locale_plus.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';
import '../../../common/di/di.dart';
import '../../../domain/usecase/change_language_use_case.dart';
import '../../../domain/usecase/change_theme_use_case.dart';

part 'app_bloc.freezed.dart';
part 'app_event.dart';
part 'app_state.dart';

@LazySingleton()
class AppBloc extends BaseBloc<AppEvent, AppState> {
  AppBloc(
    this._changeThemeUseCase,
    this._changeLanguageUseCase,
    this._initAppUseCase,
    this._isAuthenticatedUseCase,
    this._sessionRepository,
    this._logoutUseCase,
  ) : super(AppState.initial()) {
    on<AppInitiated>(_onAppInitiated);
    on<AppThemeChanged>(_onAppThemeChanged);
    on<AppLanguageChanged>(_onAppLanguageChanged);
    on<AppLogout>(_onAppLogout);
    on<AppInvalidToken>(_onAppInvalidToken);
    on<AppTime24HourFormatChanged>(_onAppTime24HourFormatChanged);
  }

  final ChangeThemeUseCase _changeThemeUseCase;
  final ChangeLanguageUseCase _changeLanguageUseCase;
  final InitAppUseCase _initAppUseCase;
  final IsAuthenticatedUseCase _isAuthenticatedUseCase;
  final SessionRepository _sessionRepository;
  final LogoutUseCase _logoutUseCase;

  void _onAppThemeChanged(AppThemeChanged event, Emitter<AppState> emit) {
    _changeThemeUseCase.execute(SetAppThemeInput(event.themeMode));
    emit(state.copyWith(themeMode: event.themeMode));
  }

  Future<void> _onAppInitiated(
    AppInitiated event,
    Emitter<AppState> emit,
  ) async {
    final initAppOutput = _initAppUseCase.execute(InitAppInput());
    final isAuthenticatedOutput =
        _isAuthenticatedUseCase.execute(IsAuthenticatedInput());
    final is24HourTime = await LocalePlus().is24HourTime();
    final appState = state.copyWith(
      locale: initAppOutput.locale,
      isAuthenticated: isAuthenticatedOutput.isAuthenticated,
      themeMode: initAppOutput.themeMode,
      is24HourFormat: is24HourTime ?? true,
    );
    emit(appState);
  }

  FutureOr<void> _onAppLanguageChanged(
    AppLanguageChanged event,
    Emitter<AppState> emit,
  ) {
    _changeLanguageUseCase.execute(SetLanguageInput(event.locale));
    emit(state.copyWith(locale: event.locale));
  }

  Future<void> _onAppLogout(AppLogout event, Emitter<AppState> emit) async {
    // updatePendingMessage();
    _setInActiveSession();
    unawaited(_logoutUseCase.execute(LogoutInput()));
    event.onSuccess?.call();
    emit(state.copyWith(isAuthenticated: false));
  }

  void _setInActiveSession() {
    var session = _sessionRepository.getActiveSession();
    if (session == null) return;
    session.logoutTime = DateTime.now();
    session.active = false;
    _sessionRepository.insert(session);
  }

  Future<void> updatePendingMessage() async {
    // Dọn dẹp tất cả các task đang chờ xử lý
    unawaited(IsolateTaskService().clean());
    unawaited(
      getIt<UpdateAllPendingMessagesToFailedUseCase>()
          .execute(UpdateAllPendingMessagesToFailedInput()),
    );
  }

  Future<void> _onAppInvalidToken(
    AppInvalidToken event,
    Emitter<AppState> emit,
  ) async {
    try {
      updatePendingMessage();
    } catch (_) {}
    _setInActiveSession();
    emit(state.copyWith(isAuthenticated: false));
  }

  void _onAppTime24HourFormatChanged(
    AppTime24HourFormatChanged event,
    Emitter<AppState> emit,
  ) {
    emit(state.copyWith(is24HourFormat: event.is24HourFormat));
  }
}
