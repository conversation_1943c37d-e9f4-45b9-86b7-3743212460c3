import 'dart:async';

import 'package:app_core/core.dart' hide Config;
import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../../../chat.dart';

part 'user_status_bloc.freezed.dart';
part 'user_status_event.dart';
part 'user_status_state.dart';

@injectable
class UserStatusBloc extends Bloc<UserStatusEvent, UserStatusState> {
  UserStatusBloc(
    this._getListUserStatusUseCase,
    this._chatUserRepository,
    this._chatFriendRepository,
  ) : super(UserStatusState.initial()) {
    on<Initiate>(_onInitiate);
    on<ReLoadStatusEvent>(_onReLoadStatus);
    on<LoadedEvent>(_onLoaded);
    on<UpdatedStatusEvent>(_onUpdatedStatus);
    on<UnSubscriptionEvent>(_onUnSubscription);
  }

  final GetListUserStatusUseCase _getListUserStatusUseCase;

  StreamSubscription? _chatUserSubscription;
  StreamSubscription? _friendSubscription;

  final ChatUserRepository _chatUserRepository;
  final ChatFriendRepository _chatFriendRepository;

  List<ChatUser> _userStatus = [];
  Set<String> userIdSet = {};

  String get _myUserId => Config.getInstance().activeSessionKey!;

  Future<void> _onInitiate(
    Initiate event,
    Emitter<UserStatusState> emit,
  ) async {
    _friendSubscription = await _chatFriendRepository.observerChatFriends(
      listener: _friendsListener,
    );

    add(ReLoadStatusEvent());
  }

  /// Handles updates to the friend list and manages user subscriptions.
  ///
  /// - Extracts all friend userIds.
  /// - Adds the current userId to the final set.
  /// - Subscribes to user updates if the set has changed.
  void _friendsListener(List<ChatFriend> friends) {
    if (isClosed) return;
    final setIds = friends
        .map(
          (friend) =>
              friend.participantIds!.firstWhere((id) => id != _myUserId),
        )
        .toSet();
    setIds.add(_myUserId);
    if (userIdSet != setIds) {
      userIdSet = setIds;
      _subscribeToUser();
    }
  }

  Future<void> _onLoaded(
    LoadedEvent event,
    Emitter<UserStatusState> emit,
  ) async {
    emit(UserStatusState.loaded(users: event.users));
  }

  Future<void> _onUpdatedStatus(
    UpdatedStatusEvent event,
    Emitter<UserStatusState> emit,
  ) async {
    emit(UserStatusState.updatedStatus(users: event.users));
  }

  /// Subscribes to the stream of chat users based on the current [userIdSet].
  ///
  /// - Cancels any existing subscription before starting a new one.
  /// - Filters out users without [statusData], except the current user.
  /// - Removes duplicates, sorts users, and dispatches a [UserStatusEvent].
  /// - Ensures the current user appears first in the list.
  Future<void> _subscribeToUser() async {
    await _chatUserSubscription?.cancel();
    _chatUserSubscription = _chatUserRepository
        .getAllUsersBySetUserIdOnChannelStream(userIdSet)
        .listen((List<ChatUser> users) {
      users.removeWhere(
        (user) => user.statusData == null && user.userId != _myUserId,
      );
      if (users.isNotEmpty) {
        _removeDuplicateUsers(users);
        users.sort((a, b) {
          if (a.userId == _myUserId) return -1;
          if (b.userId == _myUserId) return 1;
          return 0;
        });
        add(UserStatusEvent.loaded(users: users));
      }
    });
  }

  /// Removes duplicate users from the given list based on [userId].
  ///
  /// - Keeps the first occurrence of each user.
  /// - Ensures each [userId] appears only once in the list.
  void _removeDuplicateUsers(List<ChatUser> users) {
    final seenUserIds = <String>{};
    users.removeWhere((user) => !seenUserIds.add(user.userId));
  }

  FutureOr<void> _onUnSubscription(
    UnSubscriptionEvent event,
    Emitter<UserStatusState> emit,
  ) {
    _friendSubscription?.cancel();
    _chatUserSubscription?.cancel();
  }

  Future<void> _onReLoadStatus(event, Emitter<UserStatusState> emit) async {
    final output =
        await _getListUserStatusUseCase.execute(GetListUserStatusInput());
    if (output.users == null) return;
    _userStatus = output.users!;
    _chatUserRepository.forceInsertAll(_userStatus);
  }
}
