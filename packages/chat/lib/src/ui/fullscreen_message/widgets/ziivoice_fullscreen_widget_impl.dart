import 'dart:async';
import 'dart:core';
import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../message_list/constants.dart';
import '../../message_list/reaction_handler.dart';
import 'base/base_fullscreen_widget.dart';

class ZiiVoiceFullscreenWidget extends StatefulWidget {
  const ZiiVoiceFullscreenWidget({
    super.key,
    required this.url,
    required this.path,
    required this.messageItem,
    this.caption = '',
  });

  final String? url;
  final String? path;
  final String caption;
  final ui.MessageItem messageItem;

  @override
  State<ZiiVoiceFullscreenWidget> createState() =>
      _ZiiVoiceFullscreenWidgetState();
}

class _ZiiVoiceFullscreenWidgetState extends State<ZiiVoiceFullscreenWidget>
    with WidgetsBindingObserver {
  final PlayerController playerController = PlayerController();
  Timer? _timer;
  bool isPlayVideo = false;
  bool isPlayVideoState = false;
  bool isPlayAfterPause = false;
  bool showMoreAvailable = false;
  final ScrollController scrollController = ScrollController();
  ValueNotifier<Widget> playButton = ValueNotifier(
    SizedBox(
      width: 60.w,
      height: 60.w,
    ),
  );

  @override
  void initState() {
    initialZiiVoice();

    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkShowMoreAvailability();
    });
    WidgetsBinding.instance.addObserver(this);
  }

  void _checkShowMoreAvailability() {
    final textStyle = Theme.of(context)
        .textTheme
        .headlineSmall
        ?.copyWith(color: Colors.white);

    final textPainter = TextPainter(
      text: TextSpan(text: widget.caption, style: textStyle),
      maxLines: 9,
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: MediaQuery.of(context).size.width - 40.w);

    setState(() {
      showMoreAvailable = textPainter.didExceedMaxLines;
    });
  }

  Future<void> initialZiiVoice() async {
    playerController.setRefresh(true);
    playerController.onCompletion.listen((_) async {
      await playerController.seekTo(0);
      onPlayZiiVoice();
    });
    playerController.addListener(() {
      if (mounted) {
        setState(() {
          isPlayVideoState =
              playerController.playerState == PlayerState.playing;
        });
      }
    });
    try {
      String path = widget.path ?? '';

      if (widget.path == null || widget.path!.isEmpty) {
        path = (await _downloadFileAndCache()).path;
      }

      if (path.isEmpty) return;

      await playerController.preparePlayer(path: path);
      if (isPlayVideo) {
        onPlayZiiVoice();
      }
    } catch (e) {
      print('Error initializing ZiiVoice: $e');
    }
  }

  Future<File> _downloadFileAndCache() async =>
      await AppCacheManager().getFile(UrlUtils.parseCDNUrl(widget.url));

  void onPauseZiiVoice() {
    isPlayVideo = false;
    playButton.value = ClipOval(
      child: SizedBox(
        width: 60.w,
        height: 60.w,
        child: DecoratedBox(
          decoration: const BoxDecoration(
            color: Color.fromRGBO(255, 255, 255, 0.2),
          ),
          child: Padding(
            padding: EdgeInsets.only(
              left: 16.w,
              right: 16.w,
              top: 16.w,
              bottom: 16.w,
            ),
            child: ui.AppAssets.pngIconAsset(
              key: ValueKey(isPlayVideo),
              ui.AppAssets.icPlayNew,
              color: Colors.white,
              boxFit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
    playerController.pausePlayer();
  }

  Future<void> onReset() async {
    isPlayVideo = false;
    playerController.seekTo(0).then((_) {
      playerController.pausePlayer();
    });
  }

  Future<void> onPlayZiiVoice() async {
    isPlayVideo = true;
    playButton.value = SizedBox(
      width: 60.w,
      height: 60.w,
    );
    await playerController.setFinishMode(finishMode: FinishMode.pause);
    await playerController.startPlayer();
  }

  void _startTimer() {
    _timer?.cancel();
    _timer = Timer(const Duration(seconds: 2), () {
      if (isPlayVideo) {
        playButton.value = SizedBox(
          width: 60.w,
          height: 60.w,
        );
        isPlayAfterPause = false;
      }
    });
  }

  Future<void> onPlayAfterPauseZiiVoice() async {
    isPlayVideo = true;

    playButton.value = ClipOval(
      child: SizedBox(
        width: 60.w,
        height: 60.w,
        child: DecoratedBox(
          decoration: const BoxDecoration(
            color: Color.fromRGBO(255, 255, 255, 0.2),
          ),
          child: Padding(
            padding: EdgeInsets.only(
              left: 16.w,
              right: 16.w,
              top: 16.w,
              bottom: 16.w,
            ),
            child: ui.AppAssets.pngIconAsset(
              key: ValueKey(isPlayVideo),
              ui.AppAssets.icPauseNew,
              color: Colors.white,
              boxFit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );

    isPlayAfterPause = true;

    await playerController.setFinishMode(finishMode: FinishMode.pause);
    await playerController.startPlayer();
  }

  @override
  void didUpdateWidget(covariant ZiiVoiceFullscreenWidget oldWidget) {
    if (isPlayVideo) {
      onPlayZiiVoice();
    } else {
      onReset();
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    playerController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    playButton.value = SizedBox(
      width: 60.w,
      height: 60.w,
    );
    super.dispose();
  }

  Widget _buildButton() {
    return ValueListenableBuilder(
      valueListenable: playButton,
      builder: (context, value, child) {
        return Align(
          child: ui.AppSizeTransition(
            child: value,
          ),
        );
      },
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      onPauseZiiVoice();
    } else if (state == AppLifecycleState.resumed) {
      onPlayZiiVoice();
    } else if (state == AppLifecycleState.inactive) {
      onPauseZiiVoice();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: VisibilityDetector(
        key: Key('visibility_${widget.messageItem.messageId}'),
        onVisibilityChanged: (info) {
          if (!mounted) return;
          if (info.visibleBounds == Rect.zero) {
            onReset();
          } else if (info.visibleFraction == 1.0 && !isPlayVideo) {
            onPlayZiiVoice();
          }
        },
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () async {
            if (isPlayVideo) {
              onPauseZiiVoice();
            } else {
              await onPlayAfterPauseZiiVoice();
            }
            _startTimer();
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildCaption(),
              SizedBox(
                height: 36.h,
              ),
              _buildAudioWaveForm(),
            ],
          ),
        ),
      ),
    );
  }

  Column _buildAudioWaveForm() {
    final waveWidth = MediaQuery.of(context).size.width - 160.w;

    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        AudioFileWaveforms(
          playerController: playerController,
          size: Size(waveWidth, 100.h),
          waveformData: playerController.waveformExtraction.waveformData,
          waveformType: WaveformType.long,
          animationDuration: Duration(milliseconds: 10),
          playerWaveStyle: PlayerWaveStyle(
            showSeekLine: false,
            fixedWaveColor: Colors.white,
            liveWaveColor: ui.AppColors.primaryBlue,
            scaleFactor: 100,
            waveThickness: 2.8,
          ),
        ),
        SizedBox(height: 36.h),
        _buildButton(),
        SizedBox(width: 4.w),
      ],
    );
  }

  Widget _buildCaption() {
    TextStyle? handleTextStyle() {
      return !showMoreAvailable
          ? Theme.of(context)
              .textTheme
              .titleLarge
              ?.copyWith(color: Colors.white, fontSize: 20.sp)
          : Theme.of(context)
              .textTheme
              .titleMedium
              ?.copyWith(color: Colors.white, fontSize: 18.sp);
    }

    final messageFullScreenSize = handleTextStyle();
    final localization = AppLocalizations.of(context)!;

    return ConstrainedBox(
      constraints: BoxConstraints(
        maxHeight: 1.sh - 450.h,
      ),
      child: RawScrollbar(
        thickness: 2.w,
        thumbVisibility: true,
        thumbColor: Colors.white,
        controller: scrollController,
        radius: Radius.circular(8.r),
        padding: EdgeInsets.only(
          right: 8.w,
        ),
        child: Padding(
          padding: EdgeInsets.only(
            left: 20.w,
            right: 20.w,
          ),
          child: SingleChildScrollView(
            controller: scrollController,
            child: ui.AppExpandableText(
              widget.caption,
              maxLines: 10,
              textAlign: TextAlign.center,
              expandText: localization.showMore,
              linkColor: ui.AppColors.seeMoreFullScreenColor,
              style: messageFullScreenSize,
            ),
          ),
        ),
      ),
    );
  }
}

class ZiiVoiceFullscreenWidgetImpl extends BaseFullscreenWidget {
  ZiiVoiceFullscreenWidgetImpl({
    required super.messageItem,
    required super.message,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final voiceMessage = message.mediaAttachments.last.voiceMessage!;

    final (listReactions, totalReactions) =
        ReactionHandler.getListReactionsFullScreen(message);

    final quickReact =
        (message.reactions?[heartEmoji['emoji'] as String]?.isReacted ?? false);

    return ui.ViewFullScreenZiiVoiceWidget(
      interface: this,
      messageItem: messageItem,
      emojiList: listReactions,
      totalReactions: ValueNotifier(totalReactions),
      quickReact: quickReact,
      waveFormWidget: ZiiVoiceFullscreenWidget(
        url: UrlUtils.parseCDNUrl(voiceMessage.fileUrl),
        path: voiceMessage.filePath,
        messageItem: messageItem,
      ),
    );
  }
}
