import 'dart:io';
import 'package:shared/shared.dart';
import 'package:video_player/video_player.dart';

class VideoPlayerStateHolder {
  final String key;
  final String messageId;
  final VideoPlayerController controller;

  VideoPlayerStateHolder({
    required this.key,
    required this.messageId,
    required this.controller,
  });
}

class VideoControllerManager {
  factory VideoControllerManager() => _instance;
  VideoControllerManager._internal();

  static final VideoControllerManager _instance =
      VideoControllerManager._internal();

  final Map<String, VideoPlayerController> _controllers = {};
  final List<String> _usageQueue = [];

  static const int _maxControllers = 5;

  String _buildKey(String path, String messageId) => '${path}-${messageId}';

  String getKey(String path, String messageId) => _buildKey(path, messageId);

  final Map<String, VideoPlayerStateHolder> _stateHolders = {};

  VideoPlayerStateHolder? getStateHolder(String path, String messageId) {
    final key = _buildKey(path, messageId);

    final controller = _stateHolders[key]?.controller;

    if (controller == null || !controller.value.isInitialized) {
      _controllers.remove(key);
      _stateHolders.remove(key);
      _usageQueue.remove(key);
      return null;
    }
    return _stateHolders[key];
  }

  Future<VideoPlayerController> getController(
    String path,
    String messageId, {
    bool markAsRecentlyUsed = false,
  }) async {
    final key = _buildKey(path, messageId);

    if (_controllers.containsKey(key)) {
      if (markAsRecentlyUsed) {
        _usageQueue.remove(key);
        _usageQueue.add(key);
        _stateHolders.putIfAbsent(
          key,
          () => VideoPlayerStateHolder(
            key: key,
            messageId: messageId,
            controller: _controllers[key]!,
          ),
        );
      }

      return _controllers[key]!;
    }

    final controller = path.startsWith('http')
        ? VideoPlayerController.networkUrl(Uri.parse(path))
        : VideoPlayerController.file(File(path));
    await controller.initialize();
    await controller.setLooping(true);

    final holder = VideoPlayerStateHolder(
      key: key,
      messageId: messageId,
      controller: controller,
    );
    _stateHolders[key] = holder;

    if (_controllers.length >= _maxControllers) {
      final oldestKey = _usageQueue.removeAt(0);
      _controllers[oldestKey]?.dispose();
      _controllers.remove(oldestKey);
      _stateHolders.remove(oldestKey);
    }

    _controllers[key] = controller;
    _usageQueue.add(key);

    return controller;
  }

  void markControllerAsRecentlyUsed(String path, String messageId) {
    final key = _buildKey(path, messageId);

    if (_controllers.containsKey(key)) {
      _usageQueue.remove(key);
      _usageQueue.add(key);

      final controller = _controllers[key]!;
      _stateHolders[key] = VideoPlayerStateHolder(
        key: key,
        messageId: messageId,
        controller: controller,
      );
    }
  }

  void dispose(String path, String messageId) {
    final key = _buildKey(path, messageId);

    if (_controllers.containsKey(key)) {
      _controllers[key]?.dispose();
      _controllers.remove(key);
      _usageQueue.remove(key);
    }
  }

  void disposeAll() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();
    _usageQueue.clear();
    _stateHolders.clear();
  }
}
