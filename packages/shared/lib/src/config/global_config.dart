import 'package:flutter/foundation.dart';

class GlobalConfig {
  const GlobalConfig._();

  static const enableImprovePassKey = false;
  static const enableGeneralLog = kDebugMode;
  static const isPrettyJson = kDebugMode;

  /// bloc observer
  static const logOnBlocChange = false;
  static const logOnBlocCreate = false;
  static const logOnBlocClose = false;
  static const logOnBlocError = false;
  static const logOnBlocEvent = kDebugMode;
  static const logOnBlocTransition = false;

  /// navigator observer
  static const enableNavigatorObserverLog = kDebugMode;

  /// disposeBag
  static const enableDisposeBagLog = false;

  /// stream event log
  static const logOnStreamListen = false;
  static const logOnStreamData = false;
  static const logOnStreamError = false;
  static const logOnStreamDone = false;
  static const logOnStreamCancel = false;

  /// log interceptor
  static const enableLogInterceptor = kDebugMode;

  // static const enableLogInterceptor = false;
  static const enableLogRequestInfo = enableLogInterceptor;
  static const enableLogSuccessResponse = enableLogInterceptor;
  static const enableLogErrorResponse = enableLogInterceptor;
  static const enableLogResponseDuration = enableLogInterceptor;

  /// enable log usecase
  static const enableLogUseCaseInput = false;
  static const enableLogUseCaseOutput = false;
  static const enableLogUseCaseError = kDebugMode;

  /// log EventBus
  static const enableLogEventBus = false;
  static const enableLogContentEventBus = true;
  static const enableLogHandleIncludesData = false;

  static const enableAuthBoxAdmin = false;
  static const enableUserBoxAdmin = false;
  static const enableChatBoxAdmin = false;
  static const enableSearchBoxAdmin = false;
  static const enableStickerBoxAdmin = false;

  /// Chat config
  static const maxLengthMessage = 2000;
  static const messageSentAudio = 'message_sent.mp3';

  /// Quick Sticker
  static const REACTIONS_CHANNEL_ID = "REACTIONS_CHANNEL_ID";
  static const quickStickerId = "01HYJ53PYQAK5APNKK0KZRPYG8";
  static const quickStickerUrl =
      "zc://01HYJ53PYSWZ7T4K84RJHMJA9Q/01HYJ53PYQAK5APNKK0KZRPYG8/Finger_heart.tgs";
  static const defaultWaveStickerId = "01HXTX9THPXEKDWX8WZZDDBZTQ";
  static const defaultWaveStickerUrl =
      "zc://01HXTX9THSW5KJ0C253Z0H7CAY/01HXTX9THPXEKDWX8WZZDDBZTQ/Hello.tgs";

  ///Default message content
  static const CONTENT_AUDIO = '🔊 Audio';
  static const CONTENT_VIDEO = '📷 Video(s)';
  static const CONTENT_PHOTO = '📷 Photo(s)';
  static const CONTENT_ZII_MEDIA = '📷 Photos and videos';
  static const CONTENT_ZII_SHORT = '🎬 Ziishort';
  static const CONTENT_ZII_VOICE = '🎙️Ziivoice';
  static const CONTENT_FILE = '📄 File';
  static const CONTENT_STICKER = "%s Sticker";
  static const CONTENT_POKED = "👉 Poked";
  static const CONTENT_PIN_MESSAGE = "%s pinned a message.";
  static const CONTENT_UNPIN_MESSAGE = "%s unpinned a message.";
  static const CONTENT_START_A_CALL = "%s starts a call";

  // Call config
  static const ROOM_URL = 'wss://livekit.zacdn.net';

  static const TRANSLATE_TO_LANGUAGE_CODES = [
    "en",
    "vi",
    "af",
    "ar",
    "be",
    "bg",
    "bn",
    "ca",
    "cs",
    "cy",
    "da",
    "de",
    "el",
    "eo",
    "es",
    "et",
    "fa",
    "fi",
    "fr",
    "ga",
    "gl",
    "gu",
    "he",
    "hi",
    "hr",
    "ht",
    "hu",
    "id",
    "is",
    "it",
    "ja",
    "ka",
    "kn",
    "ko",
    "lt",
    "lv",
    "mk",
    "mr",
    "ms",
    "mt",
    "nl",
    "no",
    "pl",
    "pt",
    "ro",
    "ru",
    "sk",
    "sl",
    "sq",
    "sv",
    "sw",
    "ta",
    "te",
    "th",
    "tl",
    "tr",
    "uk",
    "ur",
    "zh",
  ];

  static const ZIICHAT_USER_ID = "01GNNA1J000000000000000000";
  static String ghost = "Ghost";
  static String STICKER_POKE_ID = "01J7MK58DHAW90F3M9CWVDEDQN";

  static String mentionRegex =
      r'(?<keyword>(#)([\p{Alphabetic}\p{Mark}\p{Decimal_Number}\p{Connector_Punctuation}\p{Join_Control}]+))|'
      r'(?<url>(?!@)(?:(?:https?|ftp):\/\/)?[-a-z0-9@:%._\+~#=]{1,256}\.[a-z0-9]{1,6}(\/[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)?)|'
      r'(?<username>(@)(?!.*[_.]{2})(?!.*\._)(?!.*_\.)[_a-zA-Z0-9]([a-zA-Z0-9._]{4,}[a-zA-Z0-9_]))';

  static final RegExp urlRegex = RegExp(
    r'((https?|ftp):\/\/)?(?:www\.)?([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})(:[1-9][0-9]{0,4})?((\/[\w\/.-]+)?(\?[\w&=]*)?(#[\w-]*)?)?',
    caseSensitive: false,
  );

  static Duration sendTimeoutDuration =
      Duration(minutes: 5); // 90 minutes //todo adjust to 5' for QC testing
  static Duration receiveTimeoutDuration =
      Duration(minutes: 5); // 90 minutes //todo adjust to 5' for QC testing
  static var connectTimeoutDuration = Duration(seconds: 10);
  static int uploadConcurrency = 2;

  static String messageCacheDir = 'messageCache';

  static double targetImageWidth = 1920;
  static double targetImageHeight = 1080;
  static const int maxAttempts = 5;
  static const Duration delayFactor = Duration(seconds: 1);
  static const Duration maxDelay = const Duration(seconds: 10);
  static const int maxDelayAfterWsUploadBusy = 100;
  static const Duration retryDelayAfterWsUploadBusy =
      const Duration(seconds: 5);
  static const double randomizationFactor = 0.5;

  static bool callDmEnable = false;
}
