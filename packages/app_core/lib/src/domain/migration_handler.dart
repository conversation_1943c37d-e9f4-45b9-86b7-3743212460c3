import 'package:auth/auth.dart';
import 'package:chat/chat.dart';
import 'package:get_it/get_it.dart';
import 'package:session_migration/SessionService.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';

import '../../core.dart';
import 'usecase/upsert_user_use_case.dart';

class MigrationHandler {
  final TAG = 'MigrationHandler';
  final _sessionService = SessionService();
  final _prep = GetIt.I.get<AppPreferences>();

  Future<void> migrate() async {
    if (_isSessionMigrated()) return;

    await Future.wait([
      _migrate(),
      _prep.setSessionMigrated(true),
      _prep.setFirstTimeOpenAppAfterMigration(true),
    ]);
  }

  /// Handles migration flow by executing session and token migrations.
  /// Calls [_migrateSessions] to check and migrate session data.
  /// If sessions exist, proceeds to call [_migrateToken] to continue migration.
  /// Skips token migration if there are no sessions to migrate.
  Future<void> _migrate() async {
    final shouldMigrateToken = await _migrateSessions();
    if (!shouldMigrateToken) return;
    await _migrateToken();
  }

  /// Get the current session token from the platform.
  Future<void> _migrateToken() async {
    try {
      final token = await _sessionService.getCurrentToken();

      if (token == null || token.isEmpty) return;

      final output = await GetIt.I
          .get<LoadMeMigrationUseCase>()
          .execute(LoadMeMigrationInput(token));

      if (output.user == null) return;

      await GetIt.I.get<InsertSessionUseCase>().execute(
            InsertSessionInput(
              sessionId: output.user!.userId,
              userId: output.user!.userId,
              sessionToken: token,
              active: true,
              isLogin: true,
              isLoginQR: false,
            ),
          );

      final user = output.user;
      user!.sessionKey = user.userId;
      final chatUser = ChatUser.fromJson(user.toJson());

      await GetIt.I.get<UpsertUserAndChatUserUseCase>().execute(
            UpsertUserAndChatUserInput(
              user: user,
              chatUser: chatUser,
            ),
          );
    } catch (e) {
      Log.e(name: TAG, e.toString());
    }
  }

  /// Get the list of sessions from the platform.
  /// Returns true if any sessions were found and processed.
  Future<bool> _migrateSessions() async {
    try {
      final sessions = await _sessionService.getSessions();
      sessions.forEach((session) async {
        await GetIt.I.get<InsertSessionUseCase>().execute(
              InsertSessionInput(
                sessionId: session.userId,
                userId: session.userId,
                sessionToken: '',
                active: false,
                isLogin: false,
                isLoginQR: false,
              ),
            );

        final user = User(
          sessionKey: session.userId,
          userId: session.userId,
          username: session.username,
        );
        final chatUser = ChatUser.fromJson(user.toJson());
        await GetIt.I.get<UpsertUserAndChatUserUseCase>().execute(
              UpsertUserAndChatUserInput(
                user: user,
                chatUser: chatUser,
              ),
            );
      });
      return sessions.isNotEmpty;
    } catch (e) {
      Log.e(name: TAG, e.toString());
    }
    return false;
  }

  bool _isSessionMigrated() {
    return _prep.isSessionMigrated;
  }
}
