import 'dart:convert';

import '../../core/utils/logger.dart';
import 'task_priority.dart';

enum TaskStatus {
  pending,
  running,
  completed,
  failed,
  canceled,
  unrecoverable,
}

extension TaskStatusExtension on TaskStatus {
  bool get isUnrecoverable => this == TaskStatus.unrecoverable;
}

class TaskModel {
  final String id;
  final String name;
  final Map<String, dynamic> inputData;
  final int maxRetries;
  final int retryDelay;
  final int timeout;
  final int retryCount;
  final TaskStatus status;
  final TaskPriority priority;
  final int createdAt;
  final int? completedAt;
  final bool isReferenceTask;
  final bool networkRequired;

  TaskModel({
    required this.id,
    required this.name,
    required this.inputData,
    required this.maxRetries,
    required this.retryDelay,
    required this.timeout,
    this.retryCount = 0,
    this.status = TaskStatus.pending,
    this.priority = TaskPriority.medium,
    int? createdAt,
    this.completedAt,
    this.isReferenceTask = false,
    this.networkRequired = true,
  }) : createdAt = createdAt ?? DateTime.now().millisecondsSinceEpoch;

  TaskModel copyWith({
    String? id,
    String? name,
    Map<String, dynamic>? inputData,
    int? maxRetries,
    int? retryDelay,
    int? timeout,
    int? retryCount,
    TaskStatus? status,
    TaskPriority? priority,
    int? createdAt,
    int? completedAt,
    bool? isReferenceTask,
    bool? networkRequired,
  }) {
    return TaskModel(
      id: id ?? this.id,
      name: name ?? this.name,
      inputData: inputData ?? this.inputData,
      maxRetries: maxRetries ?? this.maxRetries,
      retryDelay: retryDelay ?? this.retryDelay,
      timeout: timeout ?? this.timeout,
      retryCount: retryCount ?? this.retryCount,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      isReferenceTask: isReferenceTask ?? this.isReferenceTask,
      networkRequired: networkRequired ?? this.networkRequired,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'inputData': jsonEncode(inputData),
      'maxRetries': maxRetries,
      'retryDelay': retryDelay,
      'timeout': timeout,
      'retryCount': retryCount,
      'status': status.index,
      'priority': priority.toValue(),
      'createdAt': createdAt,
      'completedAt': completedAt,
      'isReferenceTask': isReferenceTask,
      'networkRequired': networkRequired,
    };
  }

  factory TaskModel.fromJson(Map<String, dynamic> json) {
    Map<String, dynamic> inputDataMap;
    try {
      if (json['inputData'] is String) {
        inputDataMap = jsonDecode(json['inputData']);
      } else if (json['inputData'] is Map) {
        final rawMap = json['inputData'] as Map;
        inputDataMap = Map<String, dynamic>.from(rawMap);
      } else {
        inputDataMap = {};
      }
    } catch (e) {
      RILogger.printDebug('[ResilientIsolate] Error parsing inputData: $e');
      inputDataMap = {};
    }

    return TaskModel(
      id: json['id'],
      name: json['name'],
      inputData: inputDataMap,
      maxRetries: json['maxRetries'],
      retryDelay: json['retryDelay'],
      timeout: json['timeout'],
      retryCount: json['retryCount'],
      status: TaskStatus.values[json['status']],
      priority: json.containsKey('priority')
          ? TaskPriorityExtension.fromValue(json['priority'])
          : TaskPriority.medium,
      createdAt: json['createdAt'] ?? DateTime.now().millisecondsSinceEpoch,
      completedAt: json['completedAt'],
      isReferenceTask: json['isReferenceTask'] ?? false,
      networkRequired: json['networkRequired'] ?? true,
    );
  }

  @override
  String toString() {
    return 'TaskModel{id: $id, name: $name, inputData: $inputData, maxRetries: $maxRetries, retryDelay: $retryDelay, timeout: $timeout, retryCount: $retryCount, status: $status, priority: $priority, createdAt: $createdAt, completedAt: $completedAt, networkRequired: $networkRequired}';
  }
}
