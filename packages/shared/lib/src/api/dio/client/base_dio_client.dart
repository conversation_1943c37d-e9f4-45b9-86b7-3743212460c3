import 'package:dio/dio.dart';

import '../../../../shared.dart';
import '../interceptors/api_error_interceptor.dart';
import '../interceptors/includes_data_interceptor.dart';
import '../interceptors/logging_interceptor.dart';
import '../interceptors/retry_interceptor.dart';
import '../interceptors/service_unavailable_interceptor.dart';
import '../interceptors/valid_token_interceptor.dart';

class BaseClient {
  static final BaseOptions baseOptions = BaseOptions(
    baseUrl: EnvConfig.getApiHost,
    receiveDataWhenStatusError: true,
    // Only consider 2xx and 3xx as successful responses
    // This ensures 4xx and 5xx errors are handled in onError
    // But we still need ServiceUnavailableInterceptor for backward compatibility
    validateStatus: (status) {
      return status != null && status >= 200 && status < 400;
    },
    headers: {
      EnvConfig.getClientIdEnv: EnvConfig.getClientId,
      EnvConfig.getApiHeaderEnv: EnvConfig.getApiVersion,
    },
    sendTimeout: GlobalConfig.sendTimeoutDuration,
    receiveTimeout: GlobalConfig.receiveTimeoutDuration,
    connectTimeout: GlobalConfig.connectTimeoutDuration,
  );

  static final Dio dio = Dio(baseOptions)
    ..interceptors.addAll([
      // 1. ServiceUnavailableInterceptor: Handle 503 errors with "no healthy upstream"
      // This must be first to handle 503 errors before other interceptors
      ServiceUnavailableInterceptor(),

      // 2. LoggingInterceptor: Log all requests, responses, and errors
      // This should be early to log everything but after special error handlers
      LoggingInterceptor(),

      // 3. Event-firing interceptors: These don't modify responses, just fire events
      // The order between these doesn't matter as they don't affect each other
      ApiErrorInterceptor(),
      // Handles blocked user errors
      ValidTokenInterceptor(),
      // Handles invalid token errors
      IncludesDataInterceptor(),
      // Processes includes data

      // 4. RetryInterceptor: Must be last to handle all errors after other interceptors
      // This can retry requests based on certain error conditions
      RetryInterceptor(
        dio: Dio(baseOptions),
        maxRetries: GlobalConfig.maxAttempts,
        retryIntervalMs: GlobalConfig.delayFactor.inMilliseconds,
      ),
    ]);

  static void addAuthToken(Dio dio, String token) {
    if (token.isEmpty) {
      throw Exception('API Auth Token is not set');
    }

    dio.options.headers['x-session-token'] = token;
  }
}
