import 'package:flutter/cupertino.dart';

import '../../../../../shared.dart';

abstract class BaseFutureUseCase<Input extends BaseInput,
    Output extends BaseOutput> extends BaseUseCase<Input, Future<Output>> {
  const BaseFutureUseCase();

  Future<Output> execute(Input input) {
    return RetryService().execute(() async {
      try {
        if (GlobalConfig.enableLogUseCaseInput) {
          Log.d('FutureUseCase Input: $input');
        }

        final output = await buildUseCase(input);

        if (GlobalConfig.enableLogUseCaseOutput) {
          Log.d('FutureUseCase Output: $output');
        }

        return output;
      } catch (e) {
        if (GlobalConfig.enableLogUseCaseError) {
          Log.d('FutureUseCase Error: $e');
        }
        debugPrint('BaseFutureUseCase.execute: ${e.runtimeType}');

        if (e is RetryException) {
          throw e;
        }

        if (e is AppException) {
          throw e;
        }

        throw AppUncaughtException(e);
      }
    });
  }
}
