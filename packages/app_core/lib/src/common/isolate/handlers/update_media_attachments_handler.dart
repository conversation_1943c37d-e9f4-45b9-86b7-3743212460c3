// <PERSON><PERSON> for updating media attachments, refactored to use BaseMessageHandler for consistency and maintainability.
import '../../../../core.dart';
import '../input/worker_send_media_input.dart';
import 'base_message_handler.dart';

class UpdateMediaAttachmentsHandler
    extends BaseMessageHandler<WorkerSendMediaInput> {
  UpdateMediaAttachmentsHandler({
    required super.apiClient,
    required super.retryManager,
  });

  @override
  WorkerSendMediaInput parseInput(Map<String, dynamic> inputData) {
    // Parse input data to WorkerSendMediaInput
    return WorkerSendMediaInput.fromJson(inputData);
  }

  @override
  String getApiEndpoint(WorkerSendMediaInput input) {
    // Determine the API endpoint based on DM or normal message
    return '/Message/' +
        (input.isDm() ? 'UpdateDmMediaAttachments' : 'UpdateMediaAttachments');
  }

  @override
  Future<WorkerSendMessageOutput> executeMessageOperation(
    WorkerSendMediaInput input,
    DateTime createTime,
  ) async {
    // Call API with retry and standardized error handling
    return await makeRetryableApiCall(
      input: input,
      createTime: createTime,
      apiCall: () => apiClient.put(
        getApiEndpoint(input),
        body: input.toRequest(),
      ),
    );
  }

  /// Legacy method for backward compatibility
  Future<WorkerSendMessageOutput> handleMediaMessage({
    required WorkerSendMediaInput messageInput,
    required String ref,
  }) async {
    return await handleMessage(messageInput.toJson());
  }
}
