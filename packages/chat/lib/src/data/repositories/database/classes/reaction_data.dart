import 'package:json_annotation/json_annotation.dart';

part 'reaction_data.g.dart';

@JsonSerializable(explicitToJson: true)
class ReactionData {
  @Json<PERSON>ey(name: 'isReacted')
  bool? isReacted;

  @JsonKey(name: 'total')
  int? total;

  ReactionData({
    this.isReacted,
    this.total,
  });

  factory ReactionData.fromJson(Map<String, dynamic> json) =>
      _$ReactionDataFromJson(json);

  Map<String, dynamic> toJson() => _$ReactionDataToJson(this);
}
