import 'dart:io';

import 'package:download_manager/download_manager.dart';
import 'package:path/path.dart' as path;
import 'package:shared/shared.dart';

import '../../chat.dart';

class FileMessageUtils {
  static bool canViewFile(String url) {
    final extension = path.extension(url);
    if (Platform.isIOS) {
      return OpenFileUtils.iOSMimetypeCanOpen[extension] != null;
    }
    if (Platform.isAndroid) {
      return OpenFileUtils.androidMimetypeCanOpen[extension] != null;
    }
    return true;
  }

  static String? getMimeType(String url) {
    final extension = path.extension(url);
    if (Platform.isIOS) {
      return OpenFileUtils.iOSMimetypeCanOpen[extension];
    }
    if (Platform.isAndroid) {
      return OpenFileUtils.androidMimetypeCanOpen[extension];
    }
    return null;
  }

  static Future<String?> tempFileDownloaded(String url) async {
    return (await AppCacheManager().getFileFromCache(url))?.path;
  }

  static Future<String?> downloadTempFile(String url) async {
    return (await AppCacheManager().getFile(url)).path;
  }

  static Future<void> openFile({
    required String filePath,
    String? mimetype,
  }) async {
    OpenFileUtils.openFile(filePath: filePath, mimetype: mimetype);
  }

  static List<MediaObject> getMediaList(Message message) {
    final _attachmentPropertyMap = {
      AttachmentType.PHOTO: (Attachment attachment) => attachment.photo,
      AttachmentType.VOICE_MESSAGE: (Attachment attachment) =>
          attachment.voiceMessage,
      AttachmentType.VIDEO_MESSAGE: (Attachment attachment) =>
          attachment.videoMessage,
      AttachmentType.AUDIO: (Attachment attachment) => attachment.audio,
      AttachmentType.VIDEO: (Attachment attachment) => attachment.video,
      AttachmentType.FILE: (Attachment attachment) =>
          attachment.file ?? attachment.undefined,
    };

    final propertyAccessor = _attachmentPropertyMap[message.attachmentType];
    if (propertyAccessor == null) {
      return [];
    }

    return message.mediaAttachments
        .map((attachment) => propertyAccessor(attachment))
        .whereType<MediaObject>()
        .toList();
  }

  static Future<void> downloadAllMedia(List<MediaObject> mediaList) async {
    for (var media in mediaList) {
      if (media.fileUrl!.isEmpty || media.fileUrl == null) {
        continue;
      }

      final url = media.fileUrl!;
      AppEventBus.publish(
        DownloadEvent(
          url: url,
          type: checkDownloadTypeEventBus(media.attachmentType),
          fileName: generateFileNameFromUrl(url),
        ),
      );
    }
  }

  static String generateFileNameFromUrl(String url) {
    final uri = Uri.parse(url);
    final originFileName = uri.pathSegments.last;
    final fileExtension = originFileName.split('.').last;
    final dateString = TimeUtils.format(DateTime.now(), 'yyyyMMdd_hhmmssSSS');
    return originFileName.replaceAll(
      '.${fileExtension}',
      '_${dateString}.$fileExtension',
    );
  }

  static DownloadSharedStorage? checkDownloadType(int? attachmentType) {
    if (attachmentType == AttachmentType.PHOTO.value)
      return DownloadSharedStorage.images;
    if (attachmentType == AttachmentType.VIDEO.value ||
        attachmentType == AttachmentType.VIDEO_MESSAGE.value)
      return DownloadSharedStorage.video;
    return DownloadSharedStorage.downloads;
  }

  static DownloadType? checkDownloadTypeEventBus(int? attachmentType) {
    if (attachmentType == AttachmentType.PHOTO.value)
      return DownloadType.images;
    if (attachmentType == AttachmentType.VIDEO.value ||
        attachmentType == AttachmentType.VIDEO_MESSAGE.value)
      return DownloadType.video;
    return DownloadType.downloads;
  }
}
