import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';

import '../../../../../core.dart';
import '../../../../common/isolate/resilient_isolate.dart';

part 'worker_metadata_entity.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class WorkerMetadataEntity {
  WorkerMetadataEntity({
    required this.apiHost,
    required this.fileStoreHost,
    required this.activeSessionKey,
    required this.headerJson,
    this.executorType = ExecutorType.worker,
  });

  @Id(assignable: true)
  @JsonKey(defaultValue: 0)
  int id = 0;

  @Property(uid: 21001)
  String apiHost;

  @Property(uid: 21002)
  String fileStoreHost;

  @Property(uid: 21003)
  String activeSessionKey;

  @Property(uid: 21004)
  String headerJson;

  @Property(uid: 21005)
  ExecutorType executorType;

  factory WorkerMetadataEntity.fromWorkerMetadata(WorkerMetadata metadata) {
    return WorkerMetadataEntity(
      apiHost: metadata.apiHost,
      fileStoreHost: metadata.fileStoreHost,
      activeSessionKey: metadata.activeSessionKey,
      headerJson: jsonEncode(metadata.header),
    );
  }

  WorkerMetadata toWorkerMetadata() {
    Map<String, dynamic> headerMap = jsonDecode(headerJson);
    return WorkerMetadata(
      apiHost: apiHost,
      fileStoreHost: fileStoreHost,
      activeSessionKey: activeSessionKey,
      header: Map<String, String>.from(headerMap),
    );
  }

  factory WorkerMetadataEntity.fromJson(Map<String, dynamic> json) =>
      _$WorkerMetadataEntityFromJson(json);
  Map<String, dynamic> toJson() => _$WorkerMetadataEntityToJson(this);
}
