import 'dart:async';
import 'dart:isolate';

import 'package:chat/chat.dart';
import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:synchronized/synchronized.dart';

import '../../../../core.dart';
import '../../di/di.dart';
import '../data/stores/shared_preferences_store.dart';
import '../domain/models/task_model.dart';
import '../domain/models/task_priority.dart';
import '../resilient_isolate_impl.dart';

/// Service for managing and executing tasks using ResilientIsolate
///
/// This class provides a simple API for registering and managing tasks,
/// replacing intermediate classes like WorkManagerService and ResilientIsolateService
class IsolateTaskService {
  /// Singleton instance
  static final IsolateTaskService _instance = IsolateTaskService._internal();

  /// Factory constructor to access singleton instance
  factory IsolateTaskService() {
    RILogger.printClassMethodDebug(
      'IsolateTaskService',
      'factory',
      'Getting IsolateTaskService instance: ${identityHashCode(_instance)}',
    );
    return _instance;
  }

  /// Private constructor
  IsolateTaskService._internal() {
    RILogger.printClassMethodDebug(
      'IsolateTaskService',
      '_internal',
      'Creating new IsolateTaskService instance: ${identityHashCode(this)}',
    );

    // Initialize ReceivePort to receive tasks from worker isolate
    _taskRegistrationPort = ReceivePort();
    _taskRegistrationPort.listen(_handleTaskRegistration);
  }

  /// ResilientIsolate to manage and execute tasks
  final _resilientIsolate = ResilientIsolate();

  /// ReceivePort to receive tasks from worker isolate
  late final ReceivePort _taskRegistrationPort;

  /// Queue for tasks that cannot be registered immediately
  final List<_QueuedTask> _taskQueue = [];

  /// Lock to prevent concurrent initialization
  final _initLock = Lock();

  /// Flag to track initialization process
  bool _isInitializing = false;

  /// Check if service has been initialized
  bool get isInitialized => _resilientIsolate.isInitialized;

  /// Get SendPort so worker isolate can send task registrations
  SendPort get taskRegistrationPort => _taskRegistrationPort.sendPort;

  /// Initialize service using static configuration from SendMessageConfig
  Future<void> initialize() async {
    // Use lock to prevent concurrent initialization
    return _initLock.synchronized(() async {
      try {
        if (_isInitializing) {
          RILogger.printDebug(
            'IsolateTaskService initialization in progress',
          );
          return;
        }

        if (isInitialized) {
          RILogger.printDebug(
            'IsolateTaskService has already been initialized',
          );
          return;
        }

        _isInitializing = true;

        // Initialize ResilientIsolate with static configuration from SendMessageConfig
        await _resilientIsolate.initialize(
          taskRegistrationPort: _taskRegistrationPort.sendPort,
        );

        RILogger.printDebug('IsolateTaskService initialized successfully');

        // Process tasks in the queue
        await _processQueuedTasks();

        // Double check message status and update pending messages to failed if needed
        await doubleCheckMessageStatus();

        _isInitializing = false;
      } catch (e, stackTrace) {
        _isInitializing = false;
        Log.e(
          name: 'IsolateTaskService',
          ['Error initializing IsolateTaskService', e, stackTrace],
        );
      }
    });
  }

  /// Register a one-time task
  ///
  /// Simplified API to register tasks, replacing WorkManagerService.registerOneOffTask
  /// and ResilientIsolateService.registerOneOffTask
  ///
  /// If the service is not initialized, the task will be queued and processed
  /// when the service is initialized
  Future<void> registerTask({
    required String taskId,
    required String taskName,
    required Map<String, dynamic> inputData,
    TaskPriority priority = TaskPriority.medium,
    bool isReferenceTask = false,
    bool networkRequired = true,
  }) async {
    // If not initialized, queue the task and try to initialize
    if (!isInitialized) {
      RILogger.printClassMethodDebug(
        'IsolateTaskService',
        'registerTask',
        'ResilientIsolate has not been initialized: $taskName, queueing task',
      );

      // Add to queue
      _enqueueTask(
        _QueuedTask(
          taskName: taskName,
          taskId: taskId,
          inputData: inputData,
          priority: priority,
          isReferenceTask: isReferenceTask,
          networkRequired: networkRequired,
        ),
      );

      // Try to initialize in background
      _safeInitialize();
      return;
    }

    try {
      // Try to register task
      await _resilientIsolate.registerTask(
        taskId: taskId,
        taskName: taskName,
        inputData: inputData,
        priority: priority,
        isReferenceTask: isReferenceTask,
        networkRequired: networkRequired,
      );

      Log.d(
        name: 'IsolateTaskService',
        'Task $taskName (ID: $taskId) has been registered with ResilientIsolate',
      );
    } catch (e, stackTrace) {
      Log.e(
        name: 'IsolateTaskService',
        ['Error registering task $taskName', e, stackTrace],
      );

      // If error about closed port, reinitialize and queue the task
      if (e.toString().contains('Cannot add new events after calling close')) {
        RILogger.printClassMethodDebug(
          'IsolateTaskService',
          'registerTask',
          'Port closed error detected, reinitializing and queueing task: $taskName',
        );

        // Add to queue
        _enqueueTask(
          _QueuedTask(
            taskName: taskName,
            taskId: taskId,
            inputData: inputData,
            priority: priority,
            isReferenceTask: isReferenceTask,
            networkRequired: networkRequired,
          ),
        );

        // Reset and reinitialize
        await dispose(reinitialize: true);
      }
    }
  }

  /// Process task registrations from worker isolate
  void _handleTaskRegistration(dynamic message) {
    try {
      if (message is Map<String, dynamic>) {
        if (message.containsKey('registerTask')) {
          final taskData = message['registerTask'] as Map<String, dynamic>;
          final taskName = taskData['taskName'] as String;
          final inputData = taskData['inputData'] as Map<String, dynamic>;
          final priorityIndex =
              taskData['priority'] as int? ?? TaskPriority.medium.index;
          final priority = TaskPriority.values.firstWhere(
            (p) => p.index == priorityIndex,
            orElse: () => TaskPriority.medium,
          );
          final isReferenceTask = taskData['isReferenceTask'] as bool? ?? false;
          final networkRequired = taskData['networkRequired'] as bool? ?? true;

          debugPrint(
            'IsolateTaskService._handleTaskRegistration taskData: ${taskData}',
          );

          // Get taskId from message
          final taskId = taskData['taskId'] as String;

          RILogger.printClassMethodDebug(
            'IsolateTaskService',
            '_handleTaskRegistration',
            'Received task registration from worker isolate: $taskName with taskId: $taskId',
          );

          registerTask(
            taskId: taskId,
            taskName: taskName,
            inputData: inputData,
            priority: priority,
            isReferenceTask: isReferenceTask,
            networkRequired: networkRequired,
          ).then((_) {
            RILogger.printClassMethodDebug(
              'IsolateTaskService',
              '_handleTaskRegistration',
              'Successfully registered task from worker isolate: $taskName',
            );
          });
        }
      }
    } catch (e, stackTrace) {
      Log.e(
        name: 'IsolateTaskService',
        ['Error handling task registration from worker isolate', e, stackTrace],
      );
    }
  }

  /// Add task to queue
  void _enqueueTask(_QueuedTask task) {
    _taskQueue.add(task);
    RILogger.printClassMethodDebug(
      'IsolateTaskService',
      '_enqueueTask',
      'Task queued: ${task.taskName}, queue size: ${_taskQueue.length}',
    );
  }

  /// Safe initialization in background
  Future<void> _safeInitialize() async {
    try {
      await initialize();
    } catch (e) {
      RILogger.printError('Error in safe initialization', e);
    }
  }

  /// Process tasks in the queue
  Future<void> _processQueuedTasks() async {
    if (_taskQueue.isEmpty) {
      return;
    }

    RILogger.printClassMethodDebug(
      'IsolateTaskService',
      '_processQueuedTasks',
      'Processing ${_taskQueue.length} queued tasks',
    );

    final tasks = List<_QueuedTask>.from(_taskQueue);
    _taskQueue.clear();

    for (final task in tasks) {
      try {
        await registerTask(
          taskId: task.taskId,
          taskName: task.taskName,
          inputData: task.inputData,
          priority: task.priority,
          isReferenceTask: task.isReferenceTask,
          networkRequired: task.networkRequired,
        );

        RILogger.printClassMethodDebug(
          'IsolateTaskService',
          '_processQueuedTasks',
          'Successfully processed queued task: ${task.taskName}',
        );
      } catch (e, stackTrace) {
        Log.e(
          name: 'IsolateTaskService',
          ['Error processing queued task ${task.taskName}', e, stackTrace],
        );

        // Put task back in queue if processing fails
        _enqueueTask(task);
      }
    }
  }

  /// Clean up resources and close isolate
  ///
  /// [reinitialize]: Whether to reinitialize after cleanup
  Future<void> dispose({bool reinitialize = false}) async {
    try {
      RILogger.printClassMethodDebug(
        'IsolateTaskService',
        'dispose',
        'Disposing IsolateTaskService, reinitialize: $reinitialize',
      );

      // Close ReceivePort
      _taskRegistrationPort.close();

      // Clean up ResilientIsolate
      await _resilientIsolate.dispose();

      if (reinitialize) {
        // Reinitialize
        await initialize();
      }
    } catch (e, stackTrace) {
      Log.e(
        name: 'IsolateTaskService',
        ['Error disposing IsolateTaskService', e, stackTrace],
      );
    }
  }

  /// Helper methods for backward compatibility

  Future<bool> encryptToken(String auth) async {
    return SharedPreferencesStore.encryptToken(auth);
  }

  Future<String?> getDecryptToken() async {
    return SharedPreferencesStore.getDecryptToken();
  }

  Future<bool> encryptMetadata(dynamic metadata) async {
    return SharedPreferencesStore.encryptMetadata(metadata);
  }

  Future<dynamic> getDecryptMetadata() async {
    return SharedPreferencesStore.getDecryptMetadata();
  }

  Future<void> clearAllMessageResults() async {
    try {
      SharedPreferencesStore.clearAllMessageResults();
    } catch (e) {
      debugPrint('Error clearing message result tasks: $e');
    }
  }

  Future<void> clean() async {
    unawaited(SharedPreferencesStore.clearAllTasks());
  }

  /// Handle app lifecycle state changes
  ///
  /// This method is called from main.dart when the app's lifecycle state changes.
  /// It forwards the event to ResilientIsolate and performs additional actions when app resumes.
  Future<void> handleAppLifecycleState(AppLifecycleState state) async {
    if (!isInitialized) {
      RILogger.printClassMethodDebug(
        'IsolateTaskService',
        'handleAppLifecycleState',
        'IsolateTaskService not initialized, ignoring lifecycle state change to $state',
      );
      return;
    }

    RILogger.printClassMethodDebug(
      'IsolateTaskService',
      'handleAppLifecycleState',
      'Handling app lifecycle state change to $state',
    );

    // When app resumes from background, double check message status
    if (state == AppLifecycleState.resumed) {
      RILogger.printClassMethodDebug(
        'IsolateTaskService',
        'handleAppLifecycleState',
        'App resumed from background, triggering double check message status',
      );

      // THAY ĐỔI: Thêm log chi tiết
      Log.d(
        name: 'IsolateTaskService',
        '[APP_LIFECYCLE] App resumed from background, triggering double check message status',
      );

      try {
        // Không sử dụng unawaited để đảm bảo doubleCheckMessageStatus hoàn thành trước khi tiếp tục
        await doubleCheckMessageStatus();

        RILogger.printClassMethodDebug(
          'IsolateTaskService',
          'handleAppLifecycleState',
          'Double check message status completed successfully',
        );
      } catch (e, stackTrace) {
        RILogger.printError(
          'Error during double check message status in handleAppLifecycleState',
          e,
          stackTrace,
        );
      }
    }

    // Forward the event to ResilientIsolat
    _resilientIsolate.handleAppLifecycleState(state);
  }

  /// Get stream of app lifecycle state change events
  Stream<AppLifecycleState> get appLifecycleStateStream =>
      _resilientIsolate.appLifecycleStateStream;

  /// Double check message status and update pending messages to failed if they are not in active tasks
  ///
  /// This method:
  /// 1. Retrieves all tasks from SharedPreferencesStore
  /// 2. Filters message-related tasks
  /// 3. Extracts message references from these tasks
  /// 4. Calls DoubleCheckMessageStatusUseCase to update message statuses
  Future<void> doubleCheckMessageStatus() async {
    try {
      RILogger.printClassMethodDebug(
        'IsolateTaskService',
        'doubleCheckMessageStatus',
        'Starting to double check message statuses',
      );

      // Create an empty list of active message refs by default
      final activeMessageRefs = <String>[];

      // Get all tasks from SharedPreferencesStore
      final tasks = await SharedPreferencesStore.loadAllTasks();

      if (tasks.isNotEmpty) {
        // Filter only pending tasks
        final pendingTasks = tasks.where((task) => task.status == TaskStatus.pending).toList();

        if (pendingTasks.isEmpty) {
          RILogger.printClassMethodDebug(
            'IsolateTaskService',
            'doubleCheckMessageStatus',
            'No pending tasks found, will update all pending messages to failed',
          );
        } else {
          RILogger.printClassMethodDebug(
            'IsolateTaskService',
            'doubleCheckMessageStatus',
            'Found ${pendingTasks.length} pending tasks out of ${tasks.length} total tasks',
          );

          // List of task names related to message sending
          final messageTaskNames = [
            TaskNameEnum.sendMessage.value,
            TaskNameEnum.sendQuoteMessage.value,
            TaskNameEnum.editMessage.value,
            TaskNameEnum.sendForwardMessage.value,
            TaskNameEnum.compressVideoMessage.value,
            TaskNameEnum.compressAndUploadImages.value,
            TaskNameEnum.compressAndUploadVideoMessage.value,
            TaskNameEnum.sendMessageResult.value,
          ];

          // Filter message-related tasks that are in pending status
          final messageTasks = pendingTasks
              .where(
                (task) => messageTaskNames.contains(task.name),
              )
              .toList();

          if (messageTasks.isNotEmpty) {
            // Extract message references from tasks
            for (final task in messageTasks) {
              final ref = task.inputData['ref'] as String?;
              if (ref != null && ref.isNotEmpty) {
                activeMessageRefs.add(ref);
              }
            }

            if (activeMessageRefs.isNotEmpty) {
              RILogger.printClassMethodDebug(
                'IsolateTaskService',
                'doubleCheckMessageStatus',
                'Found ${activeMessageRefs.length} active message refs from pending tasks',
              );
            } else {
              RILogger.printClassMethodDebug(
                'IsolateTaskService',
                'doubleCheckMessageStatus',
                'No active message refs found in pending tasks, will update all pending messages to failed',
              );
            }
          } else {
            RILogger.printClassMethodDebug(
              'IsolateTaskService',
              'doubleCheckMessageStatus',
              'No message-related pending tasks found, will update all pending messages to failed',
            );
          }
        }
      } else {
        RILogger.printClassMethodDebug(
          'IsolateTaskService',
          'doubleCheckMessageStatus',
          'No tasks found in SharedPreferencesStore, will update all pending messages to failed',
        );
      }

      // Call usecase to update message statuses
      try {
        final usecase = getIt<DoubleCheckMessageStatusUseCase>();
        final input = DoubleCheckMessageStatusInput(
          activeMessageRefs: activeMessageRefs,
        );
        final output = await usecase.execute(input);

        RILogger.printClassMethodDebug(
          'IsolateTaskService',
          'doubleCheckMessageStatus',
          'Successfully updated ${output.updatedCount} pending messages to failed',
        );
      } catch (e, stackTrace) {
        Log.e(
          name: 'IsolateTaskService',
          ['Error executing DoubleCheckMessageStatusUseCase', e, stackTrace],
        );
      }
    } catch (e, stackTrace) {
      Log.e(
        name: 'IsolateTaskService',
        ['Error during double check message status', e, stackTrace],
      );
    }
  }
}

/// Class to represent a task in the queue
class _QueuedTask {
  final String taskName;
  final String taskId;
  final Map<String, dynamic> inputData;
  final TaskPriority priority;
  final bool isReferenceTask;
  final bool networkRequired;

  _QueuedTask({
    required this.taskName,
    required this.taskId,
    required this.inputData,
    this.priority = TaskPriority.medium,
    this.isReferenceTask = false,
    this.networkRequired = true,
  });
}
