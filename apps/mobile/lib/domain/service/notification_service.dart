import 'dart:convert';
import 'dart:io';

import 'package:app_core/core.dart';
import 'package:auth/auth.dart' hide Config;
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart' show debugPrint;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';

import '../../data/notification_data.dart';
import '../../firebase_options.dart' show DefaultFirebaseOptions;
import '../builder/android_message_notification_builder.dart';
import '../builder/android_reaction_notification_builder.dart';
import '../builder/android_ziichat_notification_builder.dart';
import '../handler/notification/notification_channel_handler.dart';
import '../handler/notification/notification_handler.dart';
import '../handler/notification/notification_translation_handler.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

@pragma('vm:entry-point')
void localNotificationBackground(NotificationResponse notificationResponse) {
  debugPrint('localNotificationBackground: ${notificationResponse.payload}');
}

@pragma('vm:entry-point')
Future<void> firebaseMessagingHandle(RemoteMessage message) async {
  return _commonHandle(message, false);
}

@pragma('vm:entry-point')
Future<void> backgroundFirebaseMessagingHandle(RemoteMessage message) async {
  debugPrint('backgroundFirebaseMessagingHandle: ${message}');
  return _commonHandle(message, true);
}

Future<void> _commonHandle(RemoteMessage message, bool background) async {
  debugPrint('firebaseMessagingHandle: ${message.data.toString()}');

  if (message.data['notificationCount'] != null) {
    try {
      _handleUpdateNotificationCount(message.data['notificationCount'] as int);
    } catch (_) {}
    return;
  }

  await initFirebaseApp();
  await NotificationTranslationHandler().init();

  const initializationSettings = InitializationSettings(
    android: AndroidInitializationSettings('ic_ziichat_notification'),
  );

  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveBackgroundNotificationResponse: localNotificationBackground,
    onDidReceiveNotificationResponse: (response) =>
        handleNotificationClick(response, false),
  );

  final rawData = message.data;
  var notificationData;
  if (rawData['data'] == null) {
    notificationData = NotificationData.fromJson(rawData);
  } else {
    notificationData = NotificationData.fromJson(
      jsonDecode(rawData['data']),
    );
  }

  await showLocalNotificationInBackground(
    flutterLocalNotificationsPlugin,
    notificationData,
    background,
  );

  return Future.value();
}

Future<void> initFirebaseApp() async {
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  } catch (e) {
    await Firebase.initializeApp();
    debugPrint('initFirebaseApp: ${e}');
  }
}

void _handleUpdateNotificationCount(int count) {
  NotificationHandler.updateAndroidBadge(count);
}

Future<void> showLocalNotificationInBackground(
  FlutterLocalNotificationsPlugin fln,
  NotificationData data,
  bool background,
) async {
  if (!background) {
    final notificationHandler = NotificationHandler();
    if (!notificationHandler.needShowNotification(data)) return;
  }

  final manager = NotificationChannelHandler();

  if (data.hasReaction()) {
    await _setupReactionNotification(manager, data, fln);
    return;
  }
  if (data.hasChannelViewAction()) {
    await _setupMessageNotification(manager, data, fln);
    return;
  }
  if (data.hasNotificationFromZiiChat()) {
    await _setupZiiChatNotification(manager, data, fln);
  }
}

Future<void> _setupZiiChatNotification(
  NotificationChannelHandler manager,
  NotificationData data,
  FlutterLocalNotificationsPlugin fln,
) async {
  var channelName = "ZiiChat";
  await manager.createChannelIfNeeded(
    channelId: data.userId!,
    channelName: channelName,
    channelDescription: 'Notifications of user: ${data.channelName}',
    importance: Importance.high,
    enableVibration: true,
    playSound: true,
    sound: 'notification',
  );
  var platformDetails = NotificationDetails(
    android: await AndroidZiiChatNotificationBuilder.createNotificationDetails(
      data,
    ),
  );
  await fln.show(
    RandomUtils.randomInt(),
    channelName,
    await data.getReplacementContent(),
    platformDetails,
    payload: jsonEncode(data.toJson()),
  );
}

Future<void> _setupReactionNotification(
  NotificationChannelHandler manager,
  NotificationData data,
  FlutterLocalNotificationsPlugin fln,
) async {
  await manager.createChannelIfNeeded(
    channelId: GlobalConfig.REACTIONS_CHANNEL_ID,
    channelName: NotificationTranslationHandler().appLocalizations.reactions,
    channelDescription: 'Notifications for reactions',
    importance: Importance.high,
    enableVibration: true,
    playSound: true,
    sound: 'notification',
  );
  var platformDetails = NotificationDetails(
    android: await AndroidReactionNotificationBuilder.createNotificationDetails(
      data,
    ),
  );
  await fln.show(
    RandomUtils.randomInt(),
    null,
    await data.getReplacementContent(),
    platformDetails,
    payload: jsonEncode(data.toJson()),
  );
}

Future<void> _setupMessageNotification(
  NotificationChannelHandler manager,
  NotificationData data,
  FlutterLocalNotificationsPlugin fln,
) async {
  await manager.createChannelIfNeeded(
    channelId: data.channelId!,
    channelName: data.channelName!,
    channelDescription: data.channelName!,
    importance: Importance.high,
    enableVibration: true,
    playSound: true,
    sound: 'notification',
  );
  var platformDetails = NotificationDetails(
    android:
        await AndroidMessageNotificationBuilder.createNotificationDetails(data),
  );
  final oldActiveNotification =
      await manager.getNotificationByChannelId(data.channelId!);
  await fln.show(
    oldActiveNotification != null
        ? oldActiveNotification.id!
        : RandomUtils.randomInt(),
    data.title,
    data.body,
    platformDetails,
    payload: jsonEncode(data.toJson()),
  );
}

Future<void> setupFlutterNotifications() async {
  const initializationSettings = InitializationSettings(
    android: AndroidInitializationSettings('ic_ziichat_notification'),
  );
  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveBackgroundNotificationResponse: localNotificationBackground,
    onDidReceiveNotificationResponse: (response) =>
        handleNotificationClick(response, false),
  );
  if (Platform.isAndroid) {
    try {
      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions();
      await FirebaseMessaging.instance.setAutoInitEnabled(true);
      var token = await FirebaseMessaging.instance.getToken();
      Config.getInstance().notificationToken = token;
      Log.e(name: 'notificationToken', token);
    } catch (e) {
      Log.e(name: 'setupFlutterNotifications', e);
    }
  }
}

void handleNotificationClick(
  NotificationResponse details, [
  bool openApp = true,
]) async {
  try {
    if (!openApp) {
      GetIt.instance
          .get<IsAuthenticatedFutureUseCase>()
          .execute(IsAuthenticatedFutureInput())
          .then((value) {
        if (!value.isAuthenticated) {
          return;
        }
        final data = NotificationData.fromJson(jsonDecode(details.payload!));
        final notificationHandler = NotificationHandler();
        notificationHandler.onNotificationClicked(data);
      });
      return;
    }

    final data = NotificationData.fromJson(jsonDecode(details.payload!));
    final notificationHandler = NotificationHandler();
    notificationHandler.onNotificationClicked(data);
  } catch (e) {
    debugPrint('handleNotificationClick exception: ${e.toString()}');
  }
}
