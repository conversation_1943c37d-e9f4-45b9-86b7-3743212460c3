/// My new Flutter package
library;

export 'package:receive_sharing_intent/receive_sharing_intent.dart';
export 'package:share_to_suggestion/platform_interface/types/share_to_suggestion_request.dart';

export 'src/common/config/config.dart';
export 'src/data/extension/share_to_file_extension.dart';
export 'src/domain/singletons/delete_suggestion.dart';
export 'src/domain/singletons/pending_intent_handler.dart';
export 'src/domain/singletons/share_to.dart';
export 'src/domain/usecase/suggestion/add_suggestion_use_case.dart';
export 'src/ui/bloc/share_to_view_bloc.dart';
export 'src/ui/presentation/share_to/share_to_view_page.dart';
