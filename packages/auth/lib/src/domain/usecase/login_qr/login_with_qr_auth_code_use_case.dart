import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';

@Injectable()
class LoginWithQRAuthCodeUseCase extends BaseFutureUseCase<
    LoginWithQRAuthCodeUseCaseInput, LoginWithQRAuthCodeUseCaseOutput> {
  LoginWithQRAuthCodeUseCase();

  @override
  Future<LoginWithQRAuthCodeUseCaseOutput> buildUseCase(
    LoginWithQRAuthCodeUseCaseInput input,
  ) async {
    var request = V3LoginWithQRAuthCodeRequestBuilder()
      ..reqId = input.requestId
      ..reqVerifier = input.reqVerifier;
    final response =
        await AuthClient().instance.loginWithQRAuthCode(body: request.build());

    return LoginWithQRAuthCodeUseCaseOutput(response: response.data!);
  }
}

class LoginWithQRAuthCodeUseCaseInput extends BaseInput {
  LoginWithQRAuthCodeUseCaseInput({
    required this.requestId,
    required this.reqVerifier,
  });

  final String requestId;
  final String reqVerifier;
}

class LoginWithQRAuthCodeUseCaseOutput extends BaseOutput {
  LoginWithQRAuthCodeUseCaseOutput({required this.response});

  final V3LoginWithQRAuthCodeResponse response;
}
