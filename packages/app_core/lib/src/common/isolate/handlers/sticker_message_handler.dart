import 'package:shared/shared.dart';

import '../../../data/source/api/client/isolate_api_client.dart';
import '../../network/retry_manager.dart';
import '../input/worker_send_sticker_input.dart';
import '../output/worker_send_message_output.dart';
import 'base_message_handler.dart';

/// Specialized handler for sticker messages
class StickerMessageHandler extends BaseMessageHandler<WorkerSendStickerInput> {
  StickerMessageHandler({
    required super.apiClient,
    required super.retryManager,
  });

  @override
  WorkerSendStickerInput parseInput(Map<String, dynamic> inputData) {
    return WorkerSendStickerInput.fromJson(inputData);
  }

  @override
  String getApiEndpoint(WorkerSendStickerInput input) {
    return '/Message/' + (input.isDm() ? 'SendDMMessageSticker' : 'SendMessageSticker');
  }

  @override
  Future<WorkerSendMessageOutput> executeMessageOperation(
    WorkerSendStickerInput input,
    DateTime createTime,
  ) async {
    return await makeRetryableApiCall(
      input: input,
      createTime: createTime,
      apiCall: () => apiClient.post(
        getApiEndpoint(input),
        body: input.toRequest(),
      ),
    );
  }

  /// Legacy method for backward compatibility
  Future<WorkerSendMessageOutput> handleStickerMessage(
    Map<String, dynamic> inputData,
  ) async {
    return await handleMessage(inputData);
  }
}
