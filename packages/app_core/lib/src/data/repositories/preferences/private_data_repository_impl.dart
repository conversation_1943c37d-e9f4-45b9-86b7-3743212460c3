import 'package:injectable/injectable.dart' hide Order;

import '../../../../core.dart';
import '../database/database.dart';
import '../database/generated/objectbox.g.dart';

@LazySingleton(as: PrivateDataRepository)
class PrivateDataRepositoryImpl extends PrivateDataRepository {
  PrivateDataRepositoryImpl(this._store);

  final PrivateDataStore _store;

  Box<PrivateData> get _privateDataBox => _store.box<PrivateData>();

  Box<ChannelPrivateData> get _channelBox => _store.box<ChannelPrivateData>();

  Box<UserPrivateData> get _userBox => _store.box<UserPrivateData>();

  Box<CallLogPrivateData> get _callLogBox => _store.box<CallLogPrivateData>();

  @override
  int insert(PrivateData privateData) {
    final existingQuery = _privateDataBox
        .query(PrivateData_.userId.equals(privateData.userId))
        .build();
    final existing = existingQuery.findFirst();
    existingQuery.close();
    if (existing != null) {
      privateData.id = existing.id;
    }

    return _privateDataBox.put(privateData);
  }

  @override
  PrivateData? getPrivateData() {
    final query = _privateDataBox
        .query(
          PrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? ''),
        )
        .build();
    final result = query.findFirst();
    query.close();
    return result;
  }

  @override
  bool delete() {
    final query = _privateDataBox
        .query(
          PrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? ''),
        )
        .build();
    final privateData = query.findFirst();
    query.close();
    if (privateData != null) {
      return _privateDataBox.remove(privateData.id);
    }
    return false;
  }

  @override
  List<ChannelPrivateData> getChannels() {
    return _channelBox
        .query(
          ChannelPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? ''),
        )
        .build()
        .find();
  }

  @override
  List<UserPrivateData> getUsers() {
    return _userBox
        .query(
          UserPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? ''),
        )
        .build()
        .find();
  }

  @override
  List<UserPrivateData> getUsersByListUserId(List<String> listUserId) {
    return _userBox
        .query(
          UserPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(UserPrivateData_.userId.oneOf(listUserId)),
        )
        .build()
        .find();
  }

  @override
  List<CallLogPrivateData> getCallLogs() {
    return _callLogBox
        .query(
          CallLogPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? ''),
        )
        .build()
        .find();
  }

  @override
  Stream<List<ChannelPrivateData>> getStreamChannels() {
    return _channelBox
        .query(
          ChannelPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(ChannelPrivateData_.pinned.equals(true)),
        )
        .order(ChannelPrivateData_.channelId, flags: Order.descending)
        .watch(triggerImmediately: true)
        .map((q) => q.find());
  }

  @override
  Stream<List<UserPrivateData>> getStreamUsers() {
    return _userBox
        .query(
          UserPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? ''),
        )
        .watch(triggerImmediately: true)
        .map((q) => q.find());
  }

  @override
  Stream<List<CallLogPrivateData>> getStreamCallLogs() {
    return _callLogBox
        .query(
          CallLogPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? ''),
        )
        .order(CallLogPrivateData_.createTime, flags: Order.descending)
        .watch(triggerImmediately: true)
        .map((q) => q.find());
  }

  @override
  int insertChannel(ChannelPrivateData channel) {
    final privateData = getPrivateData();
    if (privateData != null) {
      channel..privateData.target = privateData;
    }
    final existingQuery = _channelBox
        .query(
          ChannelPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(ChannelPrivateData_.channelId.equals(channel.channelId)),
        )
        .build();
    final existing = existingQuery.findFirst();
    existingQuery.close();
    if (existing != null) {
      channel.id = existing.id;
    }
    return _channelBox.put(channel);
  }

  @override
  int insertUser(UserPrivateData user) {
    final privateData = getPrivateData();
    if (privateData != null) {
      user..privateData.target = privateData;
    }
    final existingQuery = _userBox
        .query(
          UserPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(UserPrivateData_.userId.equals(user.userId)),
        )
        .build();
    final existing = existingQuery.findFirst();
    existingQuery.close();
    if (existing != null) {
      user.id = existing.id;
    }
    return _userBox.put(user);
  }

  @override
  int upsert(UserPrivateData userPrivateData) {
    final existingQuery = _userBox
        .query(
          UserPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(UserPrivateData_.userId.equals(userPrivateData.userId)),
        )
        .build();
    final existing = existingQuery.findFirst();
    existingQuery.close();
    if (existing != null) {
      userPrivateData.id = existing.id;
    }
    return _userBox.put(userPrivateData);
  }

  @override
  int insertCallLog(CallLogPrivateData callLog) {
    final oldLog = getCallLog(callLog.callId);
    if (oldLog != null) {
      callLog.id = oldLog.id;
      callLog..privateData.target = oldLog.privateData.target;
    } else {
      final privateData = getPrivateData();
      if (privateData != null) {
        callLog..privateData.target = privateData;
      }
    }

    return _callLogBox.put(callLog);
  }

  @override
  bool deleteChannel(String channelId) {
    final query = _channelBox
        .query(
          ChannelPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(ChannelPrivateData_.channelId.equals(channelId)),
        )
        .build();

    final channel = query.findFirst();
    query.close();
    if (channel != null) {
      return _channelBox.remove(channel.id);
    }
    return false;
  }

  @override
  bool deleteUser(String userId) {
    final query = _userBox
        .query(
          UserPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(UserPrivateData_.userId.equals(userId)),
        )
        .build();

    final user = query.findFirst();
    query.close();
    if (user != null) {
      return _userBox.remove(user.id);
    }
    return false;
  }

  @override
  bool deleteCallLog(String callLogId) {
    final query = _callLogBox
        .query(
          CallLogPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(CallLogPrivateData_.callId.equals(callLogId)),
        )
        .build();

    final callLog = query.findFirst();
    query.close();
    if (callLog != null) {
      return _callLogBox.remove(callLog.id);
    }
    return false;
  }

  @override
  int updateChannel(ChannelPrivateData channel) {
    final privateData = getPrivateData();
    if (privateData != null) {
      channel..privateData.target = privateData;
      // final existing = _channelBox.get(channel.id);
      // if (existing != null && channel.version <= existing.version) {
      //   return -1;
      // }
    }
    final existingQuery = _channelBox
        .query(
          ChannelPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(ChannelPrivateData_.channelId.equals(channel.channelId)),
        )
        .build();
    final existing = existingQuery.findFirst();
    existingQuery.close();
    if (existing != null) {
      channel.id = existing.id;
    }
    return _channelBox.put(channel);
  }

  @override
  int updateUser(UserPrivateData user) {
    final privateData = getPrivateData();
    if (privateData != null) {
      user..privateData.target = privateData;
    }
    final existingQuery = _userBox
        .query(
          UserPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(UserPrivateData_.userId.equals(user.userId)),
        )
        .build();
    final existing = existingQuery.findFirst();
    existingQuery.close();
    if (existing != null) {
      user.id = existing.id;
    }
    return _userBox.put(user);
  }

  @override
  int updateCallLog(CallLogPrivateData callLog) {
    final privateData = getPrivateData();
    if (privateData != null) {
      callLog..privateData.target = privateData;
      final existing = _callLogBox.get(callLog.id);
      if (existing != null && callLog.version <= existing.version) {
        return -1;
      }
    }
    return _callLogBox.put(callLog);
  }

  @override
  ChannelPrivateData? getChannel(String channelId) {
    final query = _channelBox
        .query(
          ChannelPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(ChannelPrivateData_.channelId.equals(channelId)),
        )
        .build();
    final result = query.findFirst();
    query.close();
    return result;
  }

  @override
  UserPrivateData? getUser(String userId) {
    final query = _userBox
        .query(
          UserPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(UserPrivateData_.userId.equals(userId)),
        )
        .build();
    final result = query.findFirst();
    query.close();
    return result;
  }

  @override
  CallLogPrivateData? getCallLog(String callId) {
    final query = _callLogBox
        .query(
          CallLogPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(CallLogPrivateData_.callId.equals(callId)),
        )
        .build();
    final result = query.findFirst();
    query.close();
    return result;
  }

  @override
  Stream<ChannelPrivateData?> getStreamChannel(String channelId) {
    return _channelBox
        .query(
          ChannelPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(ChannelPrivateData_.channelId.equals(channelId)),
        )
        .watch(triggerImmediately: true)
        .map((q) => q.findFirst());
  }

  @override
  Stream<UserPrivateData?> getStreamUser(String userId) {
    return _userBox
        .query(
          UserPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(UserPrivateData_.userId.equals(userId)),
        )
        .watch(triggerImmediately: true)
        .map((q) => q.findFirst());
  }

  @override
  Stream<CallLogPrivateData?> getStreamCallLog(String callId) {
    return _callLogBox
        .query(
          CallLogPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(CallLogPrivateData_.callId.equals(callId)),
        )
        .watch(triggerImmediately: true)
        .map((q) => q.findFirst());
  }

  @override
  int? getMaxSortChannel() {
    final query = _channelBox
        .query(
          ChannelPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(ChannelPrivateData_.pinned.equals(true)),
        )
        .order(ChannelPrivateData_.sort, flags: Order.descending)
        .build();
    final result = query.findFirst();
    query.close();
    return result?.sort;
  }

  @override
  bool deleteSession(String sessionKey) {
    final userQuery =
        _userBox.query(UserPrivateData_.sessionKey.equals(sessionKey)).build();

    userQuery.remove();

    userQuery.close();

    final channelQuery = _channelBox
        .query(ChannelPrivateData_.sessionKey.equals(sessionKey))
        .build();

    channelQuery.remove();

    channelQuery.close();

    final callLogQuery = _callLogBox
        .query(CallLogPrivateData_.sessionKey.equals(sessionKey))
        .build();

    callLogQuery.remove();

    callLogQuery.close();

    final privateDataQuery = _privateDataBox
        .query(PrivateData_.sessionKey.equals(sessionKey))
        .build();

    privateDataQuery.remove();

    privateDataQuery.close();

    return true;
  }

  @override
  List<UserPrivateData> getUsersByKeyword(String keyword) {
    return _userBox
        .query(
          UserPrivateData_.sessionKey
              .equals(Config.getInstance().activeSessionKey ?? '')
              .and(UserPrivateData_.aliasName.contains(keyword)),
        )
        .build()
        .find();
  }
}
