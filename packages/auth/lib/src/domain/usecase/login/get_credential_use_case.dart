import 'dart:convert';

import 'package:auth_api/auth_api.dart';
import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import 'package:passkeys/authenticator.dart';
import 'package:passkeys/types.dart';
import 'package:shared/shared.dart';
import 'package:webauthn/webauthn.dart';

@Injectable()
class GetCredentialUseCase
    extends BaseFutureUseCase<GetCredentialInput, GetCredentialOutput> {
  GetCredentialUseCase();

  @override
  Future<GetCredentialOutput> buildUseCase(GetCredentialInput input) async {
    try {
      bool shouldRegisterWithOldAPI =
          await checkAuthWithOldAPI(input.passkeyMigrated);

      final response = shouldRegisterWithOldAPI
          ? await _loginWithWebAPI(input)
          : await _loginWithPasskeys(input);

      return GetCredentialOutput(
        ok: true,
        reqId: response.reqId,
        reqVerifier: response.reqVerifier,
        assertion: response.assertion,
      );
    } on PasskeyAuthCancelledException catch (e) {
      return GetCredentialOutput(
        ok: false,
        exception: AppPasskeyAuthCancelledException(
          code: e.code,
          message: e.message,
          details: e.details,
        ),
      );
    } on NoCredentialsAvailableException {
      return GetCredentialOutput(
        ok: false,
        exception: AppNoCredentialsAvailableException(),
      );
    } on UnhandledAuthenticatorException catch (e) {
      return GetCredentialOutput(
        ok: false,
        exception: AppUnhandledAuthenticatorException(
          e.code,
          e.message,
          e.details,
        ),
      );
    } on PlatformException catch (e) {
      if (e.code == 'noKeyFound') {
        return GetCredentialOutput(
          ok: false,
          exception: AppPasskeyAuthCancelledException(
            code: e.code,
            message: e.message,
            details: e.details,
          ),
        );
      }
      return GetCredentialOutput(
        ok: false,
        exception: AppUnhandledAuthenticatorException(
          e.code,
          e.message,
          e.details,
        ),
      );
    } catch (e) {
      return GetCredentialOutput(
        ok: false,
        exception: AppUnhandledAuthenticatorException(
          'UNKNOWN',
          e.toString(),
          null,
        ),
      );
    }
  }

  Future<V3LoginWithUserKeyRequest> _loginWithPasskeys(
    GetCredentialInput input,
  ) async {
    final PasskeyAuthenticator passkeyAuthenticator = PasskeyAuthenticator();

    final List<CredentialType> allowCredentials =
        input.credentialRequestOptions.allowCredentials
                ?.map(
                  (credential) => CredentialType(
                    id: credential.id ?? '',
                    type: credential.type ?? '',
                    transports: [
                      "internal",
                    ],
                  ),
                )
                .toList() ??
            [];

    final loginResult = await passkeyAuthenticator.authenticate(
      AuthenticateRequestType(
        relyingPartyId: input.credentialRequestOptions.rpId ?? '',
        challenge: input.credentialRequestOptions.challenge ?? '',
        timeout: input.credentialRequestOptions.timeout ?? 60000,
        userVerification:
            input.credentialRequestOptions.userVerification ?? 'preferred',
        allowCredentials: allowCredentials,
        mediation: MediationType.Required,
        preferImmediatelyAvailableCredentials: true,
      ),
    );

    final assertionResponse = CommonAssertionResponseBuilder()
      ..clientDataJSON = loginResult.clientDataJSON
      ..authenticatorData = loginResult.authenticatorData
      ..signature = loginResult.signature
      ..userHandle = loginResult.userHandle
      ..build();

    final assertion = CommonAssertionResultBuilder()
      ..id = loginResult.id
      ..rawId = loginResult.rawId
      ..type = 'public-key'
      ..response = assertionResponse;

    final loginWithUserKeyRequest = V3LoginWithUserKeyRequestBuilder()
      ..assertion = assertion
      ..reqId = input.reqId // Sử dụng reqId từ input
      ..reqVerifier = input.reqVerifier;

    return loginWithUserKeyRequest.build();
  }

  Future<V3LoginWithUserKeyRequest> _loginWithWebAPI(
    GetCredentialInput input,
  ) async {
    final credentialCreationOptions = input.credentialRequestOptions;

    final credentialRequestOptions = jsonDecode(
      standardSerializers.toJson(
        CommonPublicKeyCredentialRequestOptions.serializer,
        credentialCreationOptions,
      ),
    ) as Map<String, dynamic>;

    credentialRequestOptions['authenticatorSelection'] = {
      'attachment': 'platform',
      'requireResidentKey': true,
      'residentKeyRequirement': 'preferred',
    };

    final webApi = WebAPI();

    final rpOptions = CredentialRequestOptions.fromJson(<String, dynamic>{
      'publicKey': credentialRequestOptions,
    });

    final (clientData, getAssertionOptions) =
        await webApi.createGetAssertionOptions(
      'https://ziichat.com',
      rpOptions,
      true,
    );

    final attestation =
        await Authenticator.handleGetAssertion(getAssertionOptions);

    final loginResult =
        await webApi.createAssertionResponse(clientData, attestation);

    final assertionResponse = CommonAssertionResponseBuilder()
      ..clientDataJSON = b64e(loginResult.response.clientDataJSON)
      ..authenticatorData = b64e(loginResult.response.authenticatorData)
      ..signature = b64e(loginResult.response.signature)
      ..userHandle = b64e(loginResult.response.userHandle)
      ..build();

    final assertion = CommonAssertionResultBuilder()
      ..id = loginResult.id
      ..rawId = b64e(loginResult.rawId)
      ..type = loginResult.type.value
      ..response = assertionResponse;

    final loginWithUserKeyRequest = V3LoginWithUserKeyRequestBuilder()
      ..assertion = assertion
      ..reqId = input.reqId
      ..reqVerifier = input.reqVerifier;

    return loginWithUserKeyRequest.build();
  }
}

class GetCredentialInput extends BaseInput {
  GetCredentialInput({
    required this.credentialRequestOptions,
    required this.reqId,
    required this.reqVerifier,
    required this.passkeyMigrated,
  });

  final CommonPublicKeyCredentialRequestOptions credentialRequestOptions;
  final String reqId;
  final String reqVerifier;
  final bool passkeyMigrated;
}

class GetCredentialOutput extends BaseOutput {
  GetCredentialOutput({
    required this.ok,
    this.reqId,
    this.reqVerifier,
    this.assertion,
    this.exception,
  });

  final bool ok;
  final String? reqId;
  final String? reqVerifier;
  final CommonAssertionResult? assertion;
  final AppAuthenticatorException? exception;
}
