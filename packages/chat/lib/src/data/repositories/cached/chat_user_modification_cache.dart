import 'package:cached_annotation/cached_annotation.dart';

part 'chat_user_modification_cache.cached.dart';

@WithCache(useStaticCache: true)
abstract mixin class ChatUserModificationCache
    implements _$ChatUserModificationCache {
  factory ChatUserModificationCache() = _ChatUserModificationCache;

  @Cached(
    syncWrite: true,
    ttl: 180,
    limit: 100,
  )
  Future<String> setCache({
    required String sessionKey,
    required String userId,
    @ignore required String updateTime,
    @ignore String? presenceUpdateTime,
  }) async {
    return Future.value('$updateTime/${presenceUpdateTime ?? ''}');
  }

  @CachePeek("setCache")
  String? peekCached({
    required String sessionKey,
    required String userId,
  });

  @ClearCached("setCache")
  void cleanCache();
}
