<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AppGroupId</key>
	<string>$(CUSTOM_GROUP_ID)</string>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>task-identifier</string>
		<string>be.tramckrijte.workmanagerExample.iOSBackgroundAppRefresh</string>
		<string>com.pravera.flutter_foreground_task.refresh</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>mobile</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ShareMedia-$(PRODUCT_BUNDLE_IDENTIFIER)</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>DEVELOPMENT_TEAM</key>
	<string>5MBUW9X833</string>
	<key>FlutterDeepLinkingEnabled</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>We require access to your camera in order to capture and share photos and videos. In addition, you may make video calls and scan QR codes.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Webauthn User Verification</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Need to find a place to share with friends</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Need to find a place to share with friends</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We require access to your microphone in order to record audio and share voice messages and videos with sound.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>We needs permission to add photos and videos to your library to save the content you download.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We require access to your photos in order to share photos and videos from your photo library.
    </string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>The app requires speech recognition access to convert speech into text.</string>
	<key>NSUserActivityTypes</key>
	<array>
		<string>INSendMessageIntent</string>
	</array>
	<key>RTCAppGroupIdentifier</key>
	<string>$(RTC_GROUP_ID)</string>
	<key>RTCScreenSharingExtension</key>
	<string>${RTC_BUNDLE_IDENTIFIER}</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>processing</string>
		<string>fetch</string>
		<string>remote-notification</string>
		<string>audio</string>
		<string>voip</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>PHPhotoLibraryPreventAutomaticLimitedAccessAlert</key>
    <true/>
</dict>
</plist>
