name: app_core
description: Include core structure elements
version: 0.1.0+1
publish_to: none
environment:
  sdk: ^3.4.0
  flutter: ^3.22.0
dependencies:
  bloc: ^9.0.0
  dartx: ^1.2.0
  encrypted_shared_preferences: ^3.0.1
  equatable: ^2.0.7
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.0
  get_it: ^8.0.3
  injectable: ^2.5.0
  json_annotation: ^4.9.0
  rxdart: ^0.28.0
  shared:
    path: ../shared
  shared_preferences: ^2.5.3
  firebase_crashlytics: ^4.3.5
  localization_client:
    git:
      url: **************:ziichatlabs/ziichat-flutter-i18n.git
      ref: main
  user_view_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.29
      path: apis/user_view_api
  websocket_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.23
      path: apis/websocket_api
  notification_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/notification_api
  message_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.23
      path: apis/message_api
  ziichat_ui:
    git:
      url: **************:ziichatlabs/ziichat-flutter-ui.git
      ref: release/0.56.0
  user_setting_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/user_setting_api
  user_report_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/user_report_api
  user_profile_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/user_profile_api
  image_compressor:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.0.29
      path: packages/image_compressor
  search_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.25
      path: apis/search_api
  video_compressor:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.0.43
      path: packages/video_compressor
  filestore_sdk:
    git:
      url: **************:ziichatlabs/filestore-sdk.git
      ref: v0.1.0
  upload_manager:
    path: ../upload_manager
  user_manager:
    path: ../../packages/user_manager
  auth:
    path: ../../packages/auth
  chat:
    path: ../../packages/chat
  search:
    path: ../../packages/search
  call:
    path: ../../packages/call
  sticker:
    path: ../../packages/sticker
  session_migration:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.51.0
      path: packages/session_migration
  objectbox:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: objectbox
      ref: v4.1.0
  objectbox_flutter_libs:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: flutter_libs
      ref: v4.1.0
  path_provider: ^2.1.5
  freezed_annotation: ^3.0.0
  web_socket_channel: ^3.0.2
  connectivity_plus: ^6.1.3
  intl: ^0.19.0
  workmanager:
    git:
      url: https://github.com/ziichatlabs/flutter_workmanager.git
      path: workmanager
      ref: v0.1.0
  share_to:
    path: ../../packages/share_to
  http: ^1.3.0
  dio: ^5.8.0+1
  encrypt: ^5.0.3
  url_launcher: ^6.3.1
  locale_plus: ^1.6.0+1
  flutter_isolate: ^2.0.4
  uuid: ^4.3.3
  synchronized: ^3.1.0+1
dev_dependencies:
  build_runner: ^2.4.15
  flutter_test:
    sdk: flutter
  freezed: ^3.0.6
  injectable_generator: ^2.7.0
  mocktail: ^1.0.4
  very_good_analysis: ^7.0.0
  objectbox_generator:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: generator
      ref: v4.1.0
  json_serializable: ^6.9.1
objectbox:
  output_dir: src/data/repositories/database/generated
dependency_overrides:
  analyzer: 7.3.0
