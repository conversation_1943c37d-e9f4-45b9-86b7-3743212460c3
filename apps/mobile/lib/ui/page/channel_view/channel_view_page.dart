import 'dart:io';

import 'package:apns_notification/apns_notification.dart';
import 'package:auto_route/auto_route.dart';
import 'package:call/call.dart' as call;
import 'package:chat/chat.dart' as chat;
import 'package:fcm_notification/fcm_notification.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../navigation/routes/app_router.gr.dart';
import '../../utils/picker_utils.dart';
import '../crop_avatar_page/crop_avatar_page.dart';
import '../take_photo/take_photo_page.dart';
import '../withTransition.dart';

@RoutePage()
class ChannelViewPage extends StatefulWidget implements PageWithTransition {
  const ChannelViewPage({
    super.key,
    this.workspaceId,
    this.channelId,
    this.userId,
    this.focusableMessageId = '',
    this.initUserData,
    this.fromNotification,
    this.onOpenApp = false,
    this.withTransition = true,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? focusableMessageId;
  final chat.UserViewData? initUserData;
  final bool? fromNotification;
  final bool onOpenApp;
  @override
  final bool withTransition;

  @override
  State<ChannelViewPage> createState() => _ChannelViewPageState();
}

class _ChannelViewPageState extends State<ChannelViewPage>
    with AutoRouteAwareStateMixin {
  var isNavigatingToOther = false;
  final _apnsNotificationPlugin = ApnsNotification();
  final _fcmNotificationPlugin = FCMNotification();
  late bool _onOpenApp = false;

  GlobalKey<chat.ChannelViewPageState> _globalKey = GlobalKey();

  @override
  void initState() {
    _init();
    super.initState();
  }

  void _init() {
    _onOpenApp = widget.onOpenApp;
    handleClearNotification();
  }

  @override
  void didPushNext() {
    _globalKey.currentState?.didPushNext();
    super.didPushNext();
  }

  @override
  void didPopNext() {
    final currentRoute = AutoRouter.of(context).current;
    if (currentRoute.name != ChannelViewRoute.name) return;

    final args = currentRoute.args as ChannelViewRouteArgs;
    if (args.channelId == widget.channelId &&
        args.workspaceId == widget.workspaceId &&
        args.userId == widget.userId) {
      _globalKey.currentState?.didPopNext();
    }
    super.didPopNext();
  }

  void didUpdateWidget(covariant ChannelViewPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!_hasPageUpdated(oldWidget)) return;
    _globalKey = GlobalKey();
    _init();
  }

  bool _hasPageUpdated(ChannelViewPage oldWidget) =>
      oldWidget.channelId != widget.channelId ||
      oldWidget.userId != widget.userId;

  void handleClearNotification() async {
    var channelId = widget.channelId;
    if (channelId == null) {
      var output = await GetIt.instance.get<chat.GetChannelUseCase>().execute(
            chat.GetChannelInput(
              workspaceId: widget.workspaceId,
              channelId: widget.channelId,
              userId: widget.userId,
            ),
          );
      channelId = output.channel?.channelId;
    }

    if (channelId != null) {
      Platform.isIOS
          ? _apnsNotificationPlugin.clearNotificationByThreadId(channelId)
          : _fcmNotificationPlugin.clearNotificationByThreadId(channelId);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_onOpenApp) {
      _onOpenApp = false;
      return SplashPage(nextPage: _buildChannelViewPage());
    }

    return _buildChannelViewPage();
  }

  chat.ChannelViewPage _buildChannelViewPage() {
    return chat.ChannelViewPage(
      key: _globalKey,
      workspaceId: widget.workspaceId,
      channelId: widget.channelId,
      userId: widget.userId,
      focusableMessageId: widget.focusableMessageId,
      initUserData: widget.initUserData,
      onAppbarClicked: onAppbarClicked,
      onBack: onBackButtonClicked,
      onChannelDeleted: removeChannelRoute,
      goToDMMessage: goToDMMessage,
      goToViewAvatar: goToViewAvatar,
      goToTakePhotoRoute: goToTakePhotoRoute,
      goToFullscreenMessage: goToFullscreenMessage,
      goToZiiShortPage: goToZiiShortPage,
      openTakePhotoAndVideoPage: goTakePhotoAndVideoPage,
      onClickTakeChannelAvatarPhoto: onClickTakeChannelAvatarPhoto,
      onClickTapOpenGalleryAvatar: onClickTapOpenGalleryAvatar,
      fromNotification: widget.fromNotification,
      goToMeetingRoom: onGoToCallRoomPage,
    );
  }

  void onAppbarClicked(chat.Channel? channel) {
    if (widget.userId != null) {
      var workspaceId = channel?.workspaceId;
      var channelId = channel?.channelId;
      _navigateToOther(
        () => context.router.push(
          UserProfileRoute(
            workspaceId: (workspaceId != null && workspaceId.isNotEmpty)
                ? workspaceId
                : widget.workspaceId,
            channelId: (channelId != null && channelId.isNotEmpty)
                ? channelId
                : widget.channelId,
            userId: widget.userId!,
          ),
        ),
      );
    } else {
      if (channel != null) {
        _navigateToOther(
          () => context.pushRoute(
            ChannelInfoRoute(channel: channel),
          ),
        );
      }
    }
  }

  void onBackButtonClicked() {
    context.maybePop();
  }

  void goToDMMessage(String userId) {
    _navigateToOther(() => context.pushRoute(ChannelViewRoute(userId: userId)));
  }

  void goToViewAvatar(String avatarUrl) {
    _navigateToOther(
      () => context.pushRoute(
        ImageViewRoute(imageUrl: avatarUrl),
      ),
    );
  }

  void goToFullscreenMessage(
    String messageId,
    int? attachmentIndex,
    String workspaceId,
    String channelId,
  ) {
    context.pushRoute(
      FullscreenViewRoute(
        workspaceId: workspaceId,
        channelId: channelId,
        userId: widget.userId,
        attachmentIndex: attachmentIndex,
        messageId: messageId,
      ),
    );
  }

  Future<void> removeChannelRoute() async {
    AppEventBus.publish(OnGoToHomeEvent(tab: HomeTab.chats));
  }

  Future<void> _navigateToOther(Future<void> Function() function) async {
    if (isNavigatingToOther) return;
    isNavigatingToOther = true;
    await function();
    isNavigatingToOther = false;
  }

  void goToTakePhotoRoute() {
    context
        .pushRoute(TakePhotoAndVideoRoute(nameRouter: ChannelViewRoute.name));
  }

  Future<String?> goToZiiShortPage() async {
    return context.pushRoute(ZiishortRoute());
  }

  void goTakePhotoAndVideoPage() {
    context
        .pushRoute(TakePhotoAndVideoRoute(nameRouter: ChannelViewRoute.name));
  }

  void onClickTakeChannelAvatarPhoto(chat.Channel channel) {
    context.pushRoute(
      TakePhotoRoute(
        outputType: OutputType.avatar,
        avatarType: AvatarType.channel,
      ),
    );
  }

  Future<void> onClickTapOpenGalleryAvatar(chat.Channel channel) async {
    await _openGalleryPicker();
  }

  /// Opens the gallery picker to allow the user to select an image.
  /// After an image is picked, it pushes the `CropAvatarRoute` for further processing.
  ///
  /// If the `CropAvatarRoute` returns:
  /// - `null`: Indicates no result was returned (e.g., the user swiped back to exit). It reopens the gallery picker.
  /// - A `PopResult` with `result == false` and `action == "onSaveAction"`:
  ///   Indicates that the user didn't save the cropped image. It reopens the gallery picker.
  Future<void> _openGalleryPicker() async {
    final currentRouteName = context.router.current.name;
    await PickerUtils.pickImageFromGalleryForUpload(
      context,
      onPicked: (file) async {
        final result = await context.pushRoute<CropAvatarPopResult>(
          CropAvatarRoute(
            photo: file,
            previousRouteName: currentRouteName,
            avatarType: AvatarType.channel,
          ),
        );

        if (result == null) {
          _openGalleryPicker();
        } else if (result.result == false &&
            result.action == CropAvatarPopResult.onSavedAvatar) {
          _openGalleryPicker();
        }
      },
    );
  }

  void onGoToCallRoomPage({
    required chat.Channel channel,
    required Room room,
    required bool isVideoCall,
  }) {
    context.pushRoute(
      CallRoomRoute(
        args: call.RoomPageArgs(
          room: room,
          channelId: channel.channelId,
          workspaceId: channel.workspaceId,
          channelName: channel.name ?? '',
          isVideoCall: isVideoCall,
        ),
      ),
    );
  }
}
