import 'dart:async';

import '../../isolate/core/utils/logger.dart';

class IsolateHealthMonitor {
  static final IsolateHealthMonitor _instance =
      IsolateHealthMonitor._internal();
  factory IsolateHealthMonitor() => _instance;
  IsolateHealthMonitor._internal();

  int _heartbeatInterval = 10000;

  Timer? _heartbeatTimer;

  int _lastHeartbeatTime = 0;

  Function? _onIsolateUnresponsive;

  bool _isMonitoring = false;

  // Variable to track if app is in background
  bool _isAppInBackground = false;

  void startMonitoring({
    int heartbeatInterval = 10000,
    Function? onIsolateUnresponsive,
  }) {
    if (_isMonitoring) {
      stopMonitoring();
    }

    _heartbeatInterval = heartbeatInterval;
    _onIsolateUnresponsive = onIsolateUnresponsive;
    _lastHeartbeatTime = DateTime.now().millisecondsSinceEpoch;
    _isMonitoring = true;

    _heartbeatTimer = Timer.periodic(
      Duration(milliseconds: _heartbeatInterval),
      _checkHeartbeat,
    );

    RILogger.printClassMethodDebug(
      'IsolateHealthMonitor',
      'startMonitoring',
      'Started monitoring with interval: $_heartbeatInterval ms',
    );
  }

  void stopMonitoring() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
    _isMonitoring = false;

    RILogger.printClassMethodDebug(
      'IsolateHealthMonitor',
      'stopMonitoring',
      'Stopped monitoring',
    );
  }

  /// Change heartbeat check interval
  ///
  /// This method will stop the current timer and create a new one with the new interval
  /// if monitoring is enabled
  void updateHeartbeatInterval(int newHeartbeatInterval) {
    if (newHeartbeatInterval <= 0) {
      RILogger.printClassMethodDebug(
        'IsolateHealthMonitor',
        'updateHeartbeatInterval',
        'Invalid heartbeat interval: $newHeartbeatInterval, ignoring',
      );
      return;
    }

    final bool wasMonitoring = _isMonitoring;

    // Stop current monitoring if running
    if (_isMonitoring) {
      stopMonitoring();
    }

    // Update interval
    _heartbeatInterval = newHeartbeatInterval;

    RILogger.printClassMethodDebug(
      'IsolateHealthMonitor',
      'updateHeartbeatInterval',
      'Updated heartbeat interval to $_heartbeatInterval ms',
    );

    // Restart monitoring if it was running before
    if (wasMonitoring) {
      startMonitoring(
        heartbeatInterval: _heartbeatInterval,
        onIsolateUnresponsive: _onIsolateUnresponsive,
      );
    }
  }

  void updateHeartbeat() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final previousHeartbeat = _lastHeartbeatTime;
    final elapsed = now - previousHeartbeat;

    _lastHeartbeatTime = now;
  }

  /// Set app state as background or foreground
  ///
  /// This method is called from ResilientIsolate when the
  /// app's lifecycle state changes.
  void setAppInBackground(bool inBackground) {
    // If state hasn't changed, do nothing
    if (_isAppInBackground == inBackground) {
      return;
    }

    _isAppInBackground = inBackground;

    RILogger.printClassMethodDebug(
      'IsolateHealthMonitor',
      'setAppInBackground',
      'App is now ${inBackground ? "in background" : "in foreground"}',
    );

    if (!inBackground) {
      // App returns to foreground, update heartbeat time to avoid false positive
      _lastHeartbeatTime = DateTime.now().millisecondsSinceEpoch;

      RILogger.printClassMethodDebug(
        'IsolateHealthMonitor',
        'setAppInBackground',
        'Updated heartbeat time to avoid false positive detection',
      );
    }
  }

  void _checkHeartbeat(Timer timer) {
    // Don't check heartbeat when app is in background
    if (_isAppInBackground) {
      return;
    }

    final now = DateTime.now().millisecondsSinceEpoch;
    final elapsed = now - _lastHeartbeatTime;
    final threshold = _heartbeatInterval * 2;

    if (elapsed > threshold) {
      RILogger.printClassMethodDebug(
        'IsolateHealthMonitor',
        '_checkHeartbeat',
        'Isolate unresponsive for $elapsed ms (threshold: $threshold ms)',
      );

      if (_onIsolateUnresponsive != null) {
        _onIsolateUnresponsive!();
      }
    }
  }

  bool get isMonitoring => _isMonitoring;

  int get lastHeartbeatTime => _lastHeartbeatTime;
}
