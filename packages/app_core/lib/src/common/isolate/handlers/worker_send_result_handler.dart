import 'dart:isolate';
import 'dart:ui';

import 'package:chat/chat.dart';
import 'package:dio/dio.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';
import '../../isolate/data/stores/shared_preferences_store.dart';
import '../../isolate/domain/models/task_model.dart';
import '../exceptions/file_not_found_exception.dart';
import '../exceptions/send_message_timeout_exception.dart';
import '../models/api_response.dart';

class WorkerSendResultHandler {
  WorkerSendResultHandler._();

  static WorkerSendMessageOutput handleSuccess({
    required String ref,
    required Response<dynamic> response,
  }) {
    final apiResponse = ApiResponse.fromJson(response.data);
    if (!apiResponse.ok) {
      return _handleErrorResponse(ref, apiResponse);
    }
    _sendSuccessResponse(ref, response.data);

    final eventData = APIResponse.fromJson(response.data);
    final message = _parseMessage(eventData);

    RILogger.printClassMethodDebug(
      'WorkerSendResultHandler',
      'handleSuccess',
      '[DEBUG][deleteTask:$ref] Handling success response',
    );
    SharedPreferencesStore.deleteTask(ref);

    return WorkerSendMessageOutput.success(
      messageId: message?.messageId,
      workspaceId: message?.workspaceId,
      channelId: message?.channelId,
      userId: message?.userId,
    );
  }

  /// Handle skip: remove task, log, no send port call
  static WorkerSendMessageOutput handleSkip({
    required String ref,
    required String reason,
  }) {
    RILogger.printClassMethodDebug(
      'WorkerSendResultHandler',
      'handleSkip',
      '[DEBUG][skipTask:$ref] Skipping task whit: $reason',
    );
    return WorkerSendMessageOutput.skip();
  }

  static Future<WorkerSendMessageOutput> handleError({
    required String ref,
    required Object error,
  }) async {
    RILogger.printClassMethodDebug(
      'WorkerSendResultHandler',
      'handleError',
      '[DEBUG][MessageRef:$ref] Handling error response with error: $error',
    );

    // Handle SendMessageTimeoutException
    if (error is SendMessageTimeoutException) {
      _sendMessageFailure(ref, 'Send message timeout');
      // No need to retry because Timeout
      await SharedPreferencesStore.updateTaskStatus(
        ref,
        TaskStatus.unrecoverable,
      );
      return WorkerSendMessageOutput.success(
        sendMsgErrorReason: SendMsgErrorEnum.timeout.value,
      );
    }

    if (error is FileNotFoundException) {
      _sendMessageFailure(ref, 'Send message file not found');

      return WorkerSendMessageOutput.success(
        sendMsgErrorReason: SendMsgErrorEnum.fileNotFound.value,
      );
    }

    // Handle DioException
    if (error is DioException) {
      // Log detailed error information
      error.logError();

      if (error.isBlockedException) {
        _sendBlockedUser(ref);
        // User errors are blocked, no need for retry
        await SharedPreferencesStore.updateTaskStatus(
          ref,
          TaskStatus.unrecoverable,
        );
        return WorkerSendMessageOutput.success(
          sendMsgErrorReason: SendMsgErrorEnum.blockedUser.value,
        );
      }

      if (error.hasReachedMessageLimit) {
        _sendReachedMessageLimit(ref);
        // Error achieving message limits, no need for retry
        await SharedPreferencesStore.updateTaskStatus(
          ref,
          TaskStatus.unrecoverable,
        );
        return WorkerSendMessageOutput.success(
          sendMsgErrorReason: SendMsgErrorEnum.reachedMessageLimit.value,
        );
      }

      if (error.isTimeoutError) {
        _sendMessageFailure(ref, error.getReadableErrorMessage());
        // Timeout error, can retry
        await SharedPreferencesStore.updateTaskStatus(
          ref,
          TaskStatus.failed,
        );
        return WorkerSendMessageOutput.success(
          sendMsgErrorReason: SendMsgErrorEnum.timeout.value,
        );
      }

      if (error.isNetworkError) {
        _sendMessageFailure(ref, error.getReadableErrorMessage());
        // network error, can retry
        await SharedPreferencesStore.updateTaskStatus(
          ref,
          TaskStatus.failed,
        );
        return WorkerSendMessageOutput.success(
          sendMsgErrorReason: SendMsgErrorEnum.network.value,
        );
      }

      if (error.isNoHealthyUpstreamError) {
        _sendMessageFailure(ref, error.getReadableErrorMessage());
        // Server error is not available, can retry
        await SharedPreferencesStore.updateTaskStatus(
          ref,
          TaskStatus.failed,
        );
        return WorkerSendMessageOutput.success(
          sendMsgErrorReason: SendMsgErrorEnum.serverUnavailable.value,
        );
      }

      if (error.hasServerErrorHandling) {
        _handleServerError(ref, error);

        // check HTTP status code to decide whether to retry or not
        final statusCode = error.response?.statusCode;

        if (statusCode != null) {
          if (statusCode >= 400 && statusCode < 500) {
            // 4xx error (Client Error), should not retry
            await SharedPreferencesStore.updateTaskStatus(
              ref,
              TaskStatus.unrecoverable,
            );
          } else if (statusCode >= 500) {
            // 5xx error (server error), can retry
            await SharedPreferencesStore.updateTaskStatus(
              ref,
              TaskStatus.failed,
            );
          } else {
            // Other errors, no retry
            await SharedPreferencesStore.updateTaskStatus(
              ref,
              TaskStatus.unrecoverable,
            );
          }
        } else {
          // No status code, no retry
          await SharedPreferencesStore.updateTaskStatus(
            ref,
            TaskStatus.unrecoverable,
          );
        }

        return WorkerSendMessageOutput.success(
          sendMsgErrorReason: SendMsgErrorEnum.serverError.value,
        );
      }

      // Handle any other Dio errors
      _sendMessageFailure(ref, error.getReadableErrorMessage());

      // Check HTTP status code for other dio errors
      final statusCode = error.response?.statusCode;

      if (statusCode != null) {
        if (statusCode >= 400 && statusCode < 500) {
          // 4xx error (Client Error), should not retry
          await SharedPreferencesStore.updateTaskStatus(
            ref,
            TaskStatus.unrecoverable,
          );
        } else if (statusCode >= 500) {
          // 5xx error (server error), can retry
          await SharedPreferencesStore.updateTaskStatus(
            ref,
            TaskStatus.failed,
          );
        } else {
          // Other errors, no retry
          await SharedPreferencesStore.updateTaskStatus(
            ref,
            TaskStatus.unrecoverable,
          );
        }
      } else {
        // No status code, assumption is an irreversible error
        await SharedPreferencesStore.updateTaskStatus(
          ref,
          TaskStatus.unrecoverable,
        );
      }

      return WorkerSendMessageOutput.success(
        sendMsgErrorReason: SendMsgErrorEnum.unknown.value,
      );
    }

    // Handle all other errors
    _sendMessageFailure(ref, 'Unknown error: $error');
    // uncertain errors, no retry
    await SharedPreferencesStore.updateTaskStatus(
      ref,
      TaskStatus.unrecoverable,
    );
    return WorkerSendMessageOutput.success(
      sendMsgErrorReason: SendMsgErrorEnum.unknown.value,
    );
  }

  static WorkerSendMessageOutput _handleErrorResponse(
    String ref,
    ApiResponse apiResponse,
  ) {
    if (apiResponse.error?.code ==
        ResponseErrorCode.reachedMaximumMessageLimitForStrangers.code) {
      _sendReachedMessageLimit(ref);
      return WorkerSendMessageOutput.success(
        sendMsgErrorReason: SendMsgErrorEnum.reachedMessageLimit.value,
      );
    }

    // TODO: Handle other specific error cases here
    return WorkerSendMessageOutput.fail();
  }

  static Message? _parseMessage(APIResponse eventData) =>
      MessageSerializer.serializeFromJson(
        data: eventData.data?.message?.toJson(),
        includes: eventData.includes?.toJson(),
      );

  static void _sendSuccessResponse(String ref, dynamic data) =>
      _getSendPort(ref)?.send(data);

  static void _sendReachedMessageLimit(String ref) =>
      _getSendPort(ref)?.send(ReachedMessageLimitResponse(ref: ref).toJson());

  static void _sendBlockedUser(String ref) =>
      _getSendPort(ref)?.send(BlockedUserResponse(ref: ref).toJson());

  static void _sendMessageFailure(String ref, String errorMessage) =>
      _getSendPort(ref)?.send(
        SendMessageFailureResponse(
          ref: ref,
          errorMessage: errorMessage,
        ).toJson(),
      );

  static void _handleServerError(String ref, DioException error) {
    final jsonData = error.response?.data;
    if (jsonData is! Map<String, dynamic>) {
      // Handle non-JSON response data
      _sendMessageFailure(
        ref,
        'Server error: ${error.response?.statusCode} - ${error.response?.statusMessage}',
      );
      return;
    }

    final apiResponse = APIResponse.fromJson(jsonData);
    _getSendPort(ref)?.send(
      ServerErrorResponse(
        errorCode: apiResponse.error?.code ?? 0,
        ref: ref,
      ).toJson(),
    );
  }

  static SendPort? _getSendPort(String ref) {
    final sendPort = IsolateNameServer.lookupPortByName(ref);
    if (sendPort == null) {
      RILogger.printClassMethodDebug(
        'WorkerSendResultHandler',
        '_getSendPort',
        'SendPort not found for ref: $ref',
      );
    }
    return sendPort;
  }
}
