import 'dart:async';
import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter_ogg_to_aac/flutter_ogg_to_aac.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared/shared.dart';

class WaveformManager {
  // Singleton instance
  static final WaveformManager _instance = WaveformManager._internal();

  // Factory constructor to return the singleton instance
  factory WaveformManager() => _instance;

  // Private constructor with initialization
  WaveformManager._internal();

  // Application's documents directory
  Directory? _appDir;

  // Flag to track initialization status
  bool _initialized = false;

  // Completer to track initialization process
  final Completer<void> _initCompleter = Completer<void>();

  // Cache to store waveforms associated with file paths or URLs
  final Map<String, Future<List<double>>> _waveformCache = {};

  // Initialize the application's documents directory
  Future<void> init() async {
    if (_initialized) return;

    try {
      _appDir = await getApplicationCacheDirectory();
      _initialized = true;
      _initCompleter.complete();
    } catch (e) {
      _initCompleter.completeError(e);
      throw e;
    }
  }

  // Ensure initialization is complete before proceeding
  Future<void> ensureInitialized() async {
    if (!_initialized) {
      await init();
    }
    return _initCompleter.future;
  }

  // Retrieve waveform data for a local asset file
  Future<List<double>> getLocalWaveform(String assetPath) async {
    // Ensure manager is initialized before proceeding
    await ensureInitialized();

    // Check if the waveform is already cached
    if (_waveformCache.containsKey(assetPath)) {
      return _waveformCache[assetPath]!;
    }

    // Create a completer to handle asynchronous waveform generation
    final completer = Completer<List<double>>();
    _waveformCache[assetPath] = completer.future;

    // Generate waveform data and handle potential errors
    _generateWaveformFromLocal(assetPath)
        .then(completer.complete)
        .catchError((e) {
      _waveformCache.remove(assetPath);
      completer.completeError(e);
    });

    return completer.future;
  }

  // Retrieve waveform data for a remote OGG file
  Future<List<double>> getRemoteWaveformRemote(String url) async {
    await ensureInitialized();

    // Check if the waveform is already cached
    if (_waveformCache.containsKey(url)) {
      return _waveformCache[url]!;
    }

    // Create a completer to handle asynchronous waveform generation
    final completer = Completer<List<double>>();
    _waveformCache[url] = completer.future;

    // Make sure _appDir is not null before using it
    if (_appDir == null) {
      completer.completeError('Application directory not initialized');
      return completer.future;
    }

    // Create directory if it doesn't exist
    final directory = Directory('${_appDir!.path}/ziivoice');
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    final outputPath =
        '${_appDir!.path}/ziivoice/${DateTime.now().millisecondsSinceEpoch}.aac';

    try {
      final file = await AppCacheManager().getFile(url);
      final baseFilePath = file.path;
      String resultPath;

      // Check if the file is an OGG file
      if (baseFilePath.endsWith('.ogg')) {
        resultPath = (await FlutterOggToAac.convert(baseFilePath, outputPath))!;
      } else {
        resultPath = baseFilePath;
      }

      // Generate waveform data from the converted AAC file or original file
      final waveform = await _generateWaveformFromLocal(resultPath);
      completer.complete(waveform);
    } catch (e) {
      _waveformCache.remove(url);
      completer.completeError('An error occurred: $e');
      throw Exception(e);
    }

    return completer.future;
  }

  // Generate waveform data from a local file
  Future<List<double>> _generateWaveformFromLocal(String assetPath) async {
    final controller = PlayerController();
    return controller.extractWaveformData(path: assetPath);
  }
}
