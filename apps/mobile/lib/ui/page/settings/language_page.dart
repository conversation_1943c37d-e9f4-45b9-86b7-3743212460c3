import 'package:app_core/core.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

@RoutePage()
class LanguagePage extends StatefulWidget {
  const LanguagePage({super.key});

  @override
  State<LanguagePage> createState() => _LanguagePageState();
}

class _LanguagePageState extends State<LanguagePage>
    implements ui.LanguagePageInterface {
  @override
  Widget build(BuildContext context) {
    return ui.LanguagePage(
      interface: this,
    );
  }

  @override
  void onSelectLocale(Locale locale) {
    context.read<AppBloc>().add(AppLanguageChanged(locale: locale));
  }

  @override
  Locale selectedLocale() {
    return context.watch<AppBloc>().state.locale;
  }

  @override
  void onClickBack() {
    context.maybePop();
  }
}
