import 'dart:convert';

import 'package:call_api/call_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../data/repositories/source/api/client/clients.dart';
import '../models/call_data.dart';
import '../models/call_type.dart';
import '../models/rtc_ice_server.dart';

@Injectable()
class CreateCallUseCase
    extends BaseFutureUseCase<CreateCallInput, CreateCallOutput> {
  CreateCallUseCase();

  @override
  Future<CreateCallOutput> buildUseCase(
    CreateCallInput input,
  ) async {
    final bodyBuilder = V3CreateCallRequestBuilder();
    bodyBuilder.callId = input.callId;
    bodyBuilder.userId = input.userId;
    bodyBuilder.type = switch (input.callType) {
      CallType.UNSPECIFIED => V3CallTypeEnum.CALL_TYPE_UNSPECIFIED,
      CallType.VIDEO => V3CallTypeEnum.CALL_TYPE_VIDEO,
      CallType.AUDIO => V3CallTypeEnum.CALL_TYPE_AUDIO,
    };
    final response =
        await CallClient().instance.createCall(body: bodyBuilder.build());
    if (response.data?.ok ?? false) {
      final callDataApi = jsonDecode(
        standardSerializers.toJson(
          V3CallData.serializer,
          response.data!.data!.callData,
        ),
      );

      // final callData = jsonDecode(
      //   standardSerializers.toJson(
      //     V3RTCIceServer.serializer,
      //     member,
      //   ),
      // );

      return CreateCallOutput(
        rtcIceServers: null,
        callData: CallData.fromJson(callDataApi),
      );
    }
    return CreateCallOutput(rtcIceServers: null, callData: null);
  }
}

class CreateCallInput extends BaseInput {
  CreateCallInput({
    required this.callId,
    required this.userId,
    this.callType = CallType.UNSPECIFIED,
  });

  final String callId;
  final String userId;
  final CallType callType;
}

class CreateCallOutput extends BaseOutput {
  CreateCallOutput({this.rtcIceServers, this.callData});

  final List<RTCIceServer>? rtcIceServers;
  final CallData? callData;
}
