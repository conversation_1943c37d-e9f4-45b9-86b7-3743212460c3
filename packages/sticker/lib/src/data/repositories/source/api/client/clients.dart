import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:sticker_api/sticker_api.dart';

import '../../../../../common/config/config.dart';

@LazySingleton()
class StickerClient {
  late final StickerViewServiceApi _instance;

  StickerClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = StickerApi(
      dio: BaseClient.dio,
      serializers: standardSerializers,
    ).getStickerViewServiceApi();
  }

  StickerViewServiceApi get instance => _instance;
}
