import 'dart:async';

import 'package:chat/chat.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:pasteboard/pasteboard.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

class CopyHandler {
  CopyHandler();

  late StreamSubscription? _copySubscription;

  bool _isFailed = false;

  void setupCopyHandler() {
    _copySubscription = GetIt.instance
        .get<AppEventBus>()
        .on<CopyEvent>()
        .listen(_onReceivedFromCopyEvent);
  }

  void dispose() {
    _copySubscription?.cancel();
  }

  void _onReceivedFromCopyEvent(event) async {
    _isFailed = false;

    final output =
        await GetIt.instance.get<GetMessageByUserIdUseCase>().execute(
              GetMessageByUserIdInput(
                workspaceId: event.workspaceId,
                channelId: event.channelId,
                messageId: event.messageId,
              ),
            );

    if (output.message != null) {
      _onCopy(output.message!);
      if (!_isFailed) {
        SnackBarOverlayHelper().showSnackBar(
          widgetBuilder: (T) {
            return SnackBarUtilV2.showCopiedWithExclamationTextSnackBar(
              event.context,
              appLocalizations: event.appLocalizations,
            );
          },
        );
      }
      FocusScope.of(event.context).requestFocus(FocusNode());
    }
  }

  void _onCopy(Message message) {
    switch (message.messageViewType) {
      case MessageViewType.text || MessageViewType.textOwner:
        _onCopyText(message.content!);
        break;
      case MessageViewType.link || MessageViewType.linkOwner:
        _onCopyText(message.content!);
        break;
      case MessageViewType.invitation || MessageViewType.invitationOwner:
        _onCopyText(
          TranslateContentUtils.translateContent(
            message.content!,
            message.contentArguments ?? [],
          ),
        );
        break;
      case MessageViewType.location || MessageViewType.locationOwner:
        _onCopyText(message.embed?.first.locationData?.thumbnailUrl ?? '');
        break;
      case (MessageViewType.images || MessageViewType.imagesOwner):
        if (message.mediaAttachments.length == 1) {
          _onCopyImage(message.mediaAttachments);
        }
        break;
      default:
    }
  }

  void _onCopyText(String content) async {
    try {
      await Clipboard.setData(ClipboardData(text: content));
    } catch (e) {
      _isFailed = true;
    }
  }

  void _onCopyImage(ToMany<Attachment> attachments) async {
    if (attachments.isEmpty) return;

    final photo = attachments.first.photo;
    final fileUrl = photo?.fileUrl;

    if (fileUrl?.isNotEmpty ?? false) {
      try {
        final cdnUrl = UrlUtils.parseCDNUrl(fileUrl!);
        final bytes =
            (await NetworkAssetBundle(Uri.parse(cdnUrl)).load(fileUrl))
                .buffer
                .asUint8List();
        await Pasteboard.writeImage(bytes);
      } catch (e) {
        _isFailed = true;
      }
    }
  }
}
