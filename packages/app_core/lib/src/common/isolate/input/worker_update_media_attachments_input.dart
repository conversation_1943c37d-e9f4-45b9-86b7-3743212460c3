import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

import '../models/worker_upload_file.dart';
import 'worker_send_message_base.dart';

part 'worker_update_media_attachments_input.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class WorkerUpdateMediaAttachmentsInput extends WorkerSendMessageInputBase {
  WorkerUpdateMediaAttachmentsInput({
    required this.uploadFilesEmbed,
    required super.workspaceId,
    required super.channelId,
    required super.userId,
    required super.ref,
    required super.taskName,
    required super.attachmentType,
    required super.creationTime,
    required this.messageRef,
    required this.messageId,
    this.uploadConcurrency = 1,
    super.isRefTask,
  });

  final String uploadFilesEmbed;

  final String messageRef;
  final String messageId;
  final int? uploadConcurrency;

  @JsonKey(includeFromJson: false, includeToJson: false)
  List<WorkerUploadFile> get file =>
      (jsonDecode(uploadFilesEmbed) as List<dynamic>)
          .map((e) => WorkerUploadFile.fromJson(e as Map<String, dynamic>))
          .toList();

  factory WorkerUpdateMediaAttachmentsInput.fromJson(
    Map<String, dynamic> json,
  ) =>
      _$WorkerUpdateMediaAttachmentsInputFromJson(json);

  Map<String, dynamic> toJson() =>
      _$WorkerUpdateMediaAttachmentsInputToJson(this);

  @override
  WorkerUpdateMediaAttachmentsInput copyWith({
    String? uploadFilesEmbed,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? ref,
    String? taskName,
    int? attachmentType,
    DateTime? creationTime,
    String? messageRef,
    String? messageId,
    int? uploadConcurrency,
    bool? isRefTask,
  }) {
    return WorkerUpdateMediaAttachmentsInput(
      uploadFilesEmbed: uploadFilesEmbed ?? this.uploadFilesEmbed,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      userId: userId ?? this.userId,
      ref: ref ?? this.ref,
      taskName: taskName ?? this.taskName,
      attachmentType: attachmentType ?? this.attachmentType,
      creationTime: creationTime ?? this.creationTime,
      messageRef: messageRef ?? this.messageRef,
      messageId: messageId ?? this.messageId,
      uploadConcurrency: uploadConcurrency ?? this.uploadConcurrency,
      isRefTask: isRefTask ?? this.isRefTask,
    );
  }
}
