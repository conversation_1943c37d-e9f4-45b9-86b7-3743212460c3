import 'dart:async';
import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:shared/shared.dart';
import 'package:video_compressor/video_compressor.dart';

import '../../../../core.dart';
import '../exceptions/file_not_found_exception.dart';
import '../exceptions/send_message_timeout_exception.dart';
import '../output/worker_compress_video_output.dart';

class CompressVideoHandler {
  CompressVideoHandler();

  /// Create cache directory for message with video and thumbnail subfolders
  /// Structure: cacheMessage/<messageRef>/<fileRef>/video/ and cacheMessage/<messageRef>/<fileRef>/thumbnail/
  Future<Directory> _createMessageCacheDirectory(
      String messageRef, String fileRef) async {
    final cacheDir = await getApplicationCacheDirectory();
    final messageCacheDir = Directory(
      '${cacheDir.path}/${GlobalConfig.messageCacheDir}/$messageRef/$fileRef',
    );

    if (!await messageCacheDir.exists()) {
      await messageCacheDir.create(recursive: true);
    }

    return messageCacheDir;
  }

  /// Create video subdirectory within message cache directory
  /// Structure: cacheMessage/<messageRef>/<fileRef>/video/
  Future<Directory> _createVideoSubdirectory(Directory messageCacheDir) async {
    final videoDir = Directory('${messageCacheDir.path}/video');

    if (!await videoDir.exists()) {
      await videoDir.create(recursive: true);
    }

    return videoDir;
  }

  /// Create thumbnail subdirectory within message cache directory
  /// Structure: cacheMessage/<messageRef>/<fileRef>/thumbnail/
  Future<Directory> _createThumbnailSubdirectory(
      Directory messageCacheDir) async {
    final thumbnailDir = Directory('${messageCacheDir.path}/thumbnail');

    if (!await thumbnailDir.exists()) {
      await thumbnailDir.create(recursive: true);
    }

    return thumbnailDir;
  }

  /// Create unique file name based on fileRef and timestamp
  String _createUniqueFileName(String originalFileName, String fileRef) {
    final extension = path.extension(originalFileName);
    final baseName = path.basenameWithoutExtension(originalFileName);
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return '${baseName}_${fileRef}_$timestamp$extension';
  }

  String _getSafeFilePath(String filePath) {
    try {
      final fileName = path.basename(filePath);
      final directory = path.dirname(filePath);

      final safeFileName = fileName
          .replaceAll(' ', '_')
          .replaceAll('%', '_percent_')
          .replaceAll('#', '_hash_')
          .replaceAll('&', '_and_')
          .replaceAll('+', '_plus_')
          .replaceAll('?', '_question_')
          .replaceAll('=', '_equal_');

      if (safeFileName == fileName) {
        return filePath;
      }

      final newFilePath = path.join(directory, safeFileName);

      File(filePath).copySync(newFilePath);

      return newFilePath;
    } catch (e) {
      RILogger.printClassMethodDebug(
        'CompressVideoHandler',
        '_getSafeFilePath',
        'Error creating safe file path: $e',
      );
      return filePath;
    }
  }

  Future<WorkerCompressVideoOutput> compressVideo(
    WorkerCompressVideoInput inputData,
  ) async {
    if (inputData.creationTime
        .add(GlobalConfig.sendTimeoutDuration)
        .isBefore(DateTime.now())) {
      throw SendMessageTimeoutException(msgRef: inputData.file.ref);
    }
    final filePath = inputData.file.path;
    if (!File(filePath).existsSync()) {
      throw FileNotFoundException(message: 'File ${filePath} not exists');
    }

    String safeFilePath = _getSafeFilePath(filePath);

    // Get messageRef and fileRef
    final messageRef = inputData.messageRef ?? inputData.file.ref;
    final fileRef = inputData.file.fileRef ?? inputData.file.ref;
    try {
      // Create cache directory for message with subfolders
      final messageCacheDir =
          await _createMessageCacheDirectory(messageRef, fileRef);
      final videoDir = await _createVideoSubdirectory(messageCacheDir);
      final thumbnailDir = await _createThumbnailSubdirectory(messageCacheDir);

      // Create unique file name for thumbnail
      final originalFileName = path.basename(filePath);
      final thumbnailName =
          '${path.basenameWithoutExtension(originalFileName)}_${fileRef}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final uniqueThumbnailPath = path.join(thumbnailDir.path, thumbnailName);

      // Only log basic information when starting video compression
      RILogger.printClassMethodDebug(
        'CompressVideoHandler',
        'compressVideo',
        '[DEBUG][MessageRef:${messageRef}] Start compressing video: ${path.basename(filePath)} (${File(filePath).lengthSync() ~/ 1024} KB)',
      );
      final stopwatch = Stopwatch()..start();

      RILogger.printClassMethodDebug(
        'CompressVideoHandler',
        'compressVideo',
        '[DEBUG][MessageRef:${messageRef}] Starting parallel compression and thumbnail generation for ${path.basename(filePath)}',
      );

      final resultList = await Future.wait([
        VideoCompressor.compressVideo(
          path: safeFilePath,
        ),
        VideoCompressor.getFileThumbnail(
          path: safeFilePath,
          quality: 100, // Reduce quality to decrease file size
          position: -1, // Get frame from middle of video
        ),
      ]);

      final elapsedMs = stopwatch.elapsedMilliseconds;
      final videoInfo = resultList[0] as VideoInfo?;
      final fileThumbnail = resultList[1] as File;

      RILogger.printClassMethodDebug(
        'CompressVideoHandler',
        'compressVideo',
        '[DEBUG][MessageRef:${messageRef}] Completed parallel compression and thumbnail generation in ${elapsedMs}ms',
      );

      // Log video compression result with brief information
      final compressedSize = videoInfo?.file?.lengthSync() ?? 0;
      final originalSize = File(filePath).lengthSync();
      final compressionRatio = originalSize > 0
          ? (compressedSize / originalSize * 100).toStringAsFixed(1)
          : "N/A";

      RILogger.printClassMethodDebug(
        'CompressVideoHandler',
        'compressVideo',
        '[DEBUG][MessageRef:${messageRef}] Compression completed in ${elapsedMs}ms. Size: ${compressedSize ~/ 1024} KB (${compressionRatio}% of original). Thumbnail: ${fileThumbnail.existsSync()}',
      );

      // Process compressed video
      String compressedVideoPath =
          safeFilePath; // Default to original file if compression fails

      if (videoInfo?.file != null) {
        // Create unique file name for compressed video
        final originalVideoName = path.basename(videoInfo!.file!.path);
        final compressedVideoName =
            _createUniqueFileName(originalVideoName, fileRef);
        final uniqueCompressedVideoPath =
            path.join(videoDir.path, compressedVideoName);

        // Copy compressed video to video subdirectory
        try {
          videoInfo.file!.copySync(uniqueCompressedVideoPath);
          compressedVideoPath = uniqueCompressedVideoPath;

          // No need to log full path, only log when there's an error
        } catch (e) {
          RILogger.printClassMethodDebug(
            'CompressVideoHandler',
            'compressVideo',
            '[DEBUG][MessageRef:${messageRef}] Error copying compressed video: $e, using original compressed path',
          );
          compressedVideoPath = videoInfo.file!.path;
        }

        // Already logged compression info above, no need to log again
      } else {
        RILogger.printClassMethodDebug(
          'CompressVideoHandler',
          'compressVideo',
          '[DEBUG][MessageRef:${messageRef}] Compression failed, videoInfo.file is null',
        );
      }

      // Copy thumbnail to thumbnail subdirectory
      if (fileThumbnail.existsSync()) {
        try {
          fileThumbnail.copySync(uniqueThumbnailPath);

          // Only log brief information about thumbnail
          RILogger.printClassMethodDebug(
            'CompressVideoHandler',
            'compressVideo',
            '[DEBUG][MessageRef:${messageRef}] Thumbnail created: ${path.basename(uniqueThumbnailPath)} (${File(uniqueThumbnailPath).lengthSync() ~/ 1024} KB)',
          );
        } catch (e) {
          RILogger.printClassMethodDebug(
            'CompressVideoHandler',
            'compressVideo',
            '[DEBUG][MessageRef:${messageRef}] Error copying thumbnail: $e',
          );
        }
      } else {
        RILogger.printClassMethodDebug(
          'CompressVideoHandler',
          'compressVideo',
          '[DEBUG][MessageRef:${messageRef}] Thumbnail file does not exist at: ${fileThumbnail.path}',
        );
      }

      // Log final information about video compression result
      RILogger.printClassMethodDebug(
        'CompressVideoHandler',
        'compressVideo',
        '[DEBUG][MessageRef:${messageRef}] Compression result: Video=${path.basename(compressedVideoPath)}, Thumbnail=${fileThumbnail.existsSync() ? "created" : "failed"}',
      );

      return WorkerCompressVideoOutput(
        videoPath: compressedVideoPath,
        thumbnailPath: fileThumbnail.existsSync() ? uniqueThumbnailPath : '',
        duration: videoInfo?.duration?.toInt() ?? 0,
      );
    } catch (e, stackTrace) {
      final messageRef = inputData.messageRef ?? inputData.file.ref;
      RILogger.printClassMethodDebug(
        'CompressVideoHandler',
        'compressVideo',
        '[DEBUG][MessageRef:${messageRef}] Error compressing video: $e',
      );
      RILogger.printClassMethodDebug(
        'CompressVideoHandler',
        'compressVideo',
        '[DEBUG][MessageRef:${messageRef}] Stack trace: $stackTrace',
      );

      if (e.toString().contains('Illegal percent encoding in URI')) {
        RILogger.printClassMethodDebug(
          'CompressVideoHandler',
          'compressVideo',
          '[DEBUG][MessageRef:${messageRef}] Detected illegal URI encoding, trying with temp file',
        );
        final tempDir = Directory(path.dirname(filePath));
        final fileExtension = path.extension(filePath);
        final tempFilePath = path.join(
          tempDir.path,
          'temp_video_${DateTime.now().millisecondsSinceEpoch}$fileExtension',
        );

        try {
          File(filePath).copySync(tempFilePath);
          RILogger.printClassMethodDebug(
            'CompressVideoHandler',
            'compressVideo',
            '[DEBUG][MessageRef:${messageRef}] Copied to temp file: ${path.basename(tempFilePath)} (${File(tempFilePath).lengthSync() ~/ 1024} KB)',
          );
        } catch (copyError) {
          RILogger.printClassMethodDebug(
            'CompressVideoHandler',
            'compressVideo',
            '[DEBUG][MessageRef:${messageRef}] Error copying to temp file: $copyError',
          );
          rethrow;
        }

        try {
          RILogger.printClassMethodDebug(
            'CompressVideoHandler',
            'compressVideo',
            '[DEBUG][MessageRef:${messageRef}] Retrying compression with temp file',
          );
          final stopwatch = Stopwatch()..start();

          // Compress video with default parameters
          RILogger.printClassMethodDebug(
            'CompressVideoHandler',
            'compressVideo',
            '[DEBUG][MessageRef:${messageRef}] Starting retry parallel compression and thumbnail generation for temp file',
          );
          final resultList = await Future.wait([
            VideoCompressor.compressVideo(
              path: tempFilePath,
            ),
            VideoCompressor.getFileThumbnail(path: tempFilePath),
          ]);

          final elapsedMs = stopwatch.elapsedMilliseconds;
          final videoInfo = resultList[0] as VideoInfo?;
          final fileThumbnail = resultList[1] as File;

          RILogger.printClassMethodDebug(
            'CompressVideoHandler',
            'compressVideo',
            '[DEBUG][MessageRef:${messageRef}] Completed retry parallel compression and thumbnail generation in ${elapsedMs}ms',
          );

          // Log video compression result with brief information
          final compressedSize = videoInfo?.file?.lengthSync() ?? 0;
          final originalSize = File(tempFilePath).lengthSync();
          final compressionRatio = originalSize > 0
              ? (compressedSize / originalSize * 100).toStringAsFixed(1)
              : "N/A";

          RILogger.printClassMethodDebug(
            'CompressVideoHandler',
            'compressVideo',
            '[DEBUG][MessageRef:${messageRef}] Retry compression completed in ${elapsedMs}ms. Size: ${compressedSize ~/ 1024} KB (${compressionRatio}% of original)',
          );

          // Create cache directory for message with subfolders
          final messageCacheDir =
              await _createMessageCacheDirectory(messageRef, fileRef);
          final videoDir = await _createVideoSubdirectory(messageCacheDir);
          final thumbnailDir =
              await _createThumbnailSubdirectory(messageCacheDir);

          // Process compressed video
          String compressedVideoPath =
              tempFilePath; // Default to temp file if compression fails

          if (videoInfo?.file != null) {
            // Create unique file name for compressed video
            final originalVideoName = path.basename(videoInfo!.file!.path);
            final compressedVideoName =
                _createUniqueFileName(originalVideoName, fileRef);
            final uniqueCompressedVideoPath =
                path.join(videoDir.path, compressedVideoName);

            // Copy compressed video to video subdirectory
            try {
              videoInfo.file!.copySync(uniqueCompressedVideoPath);
              compressedVideoPath = uniqueCompressedVideoPath;

              // No need to log full path, only log when there's an error
            } catch (e) {
              RILogger.printClassMethodDebug(
                'CompressVideoHandler',
                'compressVideo',
                '[DEBUG][MessageRef:${messageRef}] Retry: Error copying compressed video: $e, using original compressed path',
              );
              compressedVideoPath = videoInfo.file!.path;
            }
          }

          // Process thumbnail
          String thumbnailPath = '';
          if (fileThumbnail.existsSync()) {
            // Create unique file name for thumbnail
            final thumbnailName =
                '${path.basenameWithoutExtension(tempFilePath)}_${fileRef}_${DateTime.now().millisecondsSinceEpoch}.jpg';
            final uniqueThumbnailPath =
                path.join(thumbnailDir.path, thumbnailName);

            try {
              fileThumbnail.copySync(uniqueThumbnailPath);
              thumbnailPath = uniqueThumbnailPath;

              RILogger.printClassMethodDebug(
                'CompressVideoHandler',
                'compressVideo',
                '[DEBUG][MessageRef:${messageRef}] Retry: Thumbnail copied to unique path: $uniqueThumbnailPath',
              );
            } catch (e) {
              RILogger.printClassMethodDebug(
                'CompressVideoHandler',
                'compressVideo',
                '[DEBUG][MessageRef:${messageRef}] Retry: Error copying thumbnail: $e',
              );
              thumbnailPath = fileThumbnail.path;
            }
          }

          return WorkerCompressVideoOutput(
            videoPath: compressedVideoPath,
            thumbnailPath: thumbnailPath,
            duration: videoInfo?.duration?.toInt() ?? 0,
          );
        } finally {
          if (File(tempFilePath).existsSync()) {
            try {
              File(tempFilePath).deleteSync();
            } catch (_) {}
          }
        }
      }

      // Try to create thumbnail separately if initial thumbnail creation failed
      try {
        RILogger.printClassMethodDebug(
          'CompressVideoHandler',
          'compressVideo',
          '[DEBUG][MessageRef:${messageRef}] Attempting to create thumbnail separately',
        );

        // Create cache directory for message with subfolders
        final messageCacheDir =
            await _createMessageCacheDirectory(messageRef, fileRef);
        final thumbnailDir =
            await _createThumbnailSubdirectory(messageCacheDir);

        // Create unique file name for thumbnail
        final originalFileName = path.basename(filePath);
        final thumbnailName =
            '${path.basenameWithoutExtension(originalFileName)}_${fileRef}_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final uniqueThumbnailPath = path.join(thumbnailDir.path, thumbnailName);

        final fileThumbnail = await VideoCompressor.getFileThumbnail(
          path: filePath,
          quality: 100,
          position: -1,
        );

        if (fileThumbnail.existsSync()) {
          try {
            fileThumbnail.copySync(uniqueThumbnailPath);

            RILogger.printClassMethodDebug(
              'CompressVideoHandler',
              'compressVideo',
              '[DEBUG][MessageRef:${messageRef}] Thumbnail created separately: ${path.basename(uniqueThumbnailPath)} (${File(uniqueThumbnailPath).lengthSync() ~/ 1024} KB)',
            );
            return WorkerCompressVideoOutput(
              videoPath: filePath,
              thumbnailPath: uniqueThumbnailPath,
              duration: 0,
            );
          } catch (copyError) {
            RILogger.printClassMethodDebug(
              'CompressVideoHandler',
              'compressVideo',
              '[DEBUG][MessageRef:${messageRef}] Error copying thumbnail (retry): $copyError',
            );
            return WorkerCompressVideoOutput(
              videoPath: filePath,
              thumbnailPath: fileThumbnail.path,
              duration: 0,
            );
          }
        }
      } catch (thumbnailError) {
        RILogger.printClassMethodDebug(
          'CompressVideoHandler',
          'compressVideo',
          '[DEBUG][MessageRef:${messageRef}] Error creating thumbnail separately: $thumbnailError',
        );
      }

      // If still unable to create thumbnail, return empty path
      return WorkerCompressVideoOutput(
        videoPath: filePath,
        thumbnailPath: '',
        duration: 0,
      );
    }
  }
}
