import 'package:json_annotation/json_annotation.dart';

import 'worker_send_message_base.dart';

part 'worker_send_sticker_input.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class WorkerSendStickerInput extends WorkerSendMessageInputBase {
  final String stickerId;
  final bool isPoked;

  WorkerSendStickerInput({
    required this.stickerId,
    required super.workspaceId,
    required super.channelId,
    required super.userId,
    required super.ref,
    required super.taskName,
    required super.attachmentType,
    required super.creationTime,
    required this.isPoked,
    super.isRefTask,
  });

  factory WorkerSendStickerInput.fromJson(Map<String, dynamic> json) =>
      _$WorkerSendStickerInputFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$WorkerSendStickerInputToJson(this);

  Map<String, dynamic> toRequest() {
    return isDm()
        ? {
            'userId': userId,
            'stickerId': stickerId,
            'ref': ref,
          }
        : {
            'workspaceId': workspaceId,
            'channelId': channelId,
            'stickerId': stickerId,
            'ref': ref,
          };
  }

  @override
  WorkerSendStickerInput copyWith({
    String? stickerId,
    bool? isPoked,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? ref,
    String? taskName,
    int? attachmentType,
    DateTime? creationTime,
    bool? isRefTask,
  }) {
    return WorkerSendStickerInput(
      stickerId: stickerId ?? this.stickerId,
      isPoked: isPoked ?? this.isPoked,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      userId: userId ?? this.userId,
      ref: ref ?? this.ref,
      taskName: taskName ?? this.taskName,
      attachmentType: attachmentType ?? this.attachmentType,
      creationTime: creationTime ?? this.creationTime,
      isRefTask: isRefTask ?? this.isRefTask,
    );
  }
}
