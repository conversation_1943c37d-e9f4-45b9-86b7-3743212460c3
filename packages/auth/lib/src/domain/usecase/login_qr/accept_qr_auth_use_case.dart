import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';

@Injectable()
class AcceptQRAuthUseCase extends BaseFutureUseCase<AcceptQRAuthUseCaseInput,
    AcceptQRAuthUseCaseOutput> {
  AcceptQRAuthUseCase();

  @override
  Future<AcceptQRAuthUseCaseOutput> buildUseCase(
    AcceptQRAuthUseCaseInput input,
  ) async {
    final authRequest = V3AcceptQRAuthRequestBuilder()
      ..reqId = input.reqId
      ..reqVerifier = input.reqVerifier
      ..assertion = input.assertion.toBuilder();

    final response = await AuthClient().instance.acceptQRAuth(
          body: authRequest.build(),
        );

    return AcceptQRAuthUseCaseOutput(v3acceptQRAuthResponse: response.data!);
  }
}

class AcceptQRAuthUseCaseInput extends BaseInput {
  AcceptQRAuthUseCaseInput({
    required this.reqId,
    required this.reqVerifier,
    required this.assertion,
  });

  final String reqId;
  final String reqVerifier;
  final CommonAssertionResult assertion;
}

class AcceptQRAuthUseCaseOutput extends BaseOutput {
  AcceptQRAuthUseCaseOutput({required this.v3acceptQRAuthResponse});

  final V3AcceptQRAuthResponse v3acceptQRAuthResponse;
}
