import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../../chat.dart';
import '../../../../data/repositories/database/enums/message_status.dart' as m;
import '../../../../data/repositories/extensions/message_extension.dart';
import '../../../../domain/event_bus/message/show_fullscreen_event.dart';
import '../base/base_sender_widget.dart';

class ZiiShortOwnerWidgetImpl extends BaseSenderWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isHiddenPin;
  final bool isLastMessage;

  ZiiShortOwnerWidgetImpl({
    required super.messageItem,
    required this.isOpenCheckBox,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    this.onCheckBoxButtonTap,
    this.isShowCreateTime = false,
    this.isHiddenPin = false,
    this.isLastMessage = false,
    super.key,
    required super.message,
  });

  @override
  Widget build(BuildContext context) {
    if (message.mediaAttachments.isEmpty) {
      //TODO: handle file not found view
      return const SizedBox();
    }

    final defaultMedia =
        message.mediaAttachments.last.videoMessage ?? MediaObject.nullObject();
    final defaultThumbnailUrl = defaultMedia.thumbnailUrl != null &&
            defaultMedia.thumbnailUrl!.isNotEmpty
        ? UrlUtils.parseCDNUrl(defaultMedia.thumbnailUrl!)
        : null;

    return FutureBuilder<Map<String, String?>>(
      future: _parseZiiShortAttachment(),
      builder: (context, snapshot) {
        final paths = snapshot.data ??
            {
              'videoPath': defaultMedia.filePath,
              'thumbnailPath': defaultMedia.filePath,
              'thumbnailUrl': defaultThumbnailUrl,
            };

        final videoPath = paths['videoPath'];
        final thumbnailPath = paths['thumbnailPath'];
        final thumbnailUrl = paths['thumbnailUrl'];

        final isHideOptionResend =
            message.messageStatus == m.MessageStatus.FAILURE &&
                (videoPath?.isEmpty ?? true);

        return ZiiShortMessageSenderWidget(
          isLastMessage: isLastMessage,
          messageItem: messageItem,
          onQuote: onQuote,
          isShowCreateTime: isShowCreateTime,
          onDeleteMessages: onDeleteMessages,
          onForward: onForward,
          onPinMessage: onPinMessage,
          isHiddenPin: isHiddenPin,
          onUnPinMessage: onUnPinMessage,
          onMessageItemClicked: onMessageItemClicked,
          onListReactionClicked: onListReactionClicked,
          onEmojiClicked: onEmojiClicked,
          emojiList: message.emojiList,
          thumbnailUrl: thumbnailUrl,
          thumbnailPath: thumbnailPath,
          onPlayZiiShort: (MessageItem messageItem) {
            AppEventBus.publish(
              ShowFullscreenEvent(
                messageId: messageItem.messageId,
                workspaceId: message.workspaceId,
                channelId: message.channelId,
              ),
            );
          },
          onDownload: (MessageItem messageItem) => onDownload(context),
          onResend: onResendMessage,
          onDiscard: onDiscardMessage,
          onCopy: (MessageItem messageItem) {},
          isOpenCheckBox: isOpenCheckBox,
          isCheckedMessage: isCheckedMessage,
          onCheckBoxButtonTap: onCheckBoxButtonTap,
          isHideOptionResend: isHideOptionResend,
          isHideOptionCopy: true,
          isHighlighted: isHighlighted,
          shouldAnimate: shouldAnimate,
        );
      },
    );
  }

  /// Parse ZiiShort attachment and get video and thumbnail paths from cache
  /// For ZiiShort, we need both video path (for playback) and thumbnail path (for preview)
  Future<Map<String, String?>> _parseZiiShortAttachment() async {
    if (message.mediaAttachments.isEmpty) {
      return {
        'videoPath': null,
        'thumbnailPath': null,
        'thumbnailUrl': null,
      };
    }

    final videoMessage =
        message.mediaAttachments.last.videoMessage ?? MediaObject.nullObject();

    return await FileUtils.loadVideoAndThumbnailPathsFromCache(
      messageRef: message.ref!,
      fileRef: videoMessage.fileRef ?? '',
      fallbackFilePath: videoMessage.filePath,
      thumbnailUrl: videoMessage.thumbnailUrl,
      includeThumbnailUrl: true,
    );
  }
}
