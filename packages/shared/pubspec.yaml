name: shared
description: A Very Good Project created by Very Good CLI.
version: 0.1.0+1
publish_to: none
environment:
  sdk: ^3.4.0
  flutter: ^3.22.0
dependencies:
  async: ^2.12.0
  flutter:
    sdk: flutter
  rxdart: ^0.28.0
  get_it: ^8.0.3
  injectable: ^2.5.0
  event_bus: ^2.0.1
  json_annotation: ^4.9.0
  flutter_bloc: ^9.1.0
  dio: ^5.8.0+1
  openapi_generator_annotations: ^6.1.0
  device_info_plus: ^11.3.3
  intl: ^0.19.0
  flutter_dotenv: ^5.2.1
  sprintf: ^7.0.0
  uuid: ^4.5.1
  ulid: ^2.0.1
  permission_handler: ^11.4.0
  flutter_screenutil: ^5.9.3
  flame_audio: ^2.11.5
  mime: ^2.0.0
  flutter_svg: ^2.0.17
  package_info_plus: ^8.3.0
  firebase_remote_config: ^5.4.3
  flutter_cache_manager_dio:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.0.40
      path: packages/flutter_cache_manager_dio
  geolocator: ^13.0.4
  geocoding: ^3.0.0
  ziichat_ui:
    git:
      url: **************:ziichatlabs/ziichat-flutter-ui.git
      ref: release/0.56.0
  localization_client:
    git:
      url: **************:ziichatlabs/ziichat-flutter-i18n.git
      ref: main
  webauthn:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: 0.0.1
      path: packages/webauthn
  loader_overlay:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.54.0
      path: packages/ziichat_snack_bar_overlay
  url_launcher: ^6.3.1
  open_filex: ^4.7.0
  retry: ^3.1.2
  path_provider: ^2.1.5
dev_dependencies:
  flutter_test:
    sdk: flutter
  mocktail: ^1.0.4
  freezed: ^3.0.6
  injectable_generator: ^2.7.0
  build_runner: ^2.4.15
  freezed_annotation: ^3.0.0
  very_good_analysis: ^7.0.0
  json_serializable: ^6.9.1
dependency_overrides:
  analyzer: 7.3.0
