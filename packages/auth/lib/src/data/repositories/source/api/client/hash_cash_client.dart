import 'package:hash_cash_api/hash_cash_api.dart' as hash_cash;
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

@LazySingleton()
class HashCashClient {
  HashCashClient()
      : _instance = hash_cash.HashCashApi(
          dio: BaseClient.dio,
          serializers: hash_cash.standardSerializers,
        ).getHashcashServiceApi();
  final hash_cash.HashcashServiceApi _instance;

  hash_cash.HashcashServiceApi get instance => _instance;
}
