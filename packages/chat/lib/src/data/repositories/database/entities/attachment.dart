import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';

import '../../../../../chat.dart';
import '../../extensions/file_path_extensions.dart';
import '../classes/sticker_object.dart';

part 'attachment.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true, includeIfNull: false)
class Attachment {
  @Id(assignable: true)
  int id;

  final message = ToOne<Message>();

  @Property(uid: 9001)
  @Index()
  String? attachmentId;

  @Property(uid: 9002)
  String? ref;

  @Property(uid: 9003)
  String? stickerRaw;

  @Property(uid: 9004)
  String? photoRaw;

  @Property(uid: 9005)
  String? audioRaw;

  @Property(uid: 9006)
  String? videoRaw;

  @Property(uid: 9007)
  String? voiceMessageRaw;

  @Property(uid: 9008)
  String? videoMessageRaw;

  @Property(uid: 9009)
  String? mediaMessageRaw;

  @Property(uid: 9010)
  String? fileMessageRaw;

  @Property(uid: 9011)
  String? undefinedMessageRaw;

  @Property(uid: 9012)
  bool? isTemp;

  @Property(uid: 9013)
  int? attachmentStatusRaw;

  /// Returns true if this attachment is a sticker
  bool get isSticker => stickerRaw != null;

  /// Returns true if this attachment is valid
  /// - For stickers: must have a non-null, non-empty stickerId
  /// - For other types: must have a non-null, non-empty ref
  bool get isValid {
    if (isSticker) {
      return sticker!.stickerId != null && sticker!.stickerId!.isNotEmpty;
    }
    return ref != null && ref!.isNotEmpty;
  }

  Attachment({
    this.id = 0,
    this.attachmentId,
    this.ref,
    this.stickerRaw,
    this.photoRaw,
    this.audioRaw,
    this.videoRaw,
    this.voiceMessageRaw,
    this.videoMessageRaw,
    this.mediaMessageRaw,
    this.fileMessageRaw,
    this.undefinedMessageRaw,
    this.attachmentStatusRaw,
    this.isTemp = false,
  });

  StickerObject? get sticker => stickerRaw != null
      ? StickerObject.fromJson(jsonDecode(stickerRaw!))
      : null;

  MediaObject? get photo =>
      photoRaw != null ? MediaObject.fromJson(jsonDecode(photoRaw!)) : null;

  MediaObject? get audio =>
      audioRaw != null ? MediaObject.fromJson(jsonDecode(audioRaw!)) : null;

  MediaObject? get video =>
      videoRaw != null ? MediaObject.fromJson(jsonDecode(videoRaw!)) : null;

  MediaObject? get file => fileMessageRaw != null
      ? MediaObject.fromJson(jsonDecode(fileMessageRaw!))
      : null;

  MediaObject? get undefined => undefinedMessageRaw != null
      ? MediaObject.fromJson(jsonDecode(undefinedMessageRaw!))
      : null;

  MediaObject? get voiceMessage => voiceMessageRaw != null
      ? MediaObject.fromJson(jsonDecode(voiceMessageRaw!))
      : null;

  MediaObject? get videoMessage => videoMessageRaw != null
      ? MediaObject.fromJson(jsonDecode(videoMessageRaw!))
      : null;

  MediaObject? get mediaMessage => mediaMessageRaw != null
      ? MediaObject.fromJson(jsonDecode(mediaMessageRaw!))
      : null;

  AttachmentStatusEnum? get attachmentStatus => attachmentStatusRaw != null
      ? AttachmentStatusEnum.getEnumByValue(attachmentStatusRaw)
      : AttachmentStatusEnum.UNSPECIFIED;

  set sticker(StickerObject? value) =>
      stickerRaw = value != null ? jsonEncode(value.toJson()) : null;

  set photo(MediaObject? value) =>
      photoRaw = value != null ? jsonEncode(value.toJson()) : null;

  set audio(MediaObject? value) =>
      audioRaw = value != null ? jsonEncode(value.toJson()) : null;

  set video(MediaObject? value) =>
      videoRaw = value != null ? jsonEncode(value.toJson()) : null;

  set voiceMessage(MediaObject? value) =>
      voiceMessageRaw = value != null ? jsonEncode(value.toJson()) : null;

  set videoMessage(MediaObject? value) =>
      videoMessageRaw = value != null ? jsonEncode(value.toJson()) : null;

  set mediaMessage(MediaObject? value) =>
      mediaMessageRaw = value != null ? jsonEncode(value.toJson()) : null;

  set file(MediaObject? value) =>
      fileMessageRaw = value != null ? jsonEncode(value.toJson()) : null;

  set undefined(MediaObject? value) =>
      undefinedMessageRaw = value != null ? jsonEncode(value.toJson()) : null;

  set attachmentStatus(AttachmentStatusEnum? value) =>
      attachmentStatusRaw = value != null
          ? value.rawValue()
          : AttachmentStatusEnum.UNSPECIFIED.rawValue();

  void updateWith(Attachment updatedAttachment) {
    // Preserve filePath from old attachment before updating other fields
    try {
      // Use extension to preserve filePath
      updatedAttachment.preserveLocalFilePaths(this);
    } catch (e) {
      // Error handling without debug print
    }

    // Update other fields
    attachmentId = updatedAttachment.attachmentId ?? attachmentId;
    ref = updatedAttachment.ref ?? ref;
    stickerRaw = updatedAttachment.stickerRaw ?? stickerRaw;
    photoRaw = updatedAttachment.photoRaw ?? photoRaw;
    audioRaw = updatedAttachment.audioRaw ?? audioRaw;
    videoRaw = updatedAttachment.videoRaw ?? videoRaw;
    voiceMessageRaw = updatedAttachment.voiceMessageRaw ?? voiceMessageRaw;
    videoMessageRaw = updatedAttachment.videoMessageRaw ?? videoMessageRaw;
    mediaMessageRaw = updatedAttachment.mediaMessageRaw ?? mediaMessageRaw;
    fileMessageRaw = updatedAttachment.fileMessageRaw ?? fileMessageRaw;
    attachmentStatusRaw =
        updatedAttachment.attachmentStatusRaw ?? attachmentStatusRaw;
    undefinedMessageRaw =
        updatedAttachment.undefinedMessageRaw ?? undefinedMessageRaw;
  }

  factory Attachment.fromJson(Map<String, dynamic> json) {
    var instance = _$AttachmentFromJson(json);
    instance.attachmentId ??= instance.photo?.attachmentId ??
        instance.audio?.attachmentId ??
        instance.video?.attachmentId ??
        instance.voiceMessage?.attachmentId ??
        instance.videoMessage?.attachmentId ??
        instance.mediaMessage?.attachmentId ??
        instance.file?.attachmentId ??
        instance.undefined?.attachmentId;

    instance.ref ??= instance.photo?.fileRef ??
        instance.audio?.fileRef ??
        instance.video?.fileRef ??
        instance.voiceMessage?.fileRef ??
        instance.videoMessage?.fileRef ??
        instance.mediaMessage?.fileRef ??
        instance.file?.fileRef ??
        instance.undefined?.fileRef;

    instance.attachmentStatusRaw ??= instance.photo?.fileStatus ??
        instance.audio?.fileStatus ??
        instance.video?.fileStatus ??
        instance.voiceMessage?.fileStatus ??
        instance.videoMessage?.fileStatus ??
        instance.mediaMessage?.fileStatus ??
        instance.file?.fileStatus ??
        instance.undefined?.fileStatus;

    return instance;
  }

  Map<String, dynamic> toJson() => _$AttachmentToJson(this);

  Attachment copyWith({
    int? id,
    String? attachmentId,
    String? ref,
    String? stickerRaw,
    String? photoRaw,
    String? audioRaw,
    String? videoRaw,
    String? voiceMessageRaw,
    String? videoMessageRaw,
    String? mediaMessageRaw,
    String? fileMessageRaw,
    String? undefinedMessageRaw,
    bool? isTemp,
    int? attachmentStatusRaw,
  }) {
    return Attachment(
      id: id ?? this.id,
      attachmentId: attachmentId ?? this.attachmentId,
      ref: ref ?? this.ref,
      stickerRaw: stickerRaw ?? this.stickerRaw,
      photoRaw: photoRaw ?? this.photoRaw,
      audioRaw: audioRaw ?? this.audioRaw,
      videoRaw: videoRaw ?? this.videoRaw,
      voiceMessageRaw: voiceMessageRaw ?? this.voiceMessageRaw,
      videoMessageRaw: videoMessageRaw ?? this.videoMessageRaw,
      mediaMessageRaw: mediaMessageRaw ?? this.mediaMessageRaw,
      fileMessageRaw: fileMessageRaw ?? this.fileMessageRaw,
      undefinedMessageRaw: undefinedMessageRaw ?? this.undefinedMessageRaw,
      isTemp: isTemp ?? this.isTemp,
      attachmentStatusRaw: attachmentStatusRaw ?? this.attachmentStatusRaw,
    );
  }
}
