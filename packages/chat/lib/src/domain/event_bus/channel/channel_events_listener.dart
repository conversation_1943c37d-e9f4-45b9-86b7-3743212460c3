import 'dart:async';

import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

typedef onChannelCreateOrUpdatedCallback = void Function(Channel);
typedef onChannelDeletedCallback = void Function(
  String channelId,
  String workspaceId,
);

class ChannelEventsListener {
  static final ChannelEventsListener _instance =
      ChannelEventsListener._internal();
  List<onChannelCreateOrUpdatedCallback> _channelCreatedOrUpdatedCallbacks = [];
  List<onChannelDeletedCallback> _channelDeletedCallbacks = [];

  ChannelEventsListener._internal();

  StreamSubscription? _channelCreatedSubscription;
  StreamSubscription? _channelDeletedSubscription;

  factory ChannelEventsListener() {
    return _instance;
  }

  void addListeners(
    void Function(Channel channel)? onChannelCreatedOrUpdated,
    void Function(String channelId, String workspaceId)? onChannelDeleted,
  ) {
    if (onChannelCreatedOrUpdated != null) {
      _channelCreatedOrUpdatedCallbacks = [
        ..._channelCreatedOrUpdatedCallbacks,
        onChannelCreatedOrUpdated,
      ];
    }
    if (onChannelDeleted != null) {
      _channelDeletedCallbacks = [
        ..._channelDeletedCallbacks,
        onChannelDeleted,
      ];
    }
  }

  void observer() {
    _channelCreatedSubscription =
        AppEventBus().on<ChannelCreatedEvent>().listen((event) {
      _handleChannelCreatedEvent(event);
    });
    _channelDeletedSubscription =
        AppEventBus().on<ChannelDeletedEvent>().listen((event) {
      _handleChannelDeletedEvent(event);
    });
  }

  void removeListener(
    void Function(Channel channel)? onChannelCreatedOrUpdated,
    void Function(String channelId, String workspaceId)? onChannelDeleted,
  ) {
    _channelCreatedOrUpdatedCallbacks.remove(onChannelCreatedOrUpdated);
    _channelDeletedCallbacks.remove(onChannelDeleted);
  }

  void _handleChannelCreatedEvent(ChannelCreatedEvent event) async {
    Channel channel = ChannelSerializer.serializeFromWSResponse(
      wsResponse: event.data,
    )!;
    await _insertChannel(channel);
    for (final callback in _channelCreatedOrUpdatedCallbacks) {
      callback(channel);
    }
  }

  void _handleChannelDeletedEvent(ChannelDeletedEvent event) async {
    final channelId = event.data.data.channelId!;
    final workspaceId = event.data.data.workspaceId!;
    await _deleteChannel(channelId, workspaceId);
    for (final callback in _channelDeletedCallbacks) {
      callback(channelId, workspaceId);
    }

    // Notify the end meeting room event
    AppEventBus.publish(
      EndMeetingRoomEvent(channelId: channelId, workspaceId: workspaceId),
    );
  }

  Future<void> _insertChannel(Channel channel) async {
    await GetIt.instance.get<InsertChannelUseCase>().execute(
          InsertChannelInput(channel: channel),
        );
  }

  Future<void> _deleteChannel(String channelId, String workspaceId) async {
    await GetIt.instance.get<RemoveChannelUseCase>().execute(
          RemoveChannelInput(workspaceId: workspaceId, channelId: channelId),
        );
  }

  void dispose() {
    _channelCreatedOrUpdatedCallbacks.clear();
    _channelDeletedCallbacks.clear();
    _channelCreatedSubscription?.cancel();
    _channelDeletedSubscription?.cancel();
  }
}
