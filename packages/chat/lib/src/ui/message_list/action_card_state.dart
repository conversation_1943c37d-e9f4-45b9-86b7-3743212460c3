class ActionCardState {
  const ActionCardState({
    this.hasUserMessage = true,
    this.hasAvatar = false,
    this.hasAliasName = false,
    this.hasOtherMember = false,
  });

  final bool hasUserMessage;
  final bool hasAvatar;
  final bool hasAliasName;
  final bool hasOtherMember;

  bool get showActionCard => !hasUserMessage;

  ActionCardState copyWith({
    bool? hasUserMessage,
    bool? hasAvatar,
    bool? hasAliasName,
    bool? hasOtherMember,
  }) {
    return ActionCardState(
      hasUserMessage: hasUserMessage ?? this.hasUserMessage,
      hasAvatar: hasAvatar ?? this.hasAvatar,
      hasAliasName: hasAliasName ?? this.hasAliasName,
      hasOtherMember: hasOtherMember ?? this.hasOtherMember,
    );
  }
}
