import 'package:flutter/widgets.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';

import '../../../../../../../shared.dart';

class CopyEvent extends LocalEvent {
  CopyEvent({
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
    required this.context,
    required this.appLocalizations,
    super.source = BaseEvent.LOCAL_SOURCE,
  });

  final String workspaceId;
  final String channelId;
  final String messageId;
  final BuildContext context;
  final AppLocalizations appLocalizations;

  @override
  Map<String, dynamic> toJson() => {};
}
