import 'dart:async';
import 'dart:convert';

import 'package:app_core/core.dart';
import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../data/repositories/database/entities/history.dart';
import '../../data/repositories/database/entities/search.dart';
import '../../data/repositories/interfaces/history_repository.dart';
import '../../data/repositories/interfaces/search_repository.dart';
import '../../domain/usecase/get_suggested_friends_by_type_use_case.dart';
import '../../domain/usecase/search_all_use_case.dart';
import '../../domain/usecase/search_channel_use_case.dart';
import '../../domain/usecase/search_user_use_case.dart';

part 'search_event.dart';
part 'search_state.dart';

@injectable
class SearchBloc extends BaseBloc<SearchEvent, SearchState> {
  SearchBloc(
    this._searchChannelUseCase,
    this._searchUserUseCase,
    this._searchAllUseCase,
    this._historyRepository,
    this._searchRepository,
    this._getSuggestedFriendsByTypeUseCase,
    this._searchUtils,
    this._coreLoadChatUserUseCase,
  ) : super(SearchState()) {
    on<InitiateSearchEvent>(_onInit);
    on<SearchAllEvent>(_onSearchAll);
    on<SearchUserEvent>(_onSearchUser);
    on<SearchChannelEvent>(_onSearchChannel);
    on<RemoveHistoryEvent>(_onRemoveHistory);
    on<SaveHistoryEvent>(_onSaveHistory);
    on<ChangeCurrentRecentSegmentEvent>(_onChangeCurrentRecentSegment);
    on<SaveRecentSearchEvent>(_onSaveRecentSearch);
    on<CheckUserEvent>(_onCheckUser);
    on<CheckChannelEvent>(_onCheckChannel);
    on<GetSuggestedFriendsEvent>(_onGetMoreSuggestedFriends);
    on<OnInitUserPrivateDataEvent>(_onInitUserPrivateDataEvent);
  }

  final SearchAllUseCase _searchAllUseCase;
  final SearchChannelUseCase _searchChannelUseCase;
  final SearchUserUseCase _searchUserUseCase;
  final CoreLoadChatUserUseCase _coreLoadChatUserUseCase;
  final GetSuggestedFriendsByTypeUseCase _getSuggestedFriendsByTypeUseCase;
  final HistoryRepository _historyRepository;
  final SearchRepository _searchRepository;
  final SearchUtils _searchUtils;
  CoreHandlerUtils coreHandlerUtils = CoreHandlerUtils();

  String _keyword = '';
  bool _isCheckingUSer = false;

  Future<void> _onInit(
    InitiateSearchEvent event,
    Emitter<SearchState> emit,
  ) async {
    _loadRecentSearches(emit);
    _loadHistories(emit);
    await _getSuggestedFriends(emit);
  }

  Future<void> _getSuggestedFriends(Emitter<SearchState> emit) async {
    final outputFriends = await _getSuggestedFriendsByTypeUseCase.execute(
      GetSuggestedFriendsByTypeInput(suggestionType: SuggestionType.friend),
    );
    final outputChannel = await _getSuggestedFriendsByTypeUseCase.execute(
      GetSuggestedFriendsByTypeInput(suggestionType: SuggestionType.channel),
    );

    emit(
      state.copyWith(
        isNewResults: false,
        sameChannelUsers: SearchResults(
          results: outputChannel.friends,
          nextPageToken: outputChannel.nextPageToken,
          hasNext: outputChannel.hasNext,
        ),
        usersYouMayKnow: SearchResults(
          results: outputFriends.friends,
          nextPageToken: outputFriends.nextPageToken,
          hasNext: outputFriends.hasNext,
        ),
        isFirstLoadingSuggestedFriends: false,
        isNewSameChannelUsers: true,
        isNewUsersYouMayKnow: true,
      ),
    );
  }

  void _loadRecentSearches(Emitter<SearchState> emit) {
    final recentSearches = _searchRepository.getAll();
    emit(
      state.copyWith(
        recentSearches: recentSearches,
        isNewResults: false,
        isNewSameChannelUsers: false,
        isNewUsersYouMayKnow: false,
      ),
    );
  }

  void _loadHistories(Emitter<SearchState> emit) {
    final histories = _historyRepository.getAll();
    emit(
      state.copyWith(
        history: histories,
        isNewResults: false,
        isNewSameChannelUsers: false,
        isNewUsersYouMayKnow: false,
      ),
    );
  }

  FutureOr<void> _onSearchAll(
    SearchAllEvent event,
    Emitter<SearchState> emit,
  ) async {
    if (event.keyword.isEmpty) {
      emit(
        state.copyWith(
          isSearching: false,
          allSearchResults: null,
          isNewSameChannelUsers: false,
          isNewUsersYouMayKnow: false,
        ),
      );
      return;
    }
    _keyword = event.keyword;
    emit(
      state.copyWith(
        isSearching: true,
        isNewSameChannelUsers: false,
        isNewUsersYouMayKnow: false,
      ),
    );
    final output =
        await _searchAllUseCase.execute(SearchAllInput(keyword: event.keyword));

    var search = coreHandlerUtils.searchUserSearchAliasName(
      keyword: event.keyword,
      listItemFromApi: output.searchResults,
    );

    if (_keyword == event.keyword) {
      emit(
        state.copyWith(
          allSearchResults: SearchResults(
            results: search ?? [],
            nextPageToken: '1',
            hasNext: false,
          ),
          isSearching: false,
          isNewResults: true,
          isNewSameChannelUsers: false,
          isNewUsersYouMayKnow: false,
        ),
      );
    }
  }

  FutureOr<void> _onSearchUser(
    SearchUserEvent event,
    Emitter<SearchState> emit,
  ) async {
    if (event.keyword.isEmpty) {
      emit(
        state.copyWith(
          isSearching: false,
          userSearchResults: null,
          isNewSameChannelUsers: false,
          isNewUsersYouMayKnow: false,
        ),
      );
      return;
    }
    _keyword = event.keyword;
    emit(
      state.copyWith(
        isSearching: true,
        isNewSameChannelUsers: false,
        isNewUsersYouMayKnow: false,
      ),
    );
    final output = await _searchUserUseCase.execute(
      SearchUserInput(
        keyword: event.keyword,
        nextPageToken: event.nextPageToken,
      ),
    );
    var search = coreHandlerUtils.searchUserSearchAliasName(
      keyword: event.keyword,
      listItemFromApi: output.searchResults,
    );
    if (_keyword == event.keyword) {
      emit(
        state.copyWith(
          userSearchResults: SearchResults(
            results: search ?? [],
            nextPageToken: output.nextPageToken,
            hasNext: output.hasNext,
          ),
          isSearching: false,
          isNewResults: true,
          isNewSameChannelUsers: false,
          isNewUsersYouMayKnow: false,
        ),
      );
    }
  }

  FutureOr<void> _onSearchChannel(
    SearchChannelEvent event,
    Emitter<SearchState> emit,
  ) async {
    if (event.keyword.isEmpty) {
      emit(
        state.copyWith(
          isSearching: false,
          channelSearchResults: null,
          isNewSameChannelUsers: false,
          isNewUsersYouMayKnow: false,
        ),
      );
      return;
    }
    _keyword = event.keyword;
    emit(
      state.copyWith(
        isSearching: true,
        isNewSameChannelUsers: false,
        isNewUsersYouMayKnow: false,
      ),
    );
    final output = await _searchChannelUseCase.execute(
      SearchChannelInput(
        keyword: event.keyword,
        nextPageToken: event.nextPageToken,
      ),
    );
    if (_keyword == event.keyword) {
      emit(
        state.copyWith(
          channelSearchResults: SearchResults(
            results: output.searchResults,
            nextPageToken: output.nextPageToken,
            hasNext: output.hasNext,
          ),
          isSearching: false,
          isNewResults: true,
          isNewSameChannelUsers: false,
          isNewUsersYouMayKnow: false,
        ),
      );
    }
  }

  FutureOr<void> _onRemoveHistory(
    RemoveHistoryEvent event,
    Emitter<SearchState> emit,
  ) {
    _historyRepository.delete(event.keyword);

    final newHistories = List<History>.from(state.history);
    newHistories.removeWhere((e) => e.keyword == event.keyword);
    emit(
      state.copyWith(
        history: newHistories,
        isNewResults: false,
        isNewSameChannelUsers: false,
        isNewUsersYouMayKnow: false,
      ),
    );
  }

  FutureOr<void> _onSaveHistory(
    SaveHistoryEvent event,
    Emitter<SearchState> emit,
  ) {
    if (event.keyword.isNotEmpty) {
      _historyRepository.insert(event.keyword);
      _loadHistories(emit);
    }
  }

  FutureOr<void> _onChangeCurrentRecentSegment(
    ChangeCurrentRecentSegmentEvent event,
    Emitter<SearchState> emit,
  ) {
    emit(
      state.copyWith(
        currentSearchSegmentIndex: event.index,
        isNewResults: false,
        isNewSameChannelUsers: false,
        isNewUsersYouMayKnow: false,
      ),
    );
  }

  FutureOr<void> _onSaveRecentSearch(
    SaveRecentSearchEvent event,
    Emitter<SearchState> emit,
  ) {
    final recentSearch = event.model;
    recentSearch.timestamp = DateTime.now().toUtc().millisecondsSinceEpoch;
    _searchRepository.insert(event.model);
    _loadRecentSearches(emit);
  }

  Future<void> _onCheckUser(
    CheckUserEvent event,
    Emitter<SearchState> emit,
  ) async {
    if (_isCheckingUSer) {
      return;
    }
    _isCheckingUSer = true;
    final user = await _searchUtils.checkUser(
      event.context,
      userId: event.userId,
    );

    if (user == null) {
      final dbUser = _searchRepository.getUserSearchByUserId(event.userId);
      if (dbUser != null) {
        _searchRepository.delete(dbUser.id);
        _loadRecentSearches(emit);
      }
    } else {
      _searchRepository.insert(
        Search.user(
          sessionKey: Config.getInstance().activeSessionKey!,
          userId: user.userId,
          embed: jsonEncode(
            {
              'avatar': user.profile?.avatar,
              'displayName': user.profile?.displayName,
              'username': user.username,
            },
          ),
        ),
      );
      _loadRecentSearches(emit);
      event.onUserExists();
    }
    _isCheckingUSer = false;
  }

  Future<void> _onCheckChannel(
    CheckChannelEvent event,
    Emitter<SearchState> emit,
  ) async {
    final channel = await _searchUtils.isExistChannel(
      event.context,
      channelId: event.channelId,
      workspaceId: event.workspaceId,
    );
    if (channel != null) {
      _searchRepository.insert(
        Search.channel(
          sessionKey: Config.getInstance().activeSessionKey!,
          channelId: channel.channelId,
          workspaceId: channel.workspaceId,
          embed: jsonEncode(
            {'avatar': channel.avatar, 'channelName': channel.name},
          ),
        ),
      );
      _loadRecentSearches(emit);
      event.onChannelExists();
    } else {
      final dbChannel = _searchRepository.getChannelSearch(
        event.channelId,
        event.workspaceId,
      );
      if (dbChannel != null) {
        _searchRepository.delete(dbChannel.id);
        _loadRecentSearches(emit);
      }
    }
  }

  Future<void> _onGetMoreSuggestedFriends(
    GetSuggestedFriendsEvent event,
    Emitter<SearchState> emit,
  ) async {
    final output = await _getSuggestedFriendsByTypeUseCase.execute(
      GetSuggestedFriendsByTypeInput(
        nextPageToken: event.nextPageToken,
        suggestionType: event.suggestionType,
      ),
    );
    switch (event.suggestionType) {
      case SuggestionType.channel:
        emit(
          state.copyWith(
            isNewResults: false,
            sameChannelUsers: SearchResults(
              results: output.friends,
              nextPageToken: output.nextPageToken,
              hasNext: output.hasNext,
            ),
            isNewSameChannelUsers: true,
          ),
        );
      case SuggestionType.friend:
        emit(
          state.copyWith(
            isNewResults: false,
            usersYouMayKnow: SearchResults(
              results: output.friends,
              nextPageToken: output.nextPageToken,
              hasNext: output.hasNext,
            ),
            isNewUsersYouMayKnow: true,
          ),
        );
        break;
    }
  }

  Future<void> _onInitUserPrivateDataEvent(
    OnInitUserPrivateDataEvent event,
    Emitter<SearchState> emit,
  ) async {
    coreHandlerUtils.setupUserPrivateData(
      _coreLoadChatUserUseCase,
      typeObject: TypeObject.search,
    );
  }
}
