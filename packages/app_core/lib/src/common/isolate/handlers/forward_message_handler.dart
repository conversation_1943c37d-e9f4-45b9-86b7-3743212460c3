import 'package:shared/shared.dart';

import '../../../data/source/api/client/isolate_api_client.dart';
import '../../network/retry_manager.dart';
import '../input/worker_send_forward_message_input.dart';
import '../output/worker_send_message_output.dart';
import 'base_message_handler.dart';

/// Specialized handler for forwarding messages
class ForwardMessageHandler extends BaseMessageHandler<WorkerSendForwardMessageInput> {
  ForwardMessageHandler({
    required super.apiClient,
    required super.retryManager,
  });

  @override
  WorkerSendForwardMessageInput parseInput(Map<String, dynamic> inputData) {
    return WorkerSendForwardMessageInput.fromJson(inputData);
  }

  @override
  String getApiEndpoint(WorkerSendForwardMessageInput input) {
    return '/Message/' + (input.isDm() ? 'ForwardMessagesToDMChannel' : 'ForwardMessagesToChannel');
  }

  @override
  Future<WorkerSendMessageOutput> executeMessageOperation(
    WorkerSendForwardMessageInput input,
    DateTime createTime,
  ) async {
    return await makeRetryableApiCall(
      input: input,
      createTime: createTime,
      apiCall: () => apiClient.post(
        getApiEndpoint(input),
        body: input.toRequest(),
      ),
    );
  }

  /// Legacy method for backward compatibility
  Future<WorkerSendMessageOutput> handleForwardMessage(
    Map<String, dynamic> inputData,
  ) async {
    return await handleMessage(inputData);
  }
}
