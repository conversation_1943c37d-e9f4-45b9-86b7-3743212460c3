import 'package:injectable/injectable.dart';
import 'package:message_api/message_api.dart';
import 'package:shared/shared.dart';

import '../../../data/source/api/client/report_client.dart';

@Injectable()
class MessageReportUseCase
    extends BaseFutureUseCase<MessageReportInput, MessageReportOutput> {
  MessageReportUseCase();

  @override
  Future<MessageReportOutput> buildUseCase(
    MessageReportInput input,
  ) async {
    var response;

    if (input.workspaceId == null || input.channelId == null) {
      final requestBody = V3ReportDMMessageRequestBuilder()
        ..userId = input.userId
        ..messageId = input.messageId
        ..reportCategory = input.reportCategory
        ..pretendingTo = input.pretendingTo
        ..reportReason = input.other;
      response = await MessageClient()
          .instance
          .reportDMMessage(body: requestBody.build());
    } else {
      final requestBody = V3ReportMessageRequestBuilder()
        ..workspaceId = input.workspaceId
        ..channelId = input.channelId
        ..messageId = input.messageId
        ..reportCategory = input.reportCategory
        ..pretendingTo = input.pretendingTo
        ..reportReason = input.other;
      response = await MessageClient()
          .instance
          .reportMessage(body: requestBody.build());
    }

    return MessageReportOutput(
      ok: response.data?.ok ?? false,
      error: response.data?.error,
    );
  }
}

class MessageReportInput extends BaseInput {
  MessageReportInput({
    this.workspaceId,
    this.channelId,
    this.userId,
    required this.messageId,
    this.pretendingTo,
    this.reportCategory,
    this.other,
  });

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String messageId;
  final V3PretendingTo? pretendingTo;
  final V3ReportCategory? reportCategory;
  final String? other;
}

class MessageReportOutput extends BaseOutput {
  MessageReportOutput({
    required this.ok,
    this.error,
  });

  final bool ok;
  final V3Error? error;
}
