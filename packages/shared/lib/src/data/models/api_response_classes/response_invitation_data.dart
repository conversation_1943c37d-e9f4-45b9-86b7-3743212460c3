import 'package:json_annotation/json_annotation.dart';

part 'response_invitation_data.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseInvitationData {
  @Json<PERSON>ey(name: 'channel')
  Map<String, dynamic>? channel;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'code')
  String? code;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'isExpired')
  bool? isExpired;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'expireTime')
  String? expireTime;

  @<PERSON>son<PERSON><PERSON>(name: 'isJoined')
  bool? isJoined;

  @Json<PERSON><PERSON>(name: 'invitationLink')
  String? invitationLink;

  @<PERSON>son<PERSON><PERSON>(name: 'createTime')
  String? createTime;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'updateTime')
  String? updateTime;

  ResponseInvitationData({
    this.channel,
    this.code,
    this.isExpired,
    this.expireTime,
    this.isJoined,
    this.invitationLink,
    this.createTime,
    this.updateTime,
  });

  factory ResponseInvitationData.fromJson(Map<String, dynamic> json) =>
      _$ResponseInvitationDataFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseInvitationDataToJson(this);
}
