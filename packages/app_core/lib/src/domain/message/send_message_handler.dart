import 'dart:async' show unawaited;
import 'dart:convert';

import 'package:chat/chat.dart';
import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations_en.dart';
import 'package:message_api/message_api.dart';
import 'package:shared/shared.dart';
import 'package:sticker/sticker.dart';
import 'package:upload_manager/upload_manager.dart' hide Config;

import '../../../core.dart';
import '../../common/di/di.dart';
import '../../common/isolate/input/worker_send_forward_message_input.dart';
import '../../common/isolate/input/worker_send_location_input.dart';
import '../../common/isolate/input/worker_send_media_input.dart';
import '../../common/isolate/input/worker_send_quote_message_input.dart';
import '../../common/isolate/input/worker_send_sticker_input.dart';
import '../../common/isolate/models/worker_file_metadata.dart';
import '../../common/isolate/models/worker_media_object.dart';
import '../../common/isolate/models/worker_upload_file.dart';
import 'send_file_message_handler.dart';
import 'send_image_message_handler.dart';
import 'send_video_message_handler.dart';
import 'send_voice_message_handler.dart';

/// Handles sending messages (text, sticker, media, etc.) for DM or channels.
class SendMessageHandler {
  /// Workspace ID for the current channel.
  String? _workspaceId;

  /// Channel ID for the current channel.
  String? _channelId;

  /// User ID for direct messages.
  final String? _userId;

  /// The bloc instance used for managing message states and events.
  MessagesBloc? _messagesBloc;

  /// A listener that observes message-sending completion from WorkManager.
  final SendMessageListener _sendMessageListener =
      GetIt.instance.get<SendMessageListener>();

  /// Handles sending images.
  late final SendImageMessageHandler _sendImageMessageHandler;

  /// Handles sending videos.
  late final SendVideoMessageHandler _sendVideoMessageHandler;

  /// Handles sending voice messages.
  late final SendVoiceMessageHandler _sendVoiceMessageHandler;

  /// Handles sending files.
  late final SendFileMessageHandler _sendFileMessageHandler;

  final isolateTaskService = IsolateTaskService();

  /// Creates a new instance of [SendMessageHandler].
  SendMessageHandler({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
  })  : _workspaceId = workspaceId,
        _channelId = channelId,
        _userId = userId {
    if (_userId == null && (_workspaceId == null || _channelId == null)) {
      Log.e(
        'User ID is required or both Workspace ID and Channel ID must be provided.',
      );
      return;
    }
  }

  /// Sets the [MessagesBloc] and initializes related handlers.
  set messagesBloc(MessagesBloc value) {
    if (_userId != null && _channelId == null) {
      final channel = GetIt.instance
          .get<GetChannelUseCase>()
          .execute(GetChannelInput(userId: _userId));
      _workspaceId = channel.channel?.workspaceId;
      _channelId = channel.channel?.channelId;
    }
    _messagesBloc = value;

    _sendImageMessageHandler = SendImageMessageHandler(
      workspaceId: _workspaceId,
      channelId: _channelId,
      userId: _userId,
      messagesBloc: _messagesBloc,
    );
    _sendVideoMessageHandler = SendVideoMessageHandler(
      workspaceId: _workspaceId,
      channelId: _channelId,
      userId: _userId,
      messagesBloc: _messagesBloc,
    );
    _sendVoiceMessageHandler = SendVoiceMessageHandler(
      workspaceId: _workspaceId,
      channelId: _channelId,
      userId: _userId,
      messagesBloc: _messagesBloc,
    );
    _sendFileMessageHandler = SendFileMessageHandler(
      workspaceId: _workspaceId,
      channelId: _channelId,
      userId: _userId,
      messagesBloc: _messagesBloc,
    );
  }

  /// Determines whether this is a direct message (DM).
  bool isDm() => _userId != null;

  /// Sends a text message. If [errorMessage] is provided, it is treated as a resend flow.
  Future<void> sendTextMessage(
    String message, {
    Message? errorMessage,
  }) async {
    final channel = await _getChannel();
    if (channel == null) return;

    final tempMessage = errorMessage ??
        TempMessageFactory.createTextMessage(
          workspaceId: channel.workspaceId,
          channelId: channel.channelId,
          userId: _userId,
          content: message.trim(),
        );

    if (errorMessage != null) {
      tempMessage.createTime = TimeUtils.now();
      tempMessage.updateTime = TimeUtils.now();
    }

    if (channel.isTemp) {
      channel.lastMessageCreateTime =
          StringExtensions.toFormattedString(tempMessage.createTime);
      channel.lastMessageContent = tempMessage.content;
      await getIt<InsertChannelUseCase>()
          .execute(InsertChannelInput(channel: channel));
    }

    tempMessage.messageStatusRaw = MessageStatus.PENDING.rawValue();

    if (errorMessage == null) {
      _addTempMessage(tempMessage);
    } else {
      _resendMessage(tempMessage);
    }
    _saveTempMessage(tempMessage);

    // Set up listener for this message
    _listenSentMessageResponse(tempMessage);

    // Register task in a separate Future
    unawaited(
      _registerTextMessageTask(
        channel: channel,
        message: message.trim(),
        tempMessage: tempMessage,
      ),
    );
  }

  /// Helper method to register text message sending task
  Future<void> _registerTextMessageTask({
    required Channel channel,
    required String message,
    required Message tempMessage,
  }) async {
    try {
      final inputData = WorkerSendTextInput(
        workspaceId: channel.workspaceId,
        channelId: channel.channelId,
        userId: _userId ?? '',
        ref: tempMessage.ref!,
        taskName: TaskNameEnum.sendMessage.value,
        content: message,
        attachmentType: AttachmentType.UNSPECIFIED.rawValue(),
        creationTime: DateTime.now(),
      ).toJson();

      await _registerTaskOnNextEventLoop(
        taskId: tempMessage.ref!,
        taskName: TaskNameEnum.sendMessage.value,
        inputData: inputData,
        networkRequired: true,
        methodName: 'sendTextMessage',
      );
    } catch (e, stackTrace) {
      // Handle error without affecting UI
      Log.e(
        name: 'SendMessageHandler',
        ['Error registering text message task', e, stackTrace],
      );
    }
  }

  /// Resends a text message by re-invoking [sendTextMessage] with [errorMessage].
  Future<void> resendTextMessage(Message message) async {
    await sendTextMessage(
      message.content ?? '',
      errorMessage: message,
    );
  }

  /// Sends a sticker message. If [errorMessage] is provided, it is treated as a resend flow.
  Future<void> sendStickerMessage(
    Sticker sticker, {
    Message? errorMessage,
  }) async {
    final channel = await _getChannel();
    if (channel == null) return;

    final tempMessage = errorMessage ??
        TempMessageFactory.createStickerMessage(
          workspaceId: channel.workspaceId,
          channelId: channel.channelId,
          userId: _userId,
          sticker: sticker,
        );

    if (errorMessage != null) {
      tempMessage.createTime = TimeUtils.now();
      tempMessage.updateTime = TimeUtils.now();
    }

    if (channel.isTemp) {
      channel.lastMessageCreateTime =
          StringExtensions.toFormattedString(tempMessage.createTime);
      channel.lastMessageContent = tempMessage.content;
      await getIt<InsertChannelUseCase>()
          .execute(InsertChannelInput(channel: channel));
    }

    tempMessage.messageStatusRaw = MessageStatus.PENDING.rawValue();

    if (errorMessage == null) {
      _addTempMessage(tempMessage);
    } else {
      _resendMessage(tempMessage);
    }
    _saveTempMessage(tempMessage);

    // Set up listener for this message
    _listenSentMessageResponse(tempMessage);

    // Register task in a separate Future
    unawaited(
      _registerStickerMessageTask(
        channel: channel,
        sticker: sticker,
        tempMessage: tempMessage,
        isPoked: false,
      ),
    );
  }

  /// Helper method to register sticker message sending task
  Future<void> _registerStickerMessageTask({
    required Channel channel,
    required Sticker sticker,
    required Message tempMessage,
    required bool isPoked,
  }) async {
    try {
      final inputData = WorkerSendStickerInput(
        workspaceId: channel.workspaceId,
        channelId: channel.channelId,
        userId: _userId ?? '',
        ref: tempMessage.ref!,
        taskName: TaskNameEnum.sendMessage.value,
        stickerId: sticker.stickerId,
        attachmentType: AttachmentType.STICKER.rawValue(),
        creationTime: DateTime.now(),
        isPoked: isPoked,
      ).toJson();

      await _registerTaskOnNextEventLoop(
        taskId: tempMessage.ref!,
        taskName: TaskNameEnum.sendMessage.value,
        inputData: inputData,
        networkRequired: true,
        methodName: isPoked ? 'sendPokeMessage' : 'sendStickerMessage',
      );
    } catch (e, stackTrace) {
      // Handle error without affecting UI
      Log.e(
        name: 'SendMessageHandler',
        ['Error registering sticker message task', e, stackTrace],
      );
    }
  }

  /// Resends a sticker message by re-invoking [sendStickerMessage] with [errorMessage].
  Future<void> resendStickerMessage(Message message) async {
    final stickerId = message.mediaAttachments.first.sticker?.stickerId;
    if (stickerId == null) {
      Log.e('No stickerId found');
      return;
    }

    if (stickerId == GlobalConfig.quickStickerId) {
      await sendStickerMessage(
        Sticker.quickSticker(),
        errorMessage: message,
      );
      return;
    }

    final output = await GetIt.I
        .get<GetStickerUseCase>()
        .execute(GetStickerInput(stickerId: stickerId));

    if (output.sticker == null) {
      Log.e('No sticker found');
      return;
    }

    await sendStickerMessage(
      output.sticker!,
      errorMessage: message,
    );
  }

  /// Sends multiple images.
  Future<void> sendImageMessage(
    List<UploadFile> imageList, {
    Message? errorMessage,
  }) async {
    if (imageList.isEmpty) return;

    final channel = await _getChannel();

    if (channel == null) return;

    final tempMessage = errorMessage ??
        await _sendImageMessageHandler.createTemporaryPhotoMessage(
          workspaceId: channel.workspaceId,
          channelId: channel.channelId,
          userId: _userId,
          imageList: imageList,
        );

    if (channel.isTemp) {
      channel.lastMessageCreateTime =
          StringExtensions.toFormattedString(tempMessage.createTime);
      channel.lastMessageContent = tempMessage.content;
      await getIt<InsertChannelUseCase>()
          .execute(InsertChannelInput(channel: channel));
    }

    tempMessage.messageStatusRaw = MessageStatus.PENDING.rawValue();

    if (errorMessage == null) {
      _addTempMessage(tempMessage);
    } else {
      _resendMessage(tempMessage);
    }

    _saveTempMessage(tempMessage);

    // Set up listener for this message
    _listenSentMessageResponse(tempMessage);

    unawaited(
      _registerImageMessageTask(
        channel: channel,
        tempMessage: tempMessage,
      ),
    );
  }

  /// Helper method to register image message sending task
  Future<void> _registerImageMessageTask({
    required Channel channel,
    required Message tempMessage,
  }) async {
    try {
      final taskId = tempMessage.ref!;

      final uploadFiles = tempMessage.mediaAttachments
          .where(
        (attachment) =>
            attachment.photo != null && attachment.photo!.filePath != null,
      )
          .map((attachment) {
        final file = attachment.photo!;
        return WorkerUploadFile(
          fileRef: file.fileRef,
          path: file.filePath!,
          name: file.fileMetadata?.filename ?? '',
          size: file.fileMetadata?.filesize ?? 0,
          messageRef: tempMessage.ref!,
        );
      }).toList();

      debugPrint(
        'SendMessageHandler._registerImageMessageTask: ${uploadFiles}',
      );

      if (uploadFiles.isEmpty) {
        Log.e(
          name: 'SendMessageHandler',
          'No valid upload files found in tempMessage for task registration',
        );
        return;
      }

      final inputData = WorkerCompressAndUploadImagesInput(
        workspaceId: channel.workspaceId,
        channelId: channel.channelId,
        userId: _userId,
        ref: taskId,
        taskName: TaskNameEnum.compressAndUploadImages.value,
        attachmentType: AttachmentType.PHOTO.rawValue(),
        messageRef: tempMessage.ref!,
        uploadFilesEmbed: jsonEncode(uploadFiles),
        uploadConcurrency: GlobalConfig.uploadConcurrency,
        creationTime: DateTime.now(),
      ).toJson();

      await _registerTaskOnNextEventLoop(
        taskId: taskId,
        taskName: TaskNameEnum.compressAndUploadImages.value,
        inputData: inputData,
        networkRequired: true,
        methodName: 'sendImageMessage',
      );
    } catch (e, stackTrace) {
      // Handle error without affecting UI
      Log.e(
        name: 'SendMessageHandler',
        ['Error registering image message task', e, stackTrace],
      );
    }
  }

  /// Common helper function to register task on the next event loop
  Future<void> _registerTaskOnNextEventLoop({
    required String taskId,
    required String taskName,
    required Map<String, dynamic> inputData,
    bool networkRequired = true,
    String methodName = '_registerTaskOnNextEventLoop',
    int retryCount = 0,
  }) async {
    final startTime = DateTime.now();

    // Log information about the task to be registered
    RILogger.printClassMethodDebug(
      'SendMessageHandler',
      methodName,
      '[DEBUG][MessageRef:$taskId] Scheduling task registration on next event loop: $taskName, retry: $retryCount',
    );

    // Ensure IsolateTaskService has been initialized
    try {
      if (!isolateTaskService.isInitialized) {
        RILogger.printClassMethodDebug(
          'SendMessageHandler',
          methodName,
          '[DEBUG][MessageRef:$taskId] Ensuring IsolateTaskService is initialized before registering task: $taskName',
        );

        // Initialize IsolateTaskService with static configuration from SendMessageConfig
        await isolateTaskService.initialize();

        RILogger.printClassMethodDebug(
          'SendMessageHandler',
          methodName,
          '[DEBUG][MessageRef:$taskId] IsolateTaskService initialized successfully, proceeding with task registration',
        );
      }
    } catch (e) {
      RILogger.printClassMethodDebug(
        'SendMessageHandler',
        methodName,
        '[DEBUG][MessageRef:$taskId] Error ensuring IsolateTaskService is initialized: $e',
      );

      // Limit retry attempts to avoid infinite loops
      if (retryCount < 3) {
        // Retry after 1 second
        await Future.delayed(Duration(seconds: 1));
        return _registerTaskOnNextEventLoop(
          taskId: taskId,
          taskName: taskName,
          inputData: inputData,
          networkRequired: networkRequired,
          methodName: methodName,
          retryCount: retryCount + 1,
        );
      } else {
        RILogger.printClassMethodDebug(
          'SendMessageHandler',
          methodName,
          '[DEBUG][MessageRef:$taskId] Failed to initialize ResilientIsolate after $retryCount retries, proceeding with task registration anyway',
        );
      }
    }

    // Use Future.microtask to register task on the next event loop
    // Helps reduce load on the main thread without using isolate
    RILogger.printClassMethodDebug(
      'SendMessageHandler',
      methodName,
      '[DEBUG][MessageRef:$taskId] Starting microtask for task registration: $taskName',
    );

    // Use Future.microtask to register task on the next event loop
    final taskFuture = Future.microtask(() async {
      try {
        final microtaskStartTime = DateTime.now();
        RILogger.printClassMethodDebug(
          'SendMessageHandler',
          methodName,
          '[DEBUG][MessageRef:$taskId] Inside microtask, calling IsolateTaskService.registerTask for: $taskName',
        );

        await IsolateTaskService().registerTask(
          taskId: taskId,
          taskName: taskName,
          inputData: inputData,
          networkRequired: networkRequired,
        );

        final elapsedMs =
            DateTime.now().difference(microtaskStartTime).inMilliseconds;
        RILogger.printClassMethodDebug(
          'SendMessageHandler',
          methodName,
          '[DEBUG][MessageRef:$taskId] Task registered successfully: $taskName, time in microtask: ${elapsedMs}ms',
        );

        final totalElapsedMs =
            DateTime.now().difference(startTime).inMilliseconds;
        RILogger.printClassMethodDebug(
          'SendMessageHandler',
          methodName,
          '[DEBUG][MessageRef:$taskId] Task registration process completed in ${totalElapsedMs}ms',
        );
      } catch (e, stackTrace) {
        RILogger.printClassMethodDebug(
          'SendMessageHandler',
          methodName,
          '[DEBUG][MessageRef:$taskId] Error registering task: $e',
        );
        Log.e(
          name: 'SendMessageHandler',
          ['Error registering task', e, stackTrace],
        );

        // Limit retry attempts to avoid infinite loops
        if (retryCount < 3) {
          // Retry after 1 second
          await Future.delayed(Duration(seconds: 1));
          return _registerTaskOnNextEventLoop(
            taskId: taskId,
            taskName: taskName,
            inputData: inputData,
            networkRequired: networkRequired,
            methodName: methodName,
            retryCount: retryCount + 1,
          );
        }

        // Re-throw so caller can handle
        rethrow;
      }
    });

    return taskFuture;
  }

  /// New method to register video compression and upload task
  Future<void> _registerVideoCompressionTask({
    required Channel channel,
    required UploadFile video,
    required Message tempMessage,
    required V3AttachmentTypeEnum type,
  }) async {
    try {
      final taskId = tempMessage.ref!;
      final inputData = WorkerCompressVideoInput(
        workspaceId: channel.workspaceId,
        channelId: channel.channelId,
        userId: _userId ?? '',
        ref: taskId,
        taskName: TaskNameEnum.compressAndUploadVideoMessage.value,
        attachmentType: type == V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_VIDEO
            ? AttachmentType.VIDEO.rawValue()
            : AttachmentType.VIDEO_MESSAGE.rawValue(),
        messageRef: tempMessage.ref!,
        uploadFileEmbed: jsonEncode(
          WorkerUploadFile(
            fileRef: video.fileRef,
            path: video.path,
            name: video.name,
            size: video.size,
            messageRef: tempMessage.ref!,
          ).toJson(),
        ),
        creationTime: DateTime.now(),
      ).toJson();

      await _registerTaskOnNextEventLoop(
        taskId: taskId,
        taskName: TaskNameEnum.compressAndUploadVideoMessage.value,
        inputData: inputData,
        networkRequired: true,
        methodName: 'sendVideoMessage',
      );
    } catch (e, stackTrace) {
      // Handle error without affecting UI
      Log.e(
        name: 'SendMessageHandler',
        ['Error registering video compression and upload task', e, stackTrace],
      );
    }
  }

  /// Sends multiple videos, with optional [type] override.
  Future<void> sendVideoMessage(
    List<UploadFile> videoList, {
    V3AttachmentTypeEnum type = V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_VIDEO,
    Message? errorMessage,
  }) async {
    final channel = await _getChannel();
    if (channel == null) return;

    // Create and display all temporary messages first
    final List<Map<String, dynamic>> videoMessagePairs = [];

    for (final video in videoList) {
      final tempMessage = errorMessage ??
          _sendVideoMessageHandler.createTemporaryVideoMessage(
            workspaceId: channel.workspaceId,
            channelId: channel.channelId,
            userId: _userId,
            video: video,
            type: type,
          );

      if (errorMessage != null) {
        tempMessage.createTime = TimeUtils.now();
        tempMessage.updateTime = TimeUtils.now();
      }

      if (channel.isTemp) {
        channel.lastMessageCreateTime =
            StringExtensions.toFormattedString(tempMessage.createTime);
        channel.lastMessageContent = tempMessage.content;
        await getIt<InsertChannelUseCase>()
            .execute(InsertChannelInput(channel: channel));
      }

      tempMessage.messageStatusRaw = MessageStatus.PENDING.rawValue();

      if (errorMessage == null) {
        _addTempMessage(tempMessage);
      } else {
        _resendMessage(tempMessage);
      }
      _saveTempMessage(tempMessage);

      // Set up listener for this message
      _listenSentMessageResponse(tempMessage);

      // Save video and message pair for later processing
      videoMessagePairs.add({
        'video': video,
        'message': tempMessage,
      });
    }

    // Process task registration for each video-message pair
    for (var pair in videoMessagePairs) {
      final video = pair['video'] as UploadFile;
      final tempMessage = pair['message'] as Message;

      // Register task in a separate Future
      unawaited(
        _registerVideoCompressionTask(
          channel: channel,
          video: video,
          tempMessage: tempMessage,
          type: type,
        ),
      );
    }
  }

  /// Sends a voice message.
  Future<void> sendVoiceMessage(
    UploadFile voiceFile, {
    Message? errorMessage,
  }) async {
    final channel = await _getChannel();
    if (channel == null) return;

    final tempMessage = errorMessage ??
        _sendVoiceMessageHandler.createTemporaryVoiceMessage(
          workspaceId: channel.workspaceId,
          channelId: channel.channelId,
          userId: _userId,
          voice: voiceFile,
        );

    if (errorMessage != null) {
      tempMessage.createTime = TimeUtils.now();
      tempMessage.updateTime = TimeUtils.now();
    }

    if (channel.isTemp) {
      channel.lastMessageCreateTime =
          StringExtensions.toFormattedString(tempMessage.createTime);
      channel.lastMessageContent = tempMessage.content;
      await getIt<InsertChannelUseCase>()
          .execute(InsertChannelInput(channel: channel));
    }

    tempMessage.messageStatusRaw = MessageStatus.PENDING.rawValue();

    if (errorMessage == null) {
      _addTempMessage(tempMessage);
    } else {
      _resendMessage(tempMessage);
    }
    _saveTempMessage(tempMessage);

    // Set up listener for this message
    _listenSentMessageResponse(tempMessage);

    // Register task in a separate Future
    unawaited(
      _registerVoiceMessageTask(
        channel: channel,
        voiceFile: voiceFile,
        tempMessage: tempMessage,
      ),
    );
  }

  /// Helper method to register voice message sending task
  Future<void> _registerVoiceMessageTask({
    required Channel channel,
    required UploadFile voiceFile,
    required Message tempMessage,
  }) async {
    try {
      final taskRef = tempMessage.ref!;
      final mediaMessageBodyEmbed = jsonEncode(
        WorkerSendMediaInput(
          mediaListEmbed: jsonEncode([
            WorkerMediaObject(
              attachmentType: AttachmentType.VOICE_MESSAGE.rawValue(),
              fileMetadataEmbed: jsonEncode(
                WorkerFileMetadata().toJson(),
              ),
            ),
          ]),
          workspaceId: channel.workspaceId,
          channelId: channel.channelId,
          userId: _userId,
          ref: tempMessage.ref!,
          taskName: TaskNameEnum.sendMessage.value,
          attachmentType: AttachmentType.VOICE_MESSAGE.rawValue(),
          creationTime: DateTime.now(),
        ).toJson(),
      );
      final inputData = WorkerUploadFileInput(
        ref: taskRef,
        taskName: TaskNameEnum.uploadFile.value,
        filePath: voiceFile.path,
        fileName: voiceFile.name,
        fileSize: voiceFile.size,
        fileRef: voiceFile.fileRef ?? '',
        uploadType: UploadFileTypeEnum.voice.value,
        mediaMessageBodyEmbed: mediaMessageBodyEmbed,
        messageRef: tempMessage.ref!,
        messageId: '',
      ).toJson();

      await _registerTaskOnNextEventLoop(
        taskId: taskRef,
        taskName: TaskNameEnum.uploadFile.value,
        inputData: inputData,
        networkRequired: true,
        methodName: 'sendVoiceMessage',
      );
    } catch (e, stackTrace) {
      // Handle error without affecting UI
      Log.e(
        name: 'SendMessageHandler',
        ['Error registering voice message task', e, stackTrace],
      );
    }
  }

  /// Sends a file attachment.
  Future<void> sendFileMessage(
    UploadFile attachFile, {
    Message? errorMessage,
  }) async {
    final channel = await _getChannel();
    if (channel == null) return;

    final tempMessage = errorMessage ??
        _sendFileMessageHandler.createTemporaryFileMessage(
          workspaceId: channel.workspaceId,
          channelId: channel.channelId,
          userId: _userId,
          file: attachFile,
        );

    if (errorMessage != null) {
      tempMessage.createTime = TimeUtils.now();
      tempMessage.updateTime = TimeUtils.now();
    }

    if (channel.isTemp) {
      channel.lastMessageCreateTime =
          StringExtensions.toFormattedString(tempMessage.createTime);
      channel.lastMessageContent = tempMessage.content;
      await getIt<InsertChannelUseCase>()
          .execute(InsertChannelInput(channel: channel));
    }

    tempMessage.messageStatusRaw = MessageStatus.PENDING.rawValue();

    if (errorMessage == null) {
      _addTempMessage(tempMessage);
    } else {
      _resendMessage(tempMessage);
    }
    _saveTempMessage(tempMessage);

    // Set up listener for this message
    _listenSentMessageResponse(tempMessage);

    // Register task in a separate Future
    unawaited(
      _registerFileMessageTask(
        channel: channel,
        attachFile: attachFile,
        tempMessage: tempMessage,
      ),
    );
  }

  /// Helper method to register file message sending task
  Future<void> _registerFileMessageTask({
    required Channel channel,
    required UploadFile attachFile,
    required Message tempMessage,
  }) async {
    try {
      final taskRef = tempMessage.ref!;
      final mediaMessageBodyEmbed = jsonEncode(
        WorkerSendMediaInput(
          mediaListEmbed: jsonEncode([
            WorkerMediaObject(
              attachmentType: AttachmentType.FILE.rawValue(),
              fileMetadataEmbed: jsonEncode(
                WorkerFileMetadata().toJson(),
              ),
            ),
          ]),
          workspaceId: channel.workspaceId,
          channelId: channel.channelId,
          userId: _userId,
          ref: tempMessage.ref!,
          taskName: TaskNameEnum.sendMessage.value,
          attachmentType: AttachmentType.FILE.rawValue(),
          creationTime: DateTime.now(),
        ).toJson(),
      );
      final inputData = WorkerUploadFileInput(
        ref: taskRef,
        taskName: TaskNameEnum.uploadFile.value,
        filePath: attachFile.path,
        fileName: attachFile.name,
        fileSize: attachFile.size,
        fileRef: attachFile.fileRef ?? '',
        uploadType: UploadFileTypeEnum.file.value,
        mediaMessageBodyEmbed: mediaMessageBodyEmbed,
        messageRef: tempMessage.ref!,
        messageId: '',
      ).toJson();

      await _registerTaskOnNextEventLoop(
        taskId: taskRef,
        taskName: TaskNameEnum.uploadFile.value,
        inputData: inputData,
        networkRequired: true,
        methodName: 'sendFileMessage',
      );
    } catch (e, stackTrace) {
      // Handle error without affecting UI
      Log.e(
        name: 'SendMessageHandler',
        ['Error registering file message task', e, stackTrace],
      );
    }
  }

  /// Edits an existing message by registering a task to update its content.
  Future<void> editMessage(String content, Message message) async {
    message.messageStatusRaw = MessageStatus.PENDING.rawValue();
    message.content = content.trim();
    message.ref = message.messageId;
    _updateMessage(message);

    // Set up listener for this message
    _listenEditMessageResponse(message);

    // Update quote messages
    _updateQuoteMessages(message);

    // Register task in a separate Future
    unawaited(
      _registerEditMessageTask(
        content: content.trim(),
        message: message,
      ),
    );
  }

  /// Helper method to register message editing task
  Future<void> _registerEditMessageTask({
    required String content,
    required Message message,
  }) async {
    try {
      final inputData = WorkerEditMessageInput(
        workspaceId: _workspaceId,
        channelId: _channelId,
        userId: _userId ?? '',
        ref: message.messageId,
        messageId: message.messageId,
        content: content,
        taskName: TaskNameEnum.editMessage.rawValue(),
        contentLocale: 'UNS',
        creationTime: DateTime.now(),
      ).toJson();

      await _registerTaskOnNextEventLoop(
        taskId: message.messageId,
        taskName: TaskNameEnum.editMessage.rawValue(),
        inputData: inputData,
        networkRequired: true,
        methodName: 'editMessage',
      );
    } catch (e, stackTrace) {
      // Handle error without affecting UI
      Log.e(
        name: 'SendMessageHandler',
        ['Error registering edit message task', e, stackTrace],
      );
    }
  }

  /// Sends a location message. If [errorMessage] is provided, it is treated as a resend flow.
  Future<void> sendLocationMessage({
    required String address,
    required double longitude,
    required double latitude,
    Message? errorMessage,
  }) async {
    final channel = await _getChannel();
    if (channel == null) return;

    final tempMessage = errorMessage ??
        TempMessageFactory.createLocationMessage(
          workspaceId: channel.workspaceId,
          channelId: channel.channelId,
          userId: _userId,
          address: address,
          longitude: longitude,
          latitude: latitude,
        );

    if (channel.isTemp) {
      channel.lastMessageCreateTime =
          StringExtensions.toFormattedString(tempMessage.createTime);
      channel.lastMessageContent = tempMessage.content;
      await getIt<InsertChannelUseCase>()
          .execute(InsertChannelInput(channel: channel));
    }

    tempMessage.messageStatusRaw = MessageStatus.PENDING.rawValue();

    if (errorMessage == null) {
      _addTempMessage(tempMessage);
    }

    _saveTempMessage(tempMessage);

    // Set up listener for this message
    _listenSentMessageResponse(tempMessage);

    // Register task in a separate Future
    unawaited(
      _registerLocationMessageTask(
        channel: channel,
        address: address,
        longitude: longitude,
        latitude: latitude,
        tempMessage: tempMessage,
      ),
    );
  }

  /// Helper method to register location message sending task
  Future<void> _registerLocationMessageTask({
    required Channel channel,
    required String address,
    required double longitude,
    required double latitude,
    required Message tempMessage,
  }) async {
    try {
      final inputData = WorkerSendLocationInput(
        workspaceId: channel.workspaceId,
        channelId: channel.channelId,
        userId: _userId ?? '',
        ref: tempMessage.ref!,
        taskName: TaskNameEnum.sendMessage.value,
        latitude: latitude,
        longitude: longitude,
        description: address,
        attachmentType: AttachmentType.LOCATION.rawValue(),
        creationTime: DateTime.now(),
      ).toJson();

      await _registerTaskOnNextEventLoop(
        taskId: tempMessage.ref!,
        taskName: TaskNameEnum.sendMessage.value,
        inputData: inputData,
        networkRequired: true,
        methodName: 'sendLocationMessage',
      );
    } catch (e, stackTrace) {
      // Handle error without affecting UI
      Log.e(
        name: 'SendMessageHandler',
        ['Error registering location message task', e, stackTrace],
      );
    }
  }

  /// Resends a location message by re-invoking [sendLocationMessage] with [errorMessage].
  Future<void> resendLocationMessage(Message message) async {
    if (message.embed?.first.locationData == null) {
      Log.e("Missing location data in message");
      return;
    }

    message.createTime = TimeUtils.now();
    message.updateTime = TimeUtils.now();

    await sendLocationMessage(
      address: message.embed!.first.locationData!.description ?? '',
      longitude: message.embed!.first.locationData!.longitude ?? 0.0,
      latitude: message.embed!.first.locationData!.latitude ?? 0.0,
      errorMessage: message,
    );
  }

  /// Resends a video/ziishort message by re-invoking [sendLocationMessage] with [errorMessage].
  Future<void> resendVideoMessage(Message message) async {
    for (final attachment in message.mediaAttachments) {
      attachment.attachmentStatusRaw =
          AttachmentStatusEnum.UPLOADING.rawValue();
    }
    message.createTime = TimeUtils.now();
    message.updateTime = TimeUtils.now();

    await sendVideoMessage(
      message.mediaAttachments.map((attachment) {
        final file = message.messageViewType == MessageViewType.videoOwner
            ? attachment.video
            : attachment.videoMessage;
        return UploadFile(
          path: file!.filePath!,
          name: file.fileMetadata?.filename ?? '',
          size: file.fileMetadata?.filesize ?? 0,
          fileRef: file.fileRef,
        );
      }).toList(),
      type: message.messageViewType == MessageViewType.videoOwner
          ? V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_VIDEO
          : V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_VIDEO_MESSAGE,
      errorMessage: message,
    );
  }

  /// Resends a ZiiVoice message by re-invoking [sendVoiceMessage] with [errorMessage].
  Future<void> resendVoiceMessage(Message message) async {
    for (final attachment in message.mediaAttachments) {
      attachment.attachmentStatusRaw =
          AttachmentStatusEnum.UPLOADING.rawValue();
    }
    message.createTime = TimeUtils.now();
    message.updateTime = TimeUtils.now();
    await sendVoiceMessage(
      message.mediaAttachments.map((attachment) {
        final file = attachment.voiceMessage;
        return UploadFile(
          path: file!.filePath!,
          name: file.fileMetadata?.filename ?? '',
          size: file.fileMetadata?.filesize ?? 0,
          fileRef: file.fileRef,
        );
      }).first,
      errorMessage: message,
    );
  }

  /// Resends a file message by re-invoking [sendFileMessage] with [errorMessage].
  Future<void> resendFileMessage(Message message) async {
    for (final attachment in message.mediaAttachments) {
      attachment.attachmentStatusRaw =
          AttachmentStatusEnum.UPLOADING.rawValue();
    }
    message.createTime = TimeUtils.now();
    message.updateTime = TimeUtils.now();
    await sendFileMessage(
      message.mediaAttachments.map((attachment) {
        final file = attachment.file;
        return UploadFile(
          path: file!.filePath!,
          name: file.fileMetadata?.filename ?? '',
          size: file.fileMetadata?.filesize ?? 0,
          fileRef: file.fileRef,
        );
      }).first,
      errorMessage: message,
    );
  }

  /// Resends a image message by re-invoking [sendImageMessage] with [errorMessage].
  Future<void> resendImageMessage(Message message) async {
    for (final attachment in message.mediaAttachments) {
      attachment.attachmentStatusRaw =
          AttachmentStatusEnum.UPLOADING.rawValue();
    }
    message.createTime = TimeUtils.now();
    message.updateTime = TimeUtils.now();
    // Check for null and filter attachments with photos
    final validAttachments = message.mediaAttachments
        .where(
      (attachment) =>
          attachment.photo != null && attachment.photo!.filePath != null,
    )
        .map((attachment) {
      final file = attachment.photo!;
      return UploadFile(
        path: file.filePath!,
        name: file.fileMetadata?.filename ?? '',
        size: file.fileMetadata?.filesize ?? 0,
        fileRef: file.fileRef,
      );
    }).toList();

    if (validAttachments.isNotEmpty) {
      await sendImageMessage(
        validAttachments,
        errorMessage: message,
      );
    } else {
      Log.e(
        name: 'SendMessageHandler',
        'Failed to resend image message: No valid attachments found',
      );
    }
  }

  /// Resends an attachment image by uploading it again.
  /// [attachment] contains the image file to be resent.
  /// Extracts file details and sends the image message.
  Future<void> resendAttachmentImage(Attachment attachment) async {
    final file = attachment.photo;

    // Check for null before using
    if (file == null || file.filePath == null) {
      Log.e(
        name: 'SendMessageHandler',
        'Failed to resend attachment image: File or file path is null',
      );
      return;
    }

    await sendImageMessage(
      [
        UploadFile(
          path: file.filePath!,
          name: file.fileMetadata?.filename ?? '',
          size: file.fileMetadata?.filesize ?? 0,
          fileRef: file.fileRef,
        ),
      ],
    );
  }

  /// Sends a quote message referencing [message].
  Future<void> onQuoteMessage(String content, Message message) async {
    final channel = await _getChannel();
    if (channel == null) return;

    final tempMessage = TempMessageFactory.createQuoteMessage(
      workspaceId: channel.workspaceId,
      channelId: channel.channelId,
      userId: _userId,
      content: content.trim(),
      originalMessageRaw: json.encode(
        OriginalMessage(
          messageId: message.messageId,
          content: message.content,
          attachmentType: message.attachmentTypeRaw,
          messageType: message.messageTypeRaw,
          contentLocale: message.contentLocale,
          userId: message.userId,
          createTime: message.createTime.toString(),
          updateTime: message.updateTime.toString(),
        ),
      ),
    );

    if (channel.isTemp) {
      channel.lastMessageCreateTime =
          StringExtensions.toFormattedString(tempMessage.createTime);
      channel.lastMessageContent = tempMessage.content;
      await getIt<InsertChannelUseCase>()
          .execute(InsertChannelInput(channel: channel));
    }

    tempMessage.messageStatusRaw = MessageStatus.PENDING.rawValue();
    _addTempMessage(tempMessage);
    _saveTempMessage(tempMessage);

    // Set up listener for this message
    _listenSentMessageResponse(tempMessage);

    // Register task in a separate Future
    unawaited(
      _registerQuoteMessageTask(
        channel: channel,
        content: content.trim(),
        message: message,
        tempMessage: tempMessage,
      ),
    );
  }

  /// Helper method to register quote message sending task
  Future<void> _registerQuoteMessageTask({
    required Channel channel,
    required String content,
    required Message message,
    required Message tempMessage,
  }) async {
    try {
      final inputData = WorkerSendQuoteMessageInput(
        workspaceId: channel.workspaceId,
        channelId: channel.channelId,
        userId: _userId ?? '',
        ref: tempMessage.ref!,
        taskName: TaskNameEnum.sendQuoteMessage.rawValue(),
        content: content,
        messageId: message.messageId,
        contentLocale: message.contentLocale ?? 'UNS',
        attachmentType: AttachmentType.UNSPECIFIED.rawValue(),
        creationTime: DateTime.now(),
      ).toJson();

      await _registerTaskOnNextEventLoop(
        taskId: tempMessage.ref!,
        taskName: TaskNameEnum.sendQuoteMessage.value,
        inputData: inputData,
        networkRequired: true,
        methodName: 'onQuoteMessage',
      );
    } catch (e, stackTrace) {
      // Handle error without affecting UI
      Log.e(
        name: 'SendMessageHandler',
        ['Error registering quote message task', e, stackTrace],
      );
    }
  }

  /// Sends multiple messages as forward in one batch.
  Future<void> sendForwardMessage({required List<Message> messages}) async {
    final channel = await _getChannel();
    if (channel == null) return;

    final messageIds = messages.map((message) => message.messageId).toList();

    for (final msg in messages) {
      if (channel.isTemp) {
        channel.lastMessageCreateTime =
            StringExtensions.toFormattedString(msg.createTime);
        channel.lastMessageContent = msg.content;
        await getIt<InsertChannelUseCase>()
            .execute(InsertChannelInput(channel: channel));
      }
    }

    // Register task in a separate Future
    unawaited(
      _registerForwardMessageTask(
        channel: channel,
        messageIds: messageIds,
      ),
    );
  }

  /// Helper method to register forward message sending task
  Future<void> _registerForwardMessageTask({
    required Channel channel,
    required List<String> messageIds,
  }) async {
    try {
      final ref = RandomUtils.randomUlId();

      final inputData = WorkerSendForwardMessageInput(
        workspaceId: channel.workspaceId,
        channelId: channel.channelId,
        userId: _userId ?? '',
        ref: ref,
        taskName: TaskNameEnum.sendForwardMessage.value,
        originalMessageIds: messageIds,
        attachmentType: AttachmentType.UNSPECIFIED.rawValue(),
        creationTime: DateTime.now(),
      ).toJson();

      await _registerTaskOnNextEventLoop(
        taskId: ref,
        taskName: TaskNameEnum.sendForwardMessage.value,
        inputData: inputData,
        networkRequired: true,
        methodName: 'sendForwardMessage',
      );
    } catch (e, stackTrace) {
      // Handle error without affecting UI
      Log.e(
        name: 'SendMessageHandler',
        ['Error registering forward message task', e, stackTrace],
      );
    }
  }

  /// Disposes the message listener to prevent memory leaks.
  void dispose() {
    // Do not remove sendMessageListener as it is needed to process the result of sending a message.
    // _sendMessageListener.dispose();
    _sendMessageListener.clearCallback();
  }

  /// Retrieves the current channel. For DM, it fetches or creates a temporary DM channel.
  Future<Channel?> _getChannel() async {
    if (isDm()) {
      return (await getIt<GetOrCreateTempDMChannelUseCase>().execute(
        GetOrCreateTempDMChannelInput(userId: _userId!),
      ))
          .channel;
    }
    return (await getIt<GetChannelUseCase>().execute(
      GetChannelInput(
        workspaceId: _workspaceId,
        channelId: _channelId,
      ),
    ))
        .channel;
  }

  /// Registers a listener to observe the WorkManager response for [tempMessage].
  void _listenSentMessageResponse(Message tempMessage) {
    if (tempMessage.ref == null) return;
    _sendMessageListener.listen(tempMessage.ref!, _onMessageSent);
  }

  /// Registers a listener to observe the edit response for [oldMessage].
  void _listenEditMessageResponse(Message oldMessage) {
    _sendMessageListener.listen(oldMessage.messageId, _onMessageEdited);
  }

  /// Processes the callback when an edited message is completed.
  void _onMessageEdited(Message message) {
    _updateMessage(message);
    _saveTempMessage(message);
    _timeOutUpdateMessageStatus(message);
  }

  /// Processes the callback when a sent message is completed.
  void _onMessageSent(Message message) async {
    _updateMessage(message);
    final localizationsEn = AppLocalizationsEn();
    switch (message.messageErrorReason) {
      case MessageErrorReason.BLOCKED:
        final messageSystem = TempMessageFactory.createSystemMessage(
          workspaceId: message.workspaceId,
          channelId: message.channelId,
          createTime: message.createTime?.add(DurationUtils.ms1000),
          content: localizationsEn.thisPersonIsnTReceivingMessagesRightNow,
        );
        _addTempMessage(messageSystem);
        _saveTempMessage(messageSystem);
        break;
      case MessageErrorReason.REACH_MESSAGE_LIMIT:
        final messageSystem = TempMessageFactory.createSystemMessage(
          workspaceId: message.workspaceId,
          channelId: message.channelId,
          createTime: message.createTime?.add(DurationUtils.ms1000),
          content:
              localizationsEn.youHaveReachedTheMaximumMessageLimitForStrangers,
        );
        _addTempMessage(messageSystem);
        _saveTempMessage(messageSystem);
        break;
      default:
        break;
    }

    _timeOutUpdateMessageStatus(message);
  }

  /// Adds a temporary message to the [MessagesBloc].
  void _addTempMessage(Message message) {
    if (_isMessagesBlocClosed()) return;
    _messagesBloc?.add(MessagesEvent.addTempMessage(message));
  }

  /// Updates the message in the [MessagesBloc].
  void _updateMessage(Message message) {
    debugPrint(
      'MessageListViewState.SendMessageHandler._updateMessage: ${message.messageId} - ${message.ref}',
    );
    if (_isMessagesBlocClosed()) return;
    _messagesBloc?.add(MessagesEvent.updateMessage(message));
  }

  /// Marks the message for resend in the [MessagesBloc].
  void _resendMessage(Message message) {
    if (_isMessagesBlocClosed()) return;
    _messagesBloc?.add(MessagesEvent.resendMessage(message));
  }

  /// Persists the temporary message in storage via the [MessagesBloc].
  void _saveTempMessage(Message message) {
    if (_isMessagesBlocClosed()) return;
    _messagesBloc?.add(MessagesEvent.saveTempMessage(message));
  }

  /// Checks if the [MessagesBloc] is closed or null.
  bool _isMessagesBlocClosed() {
    if (_messagesBloc?.isClosed ?? true) {
      Log.e(name: '_isMessagesBlocClosed', '_messagesBloc is null or closed');
      return true;
    }
    return false;
  }

  /// After a successful send, it reverts the message status to [MessageStatus.UNRECOGNIZED] after a short delay.
  void _timeOutUpdateMessageStatus(Message message) async {
    if (message.messageStatus != MessageStatus.SUCCESS) return;
    await Future.delayed(DurationUtils.ms3000);
    _messagesBloc?.add(
      MessagesEvent.updateMessageStatus(
        workspaceId: message.workspaceId,
        channelId: message.channelId,
        messageId: message.messageId,
        messageRef: message.ref,
        messageStatus: MessageStatus.UNRECOGNIZED,
      ),
    );
  }

  /// Update quoted messages when [originalMessage] has been edited
  Future<void> _updateQuoteMessages(Message originalMessage) async {
    final output = await getIt<EditOriginalMessageUseCase>()
        .execute(EditOriginalMessageInput(originalMessage: originalMessage));
    for (final message in output.messages) {
      _updateMessage(message);
    }
  }

  /// send poke message
  Future<void> sendPokeMessage({
    Message? errorMessage,
  }) async {
    final channel = await _getChannel();
    if (channel == null) return;
    var sticker = await getIt<StickerBloc>()
        .getStickerFromStickerId(GlobalConfig.STICKER_POKE_ID);

    final tempMessage = errorMessage ??
        TempMessageFactory.createPokeMessage(
          workspaceId: channel.workspaceId,
          channelId: channel.channelId,
          userId: _userId,
          sticker: sticker!,
        );

    if (errorMessage != null) {
      tempMessage.createTime = TimeUtils.now();
      tempMessage.updateTime = TimeUtils.now();
    }

    if (channel.isTemp) {
      channel.lastMessageCreateTime =
          StringExtensions.toFormattedString(tempMessage.createTime);
      channel.lastMessageContent = tempMessage.content;
      await getIt<InsertChannelUseCase>()
          .execute(InsertChannelInput(channel: channel));
    }

    tempMessage.messageStatusRaw = MessageStatus.PENDING.rawValue();

    if (errorMessage == null) {
      _addTempMessage(tempMessage);
    } else {
      _resendMessage(tempMessage);
    }
    _saveTempMessage(tempMessage);

    // Set up listener for this message
    _listenSentMessageResponse(tempMessage);

    // Register task in a separate Future
    unawaited(
      _registerStickerMessageTask(
        channel: channel,
        sticker: sticker!,
        tempMessage: tempMessage,
        isPoked: true,
      ),
    );
  }

  /// Resends a poke message by re-invoking [sendPokeMessage] with [errorMessage].
  Future<void> resendPokeMessage(Message message) async {
    await sendPokeMessage(
      errorMessage: message,
    );
  }
}
