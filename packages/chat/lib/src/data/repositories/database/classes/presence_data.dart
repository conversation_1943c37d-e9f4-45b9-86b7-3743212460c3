import 'package:json_annotation/json_annotation.dart';

part 'presence_data.g.dart';

@JsonSerializable(explicitToJson: true)
class PresenceData {
  final String? lastUpdateTime;
  final int? lastUpdateInSeconds;
  final int? presenceState;

  PresenceData({
    this.lastUpdateTime,
    this.lastUpdateInSeconds,
    this.presenceState,
  });

  factory PresenceData.fromJson(Map<String, dynamic> json) =>
      _$PresenceDataFromJson(json);

  Map<String, dynamic> toJson() => _$PresenceDataToJson(this);
}
