import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class UpdateMessagesUseCase
    extends BaseFutureUseCase<UpdateMessagesInput, UpdateMessagesOutput> {
  UpdateMessagesUseCase(
    this._messageRepository,
  );

  final MessageRepository _messageRepository;

  @override
  Future<UpdateMessagesOutput> buildUseCase(UpdateMessagesInput input) async {
    _messageRepository.forceUpdateMessageAll(input.messages);
    return UpdateMessagesOutput(ok: true);
  }
}

class UpdateMessagesInput extends BaseInput {
  UpdateMessagesInput({required this.messages});

  final List<Message> messages;
}

class UpdateMessagesOutput extends BaseOutput {
  UpdateMessagesOutput({required this.ok});

  final bool? ok;
}
