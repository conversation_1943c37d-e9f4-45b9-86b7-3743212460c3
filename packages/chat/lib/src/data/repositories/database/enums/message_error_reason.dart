import 'package:json_annotation/json_annotation.dart';

enum MessageErrorReason {
  @JsonValue(-1)
  UNRECOGNIZED(-1),
  @JsonValue(0)
  BLOCKED(0),
  @JsonValue(1)
  OTHER(1),
  @JsonValue(2)
  REACH_MESSAGE_LIMIT(2),
  @JsonValue(3)
  TIMEOUT(3),
  @JsonValue(4)
  NETWORK(4),
  @JsonValue(5)
  SERVER(5),
  ;

  final int value;

  const MessageErrorReason(this.value);

  static MessageErrorReason getEnumByValue(int? value) {
    if (value == null) return MessageErrorReason.UNRECOGNIZED;
    return MessageErrorReason.values.firstWhere(
      (status) => status.value == value,
      orElse: () => MessageErrorReason.UNRECOGNIZED,
    );
  }

  int rawValue() => value;
}
