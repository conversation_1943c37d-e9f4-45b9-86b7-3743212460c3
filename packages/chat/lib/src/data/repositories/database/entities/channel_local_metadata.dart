import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';

part 'channel_local_metadata.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class ChannelLocalMetadata {
  ChannelLocalMetadata({
    required this.workspaceId,
    required this.channelId,
    required this.sessionKey,
    this.translateToLanguage,
    this.translateToEnable = false,
    this.translateFromMessageId,
  });

  @Id(assignable: true)
  int id = 0;

  @Property(uid: 16001)
  @Index(type: IndexType.value)
  String workspaceId;

  @Property(uid: 16002)
  @Index(type: IndexType.value)
  String channelId;

  @Property(uid: 16003)
  @Index(type: IndexType.value)
  String sessionKey;

  @Property(uid: 16004)
  String? translateToLanguage;

  @Property(uid: 16005)
  bool? translateToEnable;

  @Property(uid: 16006)
  String? translateFromMessageId;

  factory ChannelLocalMetadata.fromJson(Map<String, dynamic> json) =>
      _$ChannelLocalMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$ChannelLocalMetadataToJson(this);
}
