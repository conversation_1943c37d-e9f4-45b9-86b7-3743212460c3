import 'package:json_annotation/json_annotation.dart';

import 'response_file_metadata.dart';
import 'response_matrix.dart';

part 'response_layout_metadata.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseLayoutMetadata {
  @J<PERSON><PERSON><PERSON>(name: 'layoutId')
  String? layoutId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'matrix')
  ResponseMatrix? matrix;

  @Json<PERSON>ey(name: 'dimensions')
  ResponseDimensions? dimensions;

  @JsonKey(name: 'orientation')
  int? orientation;

  @<PERSON>son<PERSON>ey(name: 'isRowSpan')
  bool? isRowSpan;

  @<PERSON>son<PERSON>ey(name: 'fileRef')
  String? fileRef;

  ResponseLayoutMetadata({
    this.layoutId,
    this.matrix,
    this.dimensions,
    this.orientation,
    this.isRowSpan,
    this.fileRef,
  });

  factory ResponseLayoutMetadata.fromJson(Map<String, dynamic> json) =>
      _$ResponseLayoutMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseLayoutMetadataToJson(this);
}
