import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class GetDMChannelUseCase
    extends BaseSyncUseCase<GetDMChannelInput, GetDMChannelOutput> {
  GetDMChannelUseCase(
    this._channelRepository,
  );

  final ChannelRepository _channelRepository;

  @override
  GetDMChannelOutput buildUseCase(GetDMChannelInput input) {
    Channel? channel =
        _channelRepository.getDMChannel(recipientId: input.userId);

    return GetDMChannelOutput(channel: channel);
  }
}

class GetDMChannelInput extends BaseInput {
  GetDMChannelInput({required this.userId});

  final String userId;
}

class GetDMChannelOutput extends BaseOutput {
  GetDMChannelOutput({required this.channel});

  final Channel? channel;
}
