import 'dart:convert';

import 'package:chat/chat.dart' hide Config, UserViewClient;
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:user_view_api/user_view_api.dart';

@Injectable()
class RenderUserUseCase
    extends BaseFutureUseCase<RenderUserInput, RenderUserOutput> {
  RenderUserUseCase(
    this._userRepository,
    this._chatUserRepository,
  );

  final UserRepository _userRepository;
  final ChatUserRepository _chatUserRepository;

  @override
  Future<RenderUserOutput> buildUseCase(
    RenderUserInput input,
  ) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    try {
      final result =
          await UserViewClient().instance.getUser(userId: input.userId);
      if (result.data?.ok ?? false) {
        final json = jsonDecode(
          standardSerializers.toJson(
            V3UserView.serializer,
            result.data!.data,
          ),
        );
        json['sessionKey'] = sessionKey;
        final chatUser = ChatUser.fromJson(json);
        final user = User.fromJson(json);

        await _chatUserRepository.insert(chatUser);
        await _userRepository.insert(user);

        return RenderUserOutput(ok: true);
      }
    } on Exception catch (_) {
      return RenderUserOutput(ok: false);
    }
    return RenderUserOutput(ok: false);
  }
}

class RenderUserInput extends BaseInput {
  RenderUserInput({required this.userId});

  final String userId;
}

class RenderUserOutput extends BaseOutput {
  RenderUserOutput({this.ok});

  final bool? ok;
}
