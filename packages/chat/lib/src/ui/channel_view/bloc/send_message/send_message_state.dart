// send_message_state.dart
part of 'send_message_bloc.dart';

@freezed
sealed class SendMessageState extends BaseBlocState with _$SendMessageState {
  const SendMessageState._();
  factory SendMessageState.initial() = SendMessageStateInitial;

  factory SendMessageState.messageSent() = SendMessageStateSent;

  factory SendMessageState.sendMessageFailed(String errorMessage) =
      SendMessageStateFailed;
}
