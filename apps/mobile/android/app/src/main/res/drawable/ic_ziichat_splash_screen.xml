<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:tools="http://schemas.android.com/tools"
    android:width="512dp"
    android:height="512dp"
    android:viewportWidth="512"
    android:viewportHeight="512"
    tools:ignore="VectorRaster">
    <path android:pathData="M256,0L256,0A256,256 0,0 1,512 256L512,256A256,256 0,0 1,256 512L256,512A256,256 0,0 1,0 256L0,256A256,256 0,0 1,256 0z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:centerX="68.75"
                android:centerY="20.48"
                android:gradientRadius="681.64"
                android:type="radial">
                <item
                    android:color="#FF229FD0"
                    android:offset="0" />
                <item
                    android:color="#FF1A6AFC"
                    android:offset="0.54" />
                <item
                    android:color="#FF00007A"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M251.5,273.7L192.28,278.74L272.15,164.68C272.73,163.68 274.46,164.08 274.13,165.29C258.43,240.31 265.36,208.18 251.5,273.7Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="258.43"
                android:endY="268.03"
                android:startX="239.53"
                android:startY="234.01"
                android:type="linear">
                <item
                    android:color="#FFFFFFFF"
                    android:offset="0" />
                <item
                    android:color="#FF4076A8"
                    android:offset="0.88" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M261.58,235.9L319.54,232.12L238.98,347.83C238.39,348.84 236.67,348.44 236.99,347.22C253.39,271.81 246.46,304.57 261.58,235.9Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="238.27"
                android:endY="237.79"
                android:startX="272.29"
                android:startY="279.37"
                android:type="linear">
                <item
                    android:color="#FFFFFFFF"
                    android:offset="0" />
                <item
                    android:color="#FFFFFFFF"
                    android:offset="0" />
                <item
                    android:color="#FF4076A8"
                    android:offset="0.82" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M220.63,238.42L192.28,278.74L291.19,272.44L319.54,232.12L220.63,238.42Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:endX="258.5"
                android:endY="273.66"
                android:startX="251.88"
                android:startY="234.01"
                android:type="linear">
                <item
                    android:color="#FFFBFBFB"
                    android:offset="0" />
                <item
                    android:color="#FFFFFFFF"
                    android:offset="1" />
            </gradient>
        </aapt:attr>
    </path>
</vector>
