import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';

part 'task_result_entity.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class TaskResultEntity {
  TaskResultEntity({
    required this.resultId,
    required this.resultJson,
  });

  @Id(assignable: true)
  @JsonKey(defaultValue: 0)
  int id = 0;

  @Property(uid: 22001)
  String resultId;

  @Property(uid: 22002)
  String resultJson;

  factory TaskResultEntity.fromJson(Map<String, dynamic> json) =>
      _$TaskResultEntityFromJson(json);
  Map<String, dynamic> toJson() => _$TaskResultEntityToJson(this);
}
