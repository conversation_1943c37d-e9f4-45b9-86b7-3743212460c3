import 'dart:async';

/// Manages call duration in the LiveKit application.
class CallDurationManager {
  DateTime? _startTime;
  Timer? _timer;
  Duration _duration = Duration.zero;
  Function(Duration)? _onTick;

  /// Starts tracking the call duration.
  void start() {
    _startTime = DateTime.now();
    _duration = Duration.zero;
    _timer?.cancel(); // Cancel the old timer if it exists
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      _duration = DateTime.now().difference(_startTime!);
      _onTick?.call(_duration);
    });
  }

  /// Stops tracking the call duration.
  void stop() {
    _timer?.cancel();
    _startTime = null;
    _duration = Duration.zero;
  }

  /// Sets a callback to update the call duration.
  void setOnTick(Function(Duration) onTick) {
    _onTick = onTick;
  }

  /// Gets the current call duration as a Duration object.
  Duration get currentDuration => _duration;
}
