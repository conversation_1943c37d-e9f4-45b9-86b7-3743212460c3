import 'dart:async';

import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../../user_manager.dart';
import '../../../common/di/di.dart';

class BlockUserPage extends StatefulWidget {
  const BlockUserPage({
    required this.onOpenUserProfile,
    super.key,
  });

  final Function(String) onOpenUserProfile;

  @override
  State<BlockUserPage> createState() => _BlockUserPageState();
}

class _BlockUserPageState extends BasePageState<BlockUserPage, BlockUserBloc>
    implements ui.BlockedUsersPageInterface {
  List<ui.FriendItem> _blockedUsersList = [];
  ValueNotifier<ui.ProcessStatus> processStatus =
      ValueNotifier(ui.ProcessStatus.loading);
  late AppLocalizations appLocalizations;
  late StreamSubscription? _listenEventSubscription;
  ValueNotifier<String> processContent = ValueNotifier("");
  Widget _child = SizedBox.shrink();
  ValueNotifier<bool> refresh = ValueNotifier(false);
  List<UserPrivateData> _listUserPrivateData = [];
  late final UserPrivateDataBloc _userPrivateDataBloc;
  late final BlockUserBloc _blockUserBloc;
  bool isWarning = false;

  @override
  void initState() {
    super.initState();
    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    _blockUserBloc = getIt<BlockUserBloc>();
    _blockUserBloc.add(OnLoadListBlockUserEvent());
    setupListenEventHandler();
  }

  @override
  void dispose() {
    super.dispose();
    _listenEventSubscription?.cancel();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (ModalRoute.of(context)?.isCurrent == true) {
      _blockUserBloc.add(OnLoadListBlockUserEvent());
    }
  }

  void changeProcessDialog(ui.ProcessStatus status, String content) {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      processContent.value = content;
      processStatus.value = status;
      Future.delayed(Duration(seconds: 2), () {
        if (Navigator.canPop(context)) {
          popShowDialogProcess();
        }
      });
    });
  }

  void showAlertError() {
    if (Navigator.canPop(context)) {
      popShowDialogProcess();
    }
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      Future.delayed(Duration(seconds: 1), () {
        ui.DialogUtils.showErrorOccurredTranslateDialog(
          context,
          onOkClicked: () {
            popShowDialogProcess();
          },
        );
      });
    });
  }

  void popShowDialogProcess() {
    AppEventBus.publish(
      UnBlockPopToEvent(),
    );
  }

  void setupListenEventHandler() {
    _listenEventSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ListenBlockUserEvent>()
        .listen(onReceivedListenEvent);
  }

  void onReceivedListenEvent(event) async {
    if (event is BlockEvent) {
      var user = User.fromJson(event.user);
      String? displayName = user.profile?.displayName;
      _blockedUsersList.add(
        new ui.FriendItem(
          userId: user.userId,
          avatarPath: UrlUtils.parseAvatar(user.profile?.avatar),
          isOnline: false,
          displayName: displayName != null && displayName.isNotEmpty
              ? displayName
              : user.username ?? '',
          statusEmoji: '',
          statusText: '',
        ),
      );
      _child = ui.BlockedUsersPage(
        interface: this,
      );
      refresh.value = true;
    }
    if (event is UnBlockEvent) {
      _blockedUsersList.removeWhere((item) => item.userId == event.userId);
      _child = ui.BlockedUsersPage(
        interface: this,
      );
      refresh.value = true;
    }
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
      },
    );
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((user) => user.userId == userId);

      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  void handleListBlock(List<User>? listBlockUser) {
    _blockedUsersList.clear();
    listBlockUser?.forEach((item) {
      String? displayName = item.profile?.displayName;
      _blockedUsersList.add(
        new ui.FriendItem(
          userId: item.userId,
          avatarPath: UrlUtils.parseAvatar(item.profile?.avatar),
          isOnline: true,
          displayName: getAliasName(item.userId) != null
              ? getAliasName(item.userId)!
              : displayName != null && displayName.isNotEmpty
                  ? displayName
                  : item.username ?? '',
          statusEmoji: '',
          statusText: '',
        ),
      );
    });
  }

  @override
  Widget buildPage(BuildContext context) {
    appLocalizations = AppLocalizations.of(context)!;
    return Scaffold(
      body: MultiBlocProvider(
        providers: [
          BlocProvider<BlockUserBloc>.value(
            value: _blockUserBloc,
          ),
          BlocProvider<UserPrivateDataBloc>.value(
            value: _userPrivateDataBloc,
          ),
        ],
        child: MultiBlocListener(
          listeners: [
            BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
              listenWhen: (prev, state) => prev != state,
              listener: _blocUserPrivateListener,
            ),
          ],
          child: BlocBuilder<BlockUserBloc, BlockUserState>(
            buildWhen: (prev, state) => prev != state,
            builder: (context, state) {
              state.maybeWhen(
                initial: () {
                  _child = Center(child: ui.AppCircularProgressIndicator());
                },
                loadListBlockUser: (listBlockUser) {
                  handleListBlock(listBlockUser);
                  _child = ui.BlockedUsersPage(
                    interface: this,
                  );
                },
                showProcessDialog: () {},
                updateProcessDialog: (response, popOnlyMine) {
                  if (response) {
                    if (Navigator.canPop(context)) {
                      popShowDialogProcess();
                    }
                  } else {
                    showAlertError();
                  }
                },
                displayCloseWarning: (close) {
                  isWarning = close;
                  refresh.value = true;
                },
                orElse: () {},
              );
              return ValueListenableBuilder<bool>(
                valueListenable: refresh,
                builder: (context, value, child) {
                  return _child;
                },
              );
            },
          ),
        ),
      ),
    );
  }

  @override
  List<ui.FriendItem> blockedUsersList() {
    return _blockedUsersList;
  }

  @override
  bool displaySkeleton() {
    // TODO: implement displaySkeleton
    return false;
  }

  @override
  bool isGotIt() {
    // TODO: implement isGotIt
    return isWarning;
  }

  @override
  void onClickBack() {
    Navigator.pop(context);
  }

  @override
  void onClickGotIt() {
    isWarning = true;
    _blockUserBloc.add(OnUpdateCloseWarningEvent(isClose: true));
    _child = ui.BlockedUsersPage(
      interface: this,
    );
  }

  @override
  void onClickUnBlockedUser(ui.FriendItem friendItem) {
    ui.ActionSheetUtil.showUnblockUserActionSheet(
      context,
      username: friendItem.displayName,
      onUnblock: () {
        _blockUserBloc.add(OnUnBlockUserEvent(userId: friendItem.userId));
      },
      onCancel: () {
        Navigator.pop(context);
      },
    );
  }

  @override
  void onClickUserItem(ui.FriendItem friendItem) {
    ui.ActionSheetUtil.showViewProfileOrUnblockActionSheet(
      context,
      onClickViewProfile: () {
        Navigator.pop(context);
        widget.onOpenUserProfile(friendItem.userId);
      },
      onClickUnblock: () {
        ui.ActionSheetUtil.showUnblockUserActionSheet(
          context,
          username: friendItem.displayName,
          onUnblock: () {
            popShowDialogProcess();
            _blockUserBloc.add(OnUnBlockUserEvent(userId: friendItem.userId));
          },
          onCancel: () {
            Navigator.pop(context);
          },
        );
      },
      onClickCancel: () {
        Navigator.pop(context);
      },
    );
  }
}
