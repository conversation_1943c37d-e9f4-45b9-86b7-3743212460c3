import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:objectbox/objectbox.dart';
import 'package:shared/shared.dart';

import '../../../../serializers/json_extensions.dart';
import '../classes/embed.dart';
import '../classes/mention_identification.dart';
import '../classes/original_message.dart';
import '../classes/reaction_data.dart';
import '../enums/attachment_type.dart';
import '../enums/message_error_reason.dart';
import '../enums/message_status.dart';
import '../enums/message_type.dart';
import '../enums/message_view_type.dart';
import 'attachment.dart';

part 'message.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class Message {
  Message({
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
    required this.sessionKey,
    required this.userId,
    required this.messageViewTypeRaw,
    required this.messageTypeRaw,
    required this.messageStatusRaw,
    required this.attachmentTypeRaw,
    this.id = 0,
    this.isThread = false,
    this.reportCount = 0,
    this.isReported = false,
    this.attachmentCount = 0,
    this.contentArguments,
    this.content,
    this.ref,
    this.contentLocale,
    this.createTime,
    this.updateTime,
    this.isFirstMessage = false,
    this.originalMessageRaw,
    this.reactionsRaw,
    this.mentionsRaw,
    this.embedRaw,
    this.dataMentionsRaw,
    this.editTime,
    this.isPartial = false,
    this.isTemp = false,
    this.messageErrorReasonRaw = -1,
    this.isPinned = false,
    this.pinTime,
  });

  Message copyWith({
    int? id,
    String? workspaceId,
    String? channelId,
    String? messageId,
    String? sessionKey,
    String? userId,
    int? messageViewTypeRaw,
    List<String>? contentArguments,
    String? content,
    String? ref,
    String? contentLocale,
    int? messageTypeRaw,
    int? messageStatusRaw,
    int? attachmentTypeRaw,
    bool? isThread,
    int? reportCount,
    bool? isReported,
    int? attachmentCount,
    DateTime? createTime,
    DateTime? updateTime,
    bool? isFirstMessage,
    String? originalMessageRaw,
    String? reactionsRaw,
    String? mentionsRaw,
    String? embedRaw,
    String? dataMentionsRaw,
    String? editTime,
    bool? isPartial,
    bool? isTemp,
    bool? isPinned,
    DateTime? pinTime,
  }) {
    final message = Message(
      id: id ?? this.id,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      messageId: messageId ?? this.messageId,
      sessionKey: sessionKey ?? this.sessionKey,
      userId: userId ?? this.userId,
      messageViewTypeRaw: messageViewTypeRaw ?? this.messageViewTypeRaw,
      contentArguments: contentArguments ?? this.contentArguments,
      content: content ?? this.content,
      ref: ref ?? this.ref,
      contentLocale: contentLocale ?? this.contentLocale,
      messageTypeRaw: messageTypeRaw ?? this.messageTypeRaw,
      messageStatusRaw: messageStatusRaw ?? this.messageStatusRaw,
      attachmentTypeRaw: attachmentTypeRaw ?? this.attachmentTypeRaw,
      isThread: isThread ?? this.isThread,
      reportCount: reportCount ?? this.reportCount,
      isReported: isReported ?? this.isReported,
      attachmentCount: attachmentCount ?? this.attachmentCount,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      isFirstMessage: isFirstMessage ?? this.isFirstMessage,
      originalMessageRaw: originalMessageRaw ?? this.originalMessageRaw,
      reactionsRaw: reactionsRaw ?? this.reactionsRaw,
      mentionsRaw: mentionsRaw ?? this.mentionsRaw,
      embedRaw: embedRaw ?? this.embedRaw,
      dataMentionsRaw: dataMentionsRaw ?? this.dataMentionsRaw,
      editTime: editTime ?? this.editTime,
      isPartial: isPartial ?? this.isPartial,
      isTemp: isTemp ?? this.isTemp,
      isPinned: isPinned ?? this.isPinned,
      pinTime: pinTime ?? this.pinTime,
    );

    message.mediaAttachments = this.mediaAttachments;
    message.isLastReceiveMessage = this.isLastReceiveMessage;
    return message;
  }

  MessageViewType get messageViewType =>
      MessageViewTypeExtension.fromRawValue(messageViewTypeRaw);

  OriginalMessage? get originalMessage => originalMessageRaw != null
      ? OriginalMessage.fromJson(jsonDecode(originalMessageRaw!))
      : null;

  MessageType get messageType => MessageType.getEnumByValue(messageTypeRaw);

  MessageStatus get messageStatus =>
      MessageStatus.getEnumByValue(messageStatusRaw);

  MessageErrorReason get messageErrorReason =>
      MessageErrorReason.getEnumByValue(messageErrorReasonRaw);

  AttachmentType get attachmentType =>
      AttachmentType.getEnumByValue(attachmentTypeRaw);

  Map<String, ReactionData>? get reactions => _parseReactions(reactionsRaw);

  List<String>? get mentions => _parseMentions(mentionsRaw);

  List<Embed>? get embed => _parseEmbed(embedRaw);

  List<MentionIdentification>? get dataMentions =>
      _parseDataMentions(dataMentionsRaw);

  static Map<String, ReactionData>? _parseReactions(String? reactionsRaw) {
    if (reactionsRaw != null && reactionsRaw.isNotEmpty) {
      final reactionsMap = jsonDecode(reactionsRaw);
      return Map<String, ReactionData>.from(
        reactionsMap.map((key, value) {
          return MapEntry(key, ReactionData.fromJson(value));
        }),
      );
    }
    return null;
  }

  static List<String>? _parseMentions(String? mentionsRaw) {
    if (mentionsRaw != null && mentionsRaw.isNotEmpty) {
      return List<String>.from(jsonDecode(mentionsRaw));
    }
    return null;
  }

  static List<Embed>? _parseEmbed(String? embedRaw) {
    if (embedRaw != null && embedRaw.isNotEmpty) {
      final embedList = jsonDecode(embedRaw);
      return List<Embed>.from(embedList.map((e) => Embed.fromJson(e)));
    }
    return null;
  }

  static List<MentionIdentification>? _parseDataMentions(
    String? dataMentionsRaw,
  ) {
    if (dataMentionsRaw != null && dataMentionsRaw.isNotEmpty) {
      final dataMentionsList = jsonDecode(dataMentionsRaw);
      return List<MentionIdentification>.from(
        dataMentionsList.map((e) => MentionIdentification.fromJson(e)),
      );
    }
    return null;
  }

  factory Message.fromJson(Map<String, dynamic> json) {
    final message = _$MessageFromJson(json);

    message.originalMessageRaw = json.getEncoded('originalMessage');
    message.reactionsRaw =
        json.getEncoded('reactionsRaw') ?? jsonEncode(json['reactions'] ?? {});
    message.mentionsRaw =
        json.getEncoded('mentionsRaw') ?? jsonEncode(json['mentions'] ?? []);
    message.embedRaw =
        json.getEncoded('embedRaw') ?? jsonEncode(json['embed'] ?? []);
    message.dataMentionsRaw = json.getEncoded('dataMentionsRaw') ??
        jsonEncode(json['dataMentions'] ?? []);

    if (json['mediaAttachments'] != null) {
      (json['mediaAttachments'] as List).forEach((attachmentJson) {
        final attachment = Attachment.fromJson(attachmentJson);
        attachment.message.target = message;
        message.mediaAttachments.add(attachment);
      });
    }

    return message;
  }

  Map<String, dynamic> toJson() {
    final json = _$MessageToJson(this);
    json['originalMessage'] = originalMessageRaw != null
        ? OriginalMessage.fromJson(jsonDecode(originalMessageRaw!))
        : null;
    json['mentions'] = _parseMentions(mentionsRaw);
    json['embed'] = _parseEmbed(embedRaw);
    json['dataMentionsRaw'] = _parseDataMentions(dataMentionsRaw);
    json['mediaAttachments'] = mediaAttachments.isNotEmpty
        ? mediaAttachments.map((attachment) => attachment.toJson()).toList()
        : null;

    return json;
  }

  @Id(assignable: true)
  @JsonKey(includeFromJson: false)
  int id = 0;

  @Property(uid: 4001)
  @Index()
  String workspaceId;

  @Property(uid: 4002)
  @Index()
  String channelId;

  @Property(uid: 4003)
  @Index()
  String messageId;

  @Property(uid: 4004)
  @Index()
  String sessionKey;

  @Property(uid: 4005)
  @Index()
  String userId;

  @Property(uid: 4006)
  int messageViewTypeRaw;

  @Property(uid: 4007)
  List<String>? contentArguments;

  @Property(uid: 4008)
  String? content;

  @Property(uid: 4009)
  String? ref;

  @Property(uid: 4010)
  String? contentLocale;

  @Property(uid: 4011)
  int messageTypeRaw;

  @Property(uid: 4012)
  int messageStatusRaw;

  @Property(uid: 4031)
  int messageErrorReasonRaw;

  @Property(uid: 4013)
  int attachmentTypeRaw;

  @Property(uid: 4014)
  bool isThread;

  @Property(uid: 4015)
  int reportCount;

  @Property(uid: 4016)
  bool isReported;

  @Property(uid: 4017)
  int attachmentCount;

  @Property(uid: 4018)
  @JsonKey(
    fromJson: TimeUtils.parseUTCStringToDateTime,
    toJson: TimeUtils.formatToISO8601,
  )
  @Property(type: PropertyType.date)
  DateTime? createTime;

  @Property(uid: 4019)
  @JsonKey(
    fromJson: TimeUtils.parseUTCStringToDateTime,
    toJson: TimeUtils.formatToISO8601,
  )
  @Property(type: PropertyType.date)
  DateTime? updateTime;

  @Property(uid: 4020)
  bool isFirstMessage = false;

  @Property(uid: 4021)
  String? originalMessageRaw;

  @Property(uid: 4022)
  String? reactionsRaw;

  @Property(uid: 4023)
  String? mentionsRaw;
  @Property(uid: 4024)
  String? embedRaw;

  @Property(uid: 4027)
  String? dataMentionsRaw;

  @Property(uid: 4028)
  String? editTime;

  @Property(uid: 4029)
  bool isPartial;

  @Property(uid: 4030)
  bool isTemp;

  @Property(uid: 4032)
  bool isPinned;

  @Property(uid: 4033)
  @JsonKey(
    fromJson: TimeUtils.parseUTCStringToDateTime,
    toJson: TimeUtils.formatToISO8601,
  )
  @Property(type: PropertyType.date)
  DateTime? pinTime;

  @Backlink('message')
  @JsonKey(includeToJson: false, includeFromJson: false)
  ToMany<Attachment> mediaAttachments = ToMany<Attachment>();

  @Transient()
  @JsonKey(includeToJson: false, includeFromJson: false)
  bool isLastReceiveMessage = false;

  bool get isInvitation =>
      messageViewType == MessageViewType.invitation ||
      messageViewType == MessageViewType.invitationOwner;

  bool get isSystemOrLocalType => messageViewType.isSystemOrLocalType;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! Message) return false;

    final listEquality = const DeepCollectionEquality();

    return workspaceId == other.workspaceId &&
        channelId == other.channelId &&
        messageId == other.messageId &&
        sessionKey == other.sessionKey &&
        userId == other.userId &&
        messageViewTypeRaw == other.messageViewTypeRaw &&
        listEquality.equals(contentArguments, other.contentArguments) &&
        content == other.content &&
        ref == other.ref &&
        contentLocale == other.contentLocale &&
        messageTypeRaw == other.messageTypeRaw &&
        messageStatusRaw == other.messageStatusRaw &&
        attachmentTypeRaw == other.attachmentTypeRaw &&
        isThread == other.isThread &&
        reportCount == other.reportCount &&
        isReported == other.isReported &&
        attachmentCount == other.attachmentCount &&
        createTime == other.createTime &&
        updateTime == other.updateTime &&
        isFirstMessage == other.isFirstMessage &&
        originalMessageRaw == other.originalMessageRaw &&
        reactionsRaw == other.reactionsRaw &&
        mentionsRaw == other.mentionsRaw &&
        embedRaw == other.embedRaw &&
        dataMentionsRaw == other.dataMentionsRaw &&
        editTime == other.editTime &&
        isPartial == other.isPartial &&
        isTemp == other.isTemp &&
        messageErrorReasonRaw == other.messageErrorReasonRaw &&
        isPinned == other.isPinned &&
        pinTime == other.pinTime &&
        listEquality.equals(mediaAttachments, other.mediaAttachments) &&
        isLastReceiveMessage == other.isLastReceiveMessage;
  }

  @override
  int get hashCode {
    final listEquality = const DeepCollectionEquality();
    return workspaceId.hashCode ^
        channelId.hashCode ^
        messageId.hashCode ^
        sessionKey.hashCode ^
        userId.hashCode ^
        messageViewTypeRaw.hashCode ^
        listEquality.hash(contentArguments) ^
        content.hashCode ^
        ref.hashCode ^
        contentLocale.hashCode ^
        messageTypeRaw.hashCode ^
        messageStatusRaw.hashCode ^
        attachmentTypeRaw.hashCode ^
        isThread.hashCode ^
        reportCount.hashCode ^
        isReported.hashCode ^
        attachmentCount.hashCode ^
        createTime.hashCode ^
        updateTime.hashCode ^
        isFirstMessage.hashCode ^
        originalMessageRaw.hashCode ^
        reactionsRaw.hashCode ^
        mentionsRaw.hashCode ^
        embedRaw.hashCode ^
        dataMentionsRaw.hashCode ^
        editTime.hashCode ^
        isPartial.hashCode ^
        isTemp.hashCode ^
        messageErrorReasonRaw.hashCode ^
        isPinned.hashCode ^
        pinTime.hashCode ^
        listEquality.hash(mediaAttachments) ^
        isLastReceiveMessage.hashCode;
  }
}
