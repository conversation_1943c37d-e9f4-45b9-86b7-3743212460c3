import 'package:flame_audio/flame_audio.dart';

class AudioUtils {
  AudioUtils._internal();
  static final AudioUtils instance = AudioUtils._internal();

  final Map<String, AudioPool> _audioPools = {};

  void play({
    required String file,
    double volume = 1.0,
    AudioContext? audioContext,
  }) {
    FlameAudio.play(
      file,
      volume: volume,
      audioContext: audioContext,
    );
  }

  Future<void> preloadAndCache({required String file}) async {
    await FlameAudio.audioCache.load(file);
  }

  Future<void> preloadAllAndCache({required List<String> files}) async {
    await FlameAudio.audioCache.loadAll(files);
  }

  void clear({required String file}) {
    FlameAudio.audioCache.clear(file);
  }

  Future<void> createAudioPool({
    required String file,
    int maxPlayers = 2,
  }) async {
    if (!_audioPools.containsKey(file)) {
      final pool = await FlameAudio.createPool(file, maxPlayers: maxPlayers);
      _audioPools[file] = pool;
    }
  }

  void playFromPool(String file) {
    final pool = _audioPools[file];
    if (pool != null) {
      pool.start();
    }
  }

  void disposePool(String file) {
    _audioPools[file]?.dispose();
    _audioPools.remove(file);
  }
}
