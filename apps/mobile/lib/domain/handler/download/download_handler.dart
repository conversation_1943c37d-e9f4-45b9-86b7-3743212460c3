import 'dart:async';

import 'package:download_manager/download_manager.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

@LazySingleton()
class DownloadHandler {
  DownloadHandler();

  late StreamSubscription? _downloadEventSubscription;
  late StreamSubscription? _downloadEnqueueEventSubscription;

  void setupDownloadHandler() {
    _downloadEventSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<DownloadEvent>()
        .listen(_onReceivedFromDownloadEvent);
    _downloadEnqueueEventSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<DownloadEnqueueEvent>()
        .listen(_onReceivedFromDownloadEvent);
  }

  void dispose() {
    _downloadEventSubscription?.cancel();
    _downloadEnqueueEventSubscription?.cancel();
  }

  void _onReceivedFromDownloadEvent(event) async {
    if (event is DownloadEvent) {
      download(event.url, type: mapType(event.type), fileName: event.fileName);
    }
    if (event is DownloadEnqueueEvent) {
      downloadEnqueue(
        event.url,
        type: mapType(event.type),
        fileName: event.fileName,
        messageId: event.messageId,
      );
    }
  }

  Future<void> download(
    String url, {
    DownloadSharedStorage? type,
    String? fileName,
  }) async {
    await DownloadManager.download(
      UrlUtils.parseCDNUrl(url),
      directoryDestination: type,
      onElapsedTime: (duration) {},
      onProgress: (percent) {},
      onStatus: (status) {},
      fileName: fileName,
      allowPause: false,
    );
  }

  Future<void> downloadEnqueue(
    String url, {
    DownloadSharedStorage? type,
    String? fileName,
    String? messageId,
  }) async {
    DownloadManager.enqueue(
      UrlUtils.parseCDNUrl(url),
      fileName: fileName,
      directoryDestination: DownloadSharedStorage.downloads,
      onDownloadStatus: (status) async {},
    );
  }

  DownloadSharedStorage? mapType(DownloadType? type) {
    if (type == null) return DownloadSharedStorage.downloads;
    return switch (type) {
      DownloadType.downloads => DownloadSharedStorage.downloads,
      DownloadType.images => DownloadSharedStorage.images,
      DownloadType.video => DownloadSharedStorage.video,
      DownloadType.audio => DownloadSharedStorage.audio,
      DownloadType.files => DownloadSharedStorage.files,
      DownloadType.external => DownloadSharedStorage.external,
    };
  }
}
