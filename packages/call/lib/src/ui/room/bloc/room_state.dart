part of 'room_bloc.dart';

@freezed
sealed class RoomState with _$RoomState {
  const RoomState._();

  factory RoomState.initial() = Initial;

  factory RoomState.participantListChanged(
    List<ParticipantTrack> participants,
  ) = ParticipantListChanged;

  factory RoomState.cameraStateChanged(bool isCameraOn) = CameraStateChanged;

  factory RoomState.cameraPositionChanged(CameraPosition position) =
      CameraPositionChanged;

  factory RoomState.speakerStateChanged(bool isSpeakerOn) = SpeakerStateChanged;

  factory RoomState.microStateChanged(bool isMicroOn) = MicroStateChanged;

  factory RoomState.speakerSelectorShown(
    List<MediaDevice> devices,
    String currentDeviceId,
  ) = SpeakerSelectorShown;

  factory RoomState.microSelectorShown(
    List<MediaDevice> devices,
    String currentDeviceId,
  ) = MicroSelectorShown;

  factory RoomState.focusParticipantChanged(String? participantId) =
      FocusParticipantChanged;

  factory RoomState.disconnected() = Disconnected;
}

extension RoomStateX on RoomState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(List<ParticipantTrack> track)? participantListChanged,
    T Function(bool isCameraOn)? cameraStateChanged,
    T Function(bool isSpeakerOn)? speakerStateChanged,
    T Function(String? participantId)? focusParticipantChanged,
    T Function(
      List<MediaDevice> devices,
      String currentDeviceId,
    )? speakerSelectorShown,
    T Function(bool isMicroOn)? microStateChanged,
    T Function(
      List<MediaDevice> devices,
      String currentDeviceId,
    )? microSelectorShown,
    T Function()? disconnected,
    T Function(CameraPosition position)? cameraPositionChanged,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is Initial && initial != null) return initial();

    if (state is ParticipantListChanged && participantListChanged != null) {
      return participantListChanged(state.participants);
    }

    if (state is CameraStateChanged && cameraStateChanged != null) {
      return cameraStateChanged(state.isCameraOn);
    }

    if (state is SpeakerStateChanged && speakerStateChanged != null) {
      return speakerStateChanged(state.isSpeakerOn);
    }

    if (state is SpeakerSelectorShown && speakerSelectorShown != null) {
      return speakerSelectorShown(state.devices, state.currentDeviceId);
    }

    if (state is MicroStateChanged && microStateChanged != null) {
      return microStateChanged(state.isMicroOn);
    }

    if (state is FocusParticipantChanged && focusParticipantChanged != null) {
      return focusParticipantChanged(state.participantId);
    }

    if (state is MicroSelectorShown && microSelectorShown != null) {
      return microSelectorShown(state.devices, state.currentDeviceId);
    }

    if (state is Disconnected && disconnected != null) {
      return disconnected();
    }
    if (state is CameraPositionChanged && cameraPositionChanged != null) {
      return cameraPositionChanged(state.position);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(List<ParticipantTrack> track) participantListChanged,
    required T Function(bool isCameraOn) cameraStateChanged,
    required T Function(bool isSpeakerOn) speakerStateChanged,
    required T Function(String? participantId) focusParticipantChanged,
    required T Function(
      List<MediaDevice> devices,
      String currentDeviceId,
    ) speakerSelectorShown,
    required T Function(bool isMicroOn) microStateChanged,
    required T Function(
      List<MediaDevice> devices,
      String currentDeviceId,
    ) microSelectorShown,
    required T Function() disconnected,
    required T Function(CameraPosition position) cameraPositionChanged,
  }) {
    final state = this;

    if (state is Initial) return initial();
    if (state is ParticipantListChanged) {
      return participantListChanged(state.participants);
    }

    if (state is CameraStateChanged) {
      return cameraStateChanged(state.isCameraOn);
    }

    if (state is SpeakerStateChanged) {
      return speakerStateChanged(state.isSpeakerOn);
    }

    if (state is SpeakerSelectorShown) {
      return speakerSelectorShown(state.devices, state.currentDeviceId);
    }

    if (state is MicroStateChanged) {
      return microStateChanged(state.isMicroOn);
    }

    if (state is FocusParticipantChanged) {
      return focusParticipantChanged(state.participantId);
    }

    if (state is MicroSelectorShown) {
      return microSelectorShown(state.devices, state.currentDeviceId);
    }

    if (state is Disconnected) {
      return disconnected();
    }
    if (state is CameraPositionChanged) {
      return cameraPositionChanged(state.position);
    }

    throw StateError('Unhandled state: $state');
  }
}
