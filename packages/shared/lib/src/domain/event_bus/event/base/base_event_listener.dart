import '/../../../shared.dart';

/// BaseEventListener is an abstract class designed to listen for events from
/// the AppEventBus and handle them appropriately based on their type.
///
/// This class subscribes to all events of type BaseEvent from the provided
/// AppEventBus instance. Depending on the runtime type of the event, it will
/// delegate handling to specific methods (_handleLocalEvent, _handleCloudEvent,
/// or _handleUnknownEvent). Each event handling method also logs the event if
/// logging is enabled.
///
/// Usage:
/// ```dart
/// class MyEventListener extends BaseEventListener {
///   MyEventListener(AppEventBus eventBus) : super(eventBus);
///
///   @override
///   void handleLocalEvent(LocalEvent event) {
///     // Handle local event
///   }
///
///   @override
///   void handleCloudEvent(CloudEvent event) {
///     // Handle cloud event
///   }
///
///   @override
///   void handleUnknownEvent(BaseEvent event) {
///     // Handle unknown event
///   }
/// }
///
/// void main() {
///   final eventBus = AppEventBus();
///   final listener = MyEventListener(eventBus);
///
///   // Now the listener will handle events automatically
/// }
/// ```
abstract class BaseEventListener {
  /// Constructs a BaseEventListener that listens to the provided [eventBus].
  ///
  /// The listener subscribes to all events of type [BaseEvent] and delegates
  /// the handling of these events to the appropriate methods based on the event
  /// type.
  BaseEventListener(this.eventBus) {
    eventBus.on<BaseEvent>().listen((event) {
      switch (event.runtimeType) {
        case LocalEvent:
          _handleLocalEvent(event as LocalEvent);
        case CloudEvent:
          _handleCloudEvent(event as CloudEvent);
        default:
          _handleUnknownEvent(event);
          break;
      }
    });
  }

  /// The event bus that this listener is subscribed to.
  final AppEventBus eventBus;

  /// Logs the event if logging is enabled.
  ///
  /// The [name] parameter specifies the name of the event type, and the [event]
  /// parameter is the event to be logged.
  void _logEvent(String name, BaseEvent event) {
    if (GlobalConfig.enableLogEventBus) {
      Log.d(
        name: name,
        GlobalConfig.enableLogContentEventBus ? event.toJson() : event.source,
      );
    }
  }

  /// Handles events of type [LocalEvent].
  ///
  /// This method logs the event and then delegates to [handleLocalEvent], which
  /// must be implemented by subclasses.
  void _handleLocalEvent(LocalEvent event) {
    _logEvent('LocalEvent', event);
    handleLocalEvent(event);
  }

  /// Handles events of type [CloudEvent].
  ///
  /// This method logs the event and then delegates to [handleCloudEvent], which
  /// must be implemented by subclasses.
  void _handleCloudEvent(CloudEvent event) {
    _logEvent('CloudEvent', event);
    handleCloudEvent(event);
  }

  /// Handles events of unknown type.
  ///
  /// This method logs the event and then delegates to [handleUnknownEvent],
  /// which must be implemented by subclasses.
  void _handleUnknownEvent(BaseEvent event) {
    _logEvent('UnknownEvent', event);
    handleUnknownEvent(event);
  }

  /// Method to handle [LocalEvent]s, to be implemented by subclasses.
  void handleLocalEvent(LocalEvent event);

  /// Method to handle [CloudEvent]s, to be implemented by subclasses.
  void handleCloudEvent(CloudEvent event);

  /// Method to handle unknown [BaseEvent]s, to be implemented by subclasses.
  void handleUnknownEvent(BaseEvent event);
}
