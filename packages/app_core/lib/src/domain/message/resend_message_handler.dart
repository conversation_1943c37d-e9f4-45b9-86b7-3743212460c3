import 'package:chat/chat.dart';
import 'package:shared/shared.dart';

import '../../../core.dart';

class ResendMessageHandler {
  static Future<void> resendMessage(
    SendMessageHandler sendMessageHandler,
    Message message,
  ) async {
    switch (message.messageViewType) {
      case MessageViewType.textOwner:
      case MessageViewType.linkOwner:
        sendMessageHandler.resendTextMessage(message);
        break;

      case MessageViewType.locationOwner:
        sendMessageHandler.resendLocationMessage(message);
        break;

      case MessageViewType.ziiShortsOwner:
      case MessageViewType.videoOwner:
        sendMessageHandler.resendVideoMessage(message);
        break;

      case MessageViewType.stickerOwner:
        if (message.mediaAttachments.first.sticker?.stickerId ==
            GlobalConfig.STICKER_POKE_ID) {
          sendMessageHandler.resendPokeMessage(message);
          break;
        }
        sendMessageHandler.resendStickerMessage(message);
        break;

      case MessageViewType.ziiVoiceOwner:
        sendMessageHandler.resendVoiceMessage(message);
        break;

      case MessageViewType.fileOwner:
        sendMessageHandler.resendFileMessage(message);
        break;

      case MessageViewType.imagesOwner:
        sendMessageHandler.resendImageMessage(message);
        break;

      default:
        Log.d("Unsupported message type: ${message.messageViewType}");
    }
  }

  static Future<void> resendAttachment(
    SendMessageHandler sendMessageHandler,
    Attachment attachment,
  ) async {
    switch (attachment.message.target?.messageViewType) {
      case MessageViewType.imagesOwner:
        sendMessageHandler.resendAttachmentImage(attachment);
        break;

      default:
        Log.d(
          "Unsupported message type: ${attachment.message.target?.messageViewType}",
        );
    }
  }
}
