import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:path/path.dart' as path;
import 'package:shared/shared.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../chat.dart';
import '../../message_list/constants.dart';
import '../../message_list/reaction_handler.dart';
import '../video_player/fullscreen_video_player.dart';
import 'base/base_fullscreen_widget.dart';

class VideoWidget extends StatefulWidget {
  const VideoWidget({
    super.key,
    this.videoPath,
    this.thumbnailUrl,
    this.thumbnailPath,
    required this.messageItem,
    required this.videoUrl,
  });

  final String? videoPath;
  final String? thumbnailUrl;
  final String? thumbnailPath;
  final String videoUrl;
  final MessageItem messageItem;

  @override
  State<VideoWidget> createState() => _VideoWidgetState();
}

class _VideoWidgetState extends State<VideoWidget> {
  bool isPlayVideo = false;

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key('visibility_${widget.messageItem.messageId}'),
      onVisibilityChanged: (info) {
        if (!mounted) return;
        if (info.visibleBounds == Rect.zero) {
          setState(() {
            isPlayVideo = false;
          });
        } else if (info.visibleFraction == 1.0) {
          setState(() {
            isPlayVideo = true;
          });
        }
      },
      child: FullscreenVideoPlayer(
        videoPath: widget.videoPath,
        playButton: _buildPlayButton(),
        pauseButton: _buildPauseButton(),
        mediaBoxFit: BoxFit.contain,
        isPlayVideo: isPlayVideo,
        thumbnailUrl: widget.thumbnailUrl,
        thumbnailPath: widget.thumbnailPath,
        loadingWidget: _buildLoadingWidget(),
        messageId: widget.messageItem.messageId,
        videoUrl: widget.videoUrl,
      ),
    );
  }

  Widget _buildPlayButton() {
    return SizedBox(
      width: 60.r,
      height: 60.w,
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Color.fromRGBO(255, 255, 255, 0.2),
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: EdgeInsets.only(left: 2.w),
          child: AppAssets.pngIconAsset(
            AppAssets.icPlayRounded,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return SizedBox(
      width: 60.r,
      height: 60.w,
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Color.fromRGBO(255, 255, 255, 0.2),
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: const CircularProgressIndicator(
            backgroundColor: AppColors.lightBlue,
            color: AppColors.primaryBlue,
            strokeWidth: 3,
            trackGap: 3,
          ),
        ),
      ),
    );
  }

  Widget _buildPauseButton() {
    return SizedBox(
      width: 60.r,
      height: 60.r,
      child: DecoratedBox(
        decoration: const BoxDecoration(
          color: Color.fromRGBO(255, 255, 255, 0.2),
          shape: BoxShape.circle,
        ),
        child: Padding(
          padding: EdgeInsets.only(left: 2.w),
          child: AppAssets.pngIconAsset(
            AppAssets.icPauseNew,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}

class VideoWidgetImpl extends BaseFullscreenWidget {
  VideoWidgetImpl({
    required super.messageItem,
    required super.message,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (message.mediaAttachments.isEmpty) {
      // TODO: Handle case no media attachments
      return const SizedBox();
    }

    final video =
        message.mediaAttachments.last.video ?? MediaObject.nullObject();
    final videoFile = UrlUtils.parseCDNUrl(video.fileUrl);
    final (listReactions, totalReactions) =
        ReactionHandler.getListReactionsFullScreen(message);

    final quickReact =
        (message.reactions?[heartEmoji['emoji'] as String]?.isReacted ?? false);

    return FutureBuilder<Map<String, String?>>(
      future: _loadVideoAndThumbnailPaths(video),
      builder: (context, snapshot) {
        final paths = snapshot.data ??
            {'videoPath': video.filePath, 'thumbnailPath': null};
        final videoPath = paths['videoPath'];
        final thumbnailPath = paths['thumbnailPath'];

        return ViewFullScreenVideoWidget(
          interface: this,
          messageItem: messageItem,
          emojiList: listReactions,
          totalReactions: ValueNotifier(totalReactions),
          quickReact: quickReact,
          videoWidget: VideoWidget(
            messageItem: messageItem,
            videoPath: videoPath,
            videoUrl: videoFile,
            thumbnailUrl: video.thumbnailUrl,
            thumbnailPath: thumbnailPath,
          ),
          backgroundColor: Colors.black,
        );
      },
    );
  }

  /// Load video and thumbnail paths from cache using FileUtils
  /// For fullscreen display, we need both video file path and thumbnail path
  /// Enhanced with download and cache functionality for URLs without extensions
  Future<Map<String, String?>> _loadVideoAndThumbnailPaths(
      MediaObject video) async {
    return await FileUtils.loadVideoAndThumbnailPaths(
      messageRef: message.ref!,
      fileRef: video.fileRef ?? '',
      fileUrl: video.fileUrl,
      thumbnailUrl: video.thumbnailUrl,
      fallbackFilePath: video.filePath,
      filename: video.fileMetadata?.filename,
    );
  }
}
