import '../../../../../../shared.dart';

class CallCheckMessagesEvent extends PrivateDataEvent {
  CallCheckMessagesEvent({
    required this.messageId,
    required this.channelId,
    required this.isCall,
    this.isDelete,
    this.isForward,
    this.isBlocked,
    super.source = BaseEvent.LOCAL_SOURCE,
  });

  final String channelId;
  final String messageId;
  final bool isCall;
  final bool? isDelete;
  final bool? isForward;
  final bool? isBlocked;

  @override
  Map<String, dynamic> toJson() => {
        "messageId": messageId,
        "isCall": isCall,
        "isDelete": isDelete,
        "isForward": isForward,
        "isBlocked": isBlocked,
      };
}
