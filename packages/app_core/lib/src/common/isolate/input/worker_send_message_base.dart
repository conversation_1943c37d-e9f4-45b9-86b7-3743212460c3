import 'package:json_annotation/json_annotation.dart';

part 'worker_send_message_base.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class WorkerSendMessageInputBase {
  final String? userId;
  final String? workspaceId;
  final String? channelId;
  final String ref;
  final String taskName;
  final int attachmentType;
  final DateTime creationTime;
  final bool isRefTask;

  WorkerSendMessageInputBase({
    this.userId,
    this.workspaceId,
    this.channelId,
    this.isRefTask = false,
    required this.ref,
    required this.taskName,
    required this.attachmentType,
    required this.creationTime,
  });

  bool isDm() {
    return userId != null && userId!.isNotEmpty;
  }

  String get uploadKey =>
      isDm() ? userId ?? '$workspaceId-$channelId' : '$workspaceId-$channelId';

  factory WorkerSendMessageInputBase.fromJson(Map<String, dynamic> json) =>
      _$WorkerSendMessageInputBaseFromJson(json);

  Map<String, dynamic> toJson() => _$WorkerSendMessageInputBaseToJson(this);

  WorkerSendMessageInputBase copyWith({
    String? userId,
    String? workspaceId,
    String? channelId,
    String? ref,
    String? taskName,
    int? attachmentType,
    DateTime? creationTime,
    bool? isRefTask,
  }) {
    return WorkerSendMessageInputBase(
      userId: userId ?? this.userId,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      ref: ref ?? this.ref,
      taskName: taskName ?? this.taskName,
      attachmentType: attachmentType ?? this.attachmentType,
      creationTime: creationTime ?? this.creationTime,
      isRefTask: isRefTask ?? this.isRefTask,
    );
  }

}
