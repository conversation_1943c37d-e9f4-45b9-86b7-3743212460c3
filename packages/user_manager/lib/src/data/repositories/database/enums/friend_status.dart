import 'package:freezed_annotation/freezed_annotation.dart';

enum FriendStatusEnum {
  @JsonValue(0)
  UNSPECIFIED,
  @JsonValue(1)
  NOT_FRIEND,
  @JsonValue(2)
  REQUEST_SENT,
  @JsonValue(3)
  REQUEST_RECEIVED,
  @JsonValue(4)
  REQUEST_DELETED,
  @JsonValue(5)
  FRIEND,
}

extension FriendStatusEnumExtension on FriendStatusEnum {
  static FriendStatusEnum getEnumByValue(int? value) {
    return FriendStatusEnum.values.firstWhere(
      (e) => e.toValue() == value,
      orElse: () => FriendStatusEnum.UNSPECIFIED,
    );
  }

  int toValue() {
    switch (this) {
      case FriendStatusEnum.NOT_FRIEND:
        return 1;
      case FriendStatusEnum.REQUEST_SENT:
        return 2;
      case FriendStatusEnum.REQUEST_RECEIVED:
        return 3;
      case FriendStatusEnum.REQUEST_DELETED:
        return 4;
      case FriendStatusEnum.FRIEND:
        return 5;
      default:
        return 0;
    }
  }
}
