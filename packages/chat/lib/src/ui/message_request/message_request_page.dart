import 'dart:async';

import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import '../../common/di/di.dart';
import 'bloc/message_request_bloc.dart';
import 'message_request_list_view.dart';
import 'warning_widget.dart';

class MessageRequestPage extends StatefulWidget {
  MessageRequestPage({
    required this.onTapMessage,
    super.key,
  });

  final void Function(Channel channel) onTapMessage;

  @override
  State<MessageRequestPage> createState() => _MessageRequestPageState();
}

class _MessageRequestPageState extends State<MessageRequestPage> {
  final ValueNotifier<ui.ProcessStatus> processStatus =
      ValueNotifier(ui.ProcessStatus.loading);

  final ValueNotifier<String> processContent = ValueNotifier("");

  late final AppLocalizations appLocalizations =
      GetIt.I.get<AppLocalizations>();

  Map<String, ChatUser> _mapBlockUser = {};

  late StreamSubscription? listenBlockUserEventSubscription;

  late BlockUserBloc _blockUserBloc = getIt<BlockUserBloc>();

  @override
  void initState() {
    super.initState();
    _blockUserBloc.add(OnLoadListBlockUserEvent());
    setupListenBlockUserEventHandler();
  }

  @override
  void dispose() {
    super.dispose();
    listenBlockUserEventSubscription?.cancel();
  }

  void changeProcessDialog(
    BuildContext context,
    ui.ProcessStatus status,
    String content,
  ) {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      processContent.value = content;
      processStatus.value = status;
      Future.delayed(Duration(seconds: 2), () {
        if (Navigator.canPop(context)) {
          AppEventBus.publish(
            PopToMessageRequestEvent(),
          );
        }
      });
    });
  }

  void _blocBlockUserListener(BuildContext context, BlockUserState state) {
    state.maybeWhen(
      loadListBlockUser: (listBlockUser) {
        _mapBlockUser = {};
        listBlockUser?.forEach((item) {
          _mapBlockUser.putIfAbsent(
            item.userId,
            () => ChatUser.fromJson(item.toJson()),
          );
        });
      },
      showProcessDialog: () {},
      updateProcessDialog: (response, bool? popOnlyMine) {
        if (response) {
          if (Navigator.canPop(context)) {
            popOnlyMine == true
                ? Navigator.pop(context)
                : AppEventBus.publish(
                    PopToMessageRequestEvent(),
                  );
          }
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
      },
      orElse: () {},
    );
  }

  void setupListenBlockUserEventHandler() {
    listenBlockUserEventSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ListenBlockUserEvent>()
        .listen(onReceivedListenEvent);
  }

  void onReceivedListenEvent(event) async {
    if (event is BlockEvent) {
      var item = ChatUser.fromJson(event.user);
      _mapBlockUser.putIfAbsent(item.userId, () => item);
    }
    if (event is UnBlockEvent) {
      _mapBlockUser.remove(event.userId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<MessageRequestBloc>.value(
          value: getIt<MessageRequestBloc>(),
        ),
        BlocProvider<UserPrivateDataBloc>.value(
          value: getIt<UserPrivateDataBloc>()..add(InitUserPrivateDataEvent()),
        ),
        BlocProvider<BlockUserBloc>.value(value: _blockUserBloc),
      ],
      child: BlocListener<BlockUserBloc, BlockUserState>(
        listener: _blocBlockUserListener,
        child: ui.AppScaffold(
          extendBody: false,
          extendBodyBehindAppBar: false,
          hasSafeArea: false,
          appBar: ui.MessageRequestAppbarWidget(
            onNavigatorBackButtonClicked: () {
              Navigator.pop(context);
            },
          ),
          body: _buildContent(context),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;
    return Column(
      children: [
        WarningWidget(),
        Expanded(
          child: MessageRequestListView(
            onAcceptMessage: onAccept,
            onDeleteMessage: onDeleted,
            onBlockUser: onBlockUser,
            onTapMessage: widget.onTapMessage,
            onLongTapMessage: (channel) {
              ui.ActionSheetUtil.showActionSheetMessageRequest(
                context,
                appLocalizations: appLocalizations,
                onTapMessage: () {
                  Navigator.pop(context);
                  widget.onTapMessage(channel);
                },
                onTapAccept: () {
                  Navigator.pop(context);
                  onAccept(context, channel);
                },
                onTapBlock: () {
                  Navigator.pop(context);
                  onBlockUser(context, channel);
                },
                onTapDelete: () {
                  Navigator.pop(context);
                  onDeleted(context, channel);
                },
                onTapCancel: () {
                  Navigator.pop(context);
                },
                onTapUnBlock: () {
                  onUnBlockUser(context, channel);
                },
                isBlockedUser: _mapBlockUser[channel.recipientId] != null,
              );
            },
          ),
        ),
      ],
    );
  }

  void onAccept(BuildContext context, Channel channel) {
    getIt<MessageRequestBloc>().add(
      AcceptMessageRequestEvent(
        userId: channel.userId!,
      ),
    );
  }

  void onDeleted(BuildContext context, Channel channel) {
    getIt<MessageRequestBloc>().add(
      RejectMessageRequestEvent(
        userId: channel.userId!,
      ),
    );
  }

  void onBlockUser(BuildContext context, Channel channel) {
    if (_mapBlockUser[channel.recipientId] == null) {
      ui.ActionSheetUtil.showBlockUserActionSheet(
        context,
        username: channel.name ?? '',
        onBlock: () {
          _blockUserBloc.add(OnBlockUserEvent(userId: channel.recipientId!));
        },
        onCancel: () {
          Navigator.pop(context);
        },
      );
    } else {
      ui.ActionSheetUtil.showUnblockUserActionSheet(
        context,
        username: channel.name ?? '',
        onUnblock: () {
          _blockUserBloc.add(OnUnBlockUserEvent(userId: channel.recipientId!));
        },
        onCancel: () {
          Navigator.pop(context);
        },
      );
    }
  }

  void onUnBlockUser(BuildContext context, Channel channel) {
    ui.ActionSheetUtil.showUnblockUserActionSheet(
      context,
      username: channel.name ?? '',
      onUnblock: () {
        _blockUserBloc.add(OnUnBlockUserEvent(userId: channel.recipientId!));
      },
      onCancel: () {
        Navigator.pop(context);
      },
    );
  }
}
