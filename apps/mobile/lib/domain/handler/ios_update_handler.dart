import 'dart:async';

import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../navigation/routes/app_router.dart';
import '../../navigation/routes/app_router.gr.dart';
import 'intro_force_update_handler.dart';

class IosUpdateHandler {
  late StreamSubscription? _listenAppUpdate = null;

  Future<void> checkForUpdate() async {
    try {
      // if (kDebugMode) {
      //   AppEventBus.publish(CallImprovePasskeyEvent(callPasskey: true));
      //   return;
      // }
      final (info, appId, version) = await AppUpdateUtils.checkForUpdate();
      if (info == AppUpdateType.flexible) {
        if (GetIt.instance.get<AppPreferences>().isFirstTimeInitApp &&
            _hasWelcomeFirstInStack()) {
          AppEventBus.publish(
            AppUpdateEvent(
              updateType: AppUpdateType.flexible,
              appId: appId,
              version: version,
            ),
          );
          return;
        }
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showBottomSheetUpdateFlexible(appId, version: version);
        });
      }
      if (info == AppUpdateType.immediate) {
        AppEventBus.publish(CallImprovePasskeyEvent(callPasskey: false));
        if (appId == null) return;
        if (GetIt.instance.get<AppPreferences>().isFirstTimeInitApp &&
            _hasWelcomeFirstInStack()) {
          AppEventBus.publish(
            AppUpdateEvent(
              updateType: AppUpdateType.immediate,
              appId: appId,
            ),
          );
          return;
        }

        WidgetsBinding.instance.addPostFrameCallback((_) {
          showForceUpdate(appId);
        });
      }
      if (info == AppUpdateType.introduce) {
        AppEventBus.publish(CallImprovePasskeyEvent(callPasskey: false));
      }
      if (info == AppUpdateType.noUpdate) {
        AppEventBus.publish(CallImprovePasskeyEvent(callPasskey: true));
      }
    } catch (e) {
      print('Error while checking/updating: $e');
    }
  }

  void firstTimeInitAppUpdate() {
    _listenAppUpdate?.cancel();
    _listenAppUpdate =
        GetIt.instance.get<AppEventBus>().on<AppUpdateEvent>().listen((result) {
      if (result.updateType == AppUpdateType.immediate) {
        if (GetIt.instance.get<AppRouter>().stack.length > 0) {
          showForceUpdate(result.appId);
          return;
        }
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showForceUpdate(result.appId);
        });
      }
      if (result.updateType == AppUpdateType.flexible) {
        if (GetIt.instance.get<AppRouter>().stack.length > 0) {
          showBottomSheetUpdateFlexible(result.appId, version: result.version);
          return;
        }
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showBottomSheetUpdateFlexible(result.appId);
        });
      }

      if (result.updateType == AppUpdateType.introduce) {
        if (GetIt.instance.get<AppRouter>().stack.length > 0) {
          GetIt.instance.get<AppRouter>().popUntilRoot();
          GetIt.instance.get<AppRouter>().replaceAll([
            IntroductionFirstRoute(
              isAuthenticated: false,
            ),
          ]);
          return;
        }
        IntroForceUpdateHandler().displayIntroduceForceUpdate();
      }
    });
  }

  void showForceUpdate(int? appId) {
    GetIt.instance.get<AppRouter>().popUntilRoot();
    GetIt.instance
        .get<AppRouter>()
        .replaceAll([ForceUpdateRoute(appId: "${appId}")]);
  }

  void showBottomSheetUpdateFlexible(int? appId, {String? version}) {
    ui.BottomSheetUtil.showUpdateManagerBottomSheet(
      context: GetIt.instance.get<AppRouter>().navigatorKey.currentContext!,
      onClickUpdateNow: () {
        AppEventBus.publish(CallImprovePasskeyEvent(callPasskey: false));
        if (appId == null) return;

        OpenLauncherUrl.onOpenLauncherURL(
          OpenLauncherUrl.getZiichatAppStoreURL("${appId}"),
        );
      },
      onClickLater: () {
        AppEventBus.publish(CallImprovePasskeyEvent(callPasskey: true));
        GetIt.instance.get<AppRouter>().pop();
      },
      onClose: () {
        AppEventBus.publish(CallImprovePasskeyEvent(callPasskey: true));
        GetIt.instance.get<AppRouter>().pop();
      },
      version: version,
    );
  }

  bool _hasWelcomeFirstInStack() {
    return GetIt.instance
        .get<AppRouter>()
        .stack
        .any((route) => route.routeData.name == WelcomeFirstRoute.name);
  }

  void dispose() {
    _listenAppUpdate?.cancel();
  }
}
