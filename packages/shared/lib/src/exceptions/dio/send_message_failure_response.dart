class SendMessageFailureResponse {
  final bool isSendMessageFailure;
  final String ref;
  final String? errorMessage;

  SendMessageFailureResponse({
    required this.ref,
    this.errorMessage,
    this.isSendMessageFailure = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'ref': ref,
      'isSendMessageFailure': isSendMessageFailure,
      'errorMessage': errorMessage,
    };
  }
}
