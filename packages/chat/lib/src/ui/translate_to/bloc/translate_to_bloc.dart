import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/database/entities/channel_local_metadata.dart';
import '../../../data/repositories/database/entities/translated_result.dart';
import '../../../data/repositories/database/enums/translated_status_enum.dart';
import '../../../data/repositories/translate_to_repository.dart';

part 'translate_to_bloc.freezed.dart';
part 'translate_to_event.dart'; // phần event
part 'translate_to_state.dart';

@injectable
class TranslateToBloc extends BaseBloc<TranslateToEvent, TranslateToState> {
  TranslateToBloc(
    this._translateToRepository,
  ) : super(TranslateToState.initial()) {
    on<InitiateTranslateToEvent>(_onInit);
    on<ChannelLocalMetadataChangedEvent>(_onChannelLocalMetadataChanged);
    on<TranslatedResultsChangedEvent>(_onTranslatedResultsChanged);
    on<InsertOrUpdateChannelLocalMetadataEvent>(
      _onInsertOrUpdateChannelLocalMetadata,
    );
    on<DeleteChannelLocalMetadataEvent>(_onDeleteChannelLocalMetadata);

    on<InsertOrUpdateTranslatedResultEvent>(_onInsertOrUpdateTranslatedResult);
    on<InsertOrUpdateTranslatedResultEventTimeout>(
      _onInsertOrUpdateTranslatedResultTimeout,
    );
    on<DeleteTranslatedResultEvent>(_onDeleteTranslatedResult);

    on<OnTranslateErrorEvent>(_onTranslateError);
    on<SomethingHappenedEvent>(_onSomethingHappened);

    on<CancelTranslatedLastMessageEvent>(_onCancelTranslateLastMessage);
  }

  final TranslateToRepository _translateToRepository;

  StreamSubscription? _metadataSubscription;
  StreamSubscription? _resultsSubscription;
  List<TranslatedResult> _results = [];
  ChannelLocalMetadata? _metadata;
  final Set<Future<void>> _activeFutures = {};

  @override
  Future<void> close() async {
    await _metadataSubscription?.cancel();
    await _resultsSubscription?.cancel();

    await Future.wait(_activeFutures);
    return super.close();
  }

  Future<void> _onInit(
    InitiateTranslateToEvent event,
    Emitter<TranslateToState> emit,
  ) async {
    Log.e(
      name: 'TranslateToBloc',
      '_onInit ${event.workspaceId} - ${event.channelId}',
    );

    if (event.workspaceId == null || event.channelId == null) return;

    _metadataSubscription?.cancel();
    _metadataSubscription = _translateToRepository
        .observerChannelLocalMetadata(event.workspaceId!, event.channelId!,
            (ChannelLocalMetadata? newMetadata) {
      _metadata = newMetadata;
      add(ChannelLocalMetadataChangedEvent(metadata: newMetadata));
    });

    // Observe results
    _resultsSubscription?.cancel();
    _resultsSubscription =
        _translateToRepository.observerAllTranslatedResultsInChannel(
      event.workspaceId!,
      event.channelId!,
      Config.getInstance().activeSessionKey ?? '',
      (listResults) {
        _results = listResults;
        add(TranslatedResultsChangedEvent(results: listResults));
      },
    );
  }

  FutureOr<void> _onChannelLocalMetadataChanged(
    ChannelLocalMetadataChangedEvent event,
    Emitter<TranslateToState> emit,
  ) {
    emit(
      TranslateToState.loaded(
        metadata: event.metadata,
        translatedResults: _results,
      ),
    );
  }

  FutureOr<void> _onTranslatedResultsChanged(
    TranslatedResultsChangedEvent event,
    Emitter<TranslateToState> emit,
  ) {
    emit(
      TranslateToState.loaded(
        metadata: _metadata,
        translatedResults: event.results,
      ),
    );
  }

  FutureOr<void> _onInsertOrUpdateChannelLocalMetadata(
    InsertOrUpdateChannelLocalMetadataEvent event,
    Emitter<TranslateToState> emit,
  ) {
    _translateToRepository.insertOrUpdateChannelLocalMetadata(event.metadata);
  }

  FutureOr<void> _onDeleteChannelLocalMetadata(
    DeleteChannelLocalMetadataEvent event,
    Emitter<TranslateToState> emit,
  ) {
    final output = _translateToRepository.deleteChannelLocalMetadata(
      workspaceId: event.workspaceId,
      channelId: event.channelId,
    );

    if (output) {
      emit(TranslateToState.metaDataDeleted());
    }
  }

  FutureOr<void> _onInsertOrUpdateTranslatedResult(
    InsertOrUpdateTranslatedResultEvent event,
    Emitter<TranslateToState> emit,
  ) {
    _translateToRepository.insertOrUpdateTranslatedResult(event.result);
  }

  FutureOr<void> _onInsertOrUpdateTranslatedResultTimeout(
    InsertOrUpdateTranslatedResultEventTimeout event,
    Emitter<TranslateToState> emit,
  ) {
    _translateToRepository.insertOrUpdateTranslatedResult(event.result);

    final future = Future.delayed(event.duration, () {
      final result = _results
          .where((item) => item.messageId == event.result.messageId)
          .firstOrNull;
      if (result != null &&
          result.statusRaw == TranslatedStatusEnum.TRANSLATING.value) {
        _translateToRepository.insertOrUpdateTranslatedResult(event.result);
      }
    });

    _activeFutures.add(future);

    future.whenComplete(() {
      _activeFutures.remove(future);
    });
  }

  FutureOr<void> _onDeleteTranslatedResult(
    DeleteTranslatedResultEvent event,
    Emitter<TranslateToState> emit,
  ) {
    _translateToRepository.deleteTranslatedResult(
      workspaceId: event.workspaceId,
      channelId: event.channelId,
      messageId: event.messageId,
      sessionKey:
          event.sessionKey ?? Config.getInstance().activeSessionKey ?? '',
    );
  }

  FutureOr<void> _onCancelTranslateLastMessage(
    CancelTranslatedLastMessageEvent event,
    Emitter<TranslateToState> emit,
  ) {
    final outputDeleteChannelLocalMetadata =
        _translateToRepository.deleteChannelLocalMetadata(
      workspaceId: event.workspaceId,
      channelId: event.channelId,
    );

    if (outputDeleteChannelLocalMetadata) {
      emit(TranslateToState.metaDataDeleted());
    }

    _translateToRepository.deleteTranslatedResult(
      workspaceId: event.workspaceId,
      channelId: event.channelId,
      messageId: event.messageId,
      sessionKey: event.sessionKey ?? Config.getInstance().activeSessionKey!,
    );
  }

  FutureOr<void> _onTranslateError(
    OnTranslateErrorEvent event,
    Emitter<TranslateToState> emit,
  ) {
    // Xử lý logic khi có lỗi, có thể emit state error
    emit(TranslateToState.error(event.errorMessage));
  }

  FutureOr<void> _onSomethingHappened(
    SomethingHappenedEvent event,
    Emitter<TranslateToState> emit,
  ) {
    // Demo handle event class thường không @freezed
    // ...
  }
}
