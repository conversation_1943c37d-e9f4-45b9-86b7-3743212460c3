import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../user_manager.dart';

@Injectable()
class UpsertUserStatusUseCase
    extends BaseFutureUseCase<UpsertUserStatusInput, UpsertUserStatusOutput> {
  const UpsertUserStatusUseCase(this._userRepository);

  final UserRepository _userRepository;

  @protected
  @override
  Future<UpsertUserStatusOutput> buildUseCase(
    UpsertUserStatusInput input,
  ) async {
    final user = _userRepository.getUser(input.userId);
    if (user != null) {
      user.statusData = input.status;
      _userRepository.forceInsert(user);
      return UpsertUserStatusOutput(user: user);
    }
    return UpsertUserStatusOutput();
  }
}

class UpsertUserStatusInput extends BaseInput {
  final String userId;
  final UserStatus? status;

  UpsertUserStatusInput({required this.userId, this.status});
}

class UpsertUserStatusOutput extends BaseOutput {
  final User? user;

  UpsertUserStatusOutput({this.user});
}
