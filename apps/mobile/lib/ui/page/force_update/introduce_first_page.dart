import 'dart:async';

import 'package:app_core/core.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../common/di/di.dart';
import '../../../navigation/routes/app_router.gr.dart';
import '../../widgets/app_pop_scope.dart';

@RoutePage()
class IntroductionFirstPage extends StatefulWidget {
  const IntroductionFirstPage({
    super.key,
    required this.isAuthenticated,
  });

  final bool isAuthenticated;

  @override
  State<IntroductionFirstPage> createState() => _IntroductionFirstPageState();
}

class _IntroductionFirstPageState extends State<IntroductionFirstPage> {
  @override
  void initState() {
    super.initState();
    unawaited(_updatePref());
  }

  Future<void> _updatePref() async {
    await getIt<AppPreferences>().setFirstTimeOpenAppAfterMigration(false);
    await getIt<AppPreferences>().setFirstTimeInitApp(false);
  }

  @override
  Widget build(BuildContext context) {
    return AppPopScope(
      child: ui.IntroductionFirstPage(
        onNavigation: () {
          AutoRouter.of(context).replace(
            IntroductionSliderRoute(isAuthenticated: widget.isAuthenticated),
          );
        },
      ),
    );
  }
}
