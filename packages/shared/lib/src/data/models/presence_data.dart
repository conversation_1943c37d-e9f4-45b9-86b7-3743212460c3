import 'package:json_annotation/json_annotation.dart';

part 'presence_data.g.dart';

@JsonSerializable(explicitToJson: true)
class PresenceData {
  PresenceData({
    this.userId,
    this.deviceId,
    this.isOnline,
    this.badgeValueArgument,
  });

  String? userId;
  String? deviceId;
  bool? isOnline;
  BadgeValueArgument? badgeValueArgument;

  int? get count => badgeValueArgument == null
      ? null
      : (badgeValueArgument!.unreadChannelIds?.length ?? 0) +
          (badgeValueArgument!.unreadFriendRequestIds?.length ?? 0);

  factory PresenceData.fromJson(Map<String, dynamic> json) {
    PresenceData data = _$PresenceDataFromJson(json);
    return data;
  }

  Map<String, dynamic> toJson() => _$PresenceDataToJson(this);
}

@JsonSerializable(explicitToJson: true)
class BadgeValueArgument {
  BadgeValueArgument({
    this.unreadChannelIds,
    this.unreadFriendRequestIds,
  });

  List<String>? unreadChannelIds;
  List<String>? unreadFriendRequestIds;

  factory BadgeValueArgument.fromJson(Map<String, dynamic> json) {
    BadgeValueArgument data = _$BadgeValueArgumentFromJson(json);
    return data;
  }

  Map<String, dynamic> toJson() => _$BadgeValueArgumentToJson(this);
}
