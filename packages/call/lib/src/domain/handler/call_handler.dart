import 'dart:async';

import 'package:app_core/core.dart';
import 'package:call_sdk/call_sdk.dart' hide Log;
import 'package:flutter/cupertino.dart' show debugPrint;
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';

class CallHandler {
  static final CallHandler _instance = CallHandler._internal();

  factory CallHandler() => _instance;
  final _callSdkPlugin = CallSdk();

  CallHandler._internal();

  late StreamSubscription? _callCreatedSubscription;

  // Init info caller
  String? sipCredential;
  String? sipAddress;
  String? userId;
  String? fsCallAddress;

  void init({
    required String? sipCredential,
    required String? sipAddress,
    required String? userId,
    required String? fsCallAddress,
  }) {
    debugPrint('CallHandler.init: ${[
      sipAddress,
      sipCredential,
      userId,
    ]}');

    _callCreatedSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<CallCreatedEvent>()
        .listen(_onCallCreatedEvent);

    if (sipCredential != null && sipAddress != null && userId != null) {
      this.sipCredential = sipCredential;
      this.sipAddress = sipAddress;
      this.userId = userId;
      _registerCallEvent();
    }
    _callSdkPlugin.onSIPRegistrationRequired.listen((_) {
      // Thực hiện đăng ký SIP với thông tin từ ứng dụng của bạn
      debugPrint('CallHandler.init.onSIPRegistrationRequired: ${[
        sipAddress,
        sipCredential,
        userId,
      ]}');
      _registerCallEvent();
    });
  }

  void dispose() {
    _callCreatedSubscription?.cancel();
  }

  Future<void> _registerCallEvent() async {
    try {
      debugPrint('CallHandler._registerCallEvent: ${[
        sipAddress,
        sipCredential,
        userId,
      ]}');

      final fullSipAddress = 'sip:$userId@$sipAddress';
      final sipUsername = userId ?? '';

      // split sipUsername by : and take the first part
      final sipUsernameParts = sipCredential?.split(':');
      final sipPassword = sipUsernameParts?[1] ?? "";

      await _callSdkPlugin.registerSIP(
        sipAddress: fullSipAddress,
        username: sipUsername,
        password: sipPassword,
      );

      debugPrint(
        'CallHandler._registerCallEvent: SIP registration successful for user: ${userId}',
      );
    } catch (e) {
      debugPrint(
        'CallHandler._registerCallEvent: Error registering SIP: ${e}',
      );
    }
  }

  void _onCallCreatedEvent(CallCreatedEvent event) async {
    debugPrint(
      'CallHandler._onCallCreatedEvent  ${event.toJson()}',
    );

    try {
      final callerMap = Map<String, dynamic>.from(event.caller as Map);
      final calleeMap = Map<String, dynamic>.from(event.callee as Map);

      // Convert field names to match the expected format
      final formattedCallerMap = {
        'userId': callerMap['user_id'],
        'username': callerMap['username'],
        'avatar': callerMap['avatar'],
        'displayName': callerMap['displayName'],
      };

      final formattedCalleeMap = {
        'userId': calleeMap['user_id'],
        'username': calleeMap['username'],
        'avatar': calleeMap['avatar'],
        'displayName': calleeMap['displayName'],
      };

      // add sip credentials for both caller and callee
      final callerSipCredential = {
        'username': callerMap['user_id'],
        'address': callerMap['sip_address'] ?? sipAddress,
        'password': callerMap['sip_password'] ?? "123",
      };

      final calleeSipCredential = {
        'username': calleeMap['user_id'],
        'address': calleeMap['sip_address'] ?? sipAddress,
        'password': calleeMap['sip_password'] ?? "123",
      };

      formattedCallerMap['sipAddress'] = callerSipCredential['address'];
      formattedCalleeMap['sipAddress'] = calleeSipCredential['address'];
      formattedCallerMap['sipCredential'] = callerSipCredential['username'] +
          ':' +
          callerSipCredential['password'];
      formattedCalleeMap['sipCredential'] = calleeSipCredential['username'] +
          ':' +
          calleeSipCredential['password'];

      final params = StartCallTransactionParams.create(
        hasVideo: event.isVideoCall,
        isOutgoing: true,
        caller: Caller.fromJson(formattedCallerMap),
        callee: Callee.fromJson(formattedCalleeMap),
      );

      _callSdkPlugin
          .configureClientWithSessionToken(Config.getInstance().apiAuthToken);

      await _callSdkPlugin.startCallTransaction(params);
    } catch (e) {
      debugPrint('CallHandler._onCallCreatedEvent: ${e}');
      throw e;
    }
  }
}
