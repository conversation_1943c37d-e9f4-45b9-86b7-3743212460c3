import 'package:cached_annotation/cached_annotation.dart';

import '../database/entities/user.dart';

part 'load_user_cache.cached.dart';

@WithCache(useStaticCache: true)
abstract mixin class LoadUserCache implements _$LoadUserCache {
  factory LoadUserCache() = _LoadUserCache;

  @Cached(
    syncWrite: true,
    ttl: 300,
    limit: 20,
  )
  Future<User> setCache({
    required String sessionKey,
    required String userId,
    @ignore required User user,
  }) async {
    return Future.value(user);
  }

  @CachePeek("setCache")
  User? peekCached({
    required String sessionKey,
    required String userId,
  });

  @ClearCached("setCache")
  void cleanCache();
}
