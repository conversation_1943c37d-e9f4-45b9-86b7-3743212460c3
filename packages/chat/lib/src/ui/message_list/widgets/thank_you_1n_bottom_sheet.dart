import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

class ThankYou1nBottomSheet extends StatefulWidget {
  const ThankYou1nBottomSheet({
    required this.onClickClose,
    required this.onClickBlock,
    required this.onClickCommunityStandard,
    required this.blockedUsername,
    required this.isBlocked,
    required this.context,
    required this.onClickGoToChannelSetting,
    required this.onClickLeaveChannel,
    required this.onClickCancelLeaveChannel,
    required this.myRole,
    this.onClose,
    super.key,
  });

  final BuildContext context;
  final VoidCallback onClickClose;
  final VoidCallback onClickGoToChannelSetting;
  final VoidCallback onClickCommunityStandard;
  final VoidCallback onClickBlock;
  final Future<bool> Function() onClickLeaveChannel;
  final VoidCallback onClickCancelLeaveChannel;
  final String blockedUsername;
  final ValueNotifier<bool> isBlocked;
  final Roles myRole;
  final VoidCallback? onClose;

  @override
  State<ThankYou1nBottomSheet> createState() => _ThankYou1nBottomSheetState();
}

class _ThankYou1nBottomSheetState extends State<ThankYou1nBottomSheet> {
  ValueNotifier<bool> isDisableLeaveChannel = ValueNotifier(false);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onVerticalDragStart: isDisableLeaveChannel.value ? (_) {} : null,
      onVerticalDragDown: isDisableLeaveChannel.value ? (_) {} : null,
      onVerticalDragUpdate: isDisableLeaveChannel.value ? (_) {} : null,
      onVerticalDragEnd: isDisableLeaveChannel.value ? (_) {} : null,
      child: ThankYouChannel1NBottomSheet(
        parentContext: context,
        onClickClose: widget.onClickClose,
        onClickBlock: widget.onClickBlock,
        isDisableLeaveChannel: isDisableLeaveChannel,
        onClickCommunityStandard: widget.onClickCommunityStandard,
        blockedUsername: widget.blockedUsername,
        isBlocked: widget.isBlocked,
        myRole: widget.myRole,
        onClickGoToChannelSetting: widget.onClickGoToChannelSetting,
        onClickLeaveChannel: _onLeaveChannel,
      ),
    );
  }

  void _onLeaveChannel() {
    ActionSheetUtil.showLeaveChannelActionSheet(
      context,
      onLeave: () async {
        isDisableLeaveChannel.value = await widget.onClickLeaveChannel();
        setState(() {});
      },
      onCancel: () {
        widget.onClickCancelLeaveChannel();
      },
    );
  }
}
