import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../message_list/constants.dart';
import '../../message_list/reaction_handler.dart';
import 'base/base_fullscreen_widget.dart';

class InvitationFullscreenWidgetImpl extends BaseFullscreenWidget {
  InvitationFullscreenWidgetImpl({
    required super.messageItem,
    required super.message,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final (listReactions, totalReactions) =
        ReactionHandler.getListReactionsFullScreen(message);

    final quickReact =
        (message.reactions?[heartEmoji['emoji'] as String]?.isReacted ?? false);

    var embed = (message.embed != null && message.embed!.isNotEmpty)
        ? message.embed!.first
        : null;
    var invitationData = embed?.invitationData;

    var invitationMessage = InvitationMessage(
      channelName: invitationData?.channel?['name'] ?? '',
      numMembers: invitationData?.channel?['totalMembers'] ?? 0,
      avatarUrl: UrlUtils.parseAvatar(invitationData?.channel?['avatar']),
      qr: invitationData?.invitationLink ?? '',
      hasExpired: invitationData?.isExpired ?? false,
    );

    void onClickLink(String link) {
      AppEventBus.publish(
        OnLinkClickedEvent(
          link: link,
          workspaceId: message.workspaceId,
          channelId: message.channelId,
          userId: message.userId,
          messageId: message.messageId,
        ),
      );
    }

    return ViewFullScreenInvitation(
      interface: this,
      messageItem: messageItem,
      emojiList: listReactions,
      quickReact: quickReact,
      mentions: message.mentions ?? [],
      totalReactions: ValueNotifier(totalReactions),
      content: messageItem.content ?? "",
      invitationMessage: invitationMessage,
      onUsernameClicked: (String username) {},
      onLinkClicked: onClickLink,
      onOpenLink: (MessageItem messageItem) {
        onClickLink(messageItem.content!);
      },
    );
  }
}
