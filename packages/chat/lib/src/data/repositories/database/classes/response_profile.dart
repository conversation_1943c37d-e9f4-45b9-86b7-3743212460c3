import 'package:json_annotation/json_annotation.dart';

part 'response_profile.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseProfile {
  final String? avatar;
  final String? displayName;
  final String? cover;
  final String? originalAvatar;

  ResponseProfile({
    this.avatar,
    this.displayName,
    this.cover,
    this.originalAvatar,
  });

  factory ResponseProfile.fromJson(Map<String, dynamic> json) =>
      _$ResponseProfileFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseProfileToJson(this);
}
