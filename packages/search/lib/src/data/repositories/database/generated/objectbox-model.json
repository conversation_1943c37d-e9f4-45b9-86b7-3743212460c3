{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:6657741887951982444", "lastPropertyId": "4:3003", "name": "History", "properties": [{"id": "1:7068550054483884405", "name": "id", "type": 6, "flags": 129}, {"id": "2:3001", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "3:3002", "name": "keyword", "type": 9}, {"id": "4:3003", "name": "timestamp", "type": 6}], "relations": []}, {"id": "2:4954963082479613210", "lastPropertyId": "8:2007", "name": "Search", "properties": [{"id": "1:2118967859029525046", "name": "id", "type": 6, "flags": 129}, {"id": "2:2001", "name": "<PERSON><PERSON><PERSON>", "type": 9}, {"id": "3:2002", "name": "userId", "type": 9}, {"id": "4:2003", "name": "channelId", "type": 9}, {"id": "5:2004", "name": "workspaceId", "type": 9}, {"id": "6:2005", "name": "searchType", "type": 9}, {"id": "7:2006", "name": "embed", "type": 9}, {"id": "8:2007", "name": "timestamp", "type": 6}], "relations": []}], "lastEntityId": "2:4954963082479613210", "lastIndexId": "0:0", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}