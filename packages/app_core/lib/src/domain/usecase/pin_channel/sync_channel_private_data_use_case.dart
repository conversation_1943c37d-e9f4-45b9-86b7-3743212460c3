import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:intl/intl.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';
import '../../../data/interface/private_data_comparator.dart';
import 'load_channel_private_data_use_case.dart';

part 'sync_channel_private_data_use_case.freezed.dart';

@Injectable()
class SyncChannelPrivateDataUseCase extends BaseFutureUseCase<
    SyncChannelPrivateDataUseCaseInput, SyncChannelPrivateDataUseCaseOutput> {
  const SyncChannelPrivateDataUseCase(
    this._repository,
    this._loadUseCase,
    this._getUseCase,
  );

  final LoadChannelPrivateDataUseCase _loadUseCase;
  final GetChannelPrivateDataUseCase _getUseCase;
  final PrivateDataRepository _repository;

  @protected
  @override
  Future<SyncChannelPrivateDataUseCaseOutput> buildUseCase(
    SyncChannelPrivateDataUseCaseInput input,
  ) async {
    var getOutput =
        await _getUseCase.execute(GetChannelPrivateDataUseCaseInput());

    var oldPrivateData = getOutput.data;

    try {
      final loadOutput =
          await _loadUseCase.execute(LoadChannelPrivateDataUseCaseInput());
      final newPrivateData = loadOutput.data;

      if (newPrivateData != null) {
        if (_shouldInsert(oldPrivateData, newPrivateData)) {
          _updatePrivateData(oldPrivateData, newPrivateData);
          return SyncChannelPrivateDataUseCaseOutput(ok: true);
        }
      }

      return SyncChannelPrivateDataUseCaseOutput(ok: false);
    } on Exception catch (_) {
      return SyncChannelPrivateDataUseCaseOutput(ok: false);
    }
  }

  bool _shouldInsert(PrivateData? oldData, PrivateData newData) {
    if (oldData == null) return true;
    if (oldData.updateTime != newData.updateTime) return true;
    return false;
  }

  void _updatePrivateData(
    PrivateData? oldPrivateData,
    PrivateData newPrivateData,
  ) {
    if (oldPrivateData == null) {
      DateFormat dateFormat = DateFormat("yyyy-MM-dd");
      oldPrivateData = PrivateData(
        sessionKey: Config.getInstance().activeSessionKey ?? '',
        userId: Config.getInstance().activeSessionKey ?? '',
        createTime: dateFormat.format(DateTime(2000, 01, 01)),
        updateTime: dateFormat.format(DateTime(2000, 01, 02)),
      );
    }

    var c = PrivateDataComparator();

    c.processUpdates<ChannelPrivateData>(
      oldList: oldPrivateData.channels,
      newList: newPrivateData.channels,
      onInsert: _repository.insertChannel,
      onUpdate: _repository.updateChannel,
      onDelete: _repository.deleteChannel,
    );

    c.processUpdates<UserPrivateData>(
      oldList: oldPrivateData.users,
      newList: newPrivateData.users,
      onInsert: _repository.insertUser,
      onUpdate: _repository.updateUser,
      onDelete: _repository.deleteUser,
    );

    c.processUpdates<CallLogPrivateData>(
      oldList: oldPrivateData.callLogs,
      newList: newPrivateData.callLogs,
      onInsert: _repository.insertCallLog,
      onUpdate: _repository.updateCallLog,
      onDelete: _repository.deleteCallLog,
    );
  }
}

@freezed
sealed class SyncChannelPrivateDataUseCaseInput extends BaseInput
    with _$SyncChannelPrivateDataUseCaseInput {
  const SyncChannelPrivateDataUseCaseInput._();
  factory SyncChannelPrivateDataUseCaseInput() =
      _SyncChannelPrivateDataUseCaseInput;
}

@freezed
sealed class SyncChannelPrivateDataUseCaseOutput extends BaseOutput
    with _$SyncChannelPrivateDataUseCaseOutput {
  const SyncChannelPrivateDataUseCaseOutput._();
  factory SyncChannelPrivateDataUseCaseOutput({
    @Default(false) bool ok,
  }) = _SyncChannelPrivateDataUseCaseOutput;
}
