import 'package:json_annotation/json_annotation.dart';

part 'channel_metadata.g.dart';

@JsonSerializable(explicitToJson: true)
class ChannelMetadata {
  ChannelMetadata({
    this.lastMessageId = '',
    this.unreadCount = 0,
    this.notificationStatus = true,
    this.mediaPermissionSetting = 0,
  });

  final String lastMessageId;
  final int unreadCount;
  final bool notificationStatus;
  final int mediaPermissionSetting;

  factory ChannelMetadata.fromJson(Map<String, dynamic> json) =>
      _$ChannelMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$ChannelMetadataToJson(this);
}
