import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../data/repositories/extensions/location_datamap_extensions.dart';
import '../../../../data/repositories/extensions/message_extension.dart';
import '../base/base_sender_widget.dart';

class LocationOwnerWidgetImpl extends BaseSenderWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isHiddenPin;
  final bool isLastMessage;

  LocationOwnerWidgetImpl({
    required this.isOpenCheckBox,
    this.onCheckBoxButtonTap,
    this.isHiddenPin = false,
    this.isLastMessage = false,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    this.isShowCreateTime = false,
    required super.messageItem,
    required super.message,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (!message.hasLocationData) {
      return SizedBox.shrink();
    }

    final locationData = message.firstEmbed!.locationData!;
    return ShareLocationMessageSenderWidget(
      isLastMessage: isLastMessage,
      messageItem: messageItem,
      onQuote: onQuote,
      onCopy: (messageItem) => onCopy(context, messageItem),
      emojiList: message.emojiList,
      thumbnailUrl: locationData.thumbnailUrl ?? '',
      locationDescription: locationData.description ?? '',
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      isHiddenPin: isHiddenPin,
      isShowCreateTime: isShowCreateTime,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      onEmojiClicked: onEmojiClicked,
      onMessageItemClicked: onMessageItemClicked,
      onListReactionClicked: onListReactionClicked,
      onResend: onResendMessage,
      onDiscard: onDiscardMessage,
      isOpenCheckBox: isOpenCheckBox,
      isCheckedMessage: isCheckedMessage,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      isHideOptionResend: false,
      isHideOptionCopy: false,
      isHighlighted: isHighlighted,
      shouldAnimate: shouldAnimate,
    );
  }

  @override
  void onMessageItemClicked(MessageItem messageItem) {
    if (!message.hasLocationData) return;

    final locationData = message.firstEmbed!.locationData!;

    onClickLink(locationData.mapsLink);
  }
}
