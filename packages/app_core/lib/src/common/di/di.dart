import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared/shared.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../data/repositories/database/database.dart';
import '../../data/repositories/database/generated/objectbox.g.dart';
import '../../domain/event_bus/event_listener.dart';
import 'di.config.dart';

final GetIt getIt = GetIt.instance;

@injectableInit
Future<void> configureInjection() async {
  final dir = await getApplicationSupportDirectory();

  final privateDataStore = await PrivateDataStore(
    getObjectBoxModel(),
    directory: '${dir.path}/private_data_store',
  );
  PrivateDatabase(privateDataStore);

  final taskStore = await TasksStore(
    getObjectBoxModel(),
    directory: '${dir.path}/task_store',
  );
  TasksDatabase(taskStore);

  final sharedPreferences = await SharedPreferences.getInstance();
  getIt
    ..registerSingleton<TasksStore>(taskStore)
    ..registerSingleton<PrivateDataStore>(privateDataStore)
    ..registerSingleton<SharedPreferences>(sharedPreferences)
    ..registerLazySingleton<AppEventBus>(() => AppEventBus())
    ..init();
  CoreEventListener(getIt.get<AppEventBus>());
}
