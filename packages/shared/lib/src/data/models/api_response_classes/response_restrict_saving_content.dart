import 'package:json_annotation/json_annotation.dart';

part 'response_restrict_saving_content.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseRestrictSavingContent {
  final bool enable;

  ResponseRestrictSavingContent({required this.enable});

  factory ResponseRestrictSavingContent.fromJson(Map<String, dynamic> json) =>
      _$ResponseRestrictSavingContentFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseRestrictSavingContentToJson(this);
}
