import 'package:dio/dio.dart';

import '../../../../../shared.dart';

/// Interceptor to handle invalid token errors
///
/// This interceptor only fires events and does not modify the response
class ValidTokenInterceptor extends Interceptor {
  static const tag = 'ValidTokenInterceptor';

  static const List<String> excludedApis = [
    'InitiateUserKeyAuthFlow',
    'initiateRecoveryAccountFlow',
    'InitiateRecoveryCodeGenerationFlow',
    'DecodeUserConnectLink',
  ];

  @override
  void onResponse(
    Response<dynamic> response,
    ResponseInterceptorHandler handler,
  ) {
    // Get the full URL of the current request
    final fullUrl = _getFullUrl(response.requestOptions);

    // Skip processing if the URL is in the excluded APIs list
    if (_isExcludedApi(fullUrl)) {
      handler.next(response);
      return;
    }

    if (response.statusCode == 403 && response.data == 'Invalid token') {
      AppEventBus().fire(OnTokenInvalid());
    }
    handler.next(response);
  }

  // Checks if the given URL matches any of the excluded APIs
  bool _isExcludedApi(String url) {
    return excludedApis.any((api) => url.contains(api));
  }

  // Constructs the full URL including query parameters from the RequestOptions
  String _getFullUrl(RequestOptions options) {
    return options.baseUrl +
        options.path +
        (options.queryParameters.isNotEmpty
            ? '?${Transformer.urlEncodeMap(options.queryParameters)}'
            : '');
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Get the full URL of the current request
    final fullUrl = _getFullUrl(err.requestOptions);

    // Skip processing if the URL is in the excluded APIs list
    if (_isExcludedApi(fullUrl)) {
      handler.next(err);
      return;
    }

    if (err.response?.statusCode == 403 &&
        err.response?.data == 'Invalid token') {
      AppEventBus().fire(OnTokenInvalid());
    }
    handler.next(err);
  }
}
