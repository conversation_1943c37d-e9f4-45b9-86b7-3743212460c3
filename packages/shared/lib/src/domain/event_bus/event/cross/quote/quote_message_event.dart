import '../../../../../../../shared.dart';

class QuoteMessageEvent extends LocalEvent {
  QuoteMessageEvent({
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
    this.isBlocked,
    super.source = BaseEvent.LOCAL_SOURCE,
  });

  final String workspaceId;
  final String channelId;
  final String messageId;
  final bool? isBlocked;

  @override
  Map<String, dynamic> toJson() => {};
}
