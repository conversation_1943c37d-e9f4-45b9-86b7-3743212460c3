import 'dart:async';
import 'dart:convert';

import 'package:app_core/core.dart';
import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../common/di/di.dart';
import '../../../data/repositories/database/enums/chat_friend_status.dart';
import '../../../domain/usecase/channel/get_dm_channel_use_case.dart';
import '../../../domain/usecase/chat_user/unfriend_use_case.dart';

part 'user_profile_bloc.freezed.dart';
part 'user_profile_event.dart';
part 'user_profile_state.dart';

@injectable
class UserProfileBloc extends BaseBloc<UserProfileEvent, UserProfileState> {
  UserProfileBloc(
    this._getDMChannelUseCase,
    this._clearMessageAllForMeUseCase,
    this._upsertChatUser,
    this._clearMessageAllForEveryoneUseCase,
    this._chatUserRepository,
    this._deleteAllMessagesUseCase,
    this._addFriendUseCase,
    this._acceptRequestUseCase,
    this._cancelRequestUseCase,
    this._unfriendUseCase,
    this._getChatUserUseCase,
    this._getChatUserByUsernameUseCase,
    this._loadChatUserByUsernameUseCase,
    this._visitedProfileUseCase,
    this._loadChannelUseCase,
    this._channelRepository,
    this._renderUserUseCase,
  ) : super(UserProfileState.initial()) {
    on<InitiateUserProfileEvent>(_onInit);
    on<LoadUserEvent>(_onLoadUser);
    on<UnSubscriptionEvent>(_onUnSubscription);
    on<AddFriendEvent>(_onAddFriend);
    on<AcceptRequestEvent>(_onAcceptRequest);
    on<CancelRequestEvent>(_onCancelRequest);
    on<UnfriendEvent>(_onUnfriend);
    on<ClearMessageAllForMeChannelEvent>(_onClearMessageAllForMe);
    on<ClearMessageAllForEveryoneChannelEvent>(_onClearMessageAllForEveryone);
    on<RefreshEvent>(_onRefresh);
    on<OnSettingNotificationEvent>(_onSettingNotification);
    on<ErrorEvent>(_onError);
  }

  final GetChatUserUseCase _getChatUserUseCase;
  final GetChatUserByUsernameUseCase _getChatUserByUsernameUseCase;
  final LoadChatUserByUsernameUseCase _loadChatUserByUsernameUseCase;
  final RenderUserUseCase _renderUserUseCase;

  final LoadChannelUseCase _loadChannelUseCase;
  final UpsertChatUserUseCase _upsertChatUser;
  final GetDMChannelUseCase _getDMChannelUseCase;
  final DeleteAllMessagesUseCase _deleteAllMessagesUseCase;
  final ClearMessageAllForMeUseCase _clearMessageAllForMeUseCase;
  final ClearMessageAllForEveryoneUseCase _clearMessageAllForEveryoneUseCase;
  final AddFriendUseCase _addFriendUseCase;
  final AcceptRequestUseCase _acceptRequestUseCase;
  final CancelRequestUseCase _cancelRequestUseCase;
  final VisitedProfileUseCase _visitedProfileUseCase;

  final UnfriendUseCase _unfriendUseCase;
  final ChatUserRepository _chatUserRepository;
  final ChannelRepository _channelRepository;
  StreamSubscription? _chatUserSubscription;
  StreamSubscription? _channelSubscription;
  Channel? _channel;

  Future<void> _onInit(
    InitiateUserProfileEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    _subscribeToChannel(event, emit);
    await _subscribeToUser(event, emit);
    await _userFromApi(event, emit);
  }

  void _subscribeToChannel(
    InitiateUserProfileEvent event,
    Emitter<UserProfileState> emit,
  ) {
    _channelSubscription?.cancel();

    var userId = event.userId;
    if (event.username != null) {
      userId = _loadChatUserByUsernameUseCase
          .execute(LoadChatUserByUsernameInput(userName: event.username!))
          .user
          ?.userId;
    }
    if (userId == null) {
      return;
    }

    _channelSubscription = _channelRepository.observerDMChannel(
      userId,
      (Channel? channel) async {
        if (channel != null) {
          _channel = channel;
        }
      },
    );
  }

  Future<void> _subscribeToUser(
    InitiateUserProfileEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    await _chatUserSubscription?.cancel();

    _chatUserSubscription = _chatUserRepository.observerChatUser(
      chatUserId: event.userId,
      chatUserName: event.username?.replaceAll('@', ''),
      listener: (ChatUser? user) async {
        if (user != null) {
          if (user.friendData == null) {
            if (event.username != null) {
              user = await getChatUserByUserName(event.username!);
            }
            if (event.userId != null) {
              user = await getChatUserByUserId(event.userId!);
            }
          }
          if (!isClosed) {
            add(LoadUserEvent(user: user, channel: _channel));
          }
        }
      },
    );
  }

  Future<void> _userFromApi(
    InitiateUserProfileEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    ChatUser? user;
    if (event.username != null) {
      user = await getChatUserByUserName(event.username!);
    }
    if (event.userId != null) {
      user = await getChatUserByUserId(event.userId!);
    }

    if (user != null && user.username == GlobalConfig.ghost) {
      getIt<ProcessGhostDataUseCase>()
          .execute(ProcessGhostDataInput(userIds: [user.userId]));
    }
    final loadChannel = await _loadChannelUseCase.execute(
      LoadChannelInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      ),
    );
    _channel = loadChannel.channel;
    user?.notificationStatus = _channel?.notificationStatus;

    /// call visited profile
    if (event.callVisitedProfile == true) {
      await visitedProfile(user?.userId);
    }

    if (user?.friendData == null) {
      user
        ?..chatFriendDataRaw = jsonEncode(
          ChatFriendData(
            status: ChatFriendStatusEnum.NOT_FRIEND,
          ).toJson(),
        );
    }

    if (user != null) {
      _renderUserUseCase.execute(RenderUserInput(userId: user.userId));
      await _upsertChatUser.execute(UpsertChatUserInput(user: user));
    }
    if (!isClosed) {
      add(LoadUserEvent(user: user, channel: _channel));
    }
  }

  @override
  Future<void> close() {
    _chatUserSubscription?.cancel();

    return super.close();
  }

  Future<ChatUser?> getChatUserByUserName(String userName) async {
    var getOutput = await _getChatUserByUsernameUseCase
        .execute(GetChatUserByUsernameInput(userName: userName));
    if (getOutput.user != null) {
      _chatUserRepository.insert(getOutput.user!);
    }

    return getOutput.user;
  }

  Future<ChatUser?> getChatUserByUserId(String userId) async {
    var getOutput =
        await _getChatUserUseCase.execute(GetChatUserInput(userId: userId));
    _chatUserRepository.insert(getOutput.user!);
    if (getOutput.user != null) {
      _chatUserRepository.insert(getOutput.user!);
    }
    return getOutput.user;
  }

  FutureOr<void> _onUnSubscription(
    UnSubscriptionEvent event,
    Emitter<UserProfileState> emit,
  ) {
    _chatUserSubscription?.cancel();
  }

  Future<void> _onLoadUser(
    LoadUserEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    emit(
      UserProfileState.loadUser(user: event.user, channel: event.channel),
    );
  }

  Future<void> _onAddFriend(
    AddFriendEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    final output =
        await _addFriendUseCase.execute(AddFriendInput(user: event.user));
    if (output.message != null ||
        (output.user == null && output.code == null)) {
      add(RefreshEvent());
      add(ErrorEvent(code: output.code, message: output.message));
    }
  }

  Future<void> _onCancelRequest(
    CancelRequestEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    final output = await _cancelRequestUseCase
        .execute(CancelRequestInput(user: event.user));
    if (output.message != null ||
        (output.user == null && output.code == null)) {
      add(RefreshEvent());
      add(ErrorEvent(code: output.code, message: output.message));
    }
  }

  Future<void> _onAcceptRequest(
    AcceptRequestEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    final output = await _acceptRequestUseCase
        .execute(AcceptRequestInput(userId: event.user.userId));
    if (output.message != null ||
        (output.user == null && output.code == null)) {
      add(RefreshEvent());
      add(ErrorEvent(code: output.code, message: output.message));
    }
  }

  Future<void> _onUnfriend(
    UnfriendEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    final output =
        await _unfriendUseCase.execute(UnfriendInput(user: event.user));
    if (output.message != null) {
      add(RefreshEvent());
      add(ErrorEvent(code: output.code, message: output.message));
    }
  }

  FutureOr<void> _onClearMessageAllForMe(
    ClearMessageAllForMeChannelEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    var workspaceId = event.workspaceId;
    var channelId = event.channelId;
    emit(UserProfileState.showProcessDialog());
    ClearMessageAllForMeOutput clearMessageAllForMeOutput =
        await _clearMessageAllForMeUseCase.execute(
      ClearMessageAllForMeInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      ),
    );
    if (clearMessageAllForMeOutput.ok == true) {
      if (event.channelId == null) {
        var getDMChannel = await _getDMChannelUseCase
            .execute(GetDMChannelInput(userId: event.userId!));
        channelId = getDMChannel.channel?.channelId;
        workspaceId = getDMChannel.channel?.workspaceId;
      }

      if (channelId != null) {
        AppEventBus.publish(UnPinChannelEvent(channelId: channelId));
      }
      try {
        AppEventBus.publish(
          ClearMessageEvent(workspaceId: workspaceId, channelId: channelId),
        );
        await _deleteAllMessagesUseCase.execute(
          DeleteAllMessagesInput(
            workspaceId: workspaceId!,
            channelId: channelId!,
          ),
        );
      } catch (error) {}
    }
    emit(
      UserProfileState.updateProcessDialog(
        response: clearMessageAllForMeOutput.ok,
      ),
    );
    await Future.delayed(Duration(milliseconds: 100));
    emit(UserProfileState.refresh());
  }

  FutureOr<void> _onClearMessageAllForEveryone(
    ClearMessageAllForEveryoneChannelEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    var workspaceId = event.workspaceId;
    var channelId = event.channelId;
    emit(UserProfileState.showProcessDialog());
    ClearMessageAllForEveryoneOutput clearMessageAllForEveryoneOutput =
        await _clearMessageAllForEveryoneUseCase.execute(
      ClearMessageAllForEveryoneInput(
        workspaceId: event.workspaceId,
        channelId: event.channelId,
        userId: event.userId,
      ),
    );
    if (clearMessageAllForEveryoneOutput.ok == true) {
      if (event.workspaceId == null || event.channelId == null) {
        var getDMChannel = await _getDMChannelUseCase
            .execute(GetDMChannelInput(userId: event.userId!));
        channelId = getDMChannel.channel?.channelId;
        workspaceId = getDMChannel.channel?.workspaceId;
      }

      if (channelId != null) {
        AppEventBus.publish(UnPinChannelEvent(channelId: channelId));
      }

      AppEventBus.publish(
        ClearMessageEvent(workspaceId: workspaceId, channelId: channelId),
      );
      await _deleteAllMessagesUseCase.execute(
        DeleteAllMessagesInput(
          workspaceId: workspaceId!,
          channelId: channelId!,
        ),
      );
    }
    emit(
      UserProfileState.updateProcessDialog(
        response: clearMessageAllForEveryoneOutput.ok,
      ),
    );
    await Future.delayed(Duration(milliseconds: 100));
    emit(UserProfileState.refresh());
  }

  Future<void> visitedProfile(String? userId) async {
    if (userId == null) return;
    try {
      await _visitedProfileUseCase.execute(VisitedProfileInput(userId: userId));
    } catch (ex) {
      Log.e(name: 'UserProfileBloc.visitedProfile', ex);
    }
  }

  Future<void> _onRefresh(
    RefreshEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    emit(
      UserProfileState.refresh(),
    );
  }

  Future<void> _onSettingNotification(
    OnSettingNotificationEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    await _chatUserRepository.updateNotificationChannel(
      userId: event.userId,
      isNotification: event.isNotification,
    );
  }

  Future<void> _onError(
    ErrorEvent event,
    Emitter<UserProfileState> emit,
  ) async {
    emit(UserProfileState.onError(code: event.code, message: event.message));
  }
}
