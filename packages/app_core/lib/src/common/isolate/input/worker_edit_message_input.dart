import 'package:chat/src/data/repositories/database/enums/attachment_type.dart';
import 'package:json_annotation/json_annotation.dart';

import 'worker_send_message_base.dart';

part 'worker_edit_message_input.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class WorkerEditMessageInput extends WorkerSendMessageInputBase {
  final String content;
  final String messageId;
  final String contentLocale;

  WorkerEditMessageInput({
    required this.content,
    required this.messageId,
    required this.contentLocale,
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required String ref,
    required String taskName,
    required super.creationTime,
    super.isRefTask,
  }) : super(
          workspaceId: workspaceId,
          channelId: channelId,
          userId: userId,
          ref: ref,
          taskName: taskName,
          attachmentType: AttachmentType.UNSPECIFIED.rawValue(),
        );

  factory WorkerEditMessageInput.fromJson(Map<String, dynamic> json) =>
      _$WorkerEditMessageInputFromJson(json);

  Map<String, dynamic> toJson() => _$WorkerEditMessageInputToJson(this);

  Map<String, dynamic> toRequest() {
    if (isDm()) {
      return {
        'userId': userId,
        'messageId': messageId,
        'content': content,
        'ref': ref,
        'contentLocale': contentLocale,
      };
    }

    return {
      'workspaceId': workspaceId,
      'channelId': channelId,
      'messageId': messageId,
      'content': content,
      'ref': ref,
      'contentLocale': contentLocale,
    };
  }

  @override
  WorkerEditMessageInput copyWith({
    String? content,
    String? messageId,
    String? contentLocale,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? ref,
    String? taskName,
    int? attachmentType,
    DateTime? creationTime,
    bool? isRefTask,
  }) {
    return WorkerEditMessageInput(
      content: content ?? this.content,
      messageId: messageId ?? this.messageId,
      contentLocale: contentLocale ?? this.contentLocale,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      userId: userId ?? this.userId,
      ref: ref ?? this.ref,
      taskName: taskName ?? this.taskName,
      creationTime: creationTime ?? this.creationTime,
      isRefTask: isRefTask ?? this.isRefTask,
    );
  }
}
