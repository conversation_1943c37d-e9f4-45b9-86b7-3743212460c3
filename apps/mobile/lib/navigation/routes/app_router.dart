import 'package:auth/auth.dart';
import 'package:auto_route/auto_route.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

import '../middleware/debug_route_guard.dart';
import '../middleware/route_guard.dart';
import 'app_router.gr.dart';
import 'app_router_path.dart';

@AutoRouterConfig(
  replaceInRouteName: 'Page,Route',
)
@LazySingleton()
class AppRouter extends RootStackRouter {
  @override
  RouteType get defaultRouteType {
    return RouteType.cupertino();
  }

  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          page: DebugRoute.page,
          maintainState: true,
          guards: [DebugRouteGuard()],
        ),
        AutoRoute(
          page: AuthRoute.page,
          path: AppRouterPath.authPath,
          initial: false,
        ),
        AutoRoute(
          page: HomeRoute.page,
          guards: [
            _authGuard(),
          ],
          children: [
            <PERSON>Route(page: SearchRoute.page),
            AutoRoute(page: CallLogsRoute.page),
            AutoRoute(
              page: ChannelsRoute.page,
              initial: true,
            ),
            AutoRoute(page: FriendsRoute.page),
            AutoRoute(page: ProfileRoute.page),
          ],
        ),
        AutoRoute(
          page: WelcomeFirstRoute.page,
        ),
        AutoRoute(page: SliderRoute.page),
        AutoRoute(page: WelcomeLastRoute.page),
        AutoRoute(page: FriendRequestRoute.page),
        AutoRoute(page: AppearanceRoute.page),
        AutoRoute(page: LanguageRoute.page),
        AutoRoute(page: MessageRequestRoute.page),
        AutoRoute(page: QrScannerRoute.page),
        AutoRoute(page: EditProfileRoute.page),
        AutoRoute(
          page: UserProfileRoute.page,
          path: AppRouterPath.userPath,
          guards: [_authGuard()],
        ),
        AutoRoute(page: AuthProgressRoute.page),
        AutoRoute(page: EditChannelInfoRoute.page),
        AutoRoute(page: TakePhotoRoute.page),
        AutoRoute(page: TalkTranslateRoute.page),
        AutoRoute(page: CropAvatarRoute.page),
        AutoRoute(page: CropCoverRoute.page),
        AutoRoute(page: ShareToRoute.page),
        AutoRoute(page: ImageViewRoute.page),
        AutoRoute(page: AccountSelectionRoute.page),
        AutoRoute(page: DeleteAccountRoute.page),
        AutoRoute(page: AccountDeletedRoute.page),
        AutoRoute(page: PrivacyAndSecurityRoute.page),
        AutoRoute(page: AccountDeletionCodeRoute.page),
        AutoRoute(
          page: FullscreenViewRoute.page,
          guards: [_authGuard()],
        ),
        AutoRoute(page: BlockUsersRoute.page),
        AutoRoute(
          page: NotificationRoute.page,
          guards: [_authGuard()],
        ),
        AutoRoute(
          page: ChannelViewRoute.page,
          guards: [_authGuard()],
        ),
        AutoRoute(
          page: ChannelInfoRoute.page,
          guards: [_authGuard()],
        ),
        AutoRoute(page: ZiishortRoute.page),
        AutoRoute(page: TakePhotoAndVideoRoute.page),
        AutoRoute(
          page: VideoPlaybackRoute.page,
        ),
        AutoRoute(
          page: ImagePreviewRoute.page,
        ),
        AutoRoute(page: TransferOwnershipRoute.page),
        AutoRoute(page: ForceUpdateRoute.page),
        AutoRoute(page: IntroductionFirstRoute.page),
        AutoRoute(page: IntroductionSliderRoute.page),
        AutoRoute(page: CallRoomRoute.page),
      ];

  RouteGuard _authGuard() =>
      RouteGuard(GetIt.instance.get<IsAuthenticatedUseCase>());

  Future<void> popUtilOrReplace(String routeName) async {
    bool hasRouteInStack = _hasRouteInStack(routeName);

    if (hasRouteInStack) {
      popUntilRouteWithName(routeName);
    } else {
      await replacePath(routeName);
    }
  }

  bool _hasRouteInStack(String routeName) {
    final hasRouteInStack = stack.any((route) {
      return route.name == routeName;
    });
    return hasRouteInStack;
  }

  Future<void> popUtilOrPush(String routeName) async {
    bool hasRouteInStack = _hasRouteInStack(routeName);

    if (hasRouteInStack) {
      popUntilRouteWithName(routeName);
    } else {
      await pushPath(routeName);
    }
  }

  bool hasRouteInStack(String routeName) {
    final hasRouteInStack = stack.any((route) {
      return route.name == routeName;
    });
    return hasRouteInStack;
  }
}
