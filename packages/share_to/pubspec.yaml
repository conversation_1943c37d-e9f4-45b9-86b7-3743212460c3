name: share_to
description: ZiiChat share to package
version: 0.1.0+1
publish_to: none
environment:
  sdk: ^3.5.0
  flutter: ^3.24.0
dependencies:
  flutter:
    sdk: flutter
  bloc: ^9.0.0
  flutter_bloc: ^9.1.0
  get_it: ^8.0.3
  injectable: ^2.5.0
  app_core:
    path: ../app_core
  shared:
    path: ../shared
  share_to_sdk:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.49.0
      path: packages/share_to
  ziichat_ui:
    git:
      url: **************:ziichatlabs/ziichat-flutter-ui.git
      ref: release/0.56.0
  channel_view_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/channel_view_api
  friend_view_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/friend_view_api
  search_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.25
      path: apis/search_api
  freezed_annotation: ^3.0.0
dev_dependencies:
  flutter_test:
    sdk: flutter
  mocktail: ^1.0.4
  freezed: ^3.0.6
  injectable_generator: ^2.7.0
  very_good_analysis: ^7.0.0
  build_runner: ^2.4.15
dependency_overrides:
  analyzer: 7.3.0
