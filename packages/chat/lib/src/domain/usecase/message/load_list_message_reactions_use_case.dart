import 'dart:convert';

import 'package:app_core/core.dart';
import 'package:injectable/injectable.dart';
import 'package:message_view_api/message_view_api.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class LoadListMessageReactionsUseCase extends BaseFutureUseCase<
    LoadListMessageReactionsInput, LoadListMessageReactionsOutput> {
  LoadListMessageReactionsUseCase(
    this._chatUserRepository,
    this._privateDataRepository,
  );

  final ChatUserRepository _chatUserRepository;
  final PrivateDataRepository _privateDataRepository;

  @override
  Future<LoadListMessageReactionsOutput> buildUseCase(
    LoadListMessageReactionsInput input,
  ) async {
    try {
      final response = await MessageViewClient().instance.listMessageReactions(
            workspaceId: input.workspaceId,
            channelId: input.channelId,
            messageId: input.messageId,
            emoji: input.emoji,
            nextPageToken: input.nextPageToken,
            limit: input.limit,
          );
      if (response.data?.ok ?? false) {
        final hasNext = response.data?.paging?.hasNext ?? false;
        final nextPageToken = response.data?.paging?.nextPageToken ?? '';
        final listMembers = response.data?.data?.members?.toList() ?? [];
        final List<ChatUser> users = [];
        final members = listMembers.map((memberData) {
          final json = jsonDecode(
            standardSerializers.toJson(
              V3MemberData.serializer,
              memberData,
            ),
          );
          final user = _chatUserRepository.getUser(memberData.member!.userId!);
          final aliasName =
              _privateDataRepository.getUser(user!.userId)?.aliasName;
          user.aliasName = aliasName;
          users.add(user);
          return MemberSerializer.serializeFromJson(data: json['member'])!;
        });

        return LoadListMessageReactionsOutput(
          hasNext: hasNext,
          nextPageToken: nextPageToken,
          members: members.toList(),
          users: users,
        );
      }
    } catch (ex) {
      Log.e(ex, name: 'Load Message Reaction');
    }
    return LoadListMessageReactionsOutput();
  }
}

class LoadListMessageReactionsInput extends BaseInput {
  LoadListMessageReactionsInput({
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
    required this.emoji,
    required this.nextPageToken,
    this.limit = 100,
  });

  final String workspaceId;
  final String channelId;
  final String messageId;
  final String emoji;
  final String? nextPageToken;
  final int limit;
}

class LoadListMessageReactionsOutput extends BaseOutput {
  LoadListMessageReactionsOutput({
    this.members,
    this.users,
    this.nextPageToken,
    this.hasNext = false,
  });

  final List<Member>? members;
  final List<ChatUser>? users;
  final String? nextPageToken;
  final bool hasNext;
}
