import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:web_socket_channel/io.dart';

import '../../../core.dart';
import '../../common/di/di.dart';
import '../../data/source/api/client/websocket_client.dart';
import 'websocket_listener.dart';

enum ConnectionStatus {
  disconnected,
  connecting,
  connected,
}

@lazySingleton
class WebSocketManager {
  IOWebSocketChannel? _channel;
  WebSocketListener? _listener;
  Timer? _reconnectTimer;
  String? _serverUrl;
  ConnectionStatus _connectionStatus = ConnectionStatus.disconnected;
  static const String TAG = 'WebSocketManager';
  static bool LOG_CONNECTION_ERROR = false;
  static Duration RECONNECT_INTERVAL = DurationUtils.s5;
  static Duration PING_INTERVAL = DurationUtils.s20;
  static Duration CONNECT_TIMEOUT = DurationUtils.s30;
  final NetworkManager _network;
  final WebSocketEventQueue _eventQueue;
  String? _source;
  String? _deviceId;
  String? _myUserId;
  String? _me;
  int _reconnectAttempts = 0;

  final StreamController<bool> _connectionStatusController =
      StreamController<bool>.broadcast();
  late final AppLifecycleListener _appLifecycleListener;
  AppLifecycleState _lastAppLifecycleState = AppLifecycleState.resumed;

  Stream<bool> get connectionStatus => _connectionStatusController.stream;

  String? get source => _source;

  set source(String? value) {
    _source = value;
  }

  set deviceId(String? value) {
    _deviceId = value;
  }

  set myUserId(String? value) {
    _myUserId = value;
  }

  String? get me => _me;

  set me(String? value) {
    _me = value;
  }

  StreamSubscription? _sendEventSubscription;

  WebSocketManager(this._network, this._eventQueue) {
    _sendEventSubscription =
        getIt<AppEventBus>().on<SendCloudEvent>().listen(_onReceivedCloudEvent);

    _appLifecycleListener = AppLifecycleListener(
      onStateChange: (newState) {
        _lastAppLifecycleState = newState;
      },
    );
  }

  void _onReceivedCloudEvent(SendCloudEvent event) {
    sendMessage(event.toJson());
  }

  void connect() async {
    if (isConnected || isConnecting) return;

    if (Config.getInstance().apiAuthToken.isEmpty) return;

    _connectionStatus = ConnectionStatus.connecting;

    if (_network.noConnection()) {
      Log.ws(
        name: TAG,
        "No network connection. Will retry after ${RECONNECT_INTERVAL.inSeconds}s",
      );
      _connectionStatus = ConnectionStatus.disconnected;
      _scheduleReconnect();
      return;
    }

    final wsApi = WebsocketClient().instance;
    var connectionResponse;
    debugPrint(
      'WebSocketManager.connect instance: ${wsApi.hashCode}',
    );
    try {
      connectionResponse = await wsApi.openConnection(intent: null);
    } catch (e) {
      // Perform network diagnostics asynchronously and log error

      unawaited(() async {
        final diagnostics = await collectNetworkDiagnostics();
        logConnectionError(
          e as Exception,
          diagnostics,
          _reconnectAttempts,
          RECONNECT_INTERVAL,
        );
      }());
    }

    if (connectionResponse == null) {
      _connectionStatus = ConnectionStatus.disconnected;
      Log.ws(name: TAG, "UnKnow Error when connected");
      _scheduleReconnect();
      return;
    }

    String? serverUrl = connectionResponse.data?.connectParams?.url;

    if (serverUrl == null || serverUrl.isEmpty) {
      _connectionStatus = ConnectionStatus.disconnected;
      Log.ws(name: TAG, "Invalid server URL.");
      _scheduleReconnect();
      return;
    }

    _serverUrl = serverUrl;
    _initializeConnection();
  }

  /// Pings a host and returns detailed information about the ping results
  /// including success status and latency statistics
  Future<Map<String, dynamic>> pingHost(String host) async {
    final Map<String, dynamic> pingResult = {
      'success': false,
      'latency_avg': 0.0,
      'latency_min': 0.0,
      'latency_max': 0.0,
      'packet_loss': 100.0,
      'error': null,
    };

    try {
      // Run ping command with 5 packets
      final result = await Process.run('ping', ['-c', '5', host]);

      // Store the raw output for debugging
      pingResult['raw_stdout'] = result.stdout.toString().trim();
      pingResult['raw_stderr'] = result.stderr.toString().trim();

      // Check if ping was successful
      pingResult['success'] = result.exitCode == 0;

      if (result.exitCode == 0) {
        // Parse ping statistics from output
        final output = result.stdout.toString();

        // Extract packet loss
        final packetLossRegex = RegExp(r'(\d+\.?\d*)% packet loss');
        final packetLossMatch = packetLossRegex.firstMatch(output);
        if (packetLossMatch != null && packetLossMatch.groupCount >= 1) {
          pingResult['packet_loss'] =
              double.tryParse(packetLossMatch.group(1) ?? '100.0') ?? 100.0;
        }

        // Extract latency statistics
        final latencyRegex = RegExp(
            r'min/avg/max/(?:mdev|stddev) = (\d+\.?\d*)/(\d+\.?\d*)/(\d+\.?\d*)/(\d+\.?\d*)');
        final latencyMatch = latencyRegex.firstMatch(output);
        if (latencyMatch != null && latencyMatch.groupCount >= 3) {
          pingResult['latency_min'] =
              double.tryParse(latencyMatch.group(1) ?? '0.0') ?? 0.0;
          pingResult['latency_avg'] =
              double.tryParse(latencyMatch.group(2) ?? '0.0') ?? 0.0;
          pingResult['latency_max'] =
              double.tryParse(latencyMatch.group(3) ?? '0.0') ?? 0.0;
        }
      }

      debugPrint(
        'WebSocketManager.connect pingHost:\n'
        '- command: ping -c 5 $host\n'
        '- exitCode: ${result.exitCode}\n'
        '- latency: ${pingResult['latency_avg']}ms\n'
        '- packet loss: ${pingResult['packet_loss']}%',
      );
    } catch (e) {
      pingResult['error'] = e.toString();
      debugPrint('pingHost: error=${e.runtimeType} - $e');
    }

    return pingResult;
  }

  /// Performs DNS lookup for a host and returns a list of IP addresses
  Future<Map<String, dynamic>> nsLookup(String host) async {
    final Map<String, dynamic> lookupResult = {
      'addresses': <String>[],
      'error': null,
      'success': false,
    };

    try {
      final addresses = await InternetAddress.lookup(host);
      final addressList = addresses.map((addr) => addr.address).toList();

      lookupResult['addresses'] = addressList;
      lookupResult['success'] = addressList.isNotEmpty;

      debugPrint(
        'WebSocketManager.nsLookup: $host - ${addressList.join(', ')}',
      );
    } catch (error) {
      lookupResult['error'] = error.toString();
      debugPrint('WebSocketManager.nsLookup: $host - $error');
    }

    return lookupResult;
  }

  /// Gets the current device IP addresses (both IPv4 and IPv6)
  Future<Map<String, dynamic>> getDeviceIpAddresses() async {
    final Map<String, dynamic> result = {
      'ipv4_addresses': <String>[],
      'ipv6_addresses': <String>[],
      'error': null,
    };

    try {
      // Get all network interfaces
      final interfaces = await NetworkInterface.list();

      for (var interface in interfaces) {
        // Skip loopback interfaces
        if (interface.name == 'lo' || interface.name.contains('loopback')) {
          continue;
        }

        for (var addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4) {
            result['ipv4_addresses'].add(addr.address);
          } else if (addr.type == InternetAddressType.IPv6) {
            result['ipv6_addresses'].add(addr.address);
          }
        }
      }

      debugPrint(
          'Device IP addresses: ${result['ipv4_addresses']}, ${result['ipv6_addresses']}');
    } catch (e) {
      result['error'] = e.toString();
      debugPrint('Error getting device IP addresses: $e');
    }

    return result;
  }

  /// Attempts to get DNS server information by reading system configuration
  Future<Map<String, dynamic>> getDnsServerInfo() async {
    final Map<String, dynamic> result = {
      'dns_servers': <String>[],
      'error': null,
    };

    try {
      // On Android and iOS, we can try to read DNS configuration from system files
      if (Platform.isAndroid) {
        // Try to read from /system/etc/resolv.conf
        final file = File('/system/etc/resolv.conf');
        if (await file.exists()) {
          final content = await file.readAsString();
          final dnsServers = _extractDnsServersFromResolvConf(content);
          result['dns_servers'] = dnsServers;
        }
      } else if (Platform.isIOS) {
        // On iOS, we can't directly access DNS server info
        // We'll use a fallback to common DNS servers
        result['dns_servers'] = [
          '*******',
          '*******'
        ]; // Google DNS as fallback
        result['note'] = 'Using fallback DNS servers, actual servers unknown';
      }

      // If we couldn't get DNS servers, add a note
      if (result['dns_servers'].isEmpty) {
        result['dns_servers'] = [
          '*******',
          '*******'
        ]; // Google DNS as fallback
        result['note'] =
            'Could not determine DNS servers, using fallback values';
      }

      debugPrint('DNS servers: ${result['dns_servers']}');
    } catch (e) {
      result['error'] = e.toString();
      result['dns_servers'] = ['*******', '*******']; // Google DNS as fallback
      result['note'] = 'Error getting DNS servers, using fallback values';
      debugPrint('Error getting DNS server info: $e');
    }

    return result;
  }

  /// Helper method to extract DNS servers from resolv.conf content
  List<String> _extractDnsServersFromResolvConf(String content) {
    final dnsServers = <String>[];
    final lines = content.split('\n');

    for (var line in lines) {
      line = line.trim();
      if (line.startsWith('nameserver')) {
        final parts = line.split(RegExp(r'\s+'));
        if (parts.length >= 2) {
          final serverIp = parts[1].trim();
          if (serverIp.isNotEmpty) {
            dnsServers.add(serverIp);
          }
        }
      }
    }

    return dnsServers;
  }

  /// Collects comprehensive network diagnostics information for troubleshooting connection issues
  /// Returns a map containing detailed network information including:
  /// - Current network connection type (WiFi/Mobile/None)
  /// - Device IP addresses
  /// - DNS server information
  /// - Ping results with latency statistics
  /// - DNS lookup results
  Future<Map<String, dynamic>> collectNetworkDiagnostics() async {
    final Map<String, dynamic> diagnostics = {};

    // Add timestamp
    diagnostics['timestamp'] = DateTime.now().toIso8601String();

    // Get current network connection type
    try {
      final connectionType = _network.currentStatus;
      diagnostics['network_connection_type'] = connectionType.toString();
      diagnostics['network_connection_details'] = {
        'is_wifi': _network.isWifi,
        'is_mobile': _network.isMobile,
        'is_ethernet': _network.isEthernet,
        'is_vpn': _network.isVpn,
        'is_bluetooth': _network.isBluetooth,
        'is_none': _network.isNone,
        'has_connection': _network.hasConnection(),
      };
    } catch (e) {
      diagnostics['network_connection_error'] = e.toString();
    }

    // Get device IP addresses
    diagnostics['device_ip_info'] = await getDeviceIpAddresses();

    // Get DNS server information
    diagnostics['dns_server_info'] = await getDnsServerInfo();

    // Perform ping tests with detailed latency information
    diagnostics['ping_vfb'] = await pingHost('vfb.rpc.ziiapis.com');
    diagnostics['ping_ip'] = await pingHost('*********');

    // Add Google DNS as a reference point
    diagnostics['ping_google_dns'] = await pingHost('*******');

    // Perform DNS lookups
    diagnostics['dns_lookup_vfb'] = await nsLookup('vfb.rpc.ziiapis.com');
    diagnostics['dns_lookup_ip'] = await nsLookup('*********');

    // Get device details
    try {
      // Use the getDeviceDetails function from shared/src/utils/device_info.dart
      diagnostics['device_details'] = await getDeviceDetails();
    } catch (e) {
      diagnostics['device_details_error'] = e.toString();

      // Fallback to basic platform info
      diagnostics['device_platform'] = {
        'os': Platform.operatingSystem,
        'os_version': Platform.operatingSystemVersion,
        'dart_version': Platform.version,
      };
    }

    return diagnostics;
  }

  /// Logs connection error details to both Firebase Crashlytics and application logs
  ///
  /// @param e The original exception that occurred
  /// @param diagnostics Network diagnostic information collected
  /// @param reconnectAttempts Current number of reconnection attempts
  /// @param reconnectInterval Time interval before next reconnection attempt
  void logConnectionError(
    Exception e,
    Map<String, dynamic> diagnostics,
    int reconnectAttempts,
    Duration reconnectInterval,
  ) {
    // Add retry attempt information to diagnostics
    diagnostics['reconnect_attempts'] = reconnectAttempts;
    diagnostics['reconnect_interval'] = reconnectInterval.inSeconds;

    if (LOG_CONNECTION_ERROR)
      debugPrint('WebSocketManager.logConnectionError: ${diagnostics}');

    // Record error with detailed diagnostic information
    FirebaseCrashlytics.instance.recordError(
      e,
      null,
      reason: 'WebSocket connection error',
      information: [
        'Retry attempt: $reconnectAttempts',
        'Diagnostics: ${diagnostics.toString()}',
      ],
    );

    String logReconnect =
        "Connection error: ${e.toString()} - Retry attempt $reconnectAttempts scheduled in ${reconnectInterval.inSeconds}s";

    if (LOG_CONNECTION_ERROR) {
      logReconnect = logReconnect + "\nDiagnostics: $diagnostics";
    }

    // Log detailed error information
    Log.ws(name: TAG, logReconnect);
  }

  void _initializeConnection() {
    try {
      _channel = IOWebSocketChannel.connect(
        Uri.parse(_serverUrl!),
        pingInterval: PING_INTERVAL,
        connectTimeout: CONNECT_TIMEOUT,
      );
      Log.ws(
        name: TAG,
        "Attempting to connect to WebSocket with URL: $_serverUrl",
      );

      _listener?.dispose();
      _listener = WebSocketListener(this);
      Log.ws(name: TAG, "WebSocket connection established.");
      onConnected();
    } catch (e) {
      Log.ws(name: TAG, "Error initializing WebSocket connection: $e");
      _onDisconnect();
    }
  }

  void _onDisconnect() {
    _connectionStatus = ConnectionStatus.disconnected;
    _connectionStatusController.add(false);
    Log.ws(name: TAG, "WebSocket disconnected.");
    _scheduleReconnect();
  }

  void _scheduleReconnect() {
    _reconnectTimer?.cancel();
    _reconnectAttempts++;
    _reconnectTimer = Timer(
      RECONNECT_INTERVAL,
      () {
        Log.ws(
          name: TAG,
          "Retrying connection... (attempt $_reconnectAttempts)",
        );
        connect();
      },
    );
  }

  void sendMessage(Map<String, dynamic> message) async {
    if (isDisconnected || isConnecting) {
      // If websocket is not connected, save the original message to queue
      await _eventQueue.enqueueEvent(message);
      return;
    }

    // Only add source when websocket is connected
    final mutableMessage = Map<String, dynamic>.from(message);
    if (mutableMessage[source] == null || mutableMessage[source] == '') {
      mutableMessage['source'] = _source;
    }

    // Update data for PRESENCE_UPDATED_EVENT
    if (mutableMessage['type'] == EventType.PRESENCE_UPDATED.value) {
      mutableMessage['data']['isOnline'] =
          _lastAppLifecycleState == AppLifecycleState.resumed;
      mutableMessage['data']['deviceId'] = _deviceId;
      mutableMessage['data']['userId'] = _myUserId;
    }

    Log.ws(name: TAG + ' Message sent:', mutableMessage);
    _channel!.sink.add(jsonEncode(mutableMessage));
  }

  void sendBinaryMessage(List<int> data) {
    if (isDisconnected) return;
    _channel!.sink.add(data);
    Log.ws(name: TAG, "Binary data sent.");
  }

  void onConnected() {
    _connectionStatus = ConnectionStatus.connected;
    _connectionStatusController.add(true);
    _reconnectTimer?.cancel();
    // Reset reconnect attempts counter on successful connection
    _reconnectAttempts = 0;
    Log.ws(name: TAG, "WebSocket connected.");
    checkNetworkAndReconnect();
  }

  void sendPendingEvents() {
    final pendingEvents = _eventQueue.getAllPendingEvents();
    if (pendingEvents.isNotEmpty) {
      for (var event in pendingEvents) {
        sendMessage(event);
      }
      _eventQueue.clearAllPendingEvents();
    }
  }

  void disconnect() {
    Log.ws(name: TAG, "WebSocket disconnect.");
    _reconnectTimer?.cancel();
    _listener?.dispose();
    _channel?.sink.close();
    _channel = null;
    _connectionStatus = ConnectionStatus.disconnected;
    _connectionStatusController.add(false);
    _scheduleReconnect();
  }

  void forceClose() {
    Log.ws(name: TAG, "WebSocket forceClose.");
    _reconnectTimer?.cancel();
    _listener?.dispose();
    _channel?.sink.close();
    _channel = null;
    _connectionStatus = ConnectionStatus.disconnected;
    _connectionStatusController.add(false);
  }

  void checkNetworkAndReconnect() {
    _network.onConnectivityChanged.listen((results) {
      debugPrint('WebSocketManager.checkNetworkAndReconnect: ${results}');
      if (results.first.isNone) {
        disconnect();
      } else if (isDisconnected) {
        Log.ws(
          name: TAG,
          "Network connection restored. Attempting to reconnect WebSocket.",
        );
        connect();
      }
    });
  }

  bool get isConnected => _connectionStatus == ConnectionStatus.connected;

  bool get isConnecting => _connectionStatus == ConnectionStatus.connecting;

  bool get isDisconnected => _connectionStatus == ConnectionStatus.disconnected;

  void dispose() {
    disconnect();
    _connectionStatusController.close();
    _sendEventSubscription?.cancel();
    _appLifecycleListener.dispose();
  }

  IOWebSocketChannel? getChannel() {
    return _channel;
  }
}
