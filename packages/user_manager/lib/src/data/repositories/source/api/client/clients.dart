import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_connect_api/user_connect_api.dart' as user_connect_api;
import 'package:user_profile_api/user_profile_api.dart' as user_profile_api;
import 'package:user_view_api/user_view_api.dart' as user_view_api;

import '../../../../../../user_manager.dart';

@LazySingleton()
class UserViewClient {
  late final user_view_api.UserViewServiceApi _instance;

  UserViewClient() {
    if (Config.getInstance().apiAuthToken.isNotEmpty) {
      BaseClient.addAuthToken(
        BaseClient.dio,
        Config.getInstance().apiAuthToken,
      );
    }

    _instance = user_view_api.UserViewApi(
      dio: BaseClient.dio,
      serializers: user_view_api.standardSerializers,
    ).getUserViewServiceApi();
  }

  user_view_api.UserViewServiceApi get instance => _instance;
}

@LazySingleton()
class UserProfileClient {
  late final user_profile_api.UserProfileServiceApi _instance;

  UserProfileClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = user_profile_api.UserProfileApi(
      dio: BaseClient.dio,
      serializers: user_profile_api.standardSerializers,
    ).getUserProfileServiceApi();
  }

  user_profile_api.UserProfileServiceApi get instance => _instance;
}

@LazySingleton()
class UserConnectClient {
  late final user_connect_api.UserConnectServiceApi _instance;

  UserConnectClient() {
    BaseClient.addAuthToken(BaseClient.dio, Config.getInstance().apiAuthToken);

    _instance = user_connect_api.UserConnectApi(
      dio: BaseClient.dio,
      serializers: user_connect_api.standardSerializers,
    ).getUserConnectServiceApi();
  }

  user_connect_api.UserConnectServiceApi get instance => _instance;
}
