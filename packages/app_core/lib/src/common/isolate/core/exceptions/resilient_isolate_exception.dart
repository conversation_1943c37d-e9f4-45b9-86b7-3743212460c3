class ResilientIsolateException implements Exception {
  final String code;

  final String message;

  final dynamic originalError;

  ResilientIsolateException(this.code, this.message, [this.originalError]);

  @override
  String toString() {
    final buffer = StringBuffer('ResilientIsolateException($code): $message');
    if (originalError != null) {
      buffer.write('\nOriginal error: $originalError');
    }
    return buffer.toString();
  }

  factory ResilientIsolateException.notInitialized() {
    return ResilientIsolateException(
      'not_initialized',
      'ResilientIsolate is not initialized. Call initialize() first.',
    );
  }

  factory ResilientIsolateException.handlerNotFound(String taskName) {
    return ResilientIsolateException(
      'handler_not_found',
      'No handler found for task: $taskName',
    );
  }

  factory ResilientIsolateException.taskNotFound(String taskId) {
    return ResilientIsolateException(
      'task_not_found',
      'Task not found: $taskId',
    );
  }

  factory ResilientIsolateException.taskAlreadyCompleted(String taskId) {
    return ResilientIsolateException(
      'task_already_completed',
      'Task already completed: $taskId',
    );
  }

  factory ResilientIsolateException.taskAlreadyCanceled(String taskId) {
    return ResilientIsolateException(
      'task_already_canceled',
      'Task already canceled: $taskId',
    );
  }

  factory ResilientIsolateException.taskFailed(String taskId, dynamic error) {
    return ResilientIsolateException(
      'task_failed',
      'Task failed: $taskId',
      error,
    );
  }

  factory ResilientIsolateException.isolateStartFailed(dynamic error) {
    return ResilientIsolateException(
      'isolate_start_failed',
      'Failed to start isolate',
      error,
    );
  }

  factory ResilientIsolateException.isolateCrashed(dynamic error) {
    return ResilientIsolateException(
      'isolate_crashed',
      'Isolate crashed',
      error,
    );
  }

  factory ResilientIsolateException.taskTimedOut(String taskId, int timeout) {
    return ResilientIsolateException(
      'task_timed_out',
      'Task timed out after $timeout ms: $taskId',
    );
  }

  factory ResilientIsolateException.storageFailed(
    String operation,
    dynamic error,
  ) {
    return ResilientIsolateException(
      'storage_failed',
      'Storage operation failed: $operation',
      error,
    );
  }
}
