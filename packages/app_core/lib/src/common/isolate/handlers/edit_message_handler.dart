import 'package:shared/shared.dart';

import '../../../data/source/api/client/isolate_api_client.dart';
import '../../network/retry_manager.dart';
import '../input/worker_edit_message_input.dart';
import '../output/worker_send_message_output.dart';
import 'base_message_handler.dart';

/// Specialized handler for editing messages
class EditMessageHandler extends BaseMessageHandler<WorkerEditMessageInput> {
  EditMessageHandler({
    required super.apiClient,
    required super.retryManager,
  });

  @override
  WorkerEditMessageInput parseInput(Map<String, dynamic> inputData) {
    return WorkerEditMessageInput.fromJson(inputData);
  }

  @override
  String getApiEndpoint(WorkerEditMessageInput input) {
    return '/Message/' + (input.isDm() ? 'UpdateDMMessage' : 'UpdateMessage');
  }

  @override
  Future<WorkerSendMessageOutput> executeMessageOperation(
    WorkerEditMessageInput input,
    DateTime createTime,
  ) async {
    return await makeRetryableApiCall(
      input: input,
      createTime: createTime,
      apiCall: () => apiClient.put(
        getApiEndpoint(input),
        body: input.toRequest(),
      ),
    );
  }

  /// Legacy method for backward compatibility
  Future<WorkerSendMessageOutput> handleEditMessage(
    Map<String, dynamic> inputData,
  ) async {
    return await handleMessage(inputData);
  }
}
