part of 'room_page.dart';

class CallGroupBottomWidgetImplement
    implements ui.CallGroupBottomWidgetInterface {
  CallGroupBottomWidgetImplement({
    required this.context,
    required this.roomBloc,
    required this.warmingNotifier,
  });

  final RoomBloc roomBloc;
  final BuildContext context;
  final ValueNotifier<RoomWarningState> warmingNotifier;

  bool _cameraBusy = false;
  bool _microBusy = false;

  @override
  void onCameraActiveClicked() {
    // from activate to inactive
    roomBloc.add(const ToggleCameraStateEvent(false));
  }

  @override
  void onCameraClicked() {
    // from inactivate to active
    if (_cameraBusy) return;
    _cameraBusy = true;
    _turnOnCamera();
  }

  Future<void> _turnOnCamera() async {
    final granted = await PermissionUtils.requestCameraPermission(
      context,
      onOpenSetting: () {
        BasicMessageChannel<String?> lifecycleChannel =
            SystemChannels.lifecycle;
        lifecycleChannel.setMessageHandler((msg) async {
          if (msg!.endsWith("resumed")) {
            warmingNotifier.value =
                warmingNotifier.value.copyWith(hasCameraPermission: true);
            if (await PermissionUtils.isGrantedCameraPermission()) {
              roomBloc.add(const ToggleCameraStateEvent(true));
            }
          }
          return Future(() => null);
        });
      },
    );
    if (granted) {
      warmingNotifier.value =
          warmingNotifier.value.copyWith(hasCameraPermission: true);
      roomBloc.add(const ToggleCameraStateEvent(true));
    }
    _cameraBusy = false;
  }

  @override
  void onMuteActiveClicked() {
    // from activate to inactive
    roomBloc.add(const ToggleMicroStateEvent(false));
  }

  @override
  void onMuteClicked() {
    // from inactivate to active
    if (_microBusy) return;
    _microBusy = true;
    _turnOnMicrophone();
  }

  Future<void> _turnOnMicrophone() async {
    final granted = await PermissionUtils.requestMicrophonePermission(
      context,
      onOpenSetting: () {
        BasicMessageChannel<String?> lifecycleChannel =
            SystemChannels.lifecycle;
        lifecycleChannel.setMessageHandler((msg) async {
          if (msg!.endsWith("resumed")) {
            if (await PermissionUtils.isGrantedMicrophonePermission()) {
              warmingNotifier.value =
                  warmingNotifier.value.copyWith(hasMicrophonePermission: true);
              roomBloc.add(const ToggleMicroStateEvent(true));
            }
          }
          return Future(() => null);
        });
      },
    );
    if (granted) {
      warmingNotifier.value =
          warmingNotifier.value.copyWith(hasMicrophonePermission: true);
      roomBloc.add(const ToggleMicroStateEvent(true));
    }
    _microBusy = false;
  }

  @override
  void onSpeakerActiveClicked() {
    // from activate to inactive
    roomBloc.add(const ToggleSpeakerStateEvent(false));
  }

  @override
  void onSpeakerClicked() {
    // from inactivate to active
    roomBloc.add(const ToggleSpeakerStateEvent(true));
  }

  @override
  void onLeaveClicked() {
    roomBloc.add(const DisconnectEvent());
  }

  @override
  void onChangeOutput() {
    //TODO: Not implemented in this phase
    // roomBloc.add(ShowSpeakerSelectorEvent());
  }

  @override
  void onShareScreenActiveClicked() {
    roomBloc.add(ToggleScreenShareEvent(false));
  }

  @override
  void onShareScreenClicked() {
    roomBloc.add(ToggleScreenShareEvent(true));
  }
}
