import 'dart:async';
import 'dart:typed_data';

import 'package:chat/chat.dart';
import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:get_it/get_it.dart';
import 'package:message_api/message_api.dart';
import 'package:shared/shared.dart';
import 'package:upload_manager/upload_manager.dart' hide Config;
import 'package:video_compressor/video_compressor.dart';

import '../../common/di/di.dart';

class SendVideoMessageHandler {
  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final MessagesBloc? messagesBloc;
  late UploadVideoHandler uploadVideoHandler;
  late V3AttachmentTypeEnum _videoType;

  SendVideoMessageHandler({
    required this.workspaceId,
    required this.channelId,
    required this.userId,
    required this.messagesBloc,
  });

  bool isDm() => userId != null;

  /// Sends image messages, supporting sequential and parallel uploads.
  Future<void> sendVideoMessage(
    List<UploadFile> videoList, {
    V3AttachmentTypeEnum? type,
  }) async {
    if (videoList.isEmpty) return;
    _videoType = type ?? V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_VIDEO;
    await _uploadSequentially(videoList, _videoType);
  }

  /// Handles sequential upload of images.
  Future<void> _uploadSequentially(
    List<UploadFile> videoList,
    V3AttachmentTypeEnum type,
  ) async {
    int currentUploadIndex = 0;
    uploadVideoHandler = UploadVideoHandler();
    final channel = await _getChannel();

    if (channel == null) return;

    Future<void> uploadFileAtIndex(int index) async {
      if (index >= videoList.length) return;

      var currentVideo = videoList[index];

      final temporaryMessage = createTemporaryVideoMessage(
        workspaceId: channel.workspaceId,
        channelId: channel.channelId,
        userId: userId,
        video: currentVideo,
        type: type,
      );

      if (channel.isTemp) {
        channel.lastMessageCreateTime =
            StringExtensions.toFormattedString(temporaryMessage.createTime);
        channel.lastMessageContent = temporaryMessage.content;
        await getIt<InsertChannelUseCase>()
            .execute(InsertChannelInput(channel: channel));
      }

      _addTemporaryMessage(temporaryMessage);
      _saveMessage(temporaryMessage);

      final videoInfo = await VideoCompressor.compressVideo(
        path: currentVideo.path,
      );

      final videoCompressedPath = videoInfo?.file?.path ?? currentVideo.path;

      if (currentVideo.path != videoCompressedPath) {
        currentVideo = currentVideo.copyWith(path: videoCompressedPath);
      }

      Uint8List? byteThumbnail =
          await VideoCompressor.getByteThumbnail(path: currentVideo.path);

      if (byteThumbnail == null) {
        throw Exception("Get video thumbnail not success");
      }

      await uploadVideoHandler.handleUpload(
        file: currentVideo,
        onSuccess: (UpFile file, String videoUrl) async {
          final fileRef = currentVideo.fileRef;
          await _onUploadSuccessSequential(
            index: index,
            videoUrl: videoUrl,
            file: file,
            temporaryMessage: temporaryMessage,
            fileRef: fileRef!,
            videoPath: currentVideo.path,
            duration: videoInfo?.duration ?? 0,
            byteThumbnail: byteThumbnail,
            onMessageIdAndIndexUpdated: (updatedMessageId, updatedIndex) {
              temporaryMessage.messageId = updatedMessageId;
              currentUploadIndex = updatedIndex;
            },
          );
          await uploadFileAtIndex(index + 1);
        },
        onError: _handleUploadError,
      );
    }

    await uploadFileAtIndex(currentUploadIndex);
  }

  /// Handles successful upload in sequential mode.
  Future<void> _onUploadSuccessSequential({
    required int index,
    required String videoUrl,
    required UpFile file,
    required Message temporaryMessage,
    required Function(String, int) onMessageIdAndIndexUpdated,
    required String fileRef,
    required String videoPath,
    required double duration,
    required Uint8List byteThumbnail,
  }) async {
    final fileName = 'thumbnail-${fileRef}.jpg';

    UploadFile fileThumbnail = UploadFile(
      path: '',
      name: fileName,
      size: byteThumbnail.length,
      fileData: byteThumbnail,
    );

    await uploadVideoHandler.handleUpload(
      file: fileThumbnail,
      onSuccess: (UpFile file, String thumbnailUrl) async {
        /// Handles successful upload thumbnail

        final sendMediaMessageUseCase =
            GetIt.instance.get<SendMediaMessageUseCase>();

        final fileToUpload = FileToUpload.fromUpFile(file);
        final mediaMetaData = MediaMetaDataToUpload();
        mediaMetaData.duration = duration;
        mediaMetaData.thumbnailUrl = thumbnailUrl;
        fileToUpload.mediaMetaData = mediaMetaData;
        fileToUpload.fileUrl = videoUrl;
        fileToUpload.fileRef = fileRef;
        final output = await sendMediaMessageUseCase.execute(
          SendMediaMessageInput(
            workspaceId: workspaceId,
            channelId: channelId,
            userId: userId,
            ref: temporaryMessage.ref,
            file: fileToUpload,
            attachmentType: _videoType,
          ),
        );

        if (output.message != null) {
          onMessageIdAndIndexUpdated(output.message!.messageId, index + 1);

          _updateMessage(output.message!);

          _saveMessage(output.message!);
        }
      },
      onError: _handleUploadError,
    );
  }

  /// Handles upload errors and throws appropriate exceptions.
  void _handleUploadError(UpFile file, ErrorCode errorCode, String message) {
    switch (errorCode) {
      case ErrorCode.noInternet:
        throw Exception("No internet while uploading file: $message");
      case ErrorCode.uploadError:
        throw Exception("Upload error while uploading file: $message");
      default:
        throw Exception("Unknown error while uploading file: $message");
    }
  }

  /// Creates a temporary photo message from a list of images.
  Message createTemporaryVideoMessage({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required UploadFile video,
    required V3AttachmentTypeEnum type,
  }) {
    final message = TempMessageFactory.createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      content: type == V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_VIDEO
          ? GlobalConfig.CONTENT_VIDEO
          : GlobalConfig.CONTENT_ZII_SHORT,
      messageViewType: type == V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_VIDEO
          ? MessageViewType.videoOwner
          : MessageViewType.ziiShortsOwner,
    );

    message.ref = RandomUtils.randomUlId();
    final fileRef = video.fileRef != null ? video.fileRef : UUIDUtils.random();

    if (video.fileRef != fileRef) {
      video = video.copyWith(fileRef: fileRef);
    }

    final attachment = Attachment(
      attachmentStatusRaw: AttachmentStatusEnum.UPLOADING.rawValue(),
    );
    attachment.ref = fileRef;
    if (type == V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_VIDEO) {
      attachment.video = MediaObject(
        attachmentId: fileRef,
        fileId: fileRef,
        fileRef: fileRef,
        attachmentType: AttachmentType.VIDEO.rawValue(),
        filePath: video.path,
        fileStatus: AttachmentStatusEnum.UPLOADING.rawValue(),
      );
    } else {
      attachment.videoMessage = MediaObject(
        attachmentId: fileRef,
        fileId: fileRef,
        fileRef: fileRef,
        attachmentType: AttachmentType.VIDEO_MESSAGE.rawValue(),
        filePath: video.path,
        fileStatus: AttachmentStatusEnum.UPLOADING.rawValue(),
      );
    }

    attachment.message.target = message;
    message.mediaAttachments.add(attachment);

    return message;
  }

  /// Adds a temporary message to the messages bloc.
  void _addTemporaryMessage(Message message) {
    if (_isMessagesBlocClosed()) return;
    messagesBloc?.add(MessagesEvent.addTempMessage(message));
  }

  /// Updates the message in the messages bloc.
  void _updateMessage(Message message) {
    if (_isMessagesBlocClosed()) return;
    messagesBloc?.add(MessagesEvent.updateMessage(message));
  }

  /// Saves the temporary message in the messages bloc.
  void _saveMessage(Message message) {
    if (_isMessagesBlocClosed()) return;
    messagesBloc?.add(MessagesEvent.saveTempMessage(message));
  }

  /// Checks if the messages bloc is closed.
  bool _isMessagesBlocClosed() {
    if (messagesBloc?.isClosed ?? true) {
      return true;
    }
    return false;
  }

  Future<Channel?> _getChannel() async {
    if (isDm()) {
      return (await getIt<GetOrCreateTempDMChannelUseCase>().execute(
        GetOrCreateTempDMChannelInput(userId: userId!),
      ))
          .channel;
    }
    return (await getIt<GetChannelUseCase>().execute(
      GetChannelInput(
        workspaceId: workspaceId,
        channelId: channelId,
      ),
    ))
        .channel;
  }
}
