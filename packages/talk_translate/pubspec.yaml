name: talk_translate
description: Ziichat talktranslate package .
version: 0.1.0+1
publish_to: none
environment:
  sdk: ^3.5.0
  flutter: ^3.24.0
dependencies:
  flutter:
    sdk: flutter
  bloc: ^9.0.0
  equatable: ^2.0.7
  flutter_bloc: ^9.1.0
  get_it: ^8.0.3
  injectable: ^2.5.0
  app_core:
    path: ../app_core
  shared:
    path: ../shared
  shared_preferences: ^2.5.3
  search_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.25
      path: apis/search_api
  suggestion_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/suggestion_api
  ziichat_ui:
    git:
      url: **************:ziichatlabs/ziichat-flutter-ui.git
      ref: release/0.56.0
  localization_client:
    git:
      url: **************:ziichatlabs/ziichat-flutter-i18n.git
      ref: main
  path_provider: ^2.1.5
  flutter_screenutil: ^5.9.3
  talk_and_translate:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.47.0
      path: packages/talk_and_translate/
dev_dependencies:
  flutter_test:
    sdk: flutter
  mocktail: ^1.0.4
  freezed: ^3.0.6
  injectable_generator: ^2.7.0
  very_good_analysis: ^7.0.0
  build_runner: ^2.4.15
dependency_overrides:
  analyzer: 7.3.0
