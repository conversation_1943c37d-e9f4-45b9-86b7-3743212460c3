import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';
import 'package:shared/shared.dart';

import '../../../../serializers/json_extensions.dart';
import 'chat_friend_data.dart';
import 'chat_presence_data.dart';
import 'chat_profile.dart';
import 'chat_user_status.dart';

part 'chat_user.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class ChatUser {
  ChatUser({
    required this.sessionKey,
    required this.userId,
    this.chatFriendDataRaw,
    this.username,
    this.createTime,
    this.updateTime,
    this.userType,
    this.userConnectLink,
    this.mediaPermissionSetting,
    this.presenceData,
    this.aliasName,
    this.profile,
    this.blocked,
    this.notificationStatus,
    this.partial = false,
  });

  ChatUser.mock()
      : id = 1,
        sessionKey = 'mockSessionKey',
        userId = 'mockUserId',
        chatFriendDataRaw = 'mockChatFriendDataRaw',
        username = 'mockUsername',
        createTime = 'mockCreateTime',
        updateTime = 'mockUpdateTime',
        userType = 0,
        userConnectLink = 'mockUserConnectLink',
        mediaPermissionSetting = 0,
        presenceData = null,
        aliasName = 'mockAliasName';

  ChatFriendData? get friendData => _parseChatFriend(chatFriendDataRaw);

  static ChatFriendData? _parseChatFriend(String? chatFriendDataRaw) {
    if (chatFriendDataRaw != null && chatFriendDataRaw.isNotEmpty) {
      final chatFriendData = jsonDecode(chatFriendDataRaw);
      return ChatFriendData.fromJson(chatFriendData);
    }
    return null;
  }

  @Id(assignable: true)
  @JsonKey(defaultValue: 0)
  int id = 0;

  @Index()
  @Property(uid: 10001)
  String sessionKey;

  @Property(uid: 10002)
  String userId;

  @Property(uid: 10003)
  String? username;

  @Property(uid: 10006)
  String? createTime;

  @Property(uid: 10007)
  String? updateTime;

  @Property(uid: 10008)
  int? userType;

  @Property(uid: 10010)
  String? userConnectLink;

  @Property(uid: 10011)
  int? mediaPermissionSetting;

  @Property(uid: 10012)
  String? get dbPresenceData {
    if (presenceData == null) {
      return null;
    }
    return jsonEncode(presenceData!.toJson());
  }

  set dbPresenceData(String? value) {
    if (value != null) {
      presenceData = ChatPresenceData.fromJson(jsonDecode(value));
    }
  }

  @Property(uid: 10013)
  String? aliasName;

  @Transient()
  ChatProfile? profile;

  @Transient()
  ChatPresenceData? presenceData;

  @Property(uid: 10014)
  @JsonKey(includeFromJson: false, includeToJson: false)
  String? get dbProfile {
    if (profile == null) {
      return null;
    }
    return jsonEncode(profile!.toJson());
  }

  set dbProfile(String? value) {
    if (value != null) {
      profile = ChatProfile.fromJson(jsonDecode(value));
    }
  }

  @Property(uid: 10015)
  @JsonKey(includeFromJson: false, includeToJson: false)
  String? chatFriendDataRaw;

  @Property(uid: 10017)
  bool? blocked;

  @Property(uid: 10018)
  bool? notificationStatus;

  @Property(uid: 10019)
  String? statusDataRaw;

  @Property(uid: 10020)
  bool? partial;

  @Transient()
  ChatUserStatus? get statusData {
    if (StringUtils.isNullOrEmpty(statusDataRaw)) {
      return null;
    }
    return ChatUserStatus.fromJson(jsonDecode(statusDataRaw!));
  }

  set statusData(ChatUserStatus? status) {
    if (status == null) {
      statusDataRaw = null;
    } else {
      statusDataRaw = jsonEncode(status.toJson());
    }
  }

  factory ChatUser.fromJson(Map<String, dynamic> json) {
    final chatUser = _$ChatUserFromJson(json);
    chatUser.chatFriendDataRaw = json.getEncoded('friendData');
    return chatUser;
  }

  Map<String, dynamic> toJson() => _$ChatUserToJson(this);

  String get dmChannelName {
    if (!StringUtils.isNullOrEmpty(aliasName)) {
      return aliasName!;
    }
    if (!StringUtils.isNullOrEmpty(profile?.displayName)) {
      return profile!.displayName!;
    }
    return username!;
  }

  ChatUser copyWith({
    int? id,
    String? sessionKey,
    String? userId,
    String? username,
    String? createTime,
    String? updateTime,
    int? userType,
    String? userConnectLink,
    int? mediaPermissionSetting,
    ChatPresenceData? presenceData,
    String? aliasName,
    ChatProfile? profile,
    bool? blocked,
    bool? notificationStatus,
    String? chatFriendDataRaw,
  }) {
    return ChatUser(
      sessionKey: sessionKey ?? this.sessionKey,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      userType: userType ?? this.userType,
      userConnectLink: userConnectLink ?? this.userConnectLink,
      mediaPermissionSetting:
          mediaPermissionSetting ?? this.mediaPermissionSetting,
      presenceData: presenceData ?? this.presenceData,
      aliasName: aliasName ?? this.aliasName,
      profile: profile ?? this.profile,
      blocked: blocked ?? this.blocked,
      notificationStatus: notificationStatus ?? this.notificationStatus,
      chatFriendDataRaw: chatFriendDataRaw ?? this.chatFriendDataRaw,
    )..id = id ?? this.id;
  }
}
