import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:app_core/core.dart' as core;
import 'package:cached_annotation/cached_annotation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:message_editor/message_editor.dart';
import 'package:path/path.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:shared/shared.dart';
import 'package:sticker/sticker.dart' hide Config;
import 'package:upload_manager/upload_manager.dart' hide Config;
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import '../../common/di/di.dart';
import '../../data/models/location.dart';
import '../../data/models/typing.dart';
import '../../domain/event_bus/message/edit_message_event.dart';
import '../../domain/usecase/member/get_member_use_case.dart';
import '../../utils/name_utils.dart';
import 'widgets/google_map_view.dart';
import 'widgets/ziivoice_page_editor.dart';

class EditorHandler implements ui.EditorInterface {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final MessageEditorManager _messageEditorManager = MessageEditorManager();

  final List<TypeSuggestion> _typeSuggestions = [
    TypeSuggestion(regex: RegExp(r'^#([a-zA-Z0-9]*)$'), symbol: '#'),
    TypeSuggestion(regex: RegExp(r'^@.*$'), symbol: '@'),
  ];

  List<ui.MentionItem> _allMentions;
  List<ui.MentionItem> _filteredMentions = [];
  bool _isToggleMentionPopup = false;
  String _key = '';
  String textContent = '';
  late String? _workspaceId;
  late String? _channelId;
  late MessagesBloc _messageBloc;
  late core.SendMessageHandler _sendMessageHandler;
  late VoidCallback? _onSendMessage;
  late VoidCallback? _goToTakePhoto;
  late void Function(
    List<ui.MediaItem> itemList,
  )? _onSelectedPhoto;
  late void Function(
    List<ui.MediaItem> itemList,
  )? _onSelectedFile;

  late final VoidCallback? _onTapZiishort;
  late final VoidCallback? _onTapAttachment;

  late final StreamSubscription _streamSubscription;

  late final StreamSubscription _quoteSubscription;

  late final StreamSubscription _deleteMessageSubscription;

  bool isBottomSheetDisplayed = false;
  bool isGalleryBottomSheetDisplayed = false;

  String content = '';

  ValueNotifier<bool> _isEditMessage = ValueNotifier(false);

  ValueNotifier<bool> _isShowQuoteMessageView = ValueNotifier(false);

  Message? quoteMessage;
  Message? editMessage;
  String name = '';
  bool isChannel = true;

  ChatUser? chatUser;

  Member? member;

  TypingHandler _typingHandler = GetIt.I.get<TypingHandler>();

  List<AssetPathEntity> albums = [];
  List<ui.AttachemtFolderItemData> attachmentFolderItems = [];

  bool _isPaused = false;

  final AppLocalizations appLocalizations =
      GetIt.instance.get<AppLocalizations>();

  void init({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required core.SendMessageHandler sendMessageHandler,
    VoidCallback? onSendMessage,
    VoidCallback? goToTakePhoto,
    void Function(
      List<ui.MediaItem> itemList,
    )? onSelectedPhoto,
    void Function(
      List<ui.MediaItem> itemList,
    )? onSelectedFile,
    VoidCallback? onTapZiishort,
    VoidCallback? onTapAttachment,
  }) {
    _workspaceId = workspaceId;
    _channelId = channelId;
    _sendMessageHandler = sendMessageHandler;
    _onSendMessage = onSendMessage;
    _goToTakePhoto = goToTakePhoto;
    _onSelectedPhoto = onSelectedPhoto;
    _onSelectedFile = onSelectedFile;
    _onTapZiishort = onTapZiishort;
    _onTapAttachment = onTapAttachment;

    _streamSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<EditMessageEvent>()
        .listen(_onReceivedFromEditEvent);
    _quoteSubscription = getIt
        .get<AppEventBus>()
        .on<QuoteMessageEvent>()
        .listen(_onReceivedFromQuoteEvent);
    _deleteMessageSubscription = getIt
        .get<AppEventBus>()
        .on<ClosePopupQuoteEvent>()
        .listen(_onClosePopupQuoteEvent);

    if (workspaceId != null && channelId != null) {
      _typingHandler.registerObserver(_workspaceId!, _channelId!);
    }

    _focusNode.addListener(_editorFocusListener);
  }

  void pause() {
    _isPaused = true;
  }

  void resume() {
    _isPaused = false;
  }

  void dispose() {
    _streamSubscription.cancel();
    _quoteSubscription.cancel();
    _deleteMessageSubscription.cancel();
    _focusNode.removeListener(_editorFocusListener);
  }

  set buildContext(BuildContext context) {
    _messageBloc = context.read<MessagesBloc>();
    _sendMessageHandler.messagesBloc = _messageBloc;
  }

  EditorHandler()
      : _allMentions = List.generate(7, (index) {
          if (index == 0) {
            return _buildEveryoneItem(index);
          } else {
            final randomName = _generateRandomName(7);
            final randomUsername = _generateRandomUsername();
            return ui.MentionItem(
              id: index.toString(),
              userName: randomUsername,
              name: randomName,
              url: 'https://picsum.photos/150/100',
            );
          }
        }) {
    _controller.addListener(_onTextChanged);
  }

  static ui.MentionItem _buildEveryoneItem(int index) {
    final AppLocalizations appLocalizations =
        GetIt.instance.get<AppLocalizations>();
    final name = appLocalizations.everyone;
    final username = appLocalizations.all;
    return ui.MentionItem(
      id: index.toString(),
      userName: username,
      name: name,
    );
  }

  /// Sends a typing event with the given [typingStatus].
  void _sendTypingEvent(TypingStateEnum typingStatus) {
    if (_workspaceId == null || _channelId == null) return;
    _typingHandler.sendTypingEvent(
      _workspaceId!,
      _channelId!,
      typingStatus,
    );
  }

  /// Listens for editor focus changes.
  /// - If the editor loses focus, it sends a "cancel typing" event.
  void _editorFocusListener() {
    if (!_focusNode.hasFocus) {
      _sendTypingEvent(TypingStateEnum.cancel);
    }
  }

  void _onReceivedFromQuoteEvent(QuoteMessageEvent event) async {
    if (_isPaused) return;

    _isShowQuoteMessageView.value = false;
    _focusNode.requestFocus();
    final outputGetMessage =
        await getIt.get<GetMessageByUserIdUseCase>().execute(
              GetMessageByUserIdInput(
                workspaceId: event.workspaceId,
                channelId: event.channelId,
                messageId: event.messageId,
              ),
            );

    if (outputGetMessage.message != null) {
      final outputGetChatUser = await getIt
          .get<LoadChatUserUseCase>()
          .execute(LoadChatUserInput(userId: outputGetMessage.message!.userId));
      if (outputGetChatUser.user != null) {
        chatUser = outputGetChatUser.user;
        final privateDataOutput =
            await getIt.get<core.GetUserPrivateDataByListUserUseCase>().execute(
                  core.GetUserPrivateDataByListUserUseCaseInput(
                    listUserId: [chatUser!.userId],
                  ),
                );
        if (privateDataOutput.data != null &&
            privateDataOutput.data!.length > 0) {
          chatUser!..aliasName = privateDataOutput.data!.first.aliasName;
        }
      }
      final outputMember = await getIt.get<GetMemberUseCase>().execute(
            GetMemberInput(
              workspaceId: event.workspaceId,
              channelId: event.channelId,
              userId: outputGetMessage.message!.userId,
            ),
          );
      member = outputMember.member;
      quoteMessage = outputGetMessage.message!;
      _isShowQuoteMessageView.value = true;
      _isEditMessage.value = false;
    }
  }

  void _onReceivedFromEditEvent(event) async {
    if (_isPaused) return;
    final output =
        await GetIt.instance.get<GetMessageByUserIdUseCase>().execute(
              GetMessageByUserIdInput(
                workspaceId: event.workspaceId,
                channelId: event.channelId,
                messageId: event.messageId,
              ),
            );
    editMessage = output.message;
    _isEditMessage.value = true;
    _isShowQuoteMessageView.value = false;
    if (output.message != null) {
      _controller.text = output.message!.content!;
    }
  }

  void _onClosePopupQuoteEvent(ClosePopupQuoteEvent event) {
    if (_isPaused) return;
    if (quoteMessage == null) return;
    if (!event.messageIds.contains(quoteMessage!.messageId)) return;
    _isShowQuoteMessageView.value = false;
  }

  void _onTextChanged() {
    // Handle typing event
    _sendTypingEvent(TypingStateEnum.typing);

    textContent = _controller.text;

    final cursorPosition = _controller.selection.baseOffset;
    if (isChannel == false) {
      _isToggleMentionPopup = false;
      _filteredMentions = [];
      return;
    }

    if (_controller.text.isEmpty || cursorPosition == 0) {
      _isToggleMentionPopup = false;
      _filteredMentions = [];
    } else {
      _messageEditorManager.onTypeSuggestion(
        _typeSuggestions,
        (key, value) {
          _key = key;
          if (key == '@') {
            handleFilteredMention(key, value);
          }
        },
      );
    }
  }

  void handleFilteredMention(String key, String value) {
    _isToggleMentionPopup = key.isNotEmpty;
    if (value.isEmpty) {
      _filteredMentions = _allMentions;
    } else {
      _filteredMentions = _allMentions.where((mentionItem) {
        final displayName = mentionItem.displayName?.toLowerCase() ?? '';
        final name = mentionItem.name?.toLowerCase() ?? '';
        final username = mentionItem.userName.toLowerCase();
        final nickname = mentionItem.nickName?.toLowerCase() ?? '';
        final lowerCaseMention = value.toLowerCase();

        return displayName.contains(lowerCaseMention) ||
            name.contains(lowerCaseMention) ||
            nickname.contains(lowerCaseMention) ||
            username.contains(lowerCaseMention);
      }).toList();
    }
  }

  static String _generateRandomName(int length) {
    const letters = 'abcdefghijklmnopqrstuvwxyz';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => letters.codeUnitAt(random.nextInt(letters.length)),
      ),
    );
  }

  static String _generateRandomUsername() {
    const lettersAndNumbers = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
        random.nextInt(8) + 5,
        (_) => lettersAndNumbers.codeUnitAt(
          random.nextInt(lettersAndNumbers.length),
        ),
      ),
    );
  }

  @override
  Widget typeSuggestionWidget(BuildContext context) {
    switch (_key) {
      case '@':
        return buildMembersSuggestionWidget();
      default:
        return const SizedBox.shrink();
    }
  }

  @override
  List<Widget> emptyViewsWidget(BuildContext context) {
    return [
      _messageEditorManager.isKeyboardClosed &&
              _messageEditorManager.isEmptyViewVisible
          ? buildStickerPage(context)
          : SizedBox.shrink(),
      _messageEditorManager.isKeyboardClosed &&
              _messageEditorManager.isEmptyViewVisible
          ? buildZiiVoicePage(context)
          : SizedBox.shrink(),
    ];
  }

  @override
  void onSendButtonTap(BuildContext context, String content) async {
    // Handle cancel typing event
    _sendTypingEvent(TypingStateEnum.cancel);

    var text = _handleMentionAllContent(content);

    if (_isEditMessage.value) {
      _isEditMessage.value = false;
      if (content.trim() != editMessage?.content) {
        _sendMessageHandler.editMessage(text, editMessage!);
      }
    } else if (_isShowQuoteMessageView.value) {
      _isShowQuoteMessageView.value = false;
      await _sendMessageHandler.onQuoteMessage(text, quoteMessage!);
    } else {
      _sendMessageHandler.sendTextMessage(content);
      _onSendMessage?.call();
    }
  }

  String _handleMentionAllContent(String content) {
    if (content.contains('@${appLocalizations.all}')) {
      return content.replaceAll('@${appLocalizations.all}', '@all');
    }
    return content;
  }

  @override
  Future<void> onAttachmentButtonTap(BuildContext context) async {
    final allowAccessGallery =
        await PermissionUtils.isAccessImageAndVideoPermission();

    final allowAccessGalleryNotifier = ValueNotifier(allowAccessGallery);
    if (isBottomSheetDisplayed) return;

    final allowLimitedAccess =
        await PermissionUtils.isLimitedAccessImageAndVideoPermission();
    ValueNotifier<bool> isShowChangePermission =
        ValueNotifier(allowLimitedAccess);

    isBottomSheetDisplayed = true;

    ValueNotifier<List<ui.MediaItem>> galleryPathsNotifier =
        ValueNotifier<List<ui.MediaItem>>([]);
    ValueNotifier<bool> allowAccessLocation = ValueNotifier(false);
    ValueNotifier<bool> noConnect = ValueNotifier(false);
    ValueNotifier<bool> isSkeleton = ValueNotifier(true);
    ValueNotifier<bool> isDisableChangeFolderButton = ValueNotifier(false);

    if (!allowAccessGalleryNotifier.value) {
      isDisableChangeFolderButton.value = true;
    }
    final Map<String, int> filePathToSize = {};

    LoadState loadState = LoadState();
    ValueNotifier<Location> _objectLocation =
        ValueNotifier<Location>(Location(address: '', error: ''));
    ValueNotifier<Position?> position = ValueNotifier(null);

    bool permissionEnable =
        await PermissionUtils.isEnableLocation(hasRequestPermission: false);

    allowAccessLocation.value = permissionEnable;

    if (permissionEnable) {
      PermissionUtils.streamGetPosition(position);
    }

    noConnect.value = getIt<core.NetworkManager>().noConnection();
    if (noConnect.value == true) {
      _objectLocation.value = Location(
        address: '',
        error: getIt<AppLocalizations>().noConnectionPleaseRetry,
      );
    }
    Completer<GoogleMapController>? _controller =
        Completer<GoogleMapController>();
    var googleMap = GoogleMapsView(
      objectLocation: _objectLocation,
      allowAccessLocation: allowAccessLocation,
      position: position,
      noConnect: noConnect,
      isSkeleton: isSkeleton,
      googleMapController: _controller,
    );

    if (allowAccessGalleryNotifier.value) {
      await _getListFolderItem();
    }

    ui.BottomSheetUtil.showAttachmentBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      onPressedCancel: () {
        PermissionUtils.close();
        Navigator.of(context).pop();
        isBottomSheetDisplayed = false;
      },
      isShowChangePermission: isShowChangePermission,
      allowAccessGalleryNotifier: allowAccessGalleryNotifier,
      onClickGoToSettingGallery: () => {},
      onClickOpenCamera: () => {_onClickOpenCamera(context)},
      galleryPathsNotifier: galleryPathsNotifier,
      onSendGalleryFiles: _onSendGalleryFiles,
      allowAccessLocation: allowAccessLocation,
      onClickGoToSettingLocation: () => _onClickGoToSettingLocation(context),
      mapWidget: googleMap,
      detectLocation: ValueListenableBuilder(
        valueListenable: _objectLocation,
        builder: (context, object, child) {
          return ui.CurrentLocation(
            defaultLocation:
                object.error.isNotEmpty ? object.error : object.address,
            parentContext: context,
            onClickSendLocation: () {
              if (position.value == null || object.error.isNotEmpty) {
                return;
              } else {
                ui.DialogUtils.showShareLocationTo(
                  context,
                  username: name,
                  location: object.address,
                  onCancelClicked: (context) {
                    Navigator.pop(context);
                  },
                  onShareClicked: (context) {
                    onClickSendLocation(object.address, position.value!);
                  },
                );
              }
            },
            noConnection: noConnect,
            isSkeleton: isSkeleton,
          );
        },
      ),
      onSelectFromGallery: () => _onSelectFromGallery(
        context: context,
        loadState: loadState,
        allowAccessGalleryNotifier: allowAccessGalleryNotifier,
        isShowChangePermission: isShowChangePermission,
        isDisableChangeFolderButton: isDisableChangeFolderButton,
        galleryPathsNotifier: galleryPathsNotifier,
        filePathToSize: filePathToSize,
      ),
      onSelectFromFile: () => _onSelectFromFile(context),
      fileItems: [],
      onSendFile: onSendFile,
      onClose: () => isBottomSheetDisplayed = false,
      onLoadMore: (ScrollNotification scroll) {
        if (scroll.metrics.pixels / scroll.metrics.maxScrollExtent > 0.4) {
          loadMoreItems(loadState, galleryPathsNotifier, filePathToSize,
              isLimitedAccess: allowLimitedAccess);
        }
      },
      onToggleItem: (ui.MediaItem item) => _onToggleItemFile(
        item,
        filePathToSize,
        galleryPathsNotifier,
        context,
      ),
      onClickGrantPermissionsGallery: () async {
        allowAccessGalleryNotifier.value =
            await hasGalleryAccessPermissions(context);
        isShowChangePermission.value =
            await PermissionUtils.isLimitedAccessImageAndVideoPermission();
        if (allowAccessGalleryNotifier.value) {
          await loadAlbums();
          await _getListFolderItem();
          await loadMoreItems(loadState, galleryPathsNotifier, filePathToSize,
              isLimitedAccess: allowLimitedAccess);
          isDisableChangeFolderButton.value = false;
        }
      },
      onClickGrantPermissionsLocation: () {
        _onClickGoToSettingLocation(context);
      },
      onMaxHeight: () async {
        if (allowAccessGalleryNotifier.value)
          await loadMoreItems(loadState, galleryPathsNotifier, filePathToSize,
              isLimitedAccess: allowLimitedAccess);
      },
      onTapFolder: (ui.AttachemtFolderItemData attachmentFolderItemDatas) {
        changeFolder(
          loadState: loadState,
          galleryPathsNotifier: galleryPathsNotifier,
          folderItem: attachmentFolderItemDatas,
          filePathToSize: filePathToSize,
        );
      },
      attachmentFolderItemDatas: attachmentFolderItems,
      isDisableChangeFolderButton: isDisableChangeFolderButton,
      isChangeFolderEnable: true,
      onChangePermission: () async {
        await _onChangePermission(
          isDisableChangeFolderButton,
          isShowChangePermission,
          context,
          loadState,
          galleryPathsNotifier,
          filePathToSize,
        );
      },
    );

    isBottomSheetDisplayed = false;
  }

  Future<void> _onChangePermission(
    ValueNotifier<bool> isDisableChangeFolderButton,
    ValueNotifier<bool> isShowChangePermission,
    BuildContext context,
    LoadState loadState,
    ValueNotifier<List<ui.MediaItem>> galleryPathsNotifier,
    Map<String, int> filePathToSize,
  ) async {
    isDisableChangeFolderButton.value = true;
    isShowChangePermission.value =
        await PermissionUtils.isLimitedAccessImageAndVideoPermission();

    ui.ActionSheetUtil.showChangePermissionActionSheet(
      context,
      onClickSelectMore: () async {
        Navigator.of(context).pop();
        await PhotoManager.presentLimited();

        await loadAlbums();
        await _getListFolderItem();
        await loadMoreItems(
          loadState,
          galleryPathsNotifier,
          filePathToSize,
          isLimitedAccess: true,
        );
        isDisableChangeFolderButton.value = false;
      },
      onClickGoToSettings: () {
        Navigator.of(context).pop();
        isBottomSheetDisplayed = false;
        PermissionUtils.goToSettings();
        isDisableChangeFolderButton.value = false;
      },
      onClickCancel: () {
        Navigator.of(context).pop();
        isDisableChangeFolderButton.value = false;
      },
    );
  }

  @override
  void onZiiShortButtonTap(BuildContext context) {
    FocusScope.of(context).requestFocus(FocusNode());

    if (!_canUseCameraOrMicrophone(context)) return;

    _onTapZiishort?.call();
  }

  @override
  void onZiiVoiceButtonTap(
    BuildContext context,
    VoidCallback onShowZiiVoicePage,
  ) async {
    final isGranted =
        await PermissionUtils.requestMicrophonePermission(context);
    if (isGranted) {
      onShowZiiVoicePage();
    }
  }

  @override
  void onStickerButtonTap(BuildContext context) {
    context
        .read<StickerBloc>()
        .add(LoadRecentStickersEvent(mustUpdateUi: true));
  }

  @override
  void onHeartButtonTap(BuildContext context) async {
     _handleSendTestMessage();
    // _handleSendSticker(Sticker.quickSticker());
  }

  void _handleSendTestMessage() async {
    for (int i = 1; i <= 5; i++) {
      _sendMessageHandler.sendTextMessage('Test message $i');
      // await Future.delayed(Duration(milliseconds: 300));
    }
  }

  @override
  bool isShowTypeSuggestionView() {
    return _key == '@' && _isToggleMentionPopup;
  }

  @override
  int mentionsLength() {
    return _filteredMentions.length;
  }

  @override
  TextEditingController controller() {
    return _controller;
  }

  @override
  FocusNode focusNode() {
    return _focusNode;
  }

  Widget buildStickerPage(BuildContext context) {
    return StickerKeyboard(
      onLongPressSticker: (sticker, collectionId) async {
        showPreviewSticker(sticker, collectionId, context);
      },
      onTapSticker: _handleSendSticker,
    );
  }

  Future<void> showPreviewSticker(
    Sticker sticker,
    String? collectionId,
    BuildContext context,
  ) async {
    final lottieUrl = UrlUtils.parseSticker(sticker.stickerUrl);
    getIt<StickerBloc>()
        .add(ChangeStickerOnPreviewEvent(stickerOnPreview: lottieUrl));
    await Navigator.of(context).push(
      HeroDialogRoute(
        builder: (BuildContext context) {
          return ui.StickerPreviewDialog(
            mainContext: context,
            defaultEmoji: sticker.defaultEmoji,
            sticker: StickerWidget(
              collectionId: collectionId,
              lottieUrl: lottieUrl,
              stickerId: sticker.stickerId,
              placeholder: StickerPlaceholder(stickerUrl: sticker.stickerUrl),
              size: StickerSize.x512,
              initAnimation: false,
            ),
            onSent: () {
              Navigator.of(context).pop();
              _handleSendSticker(sticker);
              context
                  .read<StickerBloc>()
                  .add(AddRecentStickerEvent(sticker: sticker));
            },
          );
        },
      ),
    );

    getIt<StickerBloc>()
        .add(ChangeStickerOnPreviewEvent(stickerOnPreview: null));
    _focusNode.unfocus();
  }

  Widget buildZiiVoicePage(BuildContext context) {
    final myAppLocale = Locale(appLocalizations.localeName);
    final Locale locale = Locale('en');
    return ZiiVoicePage(
      applocalizations: appLocalizations,
      locale: locale,
      appLocale: myAppLocale,
      onChangeLanguage: () {},
      onChangedSwitch: (bool) {},
      switchValue: true,
      onSendZiiVoice: (String audioRecordedPath) {
        _handleSendZiiVoice(audioRecordedPath);
      },
      canUseCameraOrMicrophone: _canUseCameraOrMicrophone,
    );
  }

  Widget buildMembersSuggestionWidget() {
    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: _filteredMentions.length,
      itemBuilder: (context, index) {
        final mention = _filteredMentions[index];
        return ui.MentionCardWidget(
          context: context,
          mention: mention,
          onMentionCardTap: () {
            onMentionCardTap(mention.userName, index);
          },
          index: index,
        );
      },
    );
  }

  void onMentionCardTap(String username, int index) {
    final text = _controller.text;
    final cursorPosition = _controller.selection.baseOffset;

    int startIndex = text.lastIndexOf('@', cursorPosition);

    if (startIndex > 0 && text[startIndex - 1] == '@') {
      startIndex = text.lastIndexOf('@', startIndex - 1);
    }

    if (startIndex != -1) {
      final selection = username == '@${appLocalizations.all}'
          ? username.substring(1)
          : username;
      final replacement = '@$selection ';

      _messageEditorManager.updateTextField(
        startIndex,
        cursorPosition,
        replacement,
        selection,
      );
    }
  }

  void _handleSendSticker(Sticker sticker) {
    _sendMessageHandler.sendStickerMessage(sticker);
    _onSendMessage?.call();
  }

  void setMentions(
    Map<String, ChatUser> users,
    Map<String, Member> members,
  ) async {
    final meId = Config.getInstance().activeSessionKey ?? '';
    List<ui.MentionItem> mentionItems = [];
    final me = members[meId];

    users.forEach((userId, user) {
      if (user.username != GlobalConfig.ghost) {
        final member = members[userId];
        final mentionItem = ui.MentionItem(
          id: userId,
          userName: user.username!,
          displayName: NameUtils.parseDisplayNameOfUser(user),
          nickName: NameUtils.parseNickNameOfUser(member),
          name: NameUtils.parseName(user, member),
          url: UrlUtils.parseAvatar(user.profile?.originalAvatar),
        );
        mentionItems.add(mentionItem);
      }
    });
    _allMentions.clear();

    mentionItems.sort((before, after) {
      if (before.name == null || after.name == null) return -1;
      return before.name!.compareTo(after.name!);
    });
    if (me?.role == 'owner' || me?.role == 'admin') {
      mentionItems.insert(0, _buildEveryoneItem(0));
    }
    _allMentions.addAll(mentionItems);
  }

  Future<bool> hasGalleryAccessPermissions(
    BuildContext context,
  ) async {
    return await PermissionUtils.requestImageAndVideoPermission(context);
  }

  Future<void> _onClickGoToSettingLocation(BuildContext context) async {
    var open = await PermissionUtils.openEnableLocation();
    if (!open) {
      ui.DialogUtils.showEnableLocation(
        context,
        onOkClicked: (BuildContext dialogContext) {
          AppEventBus.publish(PopToChannelViewEvent());
        },
        barrierDismissible: false,
      );
    } else {
      AppEventBus.publish(PopToChannelViewEvent());
    }
  }

  Future<void> _onClickOpenCamera(context) async {
    FocusScope.of(context).requestFocus(FocusNode());

    if (!_canUseCameraOrMicrophone(context)) return;

    final isGranted = await PermissionUtils.requestCameraPermission(context);
    bool canOpenCamera = true;

    if (!canOpenCamera) return;
    if (isGranted) {
      _goToTakePhoto?.call();
    }
  }

  void _onSendGalleryFiles(
    BuildContext buildContext,
    List<ui.MediaItem> itemList,
  ) {
    Navigator.of(buildContext).pop();
    _onSelectedPhoto?.call(itemList);
  }

  Future<void> _onSendFileGalleryFiles(
    BuildContext buildContext,
    List<ui.MediaItem> itemList,
  ) async {
    await Navigator.of(buildContext).maybePop();
    Navigator.of(buildContext).pop();
    _onSelectedFile?.call(itemList);
  }

  Future<void> _onSelectFromGallery({
    required BuildContext context,
    required ValueNotifier<bool> allowAccessGalleryNotifier,
    required ValueNotifier<bool> isShowChangePermission,
    required ValueNotifier<bool> isDisableChangeFolderButton,
    required ValueNotifier<List<ui.MediaItem>> galleryPathsNotifier,
    required LoadState loadState,
    required Map<String, int> filePathToSize,
  }) async {
    if (isBottomSheetDisplayed) return;

    isBottomSheetDisplayed = true;

    ui.BottomSheetUtil.showGalleryBottomSheet(
      context: context,
      onPressedCancel: () {
        Navigator.of(context).maybePop();
        isBottomSheetDisplayed = false;
      },
      onChangeFolderGallery: () {},
      allowAccessGalleryNotifier: allowAccessGalleryNotifier,
      onClickGoToSettingGallery: () => {},
      galleryPathsNotifier: galleryPathsNotifier,
      onSendGalleryFiles: _onSendFileGalleryFiles,
      onSendFile: onSendFile,
      onClose: () => isBottomSheetDisplayed = false,
      onLoadMore: (ScrollNotification scroll) {
        if (scroll.metrics.pixels / scroll.metrics.maxScrollExtent > 0.4) {
          loadMoreItems(
            loadState,
            galleryPathsNotifier,
            filePathToSize,
            isLimitedAccess: isShowChangePermission.value,
          );
        }
      },
      onToggleItem: (ui.MediaItem item) => _onToggleItemFile(
        item,
        filePathToSize,
        galleryPathsNotifier,
        context,
      ),
      onClickGrantPermissionsGallery: () async {
        allowAccessGalleryNotifier.value =
            await hasGalleryAccessPermissions(context);
        isShowChangePermission.value =
            await PermissionUtils.isLimitedAccessImageAndVideoPermission();
        if (allowAccessGalleryNotifier.value) {
          await loadAlbums();
          await _getListFolderItem();
          await loadMoreItems(
            loadState,
            galleryPathsNotifier,
            filePathToSize,
            isLimitedAccess: allowAccessGalleryNotifier.value,
          );
          isDisableChangeFolderButton.value = false;
        }
      },
      onMaxHeight: () async {
        if (allowAccessGalleryNotifier.value)
          await loadMoreItems(
            loadState,
            galleryPathsNotifier,
            filePathToSize,
            isLimitedAccess: isShowChangePermission.value,
          );
      },
      onTapFolder: (ui.AttachemtFolderItemData attachmentFolderItemDatas) {
        changeFolder(
          loadState: loadState,
          galleryPathsNotifier: galleryPathsNotifier,
          folderItem: attachmentFolderItemDatas,
          filePathToSize: filePathToSize,
        );
      },
      attachmentFolderItemDatas: attachmentFolderItems,
      isShowChangePermission: isShowChangePermission,
      isDisableChangeFolderButton: isDisableChangeFolderButton,
      onChangePermission: () async {
        await _onChangePermission(
          isDisableChangeFolderButton,
          isShowChangePermission,
          context,
          loadState,
          galleryPathsNotifier,
          filePathToSize,
        );
      },
    );

    isBottomSheetDisplayed = false;
  }

  Future<void> loadMoreItems(
    LoadState state,
    ValueNotifier<List<ui.MediaItem>> galleryPathsNotifier,
    Map<String, int> filePathToSize, {
    bool isLimitedAccess = false,
  }) async {
    if (state.isLoading || state.isComplete) return;

    // Clear the list when first time loadMore or select more limit acess
    if (state.currentPage == 0 || isLimitedAccess)
      galleryPathsNotifier.value.clear();

    state.isLoading = true;

    List<ui.MediaItem> batch = await _getMediaItemList(
      page: isLimitedAccess ? 0 : state.currentPage,
      pageSize: state.pageSize,
      album: albums[state.currentAlbumIndex],
    );
    if (batch.isNotEmpty) {
      galleryPathsNotifier.value =
          List<ui.MediaItem>.from(galleryPathsNotifier.value)..addAll(batch);

      filePathToSize.addEntries(
        await Future.wait(
          batch.map((item) async {
            final file = File(item.path);
            final fileSize = file.lengthSync();
            return MapEntry(item.path, fileSize);
          }),
        ),
      );

      state.currentPage++;
    } else {
      state.isComplete = true;
    }

    state.isLoading = false;
  }

  void _onToggleItemFile(
    ui.MediaItem item,
    Map<String, int> filePathToSize,
    ValueNotifier<List<ui.MediaItem>> galleryPathsNotifier,
    BuildContext context,
  ) {
    var getMineType = FileUtils.getMimeType(item.path) ?? '';
    if (item.duration == null &&
        !FileUtils.isValidImageType(
          getMineType,
        )) {
      ui.DialogUtils.showUnsupportedImageFileDialog(
        context,
        onFirstAction: (context) => Navigator.of(context).maybePop(),
      );
      return;
    }
    if (item.duration != null &&
        !FileUtils.isValidVideoType(
          getMineType,
        )) {
      ui.DialogUtils.showUnsupportedVideoFileDialog(
        context,
        onFirstAction: (context) => Navigator.of(context).maybePop(),
      );
      return;
    }

    final int? fileSize = filePathToSize[item.path];
    final items = galleryPathsNotifier.value;

    final numSelected = items.where((i) => i.selectedIndex > 0).length;

    if (fileSize != null &&
        fileSize.toMB() < FileUploadConfig.maximumFileSizeMB &&
        isItemInGalleryPaths(item, galleryPathsNotifier)) {
      if (item.selectedIndex == 0) {
        if (numSelected >= FileUploadConfig.maximumFileUpload) {
          return;
        }
        final updatedList = items.map((i) {
          if (i == item) {
            return i.copyWith(selectedIndex: numSelected + 1);
          }
          return i;
        }).toList();
        galleryPathsNotifier.value = updatedList;
      } else {
        final updatedList = items.map((i) {
          if (i == item) {
            return i.copyWith(selectedIndex: 0);
          } else if (i.selectedIndex > item.selectedIndex) {
            return i.copyWith(selectedIndex: i.selectedIndex - 1);
          }
          return i;
        }).toList();

        galleryPathsNotifier.value = updatedList;
      }
    } else {
      ui.DialogUtils.showMaximunSizeVideoDialog(
        context,
        onFirstAction: (context) => Navigator.of(context).maybePop(),
      );
    }
  }

  bool isItemInGalleryPaths(
    ui.MediaItem item,
    ValueNotifier<List<ui.MediaItem>> galleryPathsNotifier,
  ) {
    return galleryPathsNotifier.value
        .any((galleryItem) => galleryItem.path == item.path);
  }

  Future<void> _onSelectFromFile(BuildContext context) async {
    Navigator.of(context).maybePop();
    FocusScope.of(context).requestFocus(FocusNode());

    final filePickResult = await FilePicker.platform.pickFiles(
      allowMultiple: false,
      type: FileType.any,
    );

    if (filePickResult == null) {
      return;
    }

    try {
      final selectedFile = filePickResult.xFiles.first;
      final fileSize = await selectedFile.length();
      if (fileSize.toMB() > FileUploadConfig.maximumFileSizeMB) {
        ui.DialogUtils.showExceedSizeDialog(
          context,
          onFirstAction: (context) => Navigator.of(context).maybePop(),
        );
        return;
      }

      UploadFile uploadFile = UploadFile(
        fileRef: UUIDUtils.random(),
        path: selectedFile.path,
        name: selectedFile.name,
        size: await selectedFile.length(),
      );

      _sendMessageHandler.sendFileMessage(uploadFile);
      _onSendMessage?.call();
    } catch (e) {
      throw Exception("Failed to process selected file: $e");
    }
  }

  void onSendFile(BuildContext p1, ui.FileItem fileItem) {}

  Future<List<ui.MediaItem>> _getMediaItemList({
    required int page,
    required int pageSize,
    required AssetPathEntity album,
  }) async {
    int count = await album.assetCountAsync;
    if (count == 0) {
      return [];
    }

    List<AssetEntity> media =
        await album.getAssetListPaged(page: page, size: pageSize);
    List<Future<ui.MediaItem?>> futureItems =
        media.map(_convertAssetToMediaItem).toList();
    List<ui.MediaItem?> results = await Future.wait(futureItems);

    return results.whereType<ui.MediaItem>().toList();
  }

  Future<void> loadAlbums() async {
    albums = await PhotoManager.getAssetPathList(
      type: RequestType.image | RequestType.video,
    );
  }

  Future<void> changeFolder({
    required LoadState loadState,
    required ui.AttachemtFolderItemData folderItem,
    required ValueNotifier<List<ui.MediaItem>> galleryPathsNotifier,
    required Map<String, int> filePathToSize,
  }) async {
    final folderIndex =
        albums.indexWhere((album) => album.id == folderItem.folderId);

    const firstPage = 0;

    if (folderIndex < 0 || folderIndex >= albums.length) {
      debugPrint("Folder not found or index out of range");
      return;
    }

    loadState.currentAlbumIndex = folderIndex;

    // Reset state when change folder
    loadState.currentPage = 0;
    loadState.isComplete = false;
    loadState.isLoading = false;

    AssetPathEntity selectedAlbum = albums[folderIndex];

    List<ui.MediaItem> updatedAssets = await _getMediaItemList(
      page: firstPage,
      pageSize: loadState.pageSize,
      album: selectedAlbum,
    );

    filePathToSize.addEntries(
      await Future.wait(
        updatedAssets.map((item) async {
          final file = File(item.path);
          final fileSize = file.lengthSync();
          return MapEntry(item.path, fileSize);
        }),
      ),
    );

    galleryPathsNotifier.value = updatedAssets;

    loadState.currentPage = 1;
  }

  Future<List<ui.AttachemtFolderItemData>> _getListFolderItem() async {
    attachmentFolderItems.clear();

    await loadAlbums();

    if (albums.isEmpty) {
      return [];
    }
    for (var album in albums) {
      int count = await album.assetCountAsync;

      if (count == 0) continue;

      List<AssetEntity> media = await album.getAssetListPaged(page: 0, size: 1);

      ui.MediaItem? mediaItem =
          media.isNotEmpty ? await _convertAssetToMediaItem(media.first) : null;

      attachmentFolderItems.add(
        ui.AttachemtFolderItemData(
          folderId: album.id,
          titleAttachment: album.name,
          numberAttachment: count.toString(),
          mediaItem: mediaItem,
        ),
      );
    }
    return attachmentFolderItems;
  }

  Future<ui.MediaItem?> _convertAssetToMediaItem(AssetEntity asset) async {
    final File? file = await asset.originFile;

    if (file == null) return null;
    final duration = Duration(seconds: asset.duration);
    final fileSieMB = (await file.length()).toMB();

    final ThumbnailOption option = ThumbnailOption.ios(
      size: ThumbnailSize.square(300),
      format: ThumbnailFormat.jpeg,
      quality: 100,
      deliveryMode: DeliveryMode.highQualityFormat,
      resizeMode: ResizeMode.exact,
      resizeContentMode: ResizeContentMode.fill,
    );

    final thumbnail = asset.thumbnailDataWithOption(option);
    if (asset.type == AssetType.video) {
      return ui.VideoItem(
        path: file.path,
        duration: '${TimeUtils.formatDurationToMMSS(duration)}',
        thumbnail: thumbnail,
        isDisable: fileSieMB > FileUploadConfig.maximumFileSizeMB,
      );
    } else if (asset.type == AssetType.image) {
      return ui.PhotoItem(
        path: file.path,
        thumbnail: thumbnail,
        isDisable: fileSieMB > FileUploadConfig.maximumFileSizeMB,
      );
    }
    return null;
  }

  Future<void> _handleSendZiiVoice(String audioPath) async {
    if (audioPath.isEmpty) return;

    File audioFile = File(audioPath);

    UploadFile voiceUploadFile = UploadFile(
      fileRef: UUIDUtils.random(),
      path: audioFile.path,
      name: basename(audioFile.path),
      size: await audioFile.lengthSync(),
    );

    _sendMessageHandler.sendVoiceMessage(voiceUploadFile);
    _onSendMessage?.call();
  }

  @override
  Widget editMessageWidget(context) {
    return ui.EditMessageContainerWidget(
      editContent: editMessage!.content!,
      onCancel: () {
        _isEditMessage.value = false;
      },
      mentions: editMessage?.mentions ?? [],
    );
  }

  @override
  ValueNotifier<bool> isCloseEditMessageWidget() {
    return _isEditMessage;
  }

  @override
  ValueNotifier<bool> isShowQuoteMessageView() {
    return _isShowQuoteMessageView;
  }

  @override
  Widget quoteMessageView(BuildContext context) {
    return ui.QuoteMessageContainerWidget(
      quoteContent: TranslateContentUtils.translateContent(
        quoteMessage!.content!,
        quoteMessage!.contentArguments ?? [],
      ),
      onCancel: () {
        _isShowQuoteMessageView.value = false;
      },
      originalUsername: (chatUser?.aliasName?.isNotEmpty == true
              ? chatUser?.aliasName
              : member?.nickname?.isNotEmpty == true
                  ? member?.nickname
                  : (chatUser?.profile?.displayName?.isNotEmpty == true
                      ? chatUser?.profile?.displayName
                      : chatUser?.username)) ??
          '',
      originalAvatarUrl: UrlUtils.parseAvatar(chatUser!.profile?.avatar),
      mentions: quoteMessage?.mentions ?? [],
    );
  }

  void onClickSendLocation(String address, Position position) async {
    _sendMessageHandler.sendLocationMessage(
      address: address,
      longitude: position.longitude,
      latitude: position.latitude,
    );
    AppEventBus.publish(PopToChannelViewEvent());
  }

  void setName(String name) {
    this.name = name;
  }

  bool _canUseCameraOrMicrophone(BuildContext context) {
    // Check if the user is in a meeting room
    return !getIt<core.MeetingHandler>().hasJoinedMeetingRoom(context: context);
  }
}

class LoadState {
  bool isLoading;
  bool isComplete;
  int currentPage;
  int pageSize;
  int currentAlbumIndex;

  LoadState({
    this.isLoading = false,
    this.isComplete = false,
    this.currentPage = 0,
    this.pageSize = 18,
    this.currentAlbumIndex = 0,
  });
}
