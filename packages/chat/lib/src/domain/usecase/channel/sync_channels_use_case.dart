import 'dart:convert';

import 'package:channel_view_api/channel_view_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/database/entities/channel.dart';
import '../../../data/repositories/source/api/client/clients.dart';
import '../../../data/repositories/source/api/models/channel_identification.dart';
import '../../../serializers/channel_serializer.dart';

@Injectable()
class SyncChannelsUseCase
    extends BaseFutureUseCase<SyncChannelsInput, SyncChannelsOutput> {
  SyncChannelsUseCase();

  @override
  Future<SyncChannelsOutput> buildUseCase(SyncChannelsInput input) async {
    final response = await ChannelViewClient().instance.syncAllChannels(
          updateTimeAfter: TimeUtils.formatToISO8601(input.updateTimeAfter),
        );

    // Channel new or updated
    final dataChannelsUpdated = response.data?.result?.data?.toList() ?? [];
    final includes = jsonDecode(
      standardSerializers.toJson(
        V3DataInclude.serializer,
        response.data!.result!.includes!,
      ),
    );

    List<Channel> channels = [];
    for (final item in dataChannelsUpdated) {
      final json = jsonDecode(
        standardSerializers.toJson(
          Sharedv3ChannelData.serializer,
          item,
        ),
      );

      final channel = ChannelSerializer.serializeFromJson(
        data: json['channel'],
        metadata: json['channelMetadata'],
        includes: includes,
      );
      channels.add(channel!);
    }

    // Channel deleted
    final dataChannelsDeleted =
        response.data?.result?.channelDeleted?.toList() ?? [];
    List<ChannelIdentification> channelsDeleted = [];
    for (final item in dataChannelsDeleted) {
      final json = jsonDecode(
        standardSerializers.toJson(
          SyncAllChannelsResponseChannelIdentification.serializer,
          item,
        ),
      );

      final channelIdentification = ChannelIdentification.fromJson(json);
      channelsDeleted.add(channelIdentification);
    }

    return SyncChannelsOutput(
      channels: channels,
      channelsDeleted: channelsDeleted,
    );
  }
}

class SyncChannelsInput extends BaseInput {
  SyncChannelsInput({
    required this.updateTimeAfter,
  });

  final DateTime updateTimeAfter;
}

class SyncChannelsOutput extends BaseOutput {
  SyncChannelsOutput({
    required this.channels,
    required this.channelsDeleted,
  });

  final List<Channel> channels;
  final List<ChannelIdentification> channelsDeleted;
}
