import 'package:json_annotation/json_annotation.dart';
part 'typing.g.dart';

@JsonSerializable()
class Typing {
  const Typing({
    required this.workspaceId,
    required this.channelId,
    required this.actorId,
    required this.username,
    required this.avatarPath,
    this.status = TypingStateEnum.typing,
  });

  final String workspaceId;
  final String channelId;
  final String actorId;
  final String username;
  final String avatarPath;
  @JsonKey(defaultValue: TypingStateEnum.typing)
  final TypingStateEnum status;

  factory Typing.fromJson(Map<String, dynamic> json) => _$TypingFromJson(json);

  Map<String, dynamic> toJson() => _$TypingToJson(this);
}

enum TypingStateEnum {
  @JsonValue('typing')
  typing,
  @JsonValue('cancel')
  cancel
}
