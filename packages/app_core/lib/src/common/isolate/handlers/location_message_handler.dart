import 'package:shared/shared.dart';

import '../../../data/source/api/client/isolate_api_client.dart';
import '../../network/retry_manager.dart';
import '../input/worker_send_location_input.dart';
import '../output/worker_send_message_output.dart';
import 'base_message_handler.dart';

/// Specialized handler for location messages
class LocationMessageHandler extends BaseMessageHandler<WorkerSendLocationInput> {
  LocationMessageHandler({
    required super.apiClient,
    required super.retryManager,
  });

  @override
  WorkerSendLocationInput parseInput(Map<String, dynamic> inputData) {
    return WorkerSendLocationInput.fromJson(inputData);
  }

  @override
  String getApiEndpoint(WorkerSendLocationInput input) {
    return '/Message/' + (input.isDm() ? 'SendDMLocation' : 'SendLocation');
  }

  @override
  Future<WorkerSendMessageOutput> executeMessageOperation(
    WorkerSendLocationInput input,
    DateTime createTime,
  ) async {
    return await makeRetryableApiCall(
      input: input,
      createTime: createTime,
      apiCall: () => apiClient.post(
        getApiEndpoint(input),
        body: input.toRequest(),
      ),
    );
  }

  /// Legacy method for backward compatibility
  Future<WorkerSendMessageOutput> handleLocationMessage(
    Map<String, dynamic> inputData,
  ) async {
    return await handleMessage(inputData);
  }
}
