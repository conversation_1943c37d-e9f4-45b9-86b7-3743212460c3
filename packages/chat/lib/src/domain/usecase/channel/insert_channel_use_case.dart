import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class InsertChannelUseCase
    extends BaseFutureUseCase<InsertChannelInput, InsertChannelOutput> {
  InsertChannelUseCase(
    this._channelRepository,
  );

  final ChannelRepository _channelRepository;

  @override
  Future<InsertChannelOutput> buildUseCase(InsertChannelInput input) async {
    _channelRepository.insert(input.channel);
    return InsertChannelOutput(ok: true);
  }
}

class InsertChannelInput extends BaseInput {
  InsertChannelInput({required this.channel});

  final Channel channel;
}

class InsertChannelOutput extends BaseOutput {
  InsertChannelOutput({required this.ok});

  final bool? ok;
}
