import 'dart:async';
import 'dart:io';

import 'package:chat/chat.dart';
import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:upload_manager/upload_manager.dart' hide Config;

import '../../common/di/di.dart';

class SendImageMessageHandler {
  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final MessagesBloc? messagesBloc;
  final int uploadConcurrency;

  SendImageMessageHandler({
    required this.workspaceId,
    required this.channelId,
    required this.userId,
    required this.messagesBloc,
    this.uploadConcurrency = 1,
  });

  bool isDm() => userId != null;

  /// Sends image messages, supporting sequential and parallel uploads.
  Future<void> sendImageMessage(List<UploadFile> imageList) async {
    if (imageList.isEmpty) return;

    final channel = await _getChannel();

    if (channel == null) return;

    final temporaryMessage = await createTemporaryPhotoMessage(
      workspaceId: channel.workspaceId,
      channelId: channel.channelId,
      userId: userId,
      imageList: imageList,
    );

    if (channel.isTemp) {
      channel.lastMessageCreateTime =
          StringExtensions.toFormattedString(temporaryMessage.createTime);
      channel.lastMessageContent = temporaryMessage.content;
      await getIt<InsertChannelUseCase>()
          .execute(InsertChannelInput(channel: channel));
    }

    _addTemporaryMessage(temporaryMessage);

    if (uploadConcurrency <= 1) {
      await _uploadSequentially(imageList, temporaryMessage);
    } else {
      await _uploadInParallelBatches(imageList, temporaryMessage);
    }
  }

  /// Handles sequential upload of images.
  Future<void> _uploadSequentially(
    List<UploadFile> imageList,
    Message temporaryMessage,
  ) async {
    int currentUploadIndex = 0;
    final uploadImageHandler = UploadImageHandler();

    Future<void> uploadFileAtIndex(int index) async {
      if (index >= imageList.length) return;

      final currentImage = imageList[index];
      await uploadImageHandler.handleUpload(
        file: currentImage,
        onSuccess: (UpFile file, String fileUrl) async {
          final fileRef = currentImage.fileRef;

          await _onUploadSuccessSequential(
            index: index,
            fileUrl: fileUrl,
            file: file,
            temporaryMessage: temporaryMessage,
            fileRef: fileRef!,
            onMessageIdAndIndexUpdated: (updatedMessageId, updatedIndex) {
              temporaryMessage.messageId = updatedMessageId;
              currentUploadIndex = updatedIndex;
            },
          );
          await uploadFileAtIndex(index + 1);
        },
        onError: _handleUploadError,
      );
    }

    await uploadFileAtIndex(currentUploadIndex);
  }

  /// Handles parallel batch uploads of images.
  Future<void> _uploadInParallelBatches(
    List<UploadFile> imageList,
    Message temporaryMessage,
  ) async {
    if (imageList.length == 1) {
      await _uploadFirstFileToCreateMessage(imageList.first, temporaryMessage);
      return;
    }

    await _uploadFirstFileToCreateMessage(
      imageList.first,
      temporaryMessage,
      onMessageIdUpdated: (messageId) async {
        temporaryMessage.messageId = messageId;
        final remainingFiles = imageList.sublist(1);
        for (int i = 0; i < remainingFiles.length; i += uploadConcurrency) {
          final batch = remainingFiles.sublist(
            i,
            i + uploadConcurrency > remainingFiles.length
                ? remainingFiles.length
                : i + uploadConcurrency,
          );

          final futures = batch.map((uploadFile) async {
            final uploadedFile = await _uploadFileParallel(uploadFile);
            return {
              'upFile': uploadedFile,
              'fileRef': uploadFile.fileRef,
            };
          }).toList();

          final batchResults = await Future.wait(futures);

          final uploadedFiles = batchResults.map((result) {
            var uploadedFile = result['upFile'] as FileToUpload;
            final fileRef = result['fileRef'] as String;
            uploadedFile.fileRef = fileRef;
            return uploadedFile;
          }).toList();

          final updateUseCase =
              GetIt.instance.get<UpdateMediaAttachmentsUseCase>();
          final output = await updateUseCase.execute(
            UpdateMediaAttachmentsInput(
              workspaceId: workspaceId,
              channelId: channelId,
              userId: userId,
              messageId: temporaryMessage.messageId,
              messageRef: temporaryMessage.ref,
              fileUploaded: uploadedFiles,
            ),
          );

          if (output.message != null) {
            updateTemporaryMessageFromServerMessage(
              temporaryMessage: temporaryMessage,
              serverMessage: output.message!,
              newlyUploadedFiles: uploadedFiles,
              isFirstFile: false,
            );
            _updateMessage(temporaryMessage);
          }
        }
      },
    );
  }

  /// Uploads the first file to create a new message.
  Future<void> _uploadFirstFileToCreateMessage(
    UploadFile firstFile,
    Message temporaryMessage, {
    Function(String severMessageId)? onMessageIdUpdated,
  }) async {
    final uploadImageHandler = UploadImageHandler();
    await uploadImageHandler.handleUpload(
      file: firstFile,
      onSuccess: (UpFile file, String fileUrl) async {
        await _sendFirstMediaMessage(
          ref: temporaryMessage.ref,
          fileUrl: fileUrl,
          file: file,
          index: 0,
          fileRef: firstFile.fileRef!,
          temporaryMessage: temporaryMessage,
          onMessageIdAndIndexUpdated: (updatedMessageId, _) {
            temporaryMessage.messageId = updatedMessageId;
            onMessageIdUpdated?.call(updatedMessageId);
          },
        );
      },
      onError: _handleUploadError,
    );
  }

  /// Handles parallel file upload.
  Future<FileToUpload> _uploadFileParallel(UploadFile fileToUpload) async {
    final completer = Completer<FileToUpload>();
    final uploadImageHandler = UploadImageHandler();
    await uploadImageHandler.handleUpload(
      file: fileToUpload,
      onSuccess: (UpFile file, String fileUrl) async {
        completer.complete(
          FileToUpload(path: file.path, name: file.name)
            ..size = file.size
            ..metaData = file.metaData
            ..uploadState = file.uploadState
            ..fileRef = fileToUpload.fileRef
            ..fileUrl = fileUrl,
        );
      },
      onError: (f, e, msg) {
        completer.completeError("Failed to upload file: $msg");
      },
    );
    return completer.future;
  }

  /// Sends the first media message.
  Future<void> _sendFirstMediaMessage({
    required String? ref,
    required String fileUrl,
    required UpFile file,
    required Function(String, int) onMessageIdAndIndexUpdated,
    required int index,
    required String fileRef,
    required Message temporaryMessage,
  }) async {
    final sendMediaMessageUseCase =
        GetIt.instance.get<SendMediaMessageUseCase>();

    final fileToUpload = FileToUpload.fromUpFile(file);
    final mediaMetaData = MediaMetaDataToUpload();
    mediaMetaData.dimensions = await getImageDimensions(file.path);
    fileToUpload.mediaMetaData = mediaMetaData;
    fileToUpload.fileUrl = fileUrl;
    fileToUpload.fileRef = fileRef;
    final output = await sendMediaMessageUseCase.execute(
      SendMediaMessageInput(
        workspaceId: workspaceId,
        channelId: channelId,
        userId: userId,
        ref: ref,
        file: fileToUpload,
      ),
    );

    if (output.message != null) {
      updateTemporaryMessageFromServerMessage(
        temporaryMessage: temporaryMessage,
        serverMessage: output.message!,
        newlyUploadedFiles: [fileToUpload],
        isFirstFile: true,
      );

      onMessageIdAndIndexUpdated(output.message!.messageId, index + 1);

      _updateMessage(temporaryMessage);
    }
  }

  /// Handles successful upload in sequential mode.
  Future<void> _onUploadSuccessSequential({
    required int index,
    required String fileUrl,
    required UpFile file,
    required Message temporaryMessage,
    required Function(String, int) onMessageIdAndIndexUpdated,
    required String fileRef,
  }) async {
    if (index == 0) {
      final sendMediaMessageUseCase =
          GetIt.instance.get<SendMediaMessageUseCase>();
      final fileToUpload = FileToUpload.fromUpFile(file);
      final mediaMetaData = MediaMetaDataToUpload();
      mediaMetaData.dimensions = await getImageDimensions(file.path);
      fileToUpload.mediaMetaData = mediaMetaData;
      fileToUpload.fileUrl = fileUrl;
      fileToUpload.fileRef = fileRef;
      final output = await sendMediaMessageUseCase.execute(
        SendMediaMessageInput(
          workspaceId: workspaceId,
          channelId: channelId,
          userId: userId,
          ref: temporaryMessage.ref,
          file: fileToUpload,
        ),
      );

      if (output.message != null) {
        updateTemporaryMessageFromServerMessage(
          temporaryMessage: temporaryMessage,
          serverMessage: output.message!,
          newlyUploadedFiles: [fileToUpload],
          isFirstFile: true,
        );

        onMessageIdAndIndexUpdated(output.message!.messageId, index + 1);

        // _updateAttachment(temporaryMessage);

        _saveMessage(temporaryMessage);
      }
    } else {
      final updateUseCase = GetIt.instance.get<UpdateMediaAttachmentsUseCase>();
      final fileToUpload = FileToUpload.fromUpFile(file);
      final mediaMetaData = MediaMetaDataToUpload();
      mediaMetaData.dimensions = await getImageDimensions(file.path);
      fileToUpload.mediaMetaData = mediaMetaData;
      fileToUpload.fileUrl = fileUrl;
      fileToUpload.fileRef = fileRef;
      final output = await updateUseCase.execute(
        UpdateMediaAttachmentsInput(
          workspaceId: workspaceId,
          channelId: channelId,
          userId: userId,
          messageId: temporaryMessage.messageId,
          messageRef: temporaryMessage.ref,
          fileUploaded: [fileToUpload],
        ),
      );

      if (output.message != null) {
        updateTemporaryMessageFromServerMessage(
          temporaryMessage: temporaryMessage,
          serverMessage: output.message!,
          newlyUploadedFiles: [fileToUpload],
          isFirstFile: false,
        );
        onMessageIdAndIndexUpdated(temporaryMessage.messageId, index + 1);
        _updateAttachment(temporaryMessage);
        // _saveMessage(temporaryMessage);
      }
    }
  }

  /// Updates the temporary message with server message data.
  /// TODO: handle copy with for sever message
  void updateTemporaryMessageFromServerMessage({
    required Message temporaryMessage,
    required Message serverMessage,
    required bool isFirstFile,
    required List<FileToUpload> newlyUploadedFiles,
  }) {
    // If this is the first file upload, update basic information
    if (isFirstFile) {
      temporaryMessage.channelId = serverMessage.channelId;
      temporaryMessage.workspaceId = serverMessage.workspaceId;
      temporaryMessage.messageId = serverMessage.messageId;
      temporaryMessage.content = serverMessage.content;
      // Keep the old ref (or update based on logic)
      temporaryMessage.ref = temporaryMessage.ref;
      temporaryMessage.isTemp = false;
    }

    // Create map to look up server attachments
    final Map<String, Attachment> serverAttachmentMap = {};
    for (final attachment in serverMessage.mediaAttachments) {
      final fileRef = attachment.photo?.fileRef;
      if (fileRef != null && fileRef.isNotEmpty) {
        serverAttachmentMap[fileRef] = attachment;
      }
    }

    // Only update attachments corresponding to files just uploaded
    for (var i = 0; i < temporaryMessage.mediaAttachments.length; i++) {
      final oldAttachment = temporaryMessage.mediaAttachments[i];
      final oldFileRef = oldAttachment.photo?.fileRef;

      // Check if this attachment is in the batch just uploaded
      final isInNewlyUploadedList = newlyUploadedFiles
          .any((uploadedFile) => uploadedFile.fileRef == oldFileRef);

      if (oldFileRef != null && isInNewlyUploadedList) {
        // If server returns attachment corresponding to this fileRef
        if (serverAttachmentMap.containsKey(oldFileRef)) {
          final serverAttachment = serverAttachmentMap[oldFileRef]!;
          // Assign serverAttachment directly to update
          temporaryMessage.mediaAttachments[i] = serverAttachment;
          temporaryMessage.mediaAttachments[i].message.target =
              temporaryMessage;
        }
        // If server doesn't return (possibly upload error or other logic),
        // handle as needed: keep oldAttachment or assign error status...
      }
    }

    // If serverMessage.mediaAttachments and temporaryMessage.mediaAttachments
    // have the same count, we can consider all files were uploaded successfully.
    if (serverMessage.mediaAttachments.length ==
        temporaryMessage.mediaAttachments.length) {
      temporaryMessage.messageStatusRaw = MessageStatus.SUCCESS.rawValue();
      _delayUpdateMessageStatus(temporaryMessage);
    }
  }

  /// Handles upload errors and throws appropriate exceptions.
  void _handleUploadError(UpFile file, ErrorCode errorCode, String message) {
    switch (errorCode) {
      case ErrorCode.noInternet:
        throw Exception("No internet while uploading file: $message");
      case ErrorCode.uploadError:
        throw Exception("Upload error while uploading file: $message");
      default:
        throw Exception("Unknown error while uploading file: $message");
    }
  }

  // Original-structure method with parallel dimension extraction on main thread
  Future<Message> createTemporaryPhotoMessage({
    required String? workspaceId,
    required String? channelId,
    required String? userId,
    required List<UploadFile> imageList,
    String? ref,
  }) async {
    final message = TempMessageFactory.createBaseMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      content: GlobalConfig.CONTENT_PHOTO,
      messageViewType: MessageViewType.imagesOwner,
    );

    message
      ..ref = ref ?? RandomUtils.randomUlId()
      ..attachmentTypeRaw = AttachmentType.PHOTO.rawValue();

    final baseDir = Directory('${FileUtils.cacheDirPath}/${message.ref}');
    await baseDir.create(recursive: true);

    // offload file copy to isolate
    final params = _ComputeParams(imageList, baseDir.path, message.ref!);
    final copyInfos = await compute(_computeCopyInfos, params);

    // add paths to cache to avoid file system operations later
    unawaited(_precacheImage(copyInfos, message));

    // on main thread: get dimensions in parallel
    final futures = copyInfos.map((info) async {
      final dims = await getImageDimensions(
        '${FileUtils.cacheDirPath}/${info.filePath}',
      );
      return AttachmentData(
        fileRef: info.fileRef,
        fileName: info.fileName,
        filePath: info.filePath,
        width: dims.width.toInt(),
        height: dims.height.toInt(),
      );
    }).toList();
    final attachmentsData = await Future.wait(futures);

    // recreate attachments
    for (final data in attachmentsData) {
      final attachment = Attachment(
        attachmentId: UUIDUtils.random(),
        ref: data.fileRef,
        isTemp: true,
        attachmentStatusRaw: AttachmentStatusEnum.UPLOADING.rawValue(),
      )..photo = MediaObject(
          attachmentId: data.fileRef,
          fileId: data.fileRef,
          fileRef: data.fileRef,
          attachmentType: AttachmentType.PHOTO.rawValue(),
          filePath: data.filePath,
          fileStatus: AttachmentStatusEnum.UPLOADING.rawValue(),
          fileMetadata: FileMetadata(
            dimensions: Dimensions(
              width: data.width,
              height: data.height,
            ),
          ),
        );

      attachment.message.target = message;
      message.mediaAttachments.add(attachment);
    }

    return message;
  }

  Future<void> _precacheImage(
    List<_AttachmentCopyInfo> copyInfos,
    Message message,
  ) async {
    for (final info in copyInfos) {
      final fullPath = '${FileUtils.cacheDirPath}/${info.filePath}';
      FileUtils.addImagePathCache(
        messageRef: message.ref!,
        fileRef: info.fileRef,
        path: fullPath,
      );
    }
  }

  /// Adds a temporary message to the messages bloc.
  void _addTemporaryMessage(Message message) {
    if (_isMessagesBlocClosed()) return;
    messagesBloc?.add(MessagesEvent.addTempMessage(message));
  }

  /// Updates the message in the messages bloc.
  void _updateMessage(Message message) {
    print(
      'MessageListViewState.SendImageMessageHandler.updateMessage: ${message.messageId} - ${message.ref}',
    );
    if (_isMessagesBlocClosed()) return;
    messagesBloc?.add(MessagesEvent.updateMessage(message));
  }

  void _updateAttachment(Message message) {
    if (_isMessagesBlocClosed()) return;
    messagesBloc?.add(MessagesEvent.updateAttachment(message));
  }

  void _updateMessageStatus(Message message) {
    if (_isMessagesBlocClosed()) return;
    messagesBloc?.add(
      MessagesEvent.updateMessageStatus(
        workspaceId: message.workspaceId,
        channelId: message.channelId,
        messageId: message.messageId,
        messageRef: message.ref,
        messageStatus: message.messageStatus,
      ),
    );
  }

  /// Saves the temporary message in the messages bloc.
  void _saveMessage(Message message) {
    if (_isMessagesBlocClosed()) return;
    messagesBloc?.add(MessagesEvent.saveTempMessage(message));
  }

  /// Checks if the messages bloc is closed.
  bool _isMessagesBlocClosed() {
    if (messagesBloc?.isClosed ?? true) {
      return true;
    }
    return false;
  }

  /// Delay to update message status
  Future<void> _delayUpdateMessageStatus(Message temporaryMessage) async {
    await Future.delayed(DurationUtils.ms3000);

    temporaryMessage.messageStatusRaw = MessageStatus.UNRECOGNIZED.rawValue();

    _updateMessageStatus(temporaryMessage);
  }

  Future<Channel?> _getChannel() async {
    if (isDm()) {
      return (await getIt<GetOrCreateTempDMChannelUseCase>().execute(
        GetOrCreateTempDMChannelInput(userId: userId!),
      ))
          .channel;
    }
    return (await getIt<GetChannelUseCase>().execute(
      GetChannelInput(
        workspaceId: workspaceId,
        channelId: channelId,
      ),
    ))
        .channel;
  }
}

// Top-level function run in isolate to handle heavy file I/O
Future<List<_AttachmentCopyInfo>> _computeCopyInfos(
  _ComputeParams params,
) async {
  final infos = <_AttachmentCopyInfo>[];

  for (final image in params.images) {
    // ensure fileRef
    final fileRef = image.fileRef ?? UUIDUtils.random();

    // create directory for this image
    final fileDir = Directory('${params.baseDirPath}/$fileRef');
    await fileDir.create(recursive: true);

    // copy file
    final fileName = image.name;
    final cachedPath = '${fileDir.path}/$fileName';
    await File(image.path).copy(cachedPath);

    infos.add(
      _AttachmentCopyInfo(
        fileRef: fileRef,
        fileName: fileName,
        filePath: '${params.messageRef}/$fileRef/$fileName',
      ),
    );
  }

  return infos;
}

Future<Size> getImageDimensions(String path) async {
  final completer = Completer<Size>();
  FileImage(File(path)).resolve(const ImageConfiguration()).addListener(
    ImageStreamListener((ImageInfo info, bool _) {
      final size = info.image;
      if (!completer.isCompleted) {
        return completer
            .complete(Size(size.width.toDouble(), size.height.toDouble()));
      }
    }),
  );

  return await completer.future;
}

// Parameter object for isolate processing
class _ComputeParams {
  final List<UploadFile> images;
  final String baseDirPath;
  final String messageRef;

  _ComputeParams(this.images, this.baseDirPath, this.messageRef);
}

// Data returned from isolate: only file copy info
class _AttachmentCopyInfo {
  final String fileRef;
  final String fileName;
  final String filePath;

  _AttachmentCopyInfo({
    required this.fileRef,
    required this.fileName,
    required this.filePath,
  });
}

class AttachmentData {
  final String fileRef;
  final String fileName;
  final String filePath;
  final int width;
  final int height;

  AttachmentData({
    required this.fileRef,
    required this.fileName,
    required this.filePath,
    required this.width,
    required this.height,
  });
}
