name: chat
description: ZiiChat chat package
version: 0.1.0+1
publish_to: none
environment:
  sdk: ^3.4.0
  flutter: ^3.22.0
dependencies:
  bloc: ^9.0.0
  built_collection: ^5.1.1
  dartx: ^1.2.0
  google_mlkit_language_id: ^0.13.0
  google_mlkit_translation: ^0.13.0
  encrypted_shared_preferences: ^3.0.1
  equatable: ^2.0.7
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.0
  get_it: ^8.0.3
  injectable: ^2.5.0
  photo_manager: ^3.6.4
  auto_route: ^10.0.1
  file_picker: 10.1.7
  rxdart: ^0.28.0
  shared:
    path: ../shared
  app_core:
    path: ../app_core
  sticker:
    path: ../sticker
  upload_manager:
    path: ../upload_manager
  search_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.25
      path: apis/search_api
  channel_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/channel_api
  channel_view_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/channel_view_api
  friend_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/friend_api
  friend_view_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/friend_view_api
  invitation_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/invitation_api
  invitation_view_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/invitation_view_api
  member_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/member_api
  member_view_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/member_view_api
  message_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.23
      path: apis/message_api
  message_view_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/message_view_api
  user_view_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.29
      path: apis/user_view_api
  shared_preferences: ^2.5.3
  localization_client:
    git:
      url: **************:ziichatlabs/ziichat-flutter-i18n.git
      ref: main
  ziichat_ui:
    git:
      url: **************:ziichatlabs/ziichat-flutter-ui.git
      ref: release/0.56.0
  filestore_sdk:
    git:
      url: **************:ziichatlabs/filestore-sdk.git
      ref: v0.1.0
  audio_waveforms:
    git:
      url: **************:ziichatlabs/audio_waveforms.git
      ref: fix/fork-1.3.0
  path_provider: ^2.1.5
  objectbox:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: objectbox
      ref: v4.1.0
  objectbox_flutter_libs:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: flutter_libs
      ref: v4.1.0
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  infinite_scroll_pagination: ^4.0.0
  flutter_screenutil: ^5.9.3
  workmanager:
    git:
      url: https://github.com/ziichatlabs/flutter_workmanager.git
      path: workmanager
      ref: v0.1.0
  cached_annotation:
    git:
      url: **************:ziichatlabs/cached.git
      ref: v0.1.0
      path: packages/cached_annotation
  visibility_detector: ^0.4.0+2
  saver_gallery: ^4.0.1
  google_maps_flutter: ^2.12.1
  download_manager:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.55.0
      path: packages/download_manager
  video_compressor:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.0.43
      path: packages/video_compressor
  ziichat_video_player:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: v0.57.0
      path: packages/ziichat_video_player/
  scrollview_observer: ^1.26.0
  user_profile_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/user_profile_api
  dio: ^5.8.0+1
  flutter_ogg_to_aac: ^0.0.4
  path: ^1.9.1
dev_dependencies:
  build_runner: ^2.4.15
  cached:
    git:
      url: **************:ziichatlabs/cached.git
      ref: v0.1.0
      path: packages/cached
  flutter_test:
    sdk: flutter
  freezed: ^3.0.6
  injectable_generator: ^2.7.0
  mocktail: ^1.0.4
  very_good_analysis: ^7.0.0
  objectbox_generator:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: generator
      ref: v4.1.0
  json_serializable: ^6.9.1
objectbox:
  output_dir: src/data/repositories/database/generated
dependency_overrides:
  analyzer: 7.3.0
