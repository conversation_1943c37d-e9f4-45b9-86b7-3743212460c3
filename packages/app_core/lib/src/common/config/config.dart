import 'package:shared/shared.dart';

import '../di/di.dart' as di;

class Config extends BaseConfig {
  AuthData? _authData;

  String? _notificationToken;

  String? _voipToken;

  set authData(AuthData? value) {
    _authData = value;
  }

  set notificationToken(String? value) {
    _notificationToken = value;
  }

  set voipToken(String? value) {
    _voipToken = value;
  }

  String get apiAuthToken => _authData?.authToken ?? '';

  String? get activeSessionKey => _authData?.sessionKey;

  String? get notificationToken => _notificationToken;

  String? get voipToken => _voipToken;

  factory Config.getInstance() {
    return _instance;
  }

  Config._();

  static final Config _instance = Config._();

  @override
  Future<void> config() async => di.configureInjection();

  Future<bool> isRegistered() async => di.getIt.isRegistered();
}
