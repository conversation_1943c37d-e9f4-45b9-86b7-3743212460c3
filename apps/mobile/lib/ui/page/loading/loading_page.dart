import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

class LoadingPage extends StatelessWidget {
  const LoadingPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
      child: ColoredBox(
        color: Theme.of(context).colorScheme.onTertiary.withValues(alpha: 0.5),
        child: const SizedBox.expand(
          child: Center(
            child: SizedBox(
              width: 60,
              height: 60,
              child: LoadingWidget(),
            ),
          ),
        ),
      ),
    );
  }
}
