import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:flutter/widgets.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:passkeys/authenticator.dart';
import 'package:shared/shared.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../auth.dart';
import '../../data/repositories/source/api/hash_cash/hash_cash.dart';
import '../../utils/cloud_kit_utils.dart';
import '../usecase/get_active_session_use_case.dart';
import '../usecase/hash_cash_use_case.dart';
import '../usecase/improving_passkey/check_migrate_passkey_status_use_case.dart';
import '../usecase/improving_passkey/set_passkey_migrated_use_case.dart';
import '../usecase/improving_passkey/verify_migrate_passkey_use_case.dart';

class MigratePasskeyHandler {
  static const String TAG = 'MigratePasskeyHandler';
  final BuildContext context;
  final VoidCallback onHideBottomSheet;
  final String _reqChallenge = RandomUtils.generateRandomString(16);
  final ValueNotifier<PasskeysStateEnum> passkeysStateNotifier =
      ValueNotifier(PasskeysStateEnum.loading);

  HashCash? _hashCash;

  MigratePasskeyHandler({
    required this.context,
    required this.onHideBottomSheet,
  });

  Future<bool> passkeyMigrated() async {
    if (!await _shouldMigration()) return true;

    final migrationStatusOutput = await CheckMigratePasskeyStatusUseCase()
        .execute(CheckMigratePasskeyStatusInput());
    if (migrationStatusOutput.ok) {
      await _setPasskeyMigrated();
      return true;
    }
    return false;
  }

  Future<void> startMigrate() async {
    if (await passkeyMigrated()) return;

    _showMigrationDialog();
  }

  Future<bool> _shouldMigration() async {
    if (Platform.isAndroid) return false;

    if (await getDoubleOSVersion() < 16.0) return false;

    final sessionOutput = await GetIt.instance<GetActiveSessionUseCase>()
        .execute(GetActiveSessionInput());

    if (sessionOutput.session == null) return false;

    if (sessionOutput.session!.passkeyMigrated) return false;

    if (sessionOutput.session!.isLoginQR) return false;

    if (!sessionOutput.session!.isLogin) return false;

    return true;
  }

  void _showMigrationDialog() {
    BottomSheetUtil.showImprovingSecurityFloatingBottomSheet(
      context: context,
      onClose: _handleBottomSheetClose,
      onLearnMore: _onLearnMore,
      onContinue: () {
        _handleBottomSheetClose();
        _handleMigrationProcess();
      },
    );
  }

  Future<void> _handleMigrationProcess() async {
    if (await _needShowSignInAppleIdRequest()) return;

    _hashCash = await _generateHashCash();
    if (_hashCash == null) {
      _showCreateCredentialError();
      return;
    }

    final migrateRequestOutput = await _migratePasskeyRequest();
    if (migrateRequestOutput == null) {
      _showCreateCredentialError();
      return;
    }

    final createCredentialOutput =
        await _createCredential(migrateRequestOutput);

    if (createCredentialOutput.registerRequest == null) {
      _showCreateCredentialError();
      return;
    }

    final startTime = DateTime.now();
    _showVerifySecurityBottomSheet(PasskeysStateEnum.loading);

    final verifyCredentialOutput =
        await _verifyCredential(createCredentialOutput);

    if (verifyCredentialOutput.ok) {
      await _setPasskeyMigrated();
    }

    final elapsedTime = DateTime.now().difference(startTime).inMilliseconds;
    const minDelay = 2000;
    final delay = elapsedTime < minDelay ? minDelay - elapsedTime : 0;

    await Future.delayed(Duration(milliseconds: delay));

    _showProcessState(
      verifyCredentialOutput.ok
          ? PasskeysStateEnum.success
          : PasskeysStateEnum.failed,
    );
  }

  Future<void> _setPasskeyMigrated() async {
    await GetIt.instance<SetPasskeyMigratedUseCase>()
        .execute(SetPasskeyMigratedInput());
  }

  Future<HashCash?> _generateHashCash() async {
    final hashCashOutput =
        await GetIt.instance<HashCashUseCase>().execute(HashCashInput());
    return hashCashOutput.hashCash;
  }

  Future<MigratePasskeyOutput?> _migratePasskeyRequest() async {
    final reqChallengeHash =
        sha256.convert(utf8.encode(_reqChallenge)).toString();
    final migrateRequestOutput = await GetIt.instance<MigratePasskeyUseCase>()
        .execute(MigratePasskeyInput(reqChallenge: reqChallengeHash));
    return migrateRequestOutput;
  }

  Future<CreateCredentialOutput> _createCredential(
    MigratePasskeyOutput migrateRequestOutput,
  ) async {
    try {
      return await GetIt.instance<CreateCredentialUseCase>().execute(
        CreateCredentialInput(
          registerRequest: migrateRequestOutput.response!.data!,
          reqChallenge: _reqChallenge,
          hashCash: _hashCash!,
        ),
      );
    } catch (e) {
      Log.e(name: TAG, e.toString(), errorObject: e);
      return CreateCredentialOutput(registerRequest: null);
    }
  }

  Future<VerifyMigratePasskeyOutput> _verifyCredential(
    CreateCredentialOutput createCredentialOutput,
  ) async {
    try {
      return await GetIt.instance<VerifyMigratePasskeyUseCase>().execute(
        VerifyMigratePasskeyInput(
          reqId: createCredentialOutput.registerRequest!.reqId!,
          reqVerifier: createCredentialOutput.registerRequest!.reqVerifier!,
          credential: createCredentialOutput.registerRequest!.credential!,
        ),
      );
    } catch (e) {
      return VerifyMigratePasskeyOutput(ok: false);
    }
  }

  void _showVerifySecurityBottomSheet(PasskeysStateEnum state) {
    passkeysStateNotifier.value = state;

    BottomSheetUtil.showVerifySecurityFloatingBottomSheet(
      context: context,
      passkeysState: passkeysStateNotifier,
      onClose: _handleBottomSheetClose,
      onLearnMore: () {},
      onContinue: () {
        if (passkeysStateNotifier.value == PasskeysStateEnum.success) {
          onHideBottomSheet.call();
        } else {
          _handleMigrationProcess();
        }
      },
    );
  }

  Future<bool> _needShowSignInAppleIdRequest() async {
    if (!Platform.isIOS) return Future.value(false);

    if (await CloudKitUtils.isIcloudLogin()) {
      return Future.value(false);
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      BottomSheetUtil.showSignInAppleIdRequestFLoatingBottomsheet(
        context: context,
        isDismissible: false,
        enableDrag: false,
        onClose: () {
          onHideBottomSheet.call();
        },
        onTurnOn: () {
          PasskeyAuthenticator().goToSettings();
        },
        onLearnMore: _onLearnMore,
      );
    });

    return Future.value(true);
  }

  Future<void> _onLearnMore() async {
    final learnMoreUrl = Uri.parse('https://developer.apple.com/passkeys/');

    if (await canLaunchUrl(learnMoreUrl)) {
      launchUrl(
        learnMoreUrl,
        mode: LaunchMode.externalApplication,
      );
    }
  }

  void _showProcessState(PasskeysStateEnum state) {
    passkeysStateNotifier.value = state;
  }

  void _handleBottomSheetClose() {
    onHideBottomSheet.call();
  }

  void _showCreateCredentialError() {
    SnackBarUtilV2.showFloatingSnackBar(
      context: context,
      snackBarType: SnackBarType.danger,
      content: GetIt.I
          .get<AppLocalizations>()
          .thePassKeysUpgradeProcessHasBeenCanceled,
    );
  }
}
