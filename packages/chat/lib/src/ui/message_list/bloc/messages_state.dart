part of 'messages_bloc.dart';

@freezed
sealed class MessagesState extends BaseBlocState with _$MessagesState {
  const MessagesState._();

// Initial state
  factory MessagesState.initial() = MessagesStateInitial;

  // Waiting for data state
  factory MessagesState.waiting() = MessagesStateWaiting;

  // Successfully loaded messages state
  factory MessagesState.loaded({
    required List<Message> messages,
    required String? lastSeenMessageId,
    required bool hasNext,
    String? nextPageToken,
  }) = MessagesStateLoaded;

  // State for loading more messages
  factory MessagesState.onLoadMore({
    required List<Message> messages,
    required bool hasNext,
    String? nextPageToken,
  }) = MessagesStateOnLoadMore;

  // State for synchronized messages
  factory MessagesState.synced({
    required List<Message> messages,
  }) = MessagesStateSynced;

  // State for adding a temporary message
  factory MessagesState.addTempMessage(Message message) =
      MessagesStateAddTempMessage;

  // State when a new message is added
  factory MessagesState.addMessage(Message message) = MessagesStateAddMessage;

  // State for updating message status
  factory MessagesState.updateMessageStatus({
    required MessageStatus messageStatus,
    required String messageId,
    String? messageRef,
  }) = MessagesStateUpdateMessageStatus;

  // State when a message is updated
  factory MessagesState.updateMessage(Message message) =
      MessagesStateUpdateMessage;

  // State when an error occurs
  factory MessagesState.error(String error) = MessagesStateError;

  // State for clearing all messages
  factory MessagesState.clearAllMessage({
    String? workspaceId,
    String? channelId,
  }) = MessagesStateClearAllMessage;

  // State for deleting a message
  factory MessagesState.deleteMessage({
    required bool response,
  }) = MessagesStateDeleteMessage;

  // State for resending a message
  factory MessagesState.resendMessage(Message message) =
      MessagesStateResendMessage;

  // State for refreshing delete
  factory MessagesState.refreshDelete() = MessagesStateRefreshDelete;

  // State when a pin unpin message
  factory MessagesState.pinUnPinUpdateMessage(Message message) =
      PinUnPinUpdateMessage;

  // State load  pin unpin message
  factory MessagesState.loadPinUnPinUpdateMessage(List<Message> message) =
      LoadPinUnPinUpdateMessage;
}

extension MessagesStateX on MessagesState {
  T when<T>({
    required T Function() initial,
    required T Function() waiting,
    required T Function(
      List<Message> messages,
      String? lastSeenMessageId,
      bool hasNext,
      String? nextPageToken,
    ) loaded,
    required T Function(
      List<Message> messages,
      bool hasNext,
      String? nextPageToken,
    ) onLoadMore,
    required T Function(List<Message> messages) synced,
    required T Function(Message message) addTempMessage,
    required T Function(Message message) addMessage,
    required T Function(
      MessageStatus messageStatus,
      String messageId,
      String? messageRef,
    ) updateMessageStatus,
    required T Function(Message message) updateMessage,
    required T Function(String error) error,
    required T Function(String? workspaceId, String? channelId) clearAllMessage,
    required T Function(bool response) deleteMessage,
    required T Function(Message message) resendMessage,
    required T Function() refreshDelete,
    required T Function(Message message) pinUnPinUpdateMessage,
    required T Function(List<Message> message) loadPinUnPinUpdateMessage,
  }) {
    final state = this;

    if (state is MessagesStateInitial) return initial();
    if (state is MessagesStateWaiting) return waiting();
    if (state is MessagesStateLoaded) {
      return loaded(
        state.messages,
        state.lastSeenMessageId,
        state.hasNext,
        state.nextPageToken,
      );
    }
    if (state is MessagesStateOnLoadMore) {
      return onLoadMore(
        state.messages,
        state.hasNext,
        state.nextPageToken,
      );
    }
    if (state is MessagesStateSynced) return synced(state.messages);
    if (state is MessagesStateAddTempMessage)
      return addTempMessage(state.message);
    if (state is MessagesStateAddMessage) return addMessage(state.message);
    if (state is MessagesStateUpdateMessageStatus) {
      return updateMessageStatus(
        state.messageStatus,
        state.messageId,
        state.messageRef,
      );
    }
    if (state is MessagesStateUpdateMessage)
      return updateMessage(state.message);
    if (state is MessagesStateError) return error(state.error);
    if (state is MessagesStateClearAllMessage) {
      return clearAllMessage(state.workspaceId, state.channelId);
    }
    if (state is MessagesStateDeleteMessage)
      return deleteMessage(state.response);
    if (state is MessagesStateResendMessage)
      return resendMessage(state.message);
    if (state is MessagesStateRefreshDelete) return refreshDelete();
    if (state is PinUnPinUpdateMessage)
      return pinUnPinUpdateMessage(state.message);
    if (state is LoadPinUnPinUpdateMessage)
      return loadPinUnPinUpdateMessage(state.message);

    throw StateError('Unhandled MessagesState: $state');
  }

  T maybeWhen<T>({
    T Function()? initial,
    T Function()? waiting,
    T Function(
      List<Message> messages,
      String? lastSeenMessageId,
      bool hasNext,
      String? nextPageToken,
    )? loaded,
    T Function(
      List<Message> messages,
      bool hasNext,
      String? nextPageToken,
    )? onLoadMore,
    T Function(List<Message> messages)? synced,
    T Function(Message message)? addTempMessage,
    T Function(Message message)? addMessage,
    T Function(
      MessageStatus messageStatus,
      String messageId,
      String? messageRef,
    )? updateMessageStatus,
    T Function(Message message)? updateMessage,
    T Function(String error)? error,
    T Function(String? workspaceId, String? channelId)? clearAllMessage,
    T Function(bool response)? deleteMessage,
    T Function(Message message)? resendMessage,
    T Function()? refreshDelete,
    T Function(Message message)? pinUnPinUpdateMessage,
    T Function(List<Message> message)? loadPinUnPinUpdateMessage,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is MessagesStateInitial && initial != null) return initial();
    if (state is MessagesStateWaiting && waiting != null) return waiting();
    if (state is MessagesStateLoaded && loaded != null) {
      return loaded(
        state.messages,
        state.lastSeenMessageId,
        state.hasNext,
        state.nextPageToken,
      );
    }
    if (state is MessagesStateOnLoadMore && onLoadMore != null) {
      return onLoadMore(
        state.messages,
        state.hasNext,
        state.nextPageToken,
      );
    }
    if (state is MessagesStateSynced && synced != null)
      return synced(state.messages);
    if (state is MessagesStateAddTempMessage && addTempMessage != null) {
      return addTempMessage(state.message);
    }
    if (state is MessagesStateAddMessage && addMessage != null) {
      return addMessage(state.message);
    }
    if (state is MessagesStateUpdateMessageStatus &&
        updateMessageStatus != null) {
      return updateMessageStatus(
        state.messageStatus,
        state.messageId,
        state.messageRef,
      );
    }
    if (state is MessagesStateUpdateMessage && updateMessage != null) {
      return updateMessage(state.message);
    }
    if (state is MessagesStateError && error != null) return error(state.error);
    if (state is MessagesStateClearAllMessage && clearAllMessage != null) {
      return clearAllMessage(state.workspaceId, state.channelId);
    }
    if (state is MessagesStateDeleteMessage && deleteMessage != null) {
      return deleteMessage(state.response);
    }
    if (state is MessagesStateResendMessage && resendMessage != null) {
      return resendMessage(state.message);
    }
    if (state is MessagesStateRefreshDelete && refreshDelete != null) {
      return refreshDelete();
    }
    if (state is PinUnPinUpdateMessage && pinUnPinUpdateMessage != null) {
      return pinUnPinUpdateMessage(state.message);
    }
    if (state is LoadPinUnPinUpdateMessage &&
        loadPinUnPinUpdateMessage != null) {
      return loadPinUnPinUpdateMessage(state.message);
    }

    return orElse();
  }
}
