import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

@RoutePage()
class ZiishortPage extends StatefulWidget {
  const ZiishortPage({super.key});

  @override
  State<ZiishortPage> createState() => _ZiishortPageState();
}

class _ZiishortPageState extends State<ZiishortPage>
    implements RecordVideoAvatarInterface {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: PermissionUtils.isGrantedMicrophonePermission(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return ClipRect(
            child: AppScaffold(
              hasSafeArea: false,
              body: RecordVideoAvatarPage(
                interface: this,
                secondsLimit: 30,
                enableAudio: snapshot.data!,
              ),
            ),
          );
        }
        return SizedBox.shrink();
      },
    );
  }

  @override
  void onClose() {
    context.maybePop();
  }

  @override
  void onUpload(String videoPath) {
    context.maybePop(videoPath);
    AppEventBus.publish(
      SendZiishortMessageEvent(
        id: 'SendZiishortMessageID',
        filePath: videoPath,
      ),
    );
  }
}
