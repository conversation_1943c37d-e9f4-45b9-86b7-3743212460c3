import 'package:intl/intl.dart';

class TimeUtils {
  static String getCurrentUTCTimeFormatted() {
    return DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
        .format(DateTime.now().toUtc());
  }

  static DateTime? parseUTCStringToDateTime(String? utcTimeString) {
    if (utcTimeString == null) return null;
    return DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parseUtc(utcTimeString);
  }

  static DateTime now() {
    return DateTime.now().toLocal();
  }

  static String formatToISO8601(DateTime? dateTime) {
    if (dateTime == null) {
      return '';
    }
    return DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format(dateTime.toUtc());
  }

  static String format(
    DateTime? dateTime, [
    String pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
  ]) {
    if (dateTime == null) {
      return '';
    }
    return DateFormat(pattern).format(dateTime.toUtc());
  }

  static String formatDurationToMMSS(Duration duration) {
    int minutes = duration.inMinutes;
    int seconds = duration.inSeconds % 60;

    return '${_twoDigits(minutes)}:${_twoDigits(seconds)}';
  }

  static String formatDurationToHHMMSS(Duration duration) {
    final hours = _twoDigits(duration.inHours);
    final minutes = _twoDigits(duration.inMinutes.remainder(60));
    final seconds = _twoDigits(duration.inSeconds.remainder(60));

    return "$hours:$minutes:$seconds";
  }

  static String _twoDigits(int n) => n.toString().padLeft(2, '0');

  /// Gets the call duration as a formatted string (HH:MM:SS or MM:SS).
  static String formatCallDuration(Duration duration) {
    final int hours = duration.inHours;
    final int minutes = duration.inMinutes.remainder(60);
    final int seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    }
  }
}
