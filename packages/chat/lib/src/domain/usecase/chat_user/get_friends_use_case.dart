import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:friend_view_api/friend_view_api.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/database/enums/chat_friend_status.dart';
import 'upsert_chat_friends_use_case.dart';

part 'get_friends_use_case.freezed.dart';

@Injectable()
class GetFriendsUseCase
    extends BaseFutureUseCase<GetFriendsUseCaseInput, GetFriendsUseCaseOutput> {
  const GetFriendsUseCase();
  @protected
  @override
  Future<GetFriendsUseCaseOutput> buildUseCase(
    GetFriendsUseCaseInput input,
  ) async {
    final sessionKey = Config.getInstance().activeSessionKey;
    try {
      final result = await FriendViewClient().instance.listFriends();
      if (result.data?.ok != true) {
        return GetFriendsUseCaseOutput(friends: null);
      }

      final friends = result.data!.data!.where((v3FriendData) {
        return v3FriendData.friend != null;
      }).map((v3FriendData) {
        final json = jsonDecode(
          standardSerializers.toJson(V3Friend.serializer, v3FriendData.friend!),
        ) as Map<String, dynamic>;

        json['sessionKey'] = sessionKey;

        return ChatFriend.fromJson(json);
      }).toList();

      await GetIt.instance.get<UpsertChatFriendsUseCase>().execute(
            UpsertChatFriendsInput(friends: friends),
          );

      final jsonIncludes = jsonDecode(
        standardSerializers.toJson(
          V3DataInclude.serializer,
          result.data!.includes,
        ),
      );
      List<ChatUser> users = [];

      var responseIncludes = ResponseIncludes.fromJson(jsonIncludes);
      final usersIncludes = responseIncludes.users ?? [];
      for (final userInclude in usersIncludes) {
        final userSer =
            ChatUserSerializer.serializeFromJson(data: userInclude.toJson());
        if (userSer != null && userSer.userId != sessionKey) {
          if (friends.isNotEmpty) {
            final status = friends.firstWhere(
              (element) {
                return element.participantIds!.contains(userSer.userId);
              },
            ).status;
            userSer
              ..chatFriendDataRaw = jsonEncode(
                ChatFriendData(
                  status: ChatFriendStatusEnumExtension.getEnumByValue(
                    status,
                  ),
                ).toJson(),
              );
            users.add(userSer);
          }
        }
      }
      await GetIt.instance.get<UpsertChatUsersUseCase>().execute(
            UpsertChatUsersInput(users: users),
          );
      return GetFriendsUseCaseOutput(friends: users);
    } on Exception catch (_) {
      return GetFriendsUseCaseOutput(friends: null);
    }
  }
}

@freezed
sealed class GetFriendsUseCaseInput extends BaseInput
    with _$GetFriendsUseCaseInput {
  const GetFriendsUseCaseInput._();
  factory GetFriendsUseCaseInput() = _GetFriendsUseCaseInput;
}

@freezed
sealed class GetFriendsUseCaseOutput extends BaseOutput
    with _$GetFriendsUseCaseOutput {
  const GetFriendsUseCaseOutput._();
  factory GetFriendsUseCaseOutput({
    @Default(null) List<ChatUser>? friends,
  }) = _GetFriendsUseCaseOutput;
}
