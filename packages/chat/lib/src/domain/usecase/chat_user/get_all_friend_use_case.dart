import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/chat_friend_repository.dart';
import '../../../data/repositories/database/entities/chat_friend.dart';

@Injectable()
class GetAllFriendUseCase
    extends BaseFutureUseCase<GetAllFriendInput, GetAllFriendOutput> {
  const GetAllFriendUseCase(this._chatFriendRepository);

  final ChatFriendRepository _chatFriendRepository;

  @protected
  @override
  Future<GetAllFriendOutput> buildUseCase(
    GetAllFriendInput input,
  ) async {
    final friends = await _chatFriendRepository.getChatFriends();
    return GetAllFriendOutput(chatFriends: friends);
  }
}

class GetAllFriendInput extends BaseInput {
  GetAllFriendInput();
}

class GetAllFriendOutput extends BaseOutput {
  GetAllFriendOutput({required this.chatFriends});

  final List<ChatFriend> chatFriends;
}
