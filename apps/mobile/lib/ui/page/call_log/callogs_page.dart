import 'package:auto_route/auto_route.dart';
import 'package:call/call.dart' as call;
import 'package:flutter/material.dart';

import '../../../navigation/routes/app_router.gr.dart';

@RoutePage()
class CallLogsPage extends StatelessWidget {
  const CallLogsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return call.CallLogPage(
      goToDMChannel: (userId) async {
        context.router
            .popUntil((routePage) => routePage.data?.name == HomeRoute.name);
        context.pushRoute(ChannelViewRoute(userId: userId));
      },
      onCallCreated: () {
        context.router
            .popUntil((routePage) => routePage.data?.name == HomeRoute.name);
      },
    );
  }
}
