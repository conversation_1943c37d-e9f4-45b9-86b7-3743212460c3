import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

import '../models/worker_upload_file.dart';
import 'worker_send_message_base.dart';

part 'worker_compress_video_input.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class WorkerCompressVideoInput extends WorkerSendMessageInputBase {
  WorkerCompressVideoInput({
    required this.uploadFileEmbed,
    required super.workspaceId,
    required super.channelId,
    required super.userId,
    required super.ref,
    required super.taskName,
    required super.attachmentType,
    required super.creationTime,
    super.isRefTask,
    this.messageRef,
  });

  final String uploadFileEmbed;

  final String? messageRef;

  @JsonKey(includeFromJson: false, includeToJson: false)
  WorkerUploadFile get file =>
      WorkerUploadFile.fromJson(jsonDecode(uploadFileEmbed));

  factory WorkerCompressVideoInput.fromJson(Map<String, dynamic> json) =>
      _$WorkerCompressVideoInputFromJson(json);

  Map<String, dynamic> toJson() => _$WorkerCompressVideoInputToJson(this);

  @override
  WorkerCompressVideoInput copyWith({
    String? uploadFileEmbed,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? ref,
    String? taskName,
    int? attachmentType,
    DateTime? creationTime,
    String? messageRef,
    bool? isRefTask,
  }) {
    return WorkerCompressVideoInput(
      uploadFileEmbed: uploadFileEmbed ?? this.uploadFileEmbed,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      userId: userId ?? this.userId,
      ref: ref ?? this.ref,
      taskName: taskName ?? this.taskName,
      attachmentType: attachmentType ?? this.attachmentType,
      creationTime: creationTime ?? this.creationTime,
      messageRef: messageRef ?? this.messageRef,
      isRefTask: isRefTask ?? this.isRefTask,
    );
  }
}
