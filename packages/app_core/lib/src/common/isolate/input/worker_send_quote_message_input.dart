import 'package:json_annotation/json_annotation.dart';

import 'worker_send_message_base.dart';

part 'worker_send_quote_message_input.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class WorkerSendQuoteMessageInput extends WorkerSendMessageInputBase {
  final String content;
  final String messageId;
  final String contentLocale;

  WorkerSendQuoteMessageInput({
    required this.content,
    required this.messageId,
    required this.contentLocale,
    required super.workspaceId,
    required super.channelId,
    required super.userId,
    required super.ref,
    required super.taskName,
    required super.attachmentType,
    required super.creationTime,
    super.isRefTask,
  });

  factory WorkerSendQuoteMessageInput.fromJson(Map<String, dynamic> json) =>
      _$WorkerSendQuoteMessageInputFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$WorkerSendQuoteMessageInputToJson(this);

  Map<String, dynamic> toRequest() {
    return isDm()
        ? {
            'userId': userId,
            'messageId': messageId,
            'content': content,
            'ref': ref,
            'contentLocale': contentLocale,
          }
        : {
            'workspaceId': workspaceId,
            'channelId': channelId,
            'messageId': messageId,
            'content': content,
            'ref': ref,
            'contentLocale': contentLocale,
          };
  }

  @override
  WorkerSendQuoteMessageInput copyWith({
    String? content,
    String? messageId,
    String? contentLocale,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? ref,
    String? taskName,
    int? attachmentType,
    DateTime? creationTime,
    bool? isRefTask,
  }) {
    return WorkerSendQuoteMessageInput(
      content: content ?? this.content,
      messageId: messageId ?? this.messageId,
      contentLocale: contentLocale ?? this.contentLocale,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      userId: userId ?? this.userId,
      ref: ref ?? this.ref,
      taskName: taskName ?? this.taskName,
      attachmentType: attachmentType ?? this.attachmentType,
      creationTime: creationTime ?? this.creationTime,
      isRefTask: isRefTask ?? this.isRefTask,
    );
  }
}
