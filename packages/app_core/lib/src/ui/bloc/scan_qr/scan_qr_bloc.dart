import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../domain/usecase/decode_user_connect_link_use_case.dart';

part 'scan_qr_bloc.freezed.dart';
part 'scan_qr_event.dart';
part 'scan_qr_state.dart';

@injectable
class ScanQrBloc extends BaseBloc<ScanQrEvent, ScanQrState> {
  ScanQrBloc(
    this._decodeUserConnectLinkUseCase,
  ) : super(ScanQrState.initial()) {
    on<QRDataDetectedEvent>(_onQRDataDetectedEvent);
    on<ResetScanQrStateEvent>(_onInitiateScanQrEvent);
  }

  final DecodeUserConnectLinkUseCase _decodeUserConnectLinkUseCase;

  Future<void> _onQRDataDetectedEvent(
    QRDataDetectedEvent event,
    Emitter<ScanQrState> emit,
  ) async {
    final qrValue = event.qrValue;

    emit(ScanQrState.processing(qrValue));

    if (qrValue.startsWith(EnvConfig.getQrAuthHost)) {
      emit(ScanQrState.authQRLoginDetected(qrValue));
      return;
    }

    if (qrValue.startsWith(EnvConfig.getScanToConnectHost)) {
      emit(ScanQrState.scanToConnectLinkDetected(qrValue));
      final userId = await _handleScanToConnectLink(qrValue);
      if (userId != null) {
        emit(ScanQrState.userConnectLinkDecodeSuccessful(userId));
      } else {
        emit(ScanQrState.invalidQRDetected(qrValue));
      }
      return;
    }

    if (qrValue.startsWith(EnvConfig.getInvitationHost)) {
      emit(ScanQrState.invitationDetected(qrValue));
      return;
    }

    emit(ScanQrState.done(qrValue));
  }

  FutureOr<void> _onInitiateScanQrEvent(
    ResetScanQrStateEvent event,
    Emitter<ScanQrState> emit,
  ) {
    emit(ScanQrState.initial());
  }

  Future<String?> _handleScanToConnectLink(String link) async {
    final output = await _decodeUserConnectLinkUseCase
        .execute(DecodeUserConnectLinkInput(link));
    return output.userId;
  }
}
