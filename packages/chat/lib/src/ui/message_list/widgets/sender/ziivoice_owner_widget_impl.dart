import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../../chat.dart';
import '../../../../data/repositories/database/classes/audio_metadata.dart';
import '../../../../data/repositories/extensions/message_extension.dart';
import '../../../../utils/waveform_manager.dart';
import '../base/base_sender_widget.dart';

class ZiiVoiceOwnerWidgetImpl extends StatefulWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isHiddenPin;
  final bool isLastMessage;

  const ZiiVoiceOwnerWidgetImpl({
    required this.isOpenCheckBox,
    this.onCheckBoxButtonTap,
    this.isShowCreateTime = false,
    this.isHiddenPin = false,
    this.isLastMessage = false,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    required this.messageItem,
    required this.message,
    super.key,
  });

  final MessageItem messageItem;
  final Message message;

  @override
  State<ZiiVoiceOwnerWidgetImpl> createState() =>
      _ZiiVoiceOwnerWidgetImplState();
}

class _ZiiVoiceOwnerWidgetImplState extends State<ZiiVoiceOwnerWidgetImpl>
    with AutomaticKeepAliveClientMixin {
  Message get _message => widget.message;

  MessageItem get _messageItem => widget.messageItem;

  MediaObject? get _file {
    if (_message.mediaAttachments.isEmpty) {
      return null;
    }
    return _message.mediaAttachments.last.voiceMessage!;
  }

  List<double>? _waveFormData;
  WaveformState _state = WaveformState.waiting;

  @override
  void initState() {
    super.initState();
    if (_file != null) {
      _loadAudioAndWaveform();
    }
  }

  Future<void> _loadAudioAndWaveform() async {
    if (_file!.audioMetadata != null && _file!.audioMetadata!.samples != null) {
      setState(() {
        _waveFormData = _file!.audioMetadata!.samples;
        _state = WaveformState.success;
      });
      return;
    }

    try {
      Log.e(
          name: '_ZiiVoiceOwnerWidgetImplState._loadAudioAndWaveform',
          _file!.filePath);
      final waveFormData = _file!.filePath != null
          ? await WaveformManager().getLocalWaveform(_file!.filePath!)
          : await _loadWaveFormFromURL();

      final updatedAudioMetadata = AudioMetadata(samples: waveFormData);
      final updatedFile = _file!.copyWith(audioMetadata: updatedAudioMetadata);

      final attachment = _message.mediaAttachments.first.copyWith(
        voiceMessageRaw: jsonEncode(updatedFile),
      );

      AppEventBus.publish(
        OnUpdateAttachmentEvent(
          workspaceId: _message.workspaceId,
          channelId: _message.channelId,
          messageId: _message.messageId,
          data: attachment.toJson(),
        ),
      );

      if (!mounted) return;

      setState(() {
        _waveFormData = waveFormData;
        _state = WaveformState.success;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _state = WaveformState.error;
      });
      throw Exception(e);
    }
  }

  Future<List<double>> _loadWaveFormFromURL() async {
    return await WaveformManager()
        .getRemoteWaveformRemote(UrlUtils.parseCDNUrl(_file!.fileUrl!));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (_file == null) {
      //TODO: handle file not found view
      return const SizedBox();
    }

    return ZiiVoiceOwnerExtendBase(
      isLastMessage: widget.isLastMessage,
      isHiddenPin: widget.isHiddenPin,
      message: _message,
      messageItem: _messageItem,
      isShowCreateTime: widget.isShowCreateTime,
      waveFormData: _waveFormData,
      state: _state,
      isCheckedMessage: widget.isCheckedMessage,
      onCheckBoxButtonTap: widget.onCheckBoxButtonTap,
      isOpenCheckBox: widget.isOpenCheckBox,
      isHighlighted: widget.isHighlighted,
      shouldAnimate: widget.shouldAnimate,
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class ZiiVoiceOwnerExtendBase extends BaseSenderWidget {
  ZiiVoiceOwnerExtendBase({
    required this.isOpenCheckBox,
    this.onCheckBoxButtonTap,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    required super.messageItem,
    required super.message,
    this.waveFormData,
    required this.isShowCreateTime,
    required this.isHiddenPin,
    required this.isLastMessage,
    this.state = WaveformState.waiting,
    super.key,
  });

  final List<double>? waveFormData;
  final WaveformState state;
  final bool isOpenCheckBox;
  final bool isShowCreateTime;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final bool isHiddenPin;
  final bool isLastMessage;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;

  @override
  Widget build(BuildContext context) {
    final media = message.mediaAttachments.last.voiceMessage!;
    final isHideOptionResend =
        media.filePath != null ? !File(media.filePath!).existsSync() : true;
    return ZiiVoiceMessageSenderWidget(
      isLastMessage: isLastMessage,
      messageItem: messageItem,
      onQuote: onQuote,
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      isShowCreateTime: isShowCreateTime,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      onMessageItemClicked: onMessageItemClicked,
      onListReactionClicked: onListReactionClicked,
      onEmojiClicked: onEmojiClicked,
      emojiList: message.emojiList,
      isHiddenPin: isHiddenPin,
      onDownload: (MessageItem messageItem) => onDownload(context),
      onResend: onResendMessage,
      onDiscard: onDiscardMessage,
      onCopy: (MessageItem messageItem) {},
      waveFormData: waveFormData,
      state: state,
      isCheckedMessage: isCheckedMessage,
      isOpenCheckBox: isOpenCheckBox,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      isHideOptionResend: isHideOptionResend,
      isHideOptionCopy: true,
      isHighlighted: isHighlighted,
      shouldAnimate: shouldAnimate,
    );
  }
}
