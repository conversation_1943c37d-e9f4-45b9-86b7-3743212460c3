part of 'user_status_bloc.dart';

@freezed
sealed class UserStatusState with _$UserStatusState {
  const UserStatusState._();

  factory UserStatusState.initial() = UserStatusStateInitial;

  factory UserStatusState.loaded({required List<ChatUser> users}) =
      UserStatusStateLoaded;

  factory UserStatusState.updatedStatus({required List<ChatUser> users}) =
      UserStatusStateUpdatedStatus;
}

extension UserStatusStateX on UserStatusState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(List<ChatUser> users)? loaded,
    T Function(List<ChatUser> users)? updatedStatus,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is UserStatusStateInitial && initial != null) {
      return initial();
    }
    if (state is UserStatusStateLoaded && loaded != null) {
      return loaded(state.users);
    }
    if (state is UserStatusStateUpdatedStatus && updatedStatus != null) {
      return updatedStatus(state.users);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(List<ChatUser> users) loaded,
    required T Function(List<ChatUser> users) updatedStatus,
  }) {
    final state = this;

    if (state is UserStatusStateInitial) {
      return initial();
    }

    if (state is UserStatusStateLoaded) {
      return loaded(state.users);
    }
    if (state is UserStatusStateUpdatedStatus) {
      return updatedStatus(state.users);
    }

    throw StateError('Unhandled UserStatusState: $state');
  }
}
