import 'package:auto_route/auto_route.dart';
import 'package:chat/chat.dart' as chat;
import 'package:flutter/material.dart';

@RoutePage()
class TransferOwnershipPage extends StatelessWidget {
  const TransferOwnershipPage({
    super.key,
    required this.isTransferAndLeave,
    required this.channelId,
    required this.workspaceId,
  });

  final bool isTransferAndLeave;
  final String channelId;
  final String workspaceId;

  @override
  Widget build(BuildContext context) {
    return chat.TransferOwnershipPage(
      isTransferAndLeave: isTransferAndLeave,
      channelId: channelId,
      workspaceId: workspaceId,
      onBack: () => onBack(context),
    );
  }

  void onBack(BuildContext context) {
    context.maybePop();
  }
}
