import 'dart:typed_data';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../../data/notification_data.dart';
import '../handler/notification/notification_avatar_handler.dart';

class AndroidProfileNotificationBuilder {
  static Future<AndroidNotificationDetails> createNotificationDetails(
    NotificationData data,
  ) async {
    final String channelId = data.userId!;
    final String channelName = data.channelName!;

    final Uint8List? largeIcon = data.getValidAvatar() != null
        ? await NotificationAvatarHandler()
            .getRoundedAvatar(data.getValidAvatar()!)
        : null;

    final styleInformation = await _buildStyleInformation(data);

    return AndroidNotificationDetails(
      channelId,
      channelName,
      importance: Importance.high,
      priority: Priority.high,
      playSound: true,
      sound: RawResourceAndroidNotificationSound('notification'),
      styleInformation: styleInformation,
      enableLights: true,
      enableVibration: true,
      largeIcon: largeIcon != null
          ? ByteArrayAndroidBitmap(largeIcon)
          : DrawableResourceAndroidBitmap('ic_user_null'),
      vibrationPattern: Int64List.fromList([0, 500, 1000, 500]),
      autoCancel: true,
    );
  }

  static Future<BigTextStyleInformation> _buildStyleInformation(
    NotificationData data,
  ) async {
    final String contentTitle = _resolveContentTitle(data);
    final String contentText = await data.getReplacementContent();

    return BigTextStyleInformation(
      contentText,
      contentTitle: contentTitle,
      htmlFormatContentTitle: false,
      htmlFormatBigText: false,
    );
  }

  static String _resolveContentTitle(NotificationData data) {
    if (data.channelName != null && data.channelName!.isNotEmpty) {
      return data.channelName!;
    }

    return data.username!;
  }
}
