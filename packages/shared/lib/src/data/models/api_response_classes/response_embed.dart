import 'package:json_annotation/json_annotation.dart';
import 'response_invitation_data.dart';

import 'response_embed_data.dart';
import 'response_location_data.dart';

part 'response_embed.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseEmbed {
  @<PERSON><PERSON><PERSON><PERSON>(name: 'meta')
  String? meta;

  @<PERSON><PERSON><PERSON>ey(name: 'provider')
  String? provider;

  @<PERSON>son<PERSON>ey(name: 'url')
  String? url;

  @<PERSON>son<PERSON>ey(name: 'type')
  int? type;

  @<PERSON>son<PERSON>ey(name: 'embedData')
  ResponseEmbedData? embedData;

  @<PERSON>son<PERSON>ey(name: 'invitationData')
  ResponseInvitationData? invitationData;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'locationData')
  ResponseLocationData? locationData;

  ResponseEmbed({
    this.meta,
    this.provider,
    this.url,
    this.type,
    this.embedData,
    this.invitationData,
    this.locationData,
  });

  factory ResponseEmbed.fromJson(Map<String, dynamic> json) =>
      _$ResponseEmbedFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseEmbedToJson(this);
}
