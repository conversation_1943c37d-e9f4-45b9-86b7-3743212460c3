import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';
import 'package:objectbox/objectbox.dart';

import '../../../../common/isolate/domain/models/task_model.dart';
import '../../../../common/isolate/domain/models/task_priority.dart';

part 'task_entity.g.dart';

@Entity()
@JsonSerializable(explicitToJson: true)
class TaskEntity {
  TaskEntity({
    required this.taskId,
    required this.name,
    required this.inputDataJson,
    required this.maxRetries,
    required this.retryDelay,
    required this.timeout,
    this.retryCount = 0,
    this.statusValue = 0, // TaskStatus.pending
    this.priorityValue = 1, // TaskPriority.medium
    required this.createdAt,
    this.completedAt,
    this.isReferenceTask = false,
    this.networkRequired = true,
  });

  @Id(assignable: true)
  @JsonKey(defaultValue: 0)
  int id = 0;

  @Property(uid: 20001)
  String taskId;

  @Property(uid: 20002)
  String name;

  @Property(uid: 20003)
  String inputDataJson;

  @Property(uid: 20004)
  int maxRetries;

  @Property(uid: 20005)
  int retryDelay;

  @Property(uid: 20006)
  int timeout;

  @Property(uid: 20007)
  int retryCount;

  @Property(uid: 20008)
  int statusValue;

  @Property(uid: 20009)
  int priorityValue;

  @Property(uid: 20010)
  int createdAt;

  @Property(uid: 20011)
  int? completedAt;

  @Property(uid: 20012)
  bool isReferenceTask;

  @Property(uid: 20013)
  bool networkRequired;

  // Chuyển đổi từ TaskModel sang TaskEntity
  factory TaskEntity.fromTaskModel(TaskModel model) {
    return TaskEntity(
      taskId: model.id,
      name: model.name,
      inputDataJson: jsonEncode(model.inputData),
      maxRetries: model.maxRetries,
      retryDelay: model.retryDelay,
      timeout: model.timeout,
      retryCount: model.retryCount,
      statusValue: model.status.index,
      priorityValue: model.priority.toValue(),
      createdAt: model.createdAt,
      completedAt: model.completedAt,
      isReferenceTask: model.isReferenceTask,
      networkRequired: model.networkRequired,
    );
  }

  // Chuyển đổi từ TaskEntity sang TaskModel
  TaskModel toTaskModel() {
    Map<String, dynamic> inputDataMap;
    try {
      inputDataMap = jsonDecode(inputDataJson);
    } catch (e) {
      inputDataMap = {};
    }

    return TaskModel(
      id: taskId,
      name: name,
      inputData: inputDataMap,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
      timeout: timeout,
      retryCount: retryCount,
      status: TaskStatus.values[statusValue],
      priority: TaskPriorityExtension.fromValue(priorityValue),
      createdAt: createdAt,
      completedAt: completedAt,
      isReferenceTask: isReferenceTask,
      networkRequired: networkRequired,
    );
  }

  factory TaskEntity.fromJson(Map<String, dynamic> json) =>
      _$TaskEntityFromJson(json);
  Map<String, dynamic> toJson() => _$TaskEntityToJson(this);
}
