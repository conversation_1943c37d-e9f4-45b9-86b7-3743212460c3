import 'dart:async';

import 'package:chat/chat.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

@Injectable()
class GetStreamMembersUseCase
    extends BaseSyncUseCase<GetStreamMembersInput, GetStreamMembersOutput> {
  GetStreamMembersUseCase(this._memberRepository, this._loadAllMemberUseCase);

  final MemberRepository _memberRepository;
  final LoadAllMemberUseCase _loadAllMemberUseCase;

  @override
  GetStreamMembersOutput buildUseCase(GetStreamMembersInput input) {
    _loadAllMemberUseCase.execute(
      LoadAllMemberInput(
        workspaceId: input.workspaceId,
        channelId: input.channelId,
      ),
    );
    return GetStreamMembersOutput(
      streamMembers: _memberRepository
          .getStreamMembers(
            workspaceId: input.workspaceId,
            channelId: input.channelId,
          )
          .map((members) => members.map((member) => member.toJson()).toList()),
    );
  }
}

class GetStreamMembersInput extends BaseInput {
  GetStreamMembersInput({
    required this.channelId,
    required this.workspaceId,
  });

  final String channelId;
  final String workspaceId;
}

class GetStreamMembersOutput extends BaseOutput {
  GetStreamMembersOutput({this.streamMembers});

  Stream<List<Map<String, dynamic>>>? streamMembers;
}
