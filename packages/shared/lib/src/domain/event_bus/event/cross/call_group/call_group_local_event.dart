import '../../base/base_event.dart';

class CallGroupLocalEvent extends BaseEvent {
  final String channelId;
  final String workspaceId;

  CallGroupLocalEvent({
    required this.channelId,
    required this.workspaceId,
    required super.id,
    super.source = BaseEvent.LOCAL_SOURCE,
  });

  @override
  Map<String, dynamic> toJson() =>
      {"channelId": channelId, "workspaceId": workspaceId};
}

class ShowMinimizedCallGroupEvent extends CallGroupLocalEvent {
  final String? channelName;
  final int? numberParticipants;

  ShowMinimizedCallGroupEvent({
    required super.channelId,
    required super.workspaceId,
    super.id = 'SHOW_MINIMIZE_CALL_GROUP',
    this.channelName,
    this.numberParticipants,
  });
}

class HideMinimizedCallGroupEvent extends CallGroupLocalEvent {
  HideMinimizedCallGroupEvent({
    super.id = 'HIDE_MINIMIZE_CALL_GROUP',
    required super.channelId,
    required super.workspaceId,
  });
}
