import 'package:json_annotation/json_annotation.dart';

part 'embed.g.dart';

@JsonSerializable(explicitToJson: true)
class Embed {
  Embed({
    this.avatar,
    this.channelName,
    this.aliasName,
    this.displayName,
    this.username,
    this.userBadgeType,
  });

  String? avatar;

  // channel
  String? channelName;

  // user
  String? aliasName;
  String? displayName;
  String? username;
  int? userBadgeType;

  factory Embed.fromJson(Map<String, dynamic> json) => _$EmbedFromJson(json);

  Map<String, dynamic> toJson() => _$EmbedToJson(this);
}
