import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:friend_view_api/friend_view_api.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/database/enums/chat_friend_status.dart';
import '../chat_user/upsert_chat_friends_use_case.dart';

part 'get_friend_requests_use_case.freezed.dart';

@Injectable()
class GetFriendRequestUseCase
    extends BaseFutureUseCase<GetFriendRequestInput, GetFriendRequestOutput> {
  const GetFriendRequestUseCase();

  @override
  Future<GetFriendRequestOutput> buildUseCase(
    GetFriendRequestInput input,
  ) async {
    final sessionKey = Config.getInstance().activeSessionKey;

    try {
      final result =
          await FriendViewClient().instance.listInComingFriendRequests();

      if (result.data?.ok != true) {
        return GetFriendRequestOutput(friends: null);
      }

      final friends = result.data!.data!.map((v3FrienData) {
        final json = jsonDecode(
          standardSerializers.toJson(V3Friend.serializer, v3FrienData.friend!),
        ) as Map<String, dynamic>;
        json['sessionKey'] = sessionKey;
        return ChatFriend.fromJson(json);
      }).toList();

      await GetIt.instance.get<UpsertChatFriendsUseCase>().execute(
            UpsertChatFriendsInput(friends: friends),
          );
      final jsonIncludes = jsonDecode(
        standardSerializers.toJson(
          V3DataInclude.serializer,
          result.data!.includes,
        ),
      );
      List<ChatUser> users = [];

      var responseIncludes = ResponseIncludes.fromJson(jsonIncludes);
      final usersIncludes = responseIncludes.users ?? [];
      for (final userInclude in usersIncludes) {
        final userSer =
            ChatUserSerializer.serializeFromJson(data: userInclude.toJson());
        if (userSer != null && userSer.userId != sessionKey) {
          final filterFriends = friends.where(
            (element) {
              if (element.participantIds == null) return false;
              return element.participantIds!.contains(userSer.userId);
            },
          ).toList();
          if (filterFriends.isEmpty) continue;

          userSer
            ..chatFriendDataRaw = jsonEncode(
              ChatFriendData(
                status: ChatFriendStatusEnumExtension.getEnumByValue(
                  filterFriends.first.status,
                ),
              ).toJson(),
            );
          users.add(userSer);
        }
      }

      await GetIt.instance.get<UpsertChatUsersUseCase>().execute(
            UpsertChatUsersInput(users: users),
          );
      return GetFriendRequestOutput(friends: users);
    } on Exception catch (_) {
      return GetFriendRequestOutput(friends: null);
    }
  }
}

@freezed
sealed class GetFriendRequestInput extends BaseInput
    with _$GetFriendRequestInput {
  const GetFriendRequestInput._();
  factory GetFriendRequestInput() = _GetFriendRequestInput;
}

@freezed
sealed class GetFriendRequestOutput extends BaseOutput
    with _$GetFriendRequestOutput {
  const GetFriendRequestOutput._();
  factory GetFriendRequestOutput({
    @Default(null) List<ChatUser>? friends,
  }) = _GetFriendRequestOutput;
}
