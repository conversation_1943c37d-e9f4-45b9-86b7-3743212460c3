ICLOUD_ID=iCloud.com.ziichat.ios.media.flutter.cloud
BUNDLE_ID_ANDROID=com.ziichat.android.media.beta
BUNDLE_ID_IOS=com.ziichat.ios.media.flutter
CLIENT_ID_ENV=x-client-id
CLIENT_ID_ANDROID=android_5d4f9e3ad69f5435629f99da60c643a0
CLIENT_ID_IOS=
API_HOST=https://api-sb11.rpc.ziichat.dev
#API_HOST=https://api-sb00.rpc.ziichat.dev
STICKER_HOST=https://stickers-sb.ziicdn.net/
AVATAR_HOST=https://avatars-sb.ziicdn.net/
CDN_HOST=https://fs.ugc.ziicdn.net/
AUTH_HOST=https://ziichat.com/.well-known/assetlinks.json
INVITATION_HOST=https://zii.chat/i/
SCAN_TO_CONNECT_HOST=https://zii.chat/connect/
QR_AUTH_HOST=https://zii.chat/qrauth/
SEND_LINK=https://zii.chat/send?content=
FILESTORE_HOST=https://filestore-sb.ziichat.dev/
DEBUG_API=true
USE_REMOTE_CONFIG=false
API_VERSION=stable
API_HEADER_ENV=x-api-env
API_VERSION_NUMBER=0.0.1
API_AUTHOR=ZiiChat
FS_ADDRESS_CALL=caller-sb.ziichat.dev
