import 'dart:io';

import 'package:app_core/core.dart';
import 'package:auto_route/auto_route.dart';
import 'package:device_info_plus/device_info_plus.dart'; // Ensure this import is present
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:url_launcher/url_launcher.dart';

@RoutePage()
class DebugPage extends StatelessWidget {
  const DebugPage({super.key});

  static const debugUrl = 'http://127.0.0.1:8090';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Debug Tools')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          // Added to prevent overflow
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Hero(
                tag: 'debug-tag',
                child: Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildThemeSwitch(context),
                        const SizedBox(height: 20),
                        _buildLanguageSelector(context),
                        if (Platform.isAndroid) const SizedBox(height: 20),
                        if (Platform.isAndroid)
                          _buildDebugDatabaseButton(context),
                        if (Platform.isIOS) const SizedBox(height: 20),
                        if (Platform.isIOS) _buildIOSDebugAuth(context),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildThemeSwitch(BuildContext context) {
    final localization = AppLocalizations.of(context)!;
    final appState = context.watch<AppBloc>().state;
    String systemTheme =
        MediaQuery.of(context).platformBrightness == Brightness.dark
            ? localization.dark
            : localization.light;

    final Map<ThemeMode, Widget> themeModes = {
      ThemeMode.light: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Text(localization.light),
      ),
      ThemeMode.dark: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Text(localization.dark),
      ),
      ThemeMode.system: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Text(
          '${localization.system} ($systemTheme)',
          softWrap: false,
        ),
      ),
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            localization.theme,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        CupertinoSegmentedControl<ThemeMode>(
          children: themeModes,
          onValueChanged: (ThemeMode? mode) {
            if (mode != null) {
              context.read<AppBloc>().add(AppThemeChanged(themeMode: mode));
            }
          },
          groupValue: appState.themeMode,
        ),
      ],
    );
  }

  Widget _buildLanguageSelector(BuildContext context) {
    final localization = AppLocalizations.of(context)!;
    final appState = context.watch<AppBloc>().state;
    final List<String> supportedLocales = AppLocalizations.supportedLocales
        .map((locale) => locale.languageCode)
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            localization.language,
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        DropdownButton<String>(
          value: appState.locale.languageCode,
          onChanged: (String? lang) {
            if (lang != null) {
              context
                  .read<AppBloc>()
                  .add(AppLanguageChanged(locale: Locale(lang)));
            }
          },
          items: supportedLocales.map<DropdownMenuItem<String>>((String code) {
            return DropdownMenuItem<String>(
              value: code,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  code.toUpperCase(), // Display in uppercase for better readability
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDebugDatabaseButton(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            'Debug Database (Android only)',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        MaterialButton(
          onPressed: () async {
            final Uri url = Uri.parse(debugUrl);
            if (await canLaunchUrl(url)) {
              await launchUrl(url);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Could not launch $debugUrl')),
              );
            }
          },
          color: Theme.of(context).colorScheme.primary,
          textColor: Colors.white,
          child: const Text('Open Debug'),
        ),
      ],
    );
  }

  Widget _buildIOSDebugAuth(BuildContext context) {
    return IOSDebugAuthWidget();
  }
}

class IOSDebugAuthWidget extends StatefulWidget {
  @override
  _IOSDebugAuthWidgetState createState() => _IOSDebugAuthWidgetState();
}

class _IOSDebugAuthWidgetState extends State<IOSDebugAuthWidget> {
  final List<String> allIosVersions = [
    '15.5',
    '16.0',
    '17.0',
    '18.0',
    '18.2',
  ];

  String? currentVersion;
  List<String> filteredIosVersions = [];
  String? selectedVersion;
  bool isDebugAuthEnabled = false;

  @override
  void initState() {
    super.initState();
    _initCurrentVersion();
  }

  /// Initializes the current iOS version and filters the versions list.
  Future<void> _initCurrentVersion() async {
    if (!Platform.isIOS) return;

    try {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      String systemVersion = iosInfo.systemVersion; // e.g., "16.3.1"

      // Parse the system version into a list of integers
      List<int> currentVersionParts = _parseVersion(systemVersion);

      // Filter the allIosVersions list to include only versions <= currentVersion
      filteredIosVersions = allIosVersions.where((version) {
        List<int> versionParts = _parseVersion(version);
        return _isVersionLessOrEqual(versionParts, currentVersionParts);
      }).toList();

      // Prepend 'Default' option
      filteredIosVersions.insert(0, 'Default');

      // Load saved preferences using DebugAuthUtils
      bool savedDebugAuth = await DebugAuthUtils.getDebugAuthEnabled();
      String? savedSelectedVersion =
          await DebugAuthUtils.getSelectedIosVersion();

      setState(() {
        currentVersion = systemVersion;
        isDebugAuthEnabled = savedDebugAuth;
        selectedVersion =
            savedDebugAuth ? (savedSelectedVersion ?? 'Default') : null;
      });
    } catch (e) {
      // Handle any errors (e.g., unable to fetch device info)
      setState(() {
        currentVersion = 'Unknown';
        filteredIosVersions = ['Default'];
        selectedVersion = 'Default';
        isDebugAuthEnabled = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to fetch device information.')),
      );
      print('Error fetching device info: $e'); // Log the error
    }
  }

  /// Parses a version string into a list of integers.
  /// Example: "16.3.1" => [16, 3, 1]
  List<int> _parseVersion(String version) {
    return version.split('.').map((part) {
      int value = 0;
      try {
        value = int.parse(part);
      } catch (e) {
        value = 0;
      }
      return value;
    }).toList();
  }

  /// Compares two version lists.
  /// Returns true if version1 <= version2, false otherwise.
  bool _isVersionLessOrEqual(List<int> version1, List<int> version2) {
    int maxLength =
        version1.length > version2.length ? version1.length : version2.length;
    for (int i = 0; i < maxLength; i++) {
      int part1 = i < version1.length ? version1[i] : 0;
      int part2 = i < version2.length ? version2[i] : 0;
      if (part1 < part2) {
        return true;
      } else if (part1 > part2) {
        return false;
      }
    }
    return true;
  }

  /// Saves the Debug Auth state and selected version using DebugAuthUtils.
  Future<void> _savePreferences() async {
    await DebugAuthUtils.setDebugAuthEnabled(isDebugAuthEnabled);
    if (isDebugAuthEnabled && selectedVersion != null) {
      await DebugAuthUtils.setSelectedIosVersion(selectedVersion!);
    } else {
      await DebugAuthUtils.clearSelectedIosVersion();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Debug Auth Switch
        SwitchListTile(
          title: Text(
            'Enable Debug Auth',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          value: isDebugAuthEnabled,
          onChanged: (bool value) {
            setState(() {
              isDebugAuthEnabled = value;
              if (!isDebugAuthEnabled) {
                selectedVersion = null;
              } else {
                selectedVersion = selectedVersion ?? 'Default';
              }
            });
            _savePreferences();
          },
          secondary: Icon(Icons.security),
        ),
        const SizedBox(height: 10),
        // Show version selection only if Debug Auth is enabled
        if (isDebugAuthEnabled)
          currentVersion == null
              ? const Center(child: CircularProgressIndicator())
              : Column(
                  children: filteredIosVersions.map((version) {
                    bool isDefault = version == 'Default';
                    String displayText = version;

                    if (isDefault &&
                        currentVersion != null &&
                        currentVersion != 'Unknown') {
                      displayText = 'Default ($currentVersion)';
                    }

                    return RadioListTile<String>(
                      title: Text(
                        displayText,
                        style: TextStyle(
                          fontWeight:
                              isDefault ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      value: version,
                      groupValue: selectedVersion,
                      onChanged: (String? value) {
                        setState(() {
                          selectedVersion = value;
                        });
                        _savePreferences();
                      },
                    );
                  }).toList(),
                ),
      ],
    );
  }
}
