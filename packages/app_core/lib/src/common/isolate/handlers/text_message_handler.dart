import '../input/worker_send_text_input.dart';
import '../output/worker_send_message_output.dart';
import 'base_message_handler.dart';

/// Specialized handler for text messages
class TextMessageHandler extends BaseMessageHandler<WorkerSendTextInput> {
  TextMessageHandler({
    required super.apiClient,
    required super.retryManager,
  });

  @override
  WorkerSendTextInput parseInput(Map<String, dynamic> inputData) {
    return WorkerSendTextInput.fromJson(inputData);
  }

  @override
  String getApiEndpoint(WorkerSendTextInput input) {
    return '/Message/${input.isDm() ? 'SendDMMessage' : 'SendMessage'}';
  }

  @override
  Future<WorkerSendMessageOutput> executeMessageOperation(
    WorkerSendTextInput input,
    DateTime createTime,
  ) async {
    return await makeRetryableApiCall(
      input: input,
      createTime: createTime,
      apiCall: () => apiClient.post(
        getApiEndpoint(input),
        body: input.toRequest(),
      ),
    );
  }

  /// Legacy method for backward compatibility
  Future<WorkerSendMessageOutput> handleTextMessage(
    Map<String, dynamic> inputData,
  ) async {
    return await handleMessage(inputData);
  }
}
