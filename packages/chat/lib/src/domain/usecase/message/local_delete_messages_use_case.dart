import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class LocalDeleteMessagesUseCase extends BaseFutureUseCase<
    LocalDeleteMessagesInput, LocalDeleteMessagesOutput> {
  LocalDeleteMessagesUseCase(
    this._messageRepository,
  );

  final MessageRepository _messageRepository;

  @override
  Future<LocalDeleteMessagesOutput> buildUseCase(
    LocalDeleteMessagesInput input,
  ) async {
    var result = _messageRepository.deleteMessageByIds(
      workspaceId: input.workspaceId,
      channelId: input.channelId,
      messageIds: input.messageIds,
    );
    return LocalDeleteMessagesOutput(ok: result < 0 ? false : true);
  }
}

class LocalDeleteMessagesInput extends BaseInput {
  LocalDeleteMessagesInput({
    required this.workspaceId,
    required this.channelId,
    required this.messageIds,
  });

  final String workspaceId;
  final String channelId;
  final List<String> messageIds;
}

class LocalDeleteMessagesOutput extends BaseOutput {
  LocalDeleteMessagesOutput({
    required this.ok,
  });

  final bool ok;
}
