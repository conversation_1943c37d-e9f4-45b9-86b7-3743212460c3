import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../common/di/di.dart';
import 'bloc/message_request_bloc.dart';

class WarningWidget extends StatelessWidget {
  const WarningWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MessageRequestBloc, MessageRequestState>(
      buildWhen: (prev, current) => current.closedWarning != prev.closedWarning,
      builder: (context, state) {
        if (state.closedWarning) {
          return SizedBox.shrink();
        }
        return ui.MessageRequestWarningWidget(
          onCancelButtonClicked: () {
            getIt<MessageRequestBloc>().add(
              UpdatedClosedWarningStatusEvent(closedWarningStatus: true),
            );
          },
          onTapGotoManageMessages: () {},
        );
      },
    );
  }
}
