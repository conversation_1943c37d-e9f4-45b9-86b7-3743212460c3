import 'package:json_annotation/json_annotation.dart';

part 'response_friend_request.g.dart';

@JsonSerializable(explicitToJson: true)
class ResponseFriendRequest {
  final String friendId;
  final String? requestedFromUserId;
  final String? requestedToUserId;
  final List<String>? participantIds;
  final int? status;
  final String? readTime;
  final String? acceptTime;
  final String? createTime;
  final String? updateTime;

  ResponseFriendRequest({
    required this.friendId,
    this.requestedFromUserId,
    this.requestedToUserId,
    this.participantIds,
    this.status,
    this.readTime,
    this.acceptTime,
    this.createTime,
    this.updateTime,
  });

  factory ResponseFriendRequest.fromJson(Map<String, dynamic> json) =>
      _$ResponseFriendRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ResponseFriendRequestToJson(this);
}
