// extensions.dart
import 'package:channel_view_api/channel_view_api.dart';

extension V3DataIncludeExtensions on V3DataInclude {
  V3User? getUserById(String userId) {
    return users?.firstWhere((user) => user.userId == userId, orElse: null);
  }

  V3Message? getMessageById(String messageId) {
    return messages?.firstWhere(
      (message) => message.messageId == messageId,
      orElse: null,
    );
  }

  V3Channel? getChannelById(String channelId) {
    return channels?.firstWhere(
      (channel) => channel.channelId == channelId,
      orElse: null,
    );
  }

  V3Member? getMemberById(
    String workspaceId,
    String channelId,
    String memberId,
  ) {
    return members?.firstWhere(
      (member) =>
          member.workspaceId == workspaceId &&
          member.channelId == channelId &&
          member.userId == memberId,
      orElse: null,
    );
  }
}
