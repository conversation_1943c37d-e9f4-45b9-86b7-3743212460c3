import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:talk_and_translate/talk_and_translate.dart';

@injectable
class TranslateUseCase
    extends BaseFutureUseCase<TranslateUseCaseInput, TranslateUseCaseOutput> {
  TranslateUseCase();

  @override
  Future<TranslateUseCaseOutput> buildUseCase(
    TranslateUseCaseInput input,
  ) async {
    try {
      final translatedText = await input.talkAndTranslateProvider.translate(
        text: input.text,
      );
      return TranslateUseCaseOutput(translatedText: translatedText);
    } catch (e) {
      throw Exception('Error while translate: $e');
    }
  }
}

class TranslateUseCaseInput extends BaseInput {
  TranslateUseCaseInput({
    required this.text,
    required this.talkAndTranslateProvider,
  });

  final String text;
  final TalkAndTranslateProvider talkAndTranslateProvider;
}

class TranslateUseCaseOutput extends BaseOutput {
  TranslateUseCaseOutput({
    required this.translatedText,
  });
  final String translatedText;
}
