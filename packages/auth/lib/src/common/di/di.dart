import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';

import '../../data/repositories/database/auth_database.dart';
import '../../data/repositories/database/generated/objectbox.g.dart';
import 'di.config.dart';

final GetIt getIt = GetIt.instance;

@injectableInit
Future<void> configureInjection() async {
  final dir = await getApplicationSupportDirectory();

  final authStore =
      AuthStore(getObjectBoxModel(), directory: '${dir.path}/auth_store');
  AuthDatabase(authStore);
  getIt
    ..registerSingleton<AuthStore>(authStore)
    ..init();
}
