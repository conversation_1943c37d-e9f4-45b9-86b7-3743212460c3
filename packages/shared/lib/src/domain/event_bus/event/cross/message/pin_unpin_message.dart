import '../../../../../../shared.dart';

class CallPinUnPinMessageEvent extends LocalEvent {
  CallPinUnPinMessageEvent({
    required this.workspaceId,
    required this.channelId,
    required this.isChannel,
    required this.messageId,
    required this.status,
    super.source = BaseEvent.LOCAL_SOURCE,
  });

  final String workspaceId;
  final String channelId;
  final bool isChannel;
  final String messageId;
  final bool status;

  @override
  Map<String, dynamic> toJson() => {
        'workspaceId': workspaceId,
        'channelId': channelId,
        'isChannel': isChannel,
        'messageId': messageId,
        'status': status,
      };
}
