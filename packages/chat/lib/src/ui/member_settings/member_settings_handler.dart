import 'package:flutter/cupertino.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../domain/usecase/member/assign_as_admin_user_case.dart';
import '../../domain/usecase/member/dismiss_as_admin_user_case.dart';
import '../../domain/usecase/member/remove_member_from_channel_use_case.dart';
import '../../domain/usecase/member/update_nickname_use_case.dart';
import 'model_member_setting_profile.dart';

class MemberSettingsHandler {
  static Roles? getRoleFromName(String? roleName) {
    if (roleName == null) {
      return null;
    }
    if (roleName == 'owner') {
      return Roles.owner;
    }
    if (roleName == 'admin') {
      return Roles.admin;
    }

    return Roles.member;
  }

  void showMemberSettings({
    required BuildContext context,
    required MemberSettingSourcePage sourcePage,
    required ModelMemberSettingProfile myProfile,
    required ModelMemberSettingProfile memberProfile,
    void Function(String userId)? goToDMMessage,
    void Function(String avatarUrl)? goToViewAvatar,
  }) {
    BottomSheetUtil.showMemberSettingBottomSheet(
      context,
      memberProfile: memberProfile.toItemMemberSettingProfile(),
      myProfile: myProfile.toItemMemberSettingProfile(),
      memberIsGhost: memberProfile.username.toLowerCase() ==
          GlobalConfig.ghost.toLowerCase(),
      onClickMessage: (profile) {
        _onPopToSourcePage(sourcePage);
        goToDMMessage?.call(profile.userId);
      },
      onClickViewAvatar: (profile) {
        _onPopToSourcePage(sourcePage);
        goToViewAvatar?.call(profile.url!);
      },
      onClickChangeNickname: (_) {
        _onChangeNickname(
          context,
          memberProfile,
          memberProfile.nickname ?? '',
          sourcePage,
        );
      },
      onClickViewProfile: (_) {
        _onPopToSourcePage(sourcePage);
        AppEventBus.publish(
          OnGoToUserProfileEvent(userId: memberProfile.userId),
        );
      },
      onClickAssignAsAdmin: (_) {
        _onAssignAsAdmin(
          context,
          memberProfile,
          sourcePage,
        );
      },
      onClickRemoveAsAdmin: (_) {
        _onRemoveAsAdmin(
          context,
          memberProfile,
          sourcePage,
        );
      },
      onClickRemoveFromChannel: (_) {
        _onRemoveFromChannel(
          context,
          memberProfile,
          sourcePage,
        );
      },
    );
  }

  void _onChangeNickname(
    BuildContext context,
    ModelMemberSettingProfile profile,
    String initNickname,
    MemberSettingSourcePage sourcePage,
  ) {
    BottomSheetUtil.showChangeNickNameBottomSheet(
      context: context,
      onPressedCancel: () {
        Navigator.pop(context);
      },
      onPressedSave: (nickname) {
        _onPopToSourcePage(sourcePage);
        GetIt.instance.get<UpdateNickNameUseCase>().execute(
              UpdateNickNameInput(
                workspaceId: profile.workspaceId!,
                channelId: profile.channelId!,
                userId: profile.userId,
                nickname: nickname,
              ),
            );
      },
      nickName: () {
        return initNickname;
      },
    );
  }

  void _onAssignAsAdmin(
    BuildContext context,
    ModelMemberSettingProfile profile,
    MemberSettingSourcePage sourcePage,
  ) {
    ActionSheetUtil.showAssignAsAdminActionSheet(
      context,
      onAssign: () {
        _onPopToSourcePage(sourcePage);
        GetIt.instance.get<AssignAsAdminUseCase>().execute(
              AssignAsAdminInput(
                workspaceId: profile.workspaceId!,
                channelId: profile.channelId!,
                userId: profile.userId,
              ),
            );
      },
      onCancel: () {
        _onPopToSourcePage(sourcePage);
      },
    );
  }

  void _onRemoveAsAdmin(
    BuildContext context,
    ModelMemberSettingProfile profile,
    MemberSettingSourcePage sourcePage,
  ) {
    ActionSheetUtil.showRemoveAsAdminActionSheet(
      context,
      onRemove: () {
        _onPopToSourcePage(sourcePage);
        GetIt.instance.get<DismissAsAdminUseCase>().execute(
              DismissAsAdminInput(
                workspaceId: profile.workspaceId!,
                channelId: profile.channelId!,
                userId: profile.userId,
              ),
            );
      },
      onCancel: () {
        _onPopToSourcePage(sourcePage);
      },
    );
  }

  void _onRemoveFromChannel(
    BuildContext context,
    ModelMemberSettingProfile profile,
    MemberSettingSourcePage sourcePage,
  ) {
    ActionSheetUtil.showRemoveFromChannelActionSheet(
      context,
      onRemove: () {
        _onPopToSourcePage(sourcePage);
        GetIt.instance.get<RemoveMemberFromChannelUseCase>().execute(
              RemoveMemberFromChannelInput(
                userId: profile.userId,
                channelId: profile.channelId!,
                workspaceId: profile.workspaceId!,
              ),
            );
      },
      onCancel: () {
        _onPopToSourcePage(sourcePage);
      },
      username: profile.name,
    );
  }

  void _onPopToSourcePage(MemberSettingSourcePage sourcePage) {
    switch (sourcePage) {
      case MemberSettingSourcePage.channelView:
        AppEventBus.publish(PopToChannelViewEvent());
        return;
      case MemberSettingSourcePage.channelInfo:
        AppEventBus.publish(PopToChannelInfoEvent());
        return;
    }
  }
}

enum MemberSettingSourcePage { channelView, channelInfo }
