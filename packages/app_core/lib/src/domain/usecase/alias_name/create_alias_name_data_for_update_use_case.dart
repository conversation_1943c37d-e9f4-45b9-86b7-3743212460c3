import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../core.dart';
import '../../../data/interface/private_data_sync.dart';
import '../../../data/interface/sync_key_enum.dart';

part 'create_alias_name_data_for_update_use_case.freezed.dart';

@Injectable()
class CreateAliasNameForUpdateUseCase extends BaseFutureUseCase<
    CreateAliasNameForUpdateInput, CreateAliasNameForUpdateOutput> {
  const CreateAliasNameForUpdateUseCase(this._repository);

  final PrivateDataRepository _repository;

  @protected
  @override
  Future<CreateAliasNameForUpdateOutput> buildUseCase(
    CreateAliasNameForUpdateInput input,
  ) async {
    try {
      var privateUserData = _repository.getUser(input.userId);

      if (privateUserData == null) {
        privateUserData = UserPrivateData(
          sessionKey: Config.getInstance().activeSessionKey ?? '',
          userId: input.userId,
          blocked: false,
          source: '',
          dmId: input.userId,
          version: 0,
          aliasName: input.newAliasName,
        );
      } else {
        privateUserData..aliasName = input.newAliasName;
        privateUserData..version = privateUserData.version + 1;
      }

      _repository.upsert(privateUserData);

      var cloudEvent = CloudEvent.createPrivateDataSync(
        PrivateDataSync(
          key: SyncKeyEnum.users.name,
          value: privateUserData.toJson(),
        ).toJson(),
      );

      return CreateAliasNameForUpdateOutput(cloudEvent: cloudEvent.toJson());
    } on Exception catch (_) {
      return CreateAliasNameForUpdateOutput(cloudEvent: null);
    }
  }
}

@freezed
sealed class CreateAliasNameForUpdateInput extends BaseInput
    with _$CreateAliasNameForUpdateInput {
  const CreateAliasNameForUpdateInput._();
  factory CreateAliasNameForUpdateInput({
    required String userId,
    required String newAliasName,
  }) = _CreateAliasNameForUpdateInput;
}

@freezed
sealed class CreateAliasNameForUpdateOutput extends BaseOutput
    with _$CreateAliasNameForUpdateOutput {
  const CreateAliasNameForUpdateOutput._();
  factory CreateAliasNameForUpdateOutput({
    required Map<String, dynamic>? cloudEvent,
  }) = _CreateAliasNameForUpdateOutput;
}
