// base_message_handler.dart
import 'package:flutter/cupertino.dart';
import 'package:shared/shared.dart';

import '../../../data/source/api/client/isolate_api_client.dart';
import '../../isolate/data/stores/shared_preferences_store.dart';
import '../../network/retry_manager.dart';
import '../exceptions/send_message_timeout_exception.dart';
import '../input/worker_send_message_base.dart';
import '../output/worker_send_message_output.dart';
import '../resilient_isolate.dart';
import 'worker_send_result_handler.dart';

/// Base class for handling message operations with common validation logic
/// T must extend WorkerSendMessageInputBase to ensure common properties are available
abstract class BaseMessageHandler<T extends WorkerSendMessageInputBase> {
  final IsolateApiClient apiClient;
  final RetryManager retryManager;

  BaseMessageHandler({
    required this.apiClient,
    required this.retryManager,
  });

  /// Template method for handling message operations
  Future<WorkerSendMessageOutput> handleMessage(
    Map<String, dynamic> inputData,
  ) async {
    debugPrint('BaseMessageHandler.handleMessage: 1');
    final input = parseInput(inputData);
    final createTime = _extractCreationTime(inputData, input);

    // Validate task using common checks (ordered by performance cost)
    final validationResult = await _validateTask(input);
    debugPrint('BaseMessageHandler.handleMessage: 2');
    if (validationResult != null) return validationResult;

    debugPrint('BaseMessageHandler.handleMessage: 3');
    // Execute the specific message handling logic
    return await executeMessageOperation(input, createTime);
  }

  /// Parse input data into specific input type - must be implemented by subclasses
  T parseInput(Map<String, dynamic> inputData);

  /// Execute the actual message operation (API call) - must be implemented by subclasses
  Future<WorkerSendMessageOutput> executeMessageOperation(
    T input,
    DateTime createTime,
  );

  /// Get API endpoint for the message type - must be implemented by subclasses
  String getApiEndpoint(T input);

  /// Get timeout duration for the specific message type (can be overridden)
  Duration getTimeoutDuration() => GlobalConfig.sendTimeoutDuration;

  /// Perform comprehensive task validation in optimal order
  Future<WorkerSendMessageOutput?> _validateTask(T input) async {
    // 1. Timeout check - fastest (no DB access)
    final timeoutResult = await _checkMessageTimeout(input);
    if (timeoutResult != null) return timeoutResult;

    // 2. Already sent check - medium speed (simple DB lookup)
    if (!input.isRefTask) {
      final alreadySentResult = await _checkMessageAlreadySent(input.ref);
      if (alreadySentResult != null) return alreadySentResult;
    }

    // 3. Executor type check - slowest (complex DB operation)
    final executorTypeResult = await _checkExecutorType(input.ref);
    if (executorTypeResult != null) return executorTypeResult;

    // 4. Custom validations hook for subclasses
    return await performCustomValidations(input);
  }

  /// Check if message has timed out
  Future<WorkerSendMessageOutput?> _checkMessageTimeout(T input) async {
    if (input.creationTime.add(getTimeoutDuration()).isBefore(DateTime.now())) {
      return WorkerSendResultHandler.handleError(
        ref: input.ref,
        error: SendMessageTimeoutException(msgRef: input.ref),
      );
    }
    return null;
  }

  /// Check if message has already been sent
  Future<WorkerSendMessageOutput?> _checkMessageAlreadySent(String ref) async {
    if (await SharedPreferencesStore.isSent(ref)) {
      return WorkerSendResultHandler.handleSkip(
        reason: 'Task $ref is already processed, skipping',
        ref: ref,
      );
    }
    return null;
  }

  /// Check executor type compatibility
  Future<WorkerSendMessageOutput?> _checkExecutorType(String ref) async {
    final currentExecutorType = await SharedPreferencesStore.getExecutorType();
    final handlerExecutorType = TaskHandlerRegistry.executorType;

    if (handlerExecutorType != null &&
        currentExecutorType.sameType(handlerExecutorType)) {
      return WorkerSendResultHandler.handleSkip(
        reason: 'Task $ref has different executor type, skipping',
        ref: ref,
      );
    }
    return null;
  }

  /// Hook for subclasses to add custom validations
  Future<WorkerSendMessageOutput?> performCustomValidations(T input) async {
    return null; // No additional validations by default
  }

  /// Extract creation time with fallback to input.creationTime or current time
  DateTime _extractCreationTime(Map<String, dynamic> inputData, T input) {
    final creationTimeStr = inputData['creationTime'] as String?;
    return creationTimeStr != null
        ? DateTime.parse(creationTimeStr)
        : input.creationTime;
  }

  /// Helper method for making retry-enabled API calls with standardized error handling
  Future<WorkerSendMessageOutput> makeRetryableApiCall({
    required T input,
    required DateTime createTime,
    required Future<dynamic> Function() apiCall,
  }) async {
    try {
      final response = await retryManager.retry(
        task: apiCall,
        taskId: input.ref,
        createTime: createTime,
      );

      return WorkerSendResultHandler.handleSuccess(
        ref: input.ref,
        response: response,
      );
    } catch (e) {
      return WorkerSendResultHandler.handleError(
        error: e,
        ref: input.ref,
      );
    }
  }
}
