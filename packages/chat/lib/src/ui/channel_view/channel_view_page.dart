import 'dart:async';
import 'dart:io';

import 'package:app_core/core.dart' hide Config;
import 'package:async/async.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:message_api/message_api.dart';
import 'package:shared/shared.dart';
import 'package:sticker/sticker.dart' hide Config;
import 'package:upload_manager/upload_manager.dart' hide Config;
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import '../../common/di/di.dart';
import '../../data/repositories/database/entities/channel_local_metadata.dart';
import '../../data/repositories/database/entities/translated_result.dart';
import '../../data/repositories/database/enums/chat_presence_state.dart';
import '../../data/repositories/database/enums/dm_status.dart';
import '../../data/repositories/database/enums/user_badge_enum.dart';
import '../../data/repositories/extensions/location_datamap_extensions.dart';
import '../../data/repositories/extensions/message_extension.dart';
import '../../domain/event_bus/message/show_fullscreen_event.dart';
import '../../domain/event_bus/message/show_quote_message_event.dart';
import '../../domain/usecase/chat_user/get_me_use_case.dart';
import '../../domain/usecase/message/load_message_use_case.dart';
import '../../utils/file_message_utils.dart';
import '../../utils/name_utils.dart';
import '../channel_info/bloc/channel_info_bloc.dart';
import '../forward/forward_view_page.dart';
import '../member_settings/member_settings_handler.dart';
import '../message_list/message_handler.dart';
import '../message_list/message_list_view.dart';
import '../message_request/bloc/message_request_bloc.dart';
import '../translate_to/bloc/translate_to_bloc.dart';
import 'bloc/channel_view/channel_view_bloc.dart';
import 'bloc/list_member/list_member_bloc.dart';
import 'channel_view_handler.dart';
import 'widgets/original_video_action_sheet_widget.dart';
import 'widgets/original_ziishort_action_sheet_widget.dart';
import 'widgets/original_ziivoice_action_sheet_widget.dart';

class UserViewData {
  const UserViewData({
    this.username,
    this.userId,
    this.displayName,
    this.avatar,
  });

  final String? username;
  final String? userId;

  /// AliasName -> profile DisplayName -> Username
  final String? displayName;
  final String? avatar;
}

class ChannelViewPage extends StatefulWidget {
  const ChannelViewPage({
    super.key,
    this.workspaceId,
    this.channelId,
    this.userId,
    this.focusableMessageId,
    this.initUserData,
    this.onBack,
    this.onChannelDeleted,
    this.onAppbarClicked,
    this.goToDMMessage,
    this.goToViewAvatar,
    this.goToFullscreenMessage,
    this.goToZiiShortPage,
    this.openTakePhotoAndVideoPage,
    this.goToTakePhotoRoute,
    this.onClickTakeChannelAvatarPhoto,
    this.onClickTapOpenGalleryAvatar,
    this.fromNotification,
    this.goToMeetingRoom,
  }) : assert(
          userId != null || (channelId != null && workspaceId != null),
          'dmUserId or channelId must not null',
        );

  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? focusableMessageId;
  final UserViewData? initUserData;
  final VoidCallback? onBack;
  final VoidCallback? onChannelDeleted;
  final void Function(Channel? channel)? onAppbarClicked;
  final void Function(String userId)? goToDMMessage;
  final void Function(String avatarurl)? goToViewAvatar;
  final void Function(
    String messageId,
    int? attachmentIndex,
    String workspaceId,
    String channelId,
  )? goToFullscreenMessage;
  final void Function()? goToTakePhotoRoute;

  // record and return video path
  final Future<String?> Function()? goToZiiShortPage;

  final VoidCallback? openTakePhotoAndVideoPage;

  final void Function(Channel channel)? onClickTakeChannelAvatarPhoto;
  final void Function(Channel channel)? onClickTapOpenGalleryAvatar;
  final bool? fromNotification;
  final void Function({
    required Channel channel,
    required Room room,
    required bool isVideoCall,
  })? goToMeetingRoom;

  @override
  State<ChannelViewPage> createState() => ChannelViewPageState();
}

class ChannelViewPageState<T>
    extends BasePageState<ChannelViewPage, ChannelViewBloc>
    implements
        ui.ChannelViewAppBarWidgetInterface,
        ui.ChannelViewPageInterface {
  final EditorHandler _editorHandler = EditorHandler();
  final TypingHandler _typingHandler = GetIt.I.get<TypingHandler>();
  late SendMessageHandler _sendMessageHandler;
  late ReceiveMessageHandler _receiveMessageHandler;
  late final MessagesBloc _messagesBloc;
  late final ListMemberBloc _listMemberBloc;
  late final ListChatUserBloc _listChatUserBloc;
  late final UserPrivateDataBloc _userPrivateDataBloc;

  Map<String, Member> _members = {};
  Map<String, ChatUser> _users = {};

  ValueNotifier<String> _dateTimeNotifier = ValueNotifier('');
  late ValueNotifier<ui.ChannelViewAppBar> _channelViewAppBarNotifier;
  final ValueNotifier<bool> _isMessageRequestNotifier = ValueNotifier(false);
  bool _handlerIsInitialized = false;
  int badgeEnum = 0;
  ui.UserBadgeType? userBadgeType;
  bool isMessageRequest = false;
  ValueNotifier<Channel?> _channel = ValueNotifier(null);
  ChatUser? _recipientUser;
  bool _isChannelLoaded = false;
  List<UserPrivateData> _listUserPrivateData = [];
  late ChannelEventsListener _channelEventsListener;
  StreamSubscription? _showFullScreenSubscription;
  StreamSubscription? _showQuoteMessageSubscription;
  StreamSubscription? _showBottomSheetPinnedMessageSubscription;
  Map<String, ChatUser> _mapBlockUser = {};
  ValueNotifier<ui.ProcessStatus> processStatus =
      ValueNotifier(ui.ProcessStatus.loading);
  final AppLocalizations appLocalizations =
      GetIt.instance.get<AppLocalizations>();
  ValueNotifier<String> processContent = ValueNotifier("");
  late BlockUserBloc _blockUserBloc;
  final ValueNotifier<bool> isHiddenBlockUser = ValueNotifier(false);
  final ValueNotifier<bool> isHiddenEditor = ValueNotifier(false);
  final ValueNotifier<bool> isEnableClickBottom = ValueNotifier(true);
  final ValueNotifier<int> countItem = ValueNotifier(0);
  late StreamSubscription? _listenBlockEventSubscription;
  late ReportMessageBloc _reportMessageBloc;

  final _appEventBus = GetIt.instance.get<AppEventBus>();
  StreamSubscription? _subscription;
  late ui.MessageOptions messageOption = ui.MessageOptions.delete;
  StreamSubscription? _callDeleteMessageSubscription;
  StreamSubscription? _cancelCheckMessageSubscription;
  late Map<String, Message> mapCheckedMessage = {};
  late ValueNotifier<bool> isChangeAppbar = ValueNotifier(false);
  late ValueNotifier<int> _countNewValue = ValueNotifier(0);
  late ChannelViewHandler _channelViewHandler;
  late ValueNotifier<bool> isPinMessage = ValueNotifier(false);
  late ValueNotifier<ui.PinMessage> displayPinnedMessage;
  late Message? pinnedMessage = null;

  // **TranslateTo** liên quan
  late final TranslateToBloc _translateToBloc;
  late final TranslateToHandler _translateToHandler;
  ChannelLocalMetadata? _metadata;
  List<TranslatedResult>? _translateResultList;

  // **Channel info**
  String? _workspaceId;
  String? _channelId;
  late final ChannelInfoBloc _channelInfoBloc;

  // **Page status control**
  bool _isActive = true;
  bool _hasShowBottomSheetQuote = false;
  bool _hasBottomSheetPinnedMessage = false;
  late bool? _fromNotification;
  late ChatUser? meInfo;
  // **Meeting/Call**
  ValueNotifier<ui.MinimizeCallData?> _minimizeCallData = ValueNotifier(null);
  bool _hasJoinedCall = false;
  bool _hadCall = false;
  StreamSubscription? _callGroupEventSubscription;

  // **Channel status**
  Timer? _onlineTimer;
  ChatPresenceStateEnum? _recipientPresenceStatus;
  ValueNotifier<List<String>>? _typingListNotifier;

  @override
  void dispose() {
    _sendMessageHandler.dispose();
    _receiveMessageHandler.dispose();
    getIt<UserPrivateDataBloc>().add(GetPrivateDataUnSubscriptionEvent());
    _channelEventsListener.removeListener(null, _handleRemoveFromChannel);
    _listenBlockEventSubscription?.cancel();
    _subscription?.cancel();
    _showFullScreenSubscription?.cancel();
    _showQuoteMessageSubscription?.cancel();
    _showBottomSheetPinnedMessageSubscription?.cancel();
    _editorHandler.dispose();
    _callDeleteMessageSubscription?.cancel();
    _cancelCheckMessageSubscription?.cancel();
    _channelViewHandler.dispose();
    _translateToHandler.dispose();
    _fromNotification = false;
    _callGroupEventSubscription?.cancel();
    if (widget.userId == null) {
      getIt<MeetingHandler>().removeRoomListener(_meetingRoomListener);
    }
    _minimizeCallData.dispose();
    _onlineTimer?.cancel();
    super.dispose();
  }

  void didPushNext() {
    _isActive = false;
    _editorHandler.pause();
    isHiddenEditor.value = false;
  }

  void didPopNext() {
    _isActive = true;
    _editorHandler.resume();
    isHiddenEditor.value = false;
    if (widget.userId != null) {
      bloc.add(
        OnInitChannelViewEvent(
          workspaceId: widget.workspaceId,
          channelId: widget.channelId,
          userId: widget.userId,
        ),
      );
    }

    if (widget.userId == null && _minimizeCallData.value?.callJoined != true) {
      bloc.add(
        GetMeetingRoomEvent(
          workspaceId: widget.workspaceId!,
          channelId: widget.channelId!,
        ),
      );
    }
  }

  @override
  void initState() {
    _fromNotification = widget.fromNotification;
    // khởi tạo pin message
    displayPinnedMessage = ValueNotifier(
      ui.PinMessage(
        extractPinContent: () => "",
        extractName: () => "",
        extractAvatarUrl: () => "",
        isShowPinWidget: false,
      ),
    );
    _workspaceId = widget.workspaceId;
    _channelId = widget.channelId;
    _channelViewHandler = ChannelViewHandler();
    bloc.add(
      OnInitChannelViewEvent(
        workspaceId: widget.workspaceId,
        channelId: widget.channelId,
        userId: widget.userId,
      ),
    );

    // **Khởi tạo TranslateToBloc và TranslateToHandler**
    _translateToHandler = getIt<TranslateToHandler>();
    _translateToBloc = _translateToHandler.translateToBloc;
    _initTranslateBlocIfNeeded();
    _sendMessageHandler = SendMessageHandler(
      workspaceId: widget.workspaceId,
      channelId: widget.channelId,
      userId: widget.userId,
    );
    _receiveMessageHandler = ReceiveMessageHandler(
      workspaceId: widget.workspaceId,
      channelId: widget.channelId,
      userId: widget.userId,
    );
    _editorHandler.init(
      workspaceId: widget.workspaceId,
      channelId: widget.channelId,
      userId: widget.userId,
      sendMessageHandler: _sendMessageHandler,
      onSendMessage: () {
        if (_isMessageRequestNotifier.value) {
          _isMessageRequestNotifier.value = false;
        }
      },
      goToTakePhoto: widget.goToTakePhotoRoute,
      onSelectedPhoto: _handleSelectedPhoto,
      onSelectedFile: _handleSelectedFile,
      onTapZiishort: _handleRecordZiishort,
      onTapAttachment: _openAttachmentPicker,
    );

    final emptyAppBar = ui.ChannelViewAppBar(
      username: widget.initUserData?.displayName ?? '',
      activeStatus: ' ',
      avatarUrl: widget.initUserData?.avatar ?? '',
      isGroup: widget.initUserData == null,
      isShowIconStartCall: shouldShowIconStartCall(),
    );

    _channelViewAppBarNotifier = ValueNotifier(emptyAppBar);
    super.initState();
    _messagesBloc = getIt<MessagesBloc>();
    _listMemberBloc = getIt<ListMemberBloc>();
    _listChatUserBloc = getIt<ListChatUserBloc>();
    _channelInfoBloc = getIt<ChannelInfoBloc>();

    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());

    _channelEventsListener = GetIt.instance.get<ChannelEventsListener>();
    _channelEventsListener.addListeners(null, _handleRemoveFromChannel);
    _showFullScreenSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ShowFullscreenEvent>()
        .listen(_onShowFullScreen);
    _showQuoteMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ShowQuoteMessageEvent>()
        .listen(_onShowQuoteMessage);
    _showBottomSheetPinnedMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<OnShowBottomSheetPinnedMessageEvent>()
        .listen(_showPinnedMessageActionSheet);

    _blockUserBloc = getIt<BlockUserBloc>();
    _blockUserBloc.add(OnLoadListBlockUserEvent());
    setupListenBlockEventHandler();
    _reportMessageBloc = getIt<ReportMessageBloc>();

    _subscription?.cancel();
    _subscription = StreamGroup.merge([
      _appEventBus.on<TakePhotoMessageEvent>(),
      _appEventBus.on<TakeVideoMessageEvent>(),
      _appEventBus.on<SendZiishortMessageEvent>(),
      _appEventBus.on<ChooseAvatarEvent>(),
    ]).listen((event) async {
      await Future.delayed(DurationUtils.ms100);
      if (!_isActive) return;
      switch (event.runtimeType) {
        case TakePhotoMessageEvent:
          await _handleChooseImageEvent(event as TakePhotoMessageEvent);
          break;
        case TakeVideoMessageEvent:
          await _handleChooseVideoEvent(event as TakeVideoMessageEvent);
        case SendZiishortMessageEvent:
          await _handleZiishortMessageEvent(event as SendZiishortMessageEvent);
          break;
        case ChooseAvatarEvent:
          await _handleChooseAvatarEvent(event as ChooseAvatarEvent);
          break;
      }
    });
    _callDeleteMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<CallCheckMessagesEvent>()
        .listen(_onCallDeleteMessage);
    _cancelCheckMessageSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<CancelAppBarChannelViewEvent>()
        .listen(_onCancelAppBarChannelView);
    _channelViewHandler.setupDialogHandler(context);

    //Listen meeting/call events
    _callGroupEventSubscription = getIt<AppEventBus>()
        .on<CallGroupLocalEvent>()
        .listen(_handleCallGroupEvent);

    if (widget.userId == null) {
      // Only add listener when it is a 1-to-N channel
      getIt<MeetingHandler>().addRoomListener(
        _meetingRoomListener,
        triggerImmediately: true,
      );
    }

    _minimizeCallData.addListener(() {
      final hasCall = _minimizeCallData.value != null;
      if (_hadCall == hasCall) return;
      _hadCall = hasCall;
      _updateChannelAppBar();
    });
  }

  bool shouldShowIconStartCall() {
    if (_channel.value != null && !_channel.value!.isDm) return true;

    if (Platform.isAndroid) return false;

    return _channel.value?.recipientId != GlobalConfig.ZIICHAT_USER_ID &&
        GlobalConfig.callDmEnable;
  }

  void _meetingRoomListener(MeetingRoomInfo? roomInfo) {
    if (roomInfo != null &&
        roomInfo.channelId == widget.channelId &&
        roomInfo.workspaceId == widget.workspaceId) {
      _minimizeCallData.value = ui.MinimizeCallData(
        channelName: _channel.value?.name ?? '',
        numberParticipant: roomInfo.numberParticipants,
        timeValue: TimeUtils.formatCallDuration(roomInfo.duration),
        callJoined: true,
      );
      _hasJoinedCall = true;
      return;
    }

    if (!_hasJoinedCall) {
      bloc.add(
        GetMeetingRoomEvent(
          workspaceId: widget.workspaceId!,
          channelId: widget.channelId!,
        ),
      );
      return;
    }

    if (roomInfo == null && _hasJoinedCall) {
      _hasJoinedCall = false;
      _minimizeCallData.value = null;
      bloc.add(
        GetMeetingRoomEvent(
          workspaceId: widget.workspaceId!,
          channelId: widget.channelId!,
        ),
      );
      return;
    }
  }

  Future<void> _handleChooseAvatarEvent(ChooseAvatarEvent event) async {
    if (event.avatarType != AvatarType.channel) return;
    final avatar = XFile(event.filePath);
    final avatarUploadFile = UploadFile(
      path: avatar.path,
      name: avatar.name,
      size: await avatar.length(),
    );

    _channelInfoBloc.add(
      UpdateChannelAvatarEvent(
        avatar: avatarUploadFile,
        channelId: _channelId,
        workspaceId: _workspaceId,
      ),
    );
  }

  void _initTranslateBlocIfNeeded() {
    _translateToHandler.initTranslateBlocIfNeeded(
      workspaceId: widget.workspaceId,
      channelId: widget.channelId,
    );
  }

  void _onCancelAppBarChannelView(CancelAppBarChannelViewEvent event) {
    if (event.isCancel == true) {
      isHiddenEditor.value = false;
      isChangeAppbar.value = false;
    }
  }

  /// Handle Show/Hide minimize call group widget
  void _handleCallGroupEvent(CallGroupLocalEvent event) {
    if (event.channelId != _channel.value?.channelId ||
        event.workspaceId != _channel.value?.workspaceId) {
      return;
    }
    if (event is ShowMinimizedCallGroupEvent) {
      _minimizeCallData.value = ui.MinimizeCallData(
        channelName: _channel.value?.name ?? '',
        numberParticipant: event.numberParticipants ?? 0,
      );
      return;
    }
    if (event is HideMinimizedCallGroupEvent) {
      _minimizeCallData.value = null;
      return;
    }
  }

  Future<void> _handleRecordZiishort() async {
    if (widget.goToZiiShortPage == null) return;
    final isGrantedCamera =
        await PermissionUtils.requestCameraPermission(context);
    if (!isGrantedCamera) return;

    bool canOpenCamera = true;
    await PermissionUtils.requestMicrophonePermission(
      context,
      onOpenSetting: () {
        canOpenCamera = false;
      },
    );
    if (!canOpenCamera) return;
    Future.delayed(DurationUtils.ms100, () {
      widget.goToZiiShortPage!();
    });
  }

  void _handleRemoveFromChannel(String channelId, String workspaceId) {
    if (widget.workspaceId == workspaceId && widget.channelId == channelId) {
      /// leave channel will pop to home
      widget.onChannelDeleted?.call();
    }
  }

  void _onShowFullScreen(ShowFullscreenEvent event) {
    var workspaceId = (_channel.value?.workspaceId != null &&
            _channel.value!.workspaceId.isNotEmpty)
        ? _channel.value!.workspaceId
        : event.workspaceId;
    var channelId = (_channel.value?.channelId != null &&
            _channel.value!.channelId.isNotEmpty)
        ? _channel.value!.channelId
        : event.channelId;

    widget.goToFullscreenMessage?.call(
      event.messageId,
      event.attachmentIndex,
      workspaceId!,
      channelId!,
    );
  }

  void _onShowQuoteMessage(ShowQuoteMessageEvent event) {
    if (_hasShowBottomSheetQuote) return;
    _showQuoteActionSheet(event.messageId);
  }

  void _showQuoteActionSheet(String messageId) async {
    _hasShowBottomSheetQuote = true;
    Message? originalMessage;
    ui.MessageItem? originalMessageItem;

    final output = await getIt.get<LoadMessageUseCase>().execute(
          LoadMessageInput(
            workspaceId: widget.workspaceId!,
            channelId: widget.channelId!,
            messageId: messageId,
            userId: widget.userId,
          ),
        );
    // Get the phone's settings (12-hour or 24-hour format)
    bool is24HourFormat = GetIt.instance.get<AppBloc>().state.is24HourFormat;
    if (output.message != null) {
      originalMessage = output.message;
      originalMessageItem = originalMessage!.createMessageItem(
        member: _members[originalMessage.userId],
        user: _users[originalMessage.userId],
        recipientId: _channel.value?.recipientId,
        use24HourFormat: is24HourFormat,
      );

      void onClickLink(String link) {
        AppEventBus.publish(
          OnLinkClickedEvent(
            link: link,
            workspaceId: originalMessage?.workspaceId,
            channelId: originalMessage?.channelId,
            userId: originalMessage?.userId,
            messageId: originalMessage?.messageId,
          ),
        );
      }

      switch (originalMessage.messageViewType) {
        case MessageViewType.text || MessageViewType.textOwner:
          _showViewOriginalTextActionSheet(
            originalMessageItem,
            originalMessage,
          );
          break;
        case MessageViewType.link || MessageViewType.linkOwner:
          _showViewOriginalLinkActionSheet(
            originalMessageItem,
            originalMessage,
            onClickLink,
          );
          break;
        case MessageViewType.images || MessageViewType.imagesOwner:
          _showViewOriginalImagesActionSheet(
            originalMessageItem,
            originalMessage,
          );

          break;
        case MessageViewType.video || MessageViewType.videoOwner:
          _showViewOriginalVideoActionSheet(
            originalMessageItem,
            originalMessage,
          );

          break;
        case MessageViewType.invitation || MessageViewType.invitationOwner:
          break;
        case MessageViewType.location || MessageViewType.locationOwner:
          _showViewOriginalLocationActionSheet(
            originalMessageItem,
            originalMessage,
            onClickLink,
          );
          break;
        case MessageViewType.ziiVoice || MessageViewType.ziiVoiceOwner:
          _showViewOriginalZiivoiceActionSheet(
            originalMessageItem,
            originalMessage,
          );

          break;
        case MessageViewType.ziiShort || MessageViewType.ziiShortsOwner:
          _showViewOriginalZiiShortActionSheet(
            originalMessageItem,
            originalMessage,
          );

          break;
        case MessageViewType.sticker || MessageViewType.stickerOwner:
          _showViewOriginalStickerActionSheet(
            originalMessageItem,
            originalMessage,
          );
          break;
        case MessageViewType.file || MessageViewType.fileOwner:
          _showViewOriginalFileActionSheet(
            originalMessageItem,
            originalMessage,
          );
          break;
        default:
      }
    }
  }

  void _showPinnedMessageActionSheet(
    OnShowBottomSheetPinnedMessageEvent event,
  ) async {
    if (_hasBottomSheetPinnedMessage) return;
    _hasBottomSheetPinnedMessage = true;
    Message? pinnedMessage;
    ui.MessageItem? pinnedMessageItem;

    final output = await getIt.get<LoadMessageUseCase>().execute(
          LoadMessageInput(
            workspaceId: event.workspaceId,
            channelId: event.channelId,
            messageId: event.messageId,
            userId: widget.userId,
          ),
        );
    // Get the phone's settings (12-hour or 24-hour format)
    bool is24HourFormat = GetIt.instance.get<AppBloc>().state.is24HourFormat;
    if (output.message != null) {
      pinnedMessage = output.message;
      pinnedMessageItem = pinnedMessage!.createMessageItem(
        member: _members[pinnedMessage.userId],
        user: _users[pinnedMessage.userId],
        recipientId: _channel.value?.recipientId,
        use24HourFormat: is24HourFormat,
      );

      void onClickLink(String link) {
        AppEventBus.publish(
          OnLinkClickedEvent(
            link: link,
            workspaceId: pinnedMessage?.workspaceId,
            channelId: pinnedMessage?.channelId,
            userId: pinnedMessage?.userId,
            messageId: pinnedMessage?.messageId,
          ),
        );
      }

      switch (pinnedMessage.messageViewType) {
        case MessageViewType.text || MessageViewType.textOwner:
          MessageHandler.showPinnedMessageTextActionSheet(
            context,
            pinnedMessageItem,
            pinnedMessage,
            quoteMessage: pinQuoteMessage(pinnedMessage),
            isMember: widget.userId != null ? false : handleHiddenPin(),
            onCloseClick: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
            onClose: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
          );
          break;
        case MessageViewType.link || MessageViewType.linkOwner:
          MessageHandler.showPinnedMessageLinkActionSheet(
            context,
            pinnedMessageItem,
            pinnedMessage,
            onClickLink,
            quoteMessage: pinQuoteMessage(pinnedMessage),
            isMember: widget.userId != null ? false : handleHiddenPin(),
            onCloseClick: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
            onClose: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
          );
          break;
        case MessageViewType.images || MessageViewType.imagesOwner:
          MessageHandler.showPinnedMessageImagesActionSheet(
            context,
            pinnedMessageItem,
            pinnedMessage,
            isMember: widget.userId != null ? false : handleHiddenPin(),
            onCloseClick: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
            onClose: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
          );

          break;
        case MessageViewType.video || MessageViewType.videoOwner:
          MessageHandler.showPinnedMessageVideoActionSheet(
            context,
            pinnedMessageItem,
            pinnedMessage,
            isMember: widget.userId != null ? false : handleHiddenPin(),
            onCloseClick: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
            onClose: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
          );

          break;
        case MessageViewType.invitation || MessageViewType.invitationOwner:
          MessageHandler.showPinnedMessageInvitationActionSheet(
            context,
            pinnedMessageItem,
            pinnedMessage,
            quoteMessage: pinQuoteMessage(pinnedMessage),
            isMember: widget.userId != null ? false : handleHiddenPin(),
            onCloseClick: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
            onClose: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
          );
          break;
        case MessageViewType.location || MessageViewType.locationOwner:
          MessageHandler.showPinnedMessageLocationActionSheet(
            context,
            pinnedMessageItem,
            pinnedMessage,
            onClickLink,
            isMember: widget.userId != null ? false : handleHiddenPin(),
            onCloseClick: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
            onClose: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
          );
          break;
        case MessageViewType.ziiVoice || MessageViewType.ziiVoiceOwner:
          MessageHandler.showPinnedMessageZiivoiceActionSheet(
            context,
            pinnedMessageItem,
            pinnedMessage,
            isMember: widget.userId != null ? false : handleHiddenPin(),
            onCloseClick: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
            onClose: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
          );

          break;
        case MessageViewType.ziiShort || MessageViewType.ziiShortsOwner:
          MessageHandler.showPinnedMessageZiiShortActionSheet(
            context,
            pinnedMessageItem,
            pinnedMessage,
            isMember: widget.userId != null ? false : handleHiddenPin(),
            onCloseClick: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
            onClose: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
          );

          break;
        case MessageViewType.sticker || MessageViewType.stickerOwner:
          MessageHandler.showPinnedMessageStickerActionSheet(
            context,
            pinnedMessageItem,
            pinnedMessage,
            isMember: widget.userId != null ? false : handleHiddenPin(),
            onCloseClick: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
            onClose: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
          );
          break;
        case MessageViewType.file || MessageViewType.fileOwner:
          ValueNotifier<ui.FileState> fileState =
              ValueNotifier(ui.FileState.download);
          MessageHandler.showPinnedMessageFileActionSheet(
            context,
            pinnedMessageItem,
            pinnedMessage,
            fileState,
            isMember: widget.userId != null ? false : handleHiddenPin(),
            onCloseClick: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
            onClose: () {
              _hasBottomSheetPinnedMessage = false;
              popShowDialogProcess();
            },
          );
          break;
        default:
      }
    }
  }

  void _showViewOriginalTextActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    ui.ActionSheetUtil.showViewOriginalTextActionSheet(
      context,
      onCloseClicked: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      onClose: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      text: originalMessage.content ?? '',
    );
  }

  void _showViewOriginalLinkActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
    void Function(String link) onLinkClick,
  ) {
    var embed =
        (originalMessage.embed != null && originalMessage.embed!.isNotEmpty)
            ? originalMessage.embed!.first
            : null;

    var embedData = embed?.embedData;

    var invitationData = embed?.invitationData;

    void onEmbedLinkClicked() {
      if (embedData != null) {
        onLinkClick(embedData.url!);
      } else {
        onLinkClick(invitationData!.invitationLink!);
      }
    }

    ui.ActionSheetUtil.showViewOriginalLinkActionSheet(
      context,
      onCloseClicked: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      onClose: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      onLinkClick: onEmbedLinkClicked,
      linkMessage: ui.LinkMessage(
        messageContent: originalMessage.content ?? '',
        title: embedData?.title ?? "",
        description: embedData?.description ?? "",
        imageUrl: embedData?.thumbnailUrl ?? "",
      ),
    );
  }

  void _showViewOriginalLocationActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
    void Function(String link) onLinkClick,
  ) {
    final locationData = originalMessage.firstEmbed!.locationData!;

    void onLocationClicked() {
      if (!originalMessage.hasLocationData) return;

      onLinkClick(locationData.mapsLink);
    }

    ui.ActionSheetUtil.showViewOriginalShareLocationActionSheet(
      context,
      onCloseClicked: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      onClose: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      thumbnailUrl: locationData.thumbnailUrl ?? '',
      address: locationData.description ?? '',
      onOpenLocationClicked: (MessageItem) {
        onLocationClicked();
      },
    );
  }

  void _showViewOriginalImagesActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    var imageAttachments = originalMessage.mediaAttachments.map(
      (attachment) {
        final photo = attachment.photo!;
        return ui.ImageAttachment(
          attachmentId: photo.attachmentId!,
          attachmentUrl: UrlUtils.parseCDNUrl(photo.fileUrl),
          width: photo.fileMetadata?.dimensions?.width?.toDouble() ?? 0,
          height: photo.fileMetadata?.dimensions?.height?.toDouble() ?? 0,
          attachmentPath: photo.fileUrl,
        );
      },
    ).toList();
    ui.ActionSheetUtil.showViewOriginalImagesActionSheet(
      context,
      onCloseClicked: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      onClose: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      imageAttachments: imageAttachments,
    );
  }

  void _showViewOriginalVideoActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    var media = originalMessage.mediaAttachments.first.video!;
    final videoUrl = UrlUtils.parseCDNUrl(media.fileUrl);
    ui.ActionSheetUtil.showViewOriginalVideoActionSheet(
      context,
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      videoWidget: OriginalVideoActionSheetWidget(
        messageItem: originalMessageItem,
        videoPath: videoUrl,
      ),
      onCloseClicked: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      onClose: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
    );
  }

  void _showViewOriginalZiivoiceActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    var voiceMessage = originalMessage.mediaAttachments.first.voiceMessage!;
    final voiceUrl = UrlUtils.parseCDNUrl(voiceMessage.fileUrl);
    final voicePath = voiceMessage.filePath;

    ui.ActionSheetUtil.showViewOriginalZiiVoiceActionSheet(
      context,
      onCloseClicked: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      onClose: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      waveFormWidget: OriginalZiivoiceActionSheetWidget(
        messageItem: originalMessageItem,
        path: voicePath,
        url: voiceUrl,
      ),
    );
  }

  void _showViewOriginalZiiShortActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    final ziiShortUrl = originalMessage.mediaAttachments.first.videoMessage!;
    final ziiShortFile = UrlUtils.parseCDNUrl(ziiShortUrl.fileUrl);
    ui.ActionSheetUtil.showViewOriginalZiiShortActionSheet(
      context,
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      ziiShortWidget: OriginalZiishortActionSheetWidget(
        messageItem: originalMessageItem,
        ziiShortPath: ziiShortFile,
      ),
      onCloseClicked: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      onClose: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
    );
  }

  void _showViewOriginalStickerActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    ui.ActionSheetUtil.showViewOriginalStickerActionSheet(
      context,
      onCloseClicked: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      onClose: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      stickerWidget: StickerWidget.fromUrl(
        lottieUrl: UrlUtils.parseSticker(
          originalMessage.mediaAttachments.first.sticker!.stickerUrl,
        ),
        size: StickerSize.x512,
      ),
    );
  }

  void _showViewOriginalFileActionSheet(
    ui.MessageItem originalMessageItem,
    Message originalMessage,
  ) {
    final file = originalMessage.mediaAttachments.last.file ??
        originalMessage.mediaAttachments.last.undefined!;

    ValueNotifier<ui.FileState> fileState =
        ValueNotifier(ui.FileState.download);
    bool _canViewFile = true;

    _canViewFile = FileMessageUtils.canViewFile(file.fileUrl ?? file.filePath!);

    if (_canViewFile) {
      fileState.value = ui.FileState.canview;
      if (file.fileUrl != null) {
        FileMessageUtils.downloadTempFile(UrlUtils.parseCDNUrl(file.fileUrl));
      }
    }

    Future<void> onDownLoadFile(
      BuildContext context,
      Message message,
      ValueNotifier<ui.FileState> fileState,
    ) async {
      bool _canViewFile = true;
      var file = message.mediaAttachments.first.file!;
      _canViewFile =
          FileMessageUtils.canViewFile(file.fileUrl ?? file.filePath!);
      final fileUrl = UrlUtils.parseCDNUrl(file.fileUrl);
      if (_canViewFile) {
        final tempFile = await FileMessageUtils.tempFileDownloaded(
          UrlUtils.parseCDNUrl(file.fileUrl),
        );
        if (tempFile == null) {
          return;
        }
        FileMessageUtils.openFile(
          filePath: tempFile,
          mimetype: FileMessageUtils.getMimeType(fileUrl),
        );
        return;
      }

      AppEventBus.publish(
        DownloadEnqueueEvent(
          url: fileUrl,
          fileName: file.fileMetadata?.filename,
          messageId: message.messageId,
        ),
      );
    }

    ui.ActionSheetUtil.showViewOriginalFileActionSheet(
      context,
      onCloseClicked: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      onClose: () {
        _hasShowBottomSheetQuote = false;
        AppEventBus.publish(PopToChannelViewEvent());
      },
      appLocalizations: appLocalizations,
      messageItem: originalMessageItem,
      fileName: file.fileMetadata?.filename ?? "",
      fileSize: file.fileMetadata?.filesize?.toStringStorageFormat() ?? "",
      fileState: fileState,
      onFileClicked: (T) {
        onDownLoadFile(context, originalMessage, fileState);
      },
    );
  }

  void setupListenBlockEventHandler() {
    _listenBlockEventSubscription = GetIt.instance
        .get<AppEventBus>()
        .on<ListenBlockUserEvent>()
        .listen(onReceivedListenEvent);
  }

  void onReceivedListenEvent(event) async {
    if (event is BlockEvent) {
      var chatUser = ChatUser.fromJson(event.user);
      _mapBlockUser.putIfAbsent(
        chatUser.userId,
        () => chatUser,
      );
      if (chatUser.userId == widget.userId) {
        isHiddenBlockUser.value = true;
      }
    }
    if (event is UnBlockEvent) {
      if (event.userId == widget.userId) {
        _mapBlockUser.removeWhere((key, chatUser) => key == chatUser.userId);
        isHiddenBlockUser.value = false;
      }
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<MessagesBloc>.value(value: _messagesBloc),
        BlocProvider<ListMemberBloc>.value(value: _listMemberBloc),
        BlocProvider<ListChatUserBloc>.value(value: _listChatUserBloc),
        BlocProvider<UserPrivateDataBloc>.value(value: _userPrivateDataBloc),
        BlocProvider<BlockUserBloc>.value(value: _blockUserBloc),
        BlocProvider<ReportMessageBloc>.value(value: _reportMessageBloc),
        BlocProvider<TranslateToBloc>.value(value: _translateToBloc),
        BlocProvider<ChannelInfoBloc>.value(value: _channelInfoBloc),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<ListMemberBloc, ListMemberState>(
            listenWhen: (prev, state) => prev != state,
            listener: _onMembersStateChanged,
          ),
          BlocListener<ChannelInfoBloc, ChannelInfoState>(
            listenWhen: (prev, state) => prev != state,
            listener: (context, state) {},
          ),
          BlocListener<ListChatUserBloc, ListChatUserState>(
            listenWhen: (prev, state) => prev != state,
            listener: _onUsersStateChanged,
          ),
          BlocListener<ChannelViewBloc, ChannelViewState>(
            listenWhen: (prev, state) => prev != state,
            listener: (context, state) {
              _onStateChanged(state);
            },
          ),
          BlocListener<TranslateToBloc, TranslateToState>(
            listenWhen: (prev, state) => prev != state,
            listener: _onTranslateToBlocStateChanged,
          ),
          BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocUserPrivateListener,
          ),
          BlocListener<BlockUserBloc, BlockUserState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocBlockUserListener,
          ),
          BlocListener<MessagesBloc, MessagesState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocMessageListener,
          ),
        ],
        child: ui.ChannelViewPage(
          channelViewPageInterface: this,
          editorInterface: _editorHandler,
          timeIndicator: _buildTimeIndicator(),
          isMessageRequestNotifier: _isMessageRequestNotifier,
          onTapAccept: (){
            getIt<MessageRequestBloc>().add(
              AcceptMessageRequestEvent(
                userId: widget.userId!,
              ),
            );
          },
          onTapBlock: () {
            ui.ActionSheetUtil.showBlockUserActionSheet(
              context,
              username: getAliasName(_channel.value?.recipientId) ??
                  _channel.value?.name ??
                  '',
              onBlock: () {
                Navigator.pop(context);
                _blockUserBloc.add(OnBlockUserEvent(userId: widget.userId!));
              },
              onCancel: () {
                Navigator.pop(context);
              },
            );

            // TODO: Handle when doing module user
          },
          isHidden: isHiddenBlockUser,
          isHiddenEditor: isHiddenEditor,
          bottomWidget: bottomButtonWidget(messageOption),
          pinMessage: displayPinnedMessage,
          onPressPinMessage: (T) {
            if (pinnedMessage != null) {
              AppEventBus.publish(
                OnClickPinnedMessageEvent(
                  workspaceId: pinnedMessage!.workspaceId,
                  channelId: pinnedMessage!.channelId,
                  userId: pinnedMessage!.userId,
                  messageId: pinnedMessage!.messageId,
                ),
              );
            }
          },
          onLongPressPinMessage: (T) {},
          minimizeCallData: _minimizeCallData,
          onJoinToMeetingRoom: () => _onJoinToGroupCall(isVideoCall: false),
          onOpenMeetingRoom: _onOpenMeetingRoom,
        ),
      ),
    );
  }

  void _initHandler(BuildContext context) {
    if (_handlerIsInitialized) return;

    _editorHandler.buildContext = context;
    _receiveMessageHandler.messagesBloc = context.read<MessagesBloc>();
    _handlerIsInitialized = true;
  }

  Widget _buildTimeIndicator() {
    return ValueListenableBuilder(
      valueListenable: _dateTimeNotifier,
      builder: (_, time, __) {
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 250),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(opacity: animation, child: child);
          },
          child:
              time.isEmpty ? SizedBox.shrink() : ui.TimeIndicator(time: time),
        );
      },
    );
  }

  void _onStateChanged(ChannelViewState state) {
    _initHandler(context);
    state.maybeWhen(
      channelLoaded: (channel) {
        if (channel == null) return;

        if (_channel.value == null ||
            (channel.channelId.isNotEmpty && channel.workspaceId.isNotEmpty)) {
          _channel.value = channel;
        }

        _countNewValue.value = channel.metadata.target?.unreadCount ?? 0;

        if ((_workspaceId ?? '').isEmpty) _workspaceId = channel.workspaceId;

        if ((_channelId ?? '').isEmpty) _channelId = channel.channelId;

        _editorHandler.isChannel = _channel.value?.type?.isChannel ?? false;

        if (_channel.value!.type!.isDm && _channel.value?.dbDMStatus != null) {
          _isMessageRequestNotifier.value =
              channel.dmStatus == DMStatusEnum.PENDING &&
                  channel.userId != Config.getInstance().activeSessionKey;
          isHiddenBlockUser.value =
              _mapBlockUser[_channel.value?.recipientId] == null ? false : true;
        }

        _updateChannelAppBar();
        if ((_workspaceId ?? '').isNotEmpty && (_channelId ?? '').isNotEmpty)
          _initTranslateBlocIfNeeded();
      },
      userLoaded: (user) {
        if (user == null) return;
        _recipientUser = user;
        badgeEnum = user.profile?.userBadgeType ?? 0;

        userBadgeType =
            UserBadgeEnumExtension.getEnumByValue(badgeEnum).toUserBadgeType();
        _updateChannelAppBar();
        _setupOnlineStatusTimer();
      },
      channelNotExists: () {
        setState(() {
          _isChannelLoaded = true;
        });
      },
      orElse: () {},
    );
  }

  /// Sets up a timer to periodically update the recipient's online status.
  ///
  /// - Caches initial presence state from [presenceData].
  /// - Marks the user as online if they are typing.
  /// - Triggers [_updateChannelAppBar] every second.
  /// - Cancels any previous timer before starting a new one.
  void _setupOnlineStatusTimer() {
    if (_recipientUser?.presenceData != null) {
      _recipientPresenceStatus = _recipientUser?.presenceData?.presenceState;
      _onlineTimer?.cancel();
      _onlineTimer = Timer.periodic(DurationUtils.ms1000, (timer) {
        if (_typingListNotifier?.value.contains(_recipientUser!.username!) ??
            false) {
          _recipientPresenceStatus = ChatPresenceStateEnum.ONLINE;
        }
        _updateChannelAppBar();
      });
    }
  }

  void _blocBlockUserListener(BuildContext context, BlockUserState state) {
    _initHandler(context);
    state.maybeWhen(
      updateProcessDialog: (response, bool? popOnlyMine) {
        if (response) {
          if (Navigator.canPop(context)) {
            popOnlyMine == true
                ? Navigator.pop(context)
                : popShowDialogProcess();
          }
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
      },
      loadListBlockUser: (listBlockUser) {
        _mapBlockUser = {};
        listBlockUser?.forEach((item) {
          _mapBlockUser.putIfAbsent(
            item.userId,
            () => ChatUser.fromJson(item.toJson()),
          );
        });
        isHiddenBlockUser.value =
            _mapBlockUser[_channel.value?.recipientId] == null ? false : true;
      },
      orElse: () {},
    );
  }

  void changeProcessDialog(ui.ProcessStatus status, String content) {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      processContent.value = content;
      processStatus.value = status;
      Future.delayed(Duration(seconds: 2), () {
        if (Navigator.canPop(context)) {
          popShowDialogProcess();
        }
      });
    });
  }

  void popShowDialogProcess() {
    AppEventBus.publish(
      PopToChannelViewEvent(),
    );
  }

  @override
  void onAppbarClicked() {
    widget.onAppbarClicked?.call(_channel.value);
  }

  void callChannelInfo() {
    widget.onAppbarClicked?.call(_channel.value);
  }

  @override
  void onBackButtonClicked() {
    widget.onBack?.call();
  }

  @override
  Widget appBar() {
    return ValueListenableBuilder(
      valueListenable: isChangeAppbar,
      builder: (context, value, child) {
        return value
            ? ui.ChannelViewAppBarMessageOptionWidget(
                countItem: countItem,
                onBackButtonClicked: onBackButtonClicked,
                onCancelClicked: () {
                  AppEventBus.publish(
                    CancelAppBarChannelViewEvent(isCancel: true),
                  );
                  isChangeAppbar.value = false;
                },
              )
            : ui.ChannelViewAppBarWidget(
                channelViewAppBarNotifier: _channelViewAppBarNotifier,
                interface: this,
              );
      },
    );
  }

  @override
  Widget body() {
    return ValueListenableBuilder(
      valueListenable: _minimizeCallData,
      builder: (context, minimizeCallData, _) {
        return ValueListenableBuilder(
          valueListenable: _editorHandler.isCloseEditMessageWidget(),
          builder: (context, isCloseEditViewMessage, _) {
            return ValueListenableBuilder(
              valueListenable: _editorHandler.isShowQuoteMessageView(),
              builder: (context, isShowQuoteMessageView, _) {
                return ValueListenableBuilder(
                  valueListenable: _channel,
                  builder: (context, channel, _) {
                    return MessageListView(
                      workspaceId: widget.workspaceId,
                      channelId: widget.channelId,
                      userId: widget.userId,
                      hasPinned: displayPinnedMessage.value.isShowPinWidget,
                      hasMinimizeCall: minimizeCallData != null,
                      messageId: widget.focusableMessageId,
                      channel: channel,
                      sendMessageHandler: _sendMessageHandler,
                      translateToHandler: _translateToHandler,
                      onChangeVisibility: _onMessageChangeVisibility,
                      isMessageRequestNotifier: _isMessageRequestNotifier,
                      goToDMMessage: widget.goToDMMessage,
                      goToViewAvatar: widget.goToViewAvatar,
                      goToChannelInfo: callChannelInfo,
                      mapCheckedMessage: mapCheckedMessage,
                      isEnableClickBottom: isEnableClickBottom,
                      isHiddenEditor: isHiddenEditor,
                      countItem: countItem,
                      isChangeAppbar: isChangeAppbar,
                      metaData: _metadata,
                      translatedResultList: _translateResultList,
                      isHiddenBlockUser: isHiddenBlockUser,
                      onClickTakeChannelAvatarPhoto:
                          widget.onClickTakeChannelAvatarPhoto,
                      onClickTapOpenGalleryAvatar:
                          widget.onClickTapOpenGalleryAvatar,
                      isChannelLoaded: _isChannelLoaded,
                      countNewValue: _countNewValue,
                      isShowQuoteViewMessage: isShowQuoteMessageView,
                      isCloseEditViewMessage: isCloseEditViewMessage,
                      fromNotification: _fromNotification,
                      clearAllMessage: clearAllMessage,
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
  }

  void _onTranslateToBlocStateChanged(
    BuildContext context,
    TranslateToState state,
  ) {
    _translateToHandler.handleTranslateToState(
      context: context,
      state: state,
      onLoaded: (metadata, translateResultList) {
        setState(() {
          _metadata = metadata;
          _translateResultList = translateResultList;
          if (pinnedMessage == null) return;
          List<TranslatedResult> translatePinnedMessages = translateResultList
              .where((item) => item.messageId == pinnedMessage!.messageId)
              .toList();
          if (translatePinnedMessages.isNotEmpty) {
            handleListenPinnedMessage();
          }
        });
      },
    );
  }

  void _onMessageChangeVisibility(
    int firstIndex,
    int lastIndex,
    Message firstItem,
    Message lastItem,
    Map<String, GlobalKey> timeIndicatorGlobalKeys,
    double paddingTop,
  ) {
    if (!mounted) return;
    final stringTime = firstItem.createTime!
        .toLocaleTime(Localizations.localeOf(context).toLanguageTag());
    final timeIndicatorInList = timeIndicatorGlobalKeys[stringTime]
        ?.currentContext
        ?.findRenderObject() as RenderBox?;
    if (timeIndicatorInList == null ||
        timeIndicatorInList.localToGlobal(Offset.zero).dy +
                (timeIndicatorInList.size.height * 2) <
            paddingTop) {
      _dateTimeNotifier.value = stringTime;
    } else {
      _dateTimeNotifier.value = '';
    }
  }

  void clearAllMessage() {
    _dateTimeNotifier.value = '';
  }

  void _onMembersStateChanged(BuildContext context, ListMemberState state) {
    _initHandler(context);
    state.when(
      loading: () {},
      loaded: (members) {
        _members = members;
        _handleMentions();
      },
      error: (error) => ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(error)),
      ),
    );
  }

  void _onUsersStateChanged(BuildContext context, ListChatUserState state) {
    _initHandler(context);
    state.when(
      loading: () {},
      loaded: (users) {
        _users = users;

        _handleMentions();
      },
      error: (error) => ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(error)),
      ),
    );
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    _initHandler(context);
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
        if (_channel.value?.type?.isDm == true) {
          _typingListNotifier = _typingHandler.typingListNotifier(
            _workspaceId ?? "",
            _channelId ?? "",
          );
          _channelViewAppBarNotifier.value = ui.ChannelViewAppBar(
            username: getAliasName(_channel.value?.recipientId) ??
                _channel.value!.name!,
            badgeType: userBadgeType,
            activeStatus: _getChannelStatus(),
            typingNotifier: _typingListNotifier,
            avatarUrl: _channel.value?.fullAvatarUrl,
            isGroup: !(_channel.value?.type?.isDm ?? false),
            isShowIconStartCall: shouldShowIconStartCall(),
          );
        }
        _editorHandler.setName(
          getAliasName(_channel.value?.recipientId) ??
              _channel.value?.name ??
              '',
        );
        _users = _addAliasName(_users);
        _handleMentions();
      },
    );
  }

  Map<String, ChatUser> _addAliasName(Map<String, ChatUser> users) {
    return users.map((key, user) {
      user.aliasName = getAliasName(user.userId);
      return MapEntry(key, user);
    });
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((user) => user.userId == userId);
      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  void _blocMessageListener(BuildContext context, MessagesState state) {
    state.maybeWhen(
      loadPinUnPinUpdateMessage: (messages) {
        final newPinnedMessage = messages.isEmpty ? null : messages.first;
        if (pinnedMessage?.messageId == newPinnedMessage?.messageId &&
            pinnedMessage?.updateTime == newPinnedMessage?.updateTime) return;
        pinnedMessage = newPinnedMessage;
        handleListenPinnedMessage();
      },
      pinUnPinUpdateMessage: (message) {
        pinnedMessage = message.isPinned == true ? message : null;
        handleListenPinnedMessage();
      },
      orElse: () {},
    );
  }

  void handleListenPinnedMessage() {
    var content = pinnedMessage?.content;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      var chatUser = _users[pinnedMessage?.userId];
      chatUser?.aliasName = getAliasName(pinnedMessage?.userId);
      if (pinnedMessage?.messageViewType == MessageViewType.invitation ||
          pinnedMessage?.messageViewType == MessageViewType.invitationOwner) {
        if (pinnedMessage?.contentArguments != null)
          content = TranslateContentUtils.translateContent(
            pinnedMessage?.content ?? '',
            pinnedMessage!.contentArguments ?? [],
          );
      }
      TranslatedResult? translatedResult;
      if (pinnedMessage != null) {
        translatedResult = MessageHandler.handelTranslateTo(
          message: pinnedMessage!,
          translateToHandler: _translateToHandler,
          metaData: _metadata,
          translateResultList: _translateResultList,
        );
      }
      content = translatedResult?.isShowTranslateResult == true
          ? translatedResult?.translatedContent
          : content;
      displayPinnedMessage.value = ui.PinMessage(
        extractPinContent: () => content ?? "",
        extractName: () =>
            NameUtils.parseName(
              chatUser,
              _members[pinnedMessage?.userId],
            ) ??
            "",
        extractAvatarUrl: () => UrlUtils.parseAvatar(chatUser?.profile?.avatar),
        isShowPinWidget: pinnedMessage != null,
      );
    });
  }

  void _handleMentions() {
    _users = _addAliasName(_users);
    _editorHandler.setMentions(_users, _members);
  }

  void _openAttachmentPicker() {
    widget.openTakePhotoAndVideoPage?.call();
  }

  Future<void> _handleChooseImageEvent(TakePhotoMessageEvent event) async {
    final photo = await XFile(event.filePath);

    UploadFile imageUploadFile = UploadFile(
      fileRef: UUIDUtils.random(),
      path: photo.path,
      name: photo.name,
      size: await photo.length(),
    );

    _sendMessageHandler.sendImageMessage([imageUploadFile]);
  }

  Future<void> _handleChooseVideoEvent(TakeVideoMessageEvent event) async {
    final video = await XFile(event.filePath);

    UploadFile videoUploadFile = UploadFile(
      fileRef: UUIDUtils.random(),
      path: video.path,
      name: video.name,
      size: await video.length(),
    );

    _sendMessageHandler.sendVideoMessage([videoUploadFile]);
  }

  Future<void> _handleZiishortMessageEvent(
    SendZiishortMessageEvent event,
  ) async {
    final video = await XFile(event.filePath);

    UploadFile videoUploadFile = UploadFile(
      fileRef: UUIDUtils.random(),
      path: video.path,
      name: video.name,
      size: await video.length(),
    );

    _sendMessageHandler.sendVideoMessage(
      [videoUploadFile],
      type: V3AttachmentTypeEnum.ATTACHMENT_TYPE_ENUM_VIDEO_MESSAGE,
    );
  }

  Future<void> _handleSelectedPhoto(List<ui.MediaItem> itemList) async {
    itemList.sort((a, b) => a.selectedIndex.compareTo(b.selectedIndex));
    List<UploadFile> imageList = [];
    List<UploadFile> videoList = [];

    for (final item in itemList) {
      final fileXFile = XFile(item.path);
      UploadFile fileTemp = UploadFile(
        fileRef: UUIDUtils.random(),
        path: fileXFile.path,
        name: fileXFile.name,
        size: await fileXFile.length(),
      );

      switch (item.runtimeType) {
        case ui.PhotoItem:
          imageList.add(fileTemp);
          break;
        case ui.VideoItem:
          videoList.add(fileTemp);
          break;
      }
    }

    if (imageList.isNotEmpty) {
      _sendMessageHandler.sendImageMessage(imageList);
    }

    if (videoList.isNotEmpty) {
      _sendMessageHandler.sendVideoMessage(videoList);
    }
  }

  Future<void> _handleSelectedFile(List<ui.MediaItem> itemList) async {
    await Future.wait(
      itemList.map((item) async {
        final fileXFile = XFile(item.path);
        final uploadFile = UploadFile(
          fileRef: UUIDUtils.random(),
          path: fileXFile.path,
          name: fileXFile.name,
          size: await fileXFile.length(),
        );
        _sendMessageHandler.sendFileMessage(uploadFile);
      }),
    );
  }

  Widget bottomButtonWidget(ui.MessageOptions messageOptions) {
    return ui.BottomButtonWidget(
      onBottomButtonClicked: () {
        if (messageOptions == ui.MessageOptions.delete) {
          handleDeleteMessage();
        }
        if (messageOptions == ui.MessageOptions.forward) {
          handleForward();
        }
      },
      messageOptions: messageOptions,
      isEnableClick: isEnableClickBottom,
    );
  }

  void _onCallDeleteMessage(CallCheckMessagesEvent event) {
    if (ULIDUtils.isValidUlid(_channelId!)) {
      if (_channelId != event.channelId) return;
    }

    if (event.isDelete == true) {
      messageOption = ui.MessageOptions.delete;
    }
    if (event.isForward == true) {
      messageOption = ui.MessageOptions.forward;
    }

    isHiddenEditor.value = true;
    isChangeAppbar.value = true;
    FocusScope.of(context).requestFocus(FocusNode());
  }

  void _updateChannelAppBar() {
    String channelName = _channel.value?.name ?? '';
    String? channelAvatar = _channel.value?.fullAvatarUrl;
    if (_channel.value != null && _channel.value!.type!.isDm) {
      final aliasName = getAliasName(_channel.value?.recipientId);
      _recipientUser?.aliasName = aliasName;
      channelName = _recipientUser != null
          ? NameUtils.parseNameOfUser(_recipientUser!) ?? ''
          : '';
      channelAvatar = UrlUtils.parseAvatar(_recipientUser?.profile?.avatar);
    }
    _typingListNotifier = _typingHandler.typingListNotifier(
      _workspaceId ?? "",
      _channelId ?? "",
    );

    _channelViewAppBarNotifier.value = ui.ChannelViewAppBar(
      username: channelName,
      activeStatus: _getChannelStatus(),
      typingNotifier: _typingListNotifier,
      badgeType: userBadgeType,
      avatarUrl: channelAvatar,
      isGroup: !(_channel.value?.type?.isDm ?? false),
      isShowIconStartCall: shouldShowIconStartCall(),
    );
    _editorHandler.setName(channelName); // Name for share location dialog
  }

  String _getChannelStatus() {
    if (_channel.value == null) return '';
    if (_channel.value!.type!.isDm) {
      return _getDMActiveStatus();
    }
    return getIt<AppLocalizations>()
        .countMembers('${_channel.value!.totalMembers ?? 1}');
  }

  /// Returns the recipient user's active (online) status in a DM.
  ///
  /// - Returns an empty string if the user is ZiiChat.
  /// - Otherwise, formats online status based on [presenceData].
  String _getDMActiveStatus() {
    if (_recipientUser?.userId == GlobalConfig.ZIICHAT_USER_ID) return '';
    return _recipientUser?.presenceData?.getOnlineStatus(
          use24Hour: getIt<AppBloc>().state.is24HourFormat,
          presenceStatus: _recipientPresenceStatus,
        ) ??
        '';
  }

  void handleDeleteMessage() {
    if (_channel.value!.type!.isDm) {
      _channel.value?.recipientId == GlobalConfig.ZIICHAT_USER_ID
          ? showDelete1nMessage(isForMe: true)
          : showDeleteDMMessage();
    } else {
      var memberMe = _members[ChannelViewHandler.sessionKey];
      ui.Roles? role = MemberSettingsHandler.getRoleFromName(memberMe?.role);
      switch (role) {
        case null:
          break;
        case ui.Roles.owner || ui.Roles.admin:
          showDelete1nMessage();
        case ui.Roles.member:
          showDeleteMessageRoleMember();
      }
    }
  }

  bool handleHiddenPin() {
    var memberMe = _members[Config.getInstance().activeSessionKey ?? ''];
    ui.Roles? role = MemberSettingsHandler.getRoleFromName(memberMe?.role);
    switch (role) {
      case null:
        return true;
      case ui.Roles.owner || ui.Roles.admin:
        return false;
      case ui.Roles.member:
        return true;
    }
  }

  void handleForward() {
    ui.BottomSheetUtil.showDefaultBottomSheet(
      context: context,
      child: ForwardViewPage(
        workspaceId: _channel.value?.workspaceId,
        channelId: _channel.value?.channelId,
        userId: widget.userId,
        mapCheckedMessage: mapCheckedMessage,
        messagesBloc: _messagesBloc,
      ),
    );
  }

  void showDeleteDMMessage() {
    List<String> messagesIds = [];
    List<String> messagesTempIds = [];
    mapCheckedMessage.forEach((key, value) {
      value.isTemp == true ? messagesTempIds.add(key) : messagesIds.add(key);
    });
    var aliasName = getAliasName(_channel.value?.recipientId);
    ui.ActionSheetUtil.showDeleteDmMessagesActionSheet(
      context,
      friendName: aliasName ?? _channel.value?.name ?? '',
      onCancel: () {
        Navigator.pop(context);
      },
      onClickDeleteForMe: () {
        if (messagesTempIds.isNotEmpty) {
          _messagesBloc.add(
            MessagesEvent.OnLocalDeleteMessageEvent(
              workspaceId: widget.workspaceId,
              channelId: widget.channelId,
              userId: widget.userId,
              messageIds: messagesTempIds,
            ),
          );
        }
        _messagesBloc.add(
          MessagesEvent.OnDeleteMessageForMeEvent(
            workspaceId: widget.workspaceId,
            channelId: widget.channelId,
            userId: widget.userId,
            messageIds: messagesIds,
          ),
        );
        isChangeAppbar.value = false;
      },
      onClickDeleteForMeAndFriend: () {
        if (messagesTempIds.isNotEmpty) {
          _messagesBloc.add(
            MessagesEvent.OnLocalDeleteMessageEvent(
              workspaceId: widget.workspaceId,
              channelId: widget.channelId,
              userId: widget.userId,
              messageIds: messagesTempIds,
            ),
          );
        }
        _messagesBloc.add(
          MessagesEvent.OnDeleteMessageForEveryOneEvent(
            workspaceId: widget.workspaceId,
            channelId: widget.channelId,
            userId: widget.userId,
            messageIds: messagesIds,
          ),
        );
        isChangeAppbar.value = false;
        AppEventBus.publish(ClosePopupQuoteEvent(messageIds: messagesIds));
      },
      is1Message: messagesIds.length > 1 ? false : true,
    );
  }

  void showDelete1nMessage({bool? isForMe}) {
    List<String> messagesIds = [];
    List<String> messagesTempIds = [];
    mapCheckedMessage.forEach((key, value) {
      value.isTemp == true ? messagesTempIds.add(key) : messagesIds.add(key);
    });

    ui.ActionSheetUtil.showDeleteMessagesActionSheet(
      context,
      onCancel: () {
        Navigator.pop(context);
      },
      onClickDeleteForMe: () {
        if (messagesTempIds.isNotEmpty) {
          _messagesBloc.add(
            MessagesEvent.OnLocalDeleteMessageEvent(
              workspaceId: widget.workspaceId,
              channelId: widget.channelId,
              userId: widget.userId,
              messageIds: messagesTempIds,
            ),
          );
        }
        _messagesBloc.add(
          MessagesEvent.OnDeleteMessageForMeEvent(
            workspaceId: widget.workspaceId,
            channelId: widget.channelId,
            userId: widget.userId,
            messageIds: messagesIds,
          ),
        );
        isChangeAppbar.value = false;
      },
      is1Message: messagesIds.length > 1 ? false : true,
      onClickDeleteForEveryone: () {
        if (messagesTempIds.isNotEmpty) {
          _messagesBloc.add(
            MessagesEvent.OnLocalDeleteMessageEvent(
              workspaceId: widget.workspaceId,
              channelId: widget.channelId,
              userId: widget.userId,
              messageIds: messagesTempIds,
            ),
          );
        }
        _messagesBloc.add(
          MessagesEvent.OnDeleteMessageForEveryOneEvent(
            workspaceId: widget.workspaceId,
            channelId: widget.channelId,
            messageIds: messagesIds,
          ),
        );
        isChangeAppbar.value = false;
        AppEventBus.publish(ClosePopupQuoteEvent(messageIds: messagesIds));
      },
      isRoleMember: isForMe ?? false,
    );
  }

  void showDeleteMessageRoleMember() {
    bool check = mapCheckedMessage.values.any(
      (message) =>
          message.messageViewType == MessageViewType.text ||
          message.messageViewType == MessageViewType.link ||
          message.messageViewType == MessageViewType.file ||
          message.messageViewType == MessageViewType.sticker ||
          message.messageViewType == MessageViewType.ziiShort ||
          message.messageViewType == MessageViewType.ziiVoice ||
          message.messageViewType == MessageViewType.location ||
          message.messageViewType == MessageViewType.video ||
          message.messageViewType == MessageViewType.images ||
          message.messageViewType == MessageViewType.invitation ||
          message.messageViewType == MessageViewType.wave,
    );
    showDelete1nMessage(isForMe: check);
  }

  ui.QuoteMessage? pinQuoteMessage(Message message) {
    if (message.originalMessage == null) return null;
    var chatUser = _users[message.originalMessage!.userId];
    chatUser?.aliasName = getAliasName(message.originalMessage!.userId);
    var member = _members[message.originalMessage!.userId];
    return ui.QuoteMessage(
      originalUsername: NameUtils.parseName(chatUser, member) ?? "",
      originalAvatarUrl: chatUser?.profile?.avatar ?? "",
      originalContent: message.originalMessage?.content ?? "",
    );
  }

  //region Join to meeting room
  void _onOpenMeetingRoom() {
    try {
      final callHandler = getIt<MeetingHandler>();
      final currentRoom = callHandler.getCurrentRoom(
        channelId: _channel.value!.channelId,
        workspaceId: _channel.value!.workspaceId,
      );
      if (currentRoom != null) {
        widget.goToMeetingRoom?.call(
          room: currentRoom,
          channel: _channel.value!,
          isVideoCall: true,
        );
        return;
      }
    } on InOtherRoomException {
      _showCannotMakeThisCallDialog();
    }
  }

  /// Joins a video call by checking permissions and connecting to a room.
  Future<void> _onJoinToGroupCall({bool isVideoCall = true}) async {
    try {
      final callHandler = getIt<MeetingHandler>();
      final currentRoom = callHandler.getCurrentRoom(
        channelId: _channel.value!.channelId,
        workspaceId: _channel.value!.workspaceId,
      );
      if (currentRoom == null) {
        final canCall =
            await _forcePermissionsForCallIsGranted(requestCamera: isVideoCall);
        if (!canCall) return;
        LoadingOverlayHelper.showLoading(context);
        await getIt<MeetingHandler>().getRoomToken(
          channelId: _channel.value!.channelId,
          workspaceId: _channel.value!.workspaceId,
          onSuccess: (token, host) => _connectToRoom(token, host, isVideoCall),
          onError: (error) {
            LoadingOverlayHelper.hideLoading(context);
            _showErrorOccurredDialog();
          },
        );
        return;
      }

      widget.goToMeetingRoom?.call(
        room: currentRoom,
        channel: _channel.value!,
        isVideoCall: true,
      );
    } on InOtherRoomException {
      _showCannotMakeThisCallDialog();
    }
  }

  /// Connect to room with token and host
  void _connectToRoom(String token, String host, bool isVideoCall) {
    getIt<MeetingHandler>().connectToRoom(
      token: token,
      host: host,
      onConnected: (room) {
        LoadingOverlayHelper.hideLoading(context);
        widget.goToMeetingRoom?.call(
          room: room,
          channel: _channel.value!,
          isVideoCall: isVideoCall,
        );
      },
      onError: (error) {
        LoadingOverlayHelper.hideLoading(context);
        _showErrorOccurredDialog();
      },
    );
  }

  /// Requests microphone and optionally camera permissions.
  Future<bool> _checkPermissionsForCall({bool requestCamera = true}) async {
    final completer = Completer<bool>();
    bool _isOpenSetting = false;
    PermissionUtils.requestMicrophonePermission(
      context,
      onOpenSetting: () {
        _isOpenSetting = true;
      },
    ).then((_) {
      if (_isOpenSetting) {
        return completer.complete(false);
      }
      if (requestCamera) {
        PermissionUtils.requestCameraPermission(
          context,
          onOpenSetting: () {
            _isOpenSetting = true;
          },
        ).then((_) {
          if (_isOpenSetting) {
            return completer.complete(false);
          }
          return completer.complete(true);
        });
      } else {
        return completer.complete(true);
      }
    });
    return completer.future;
  }

  /// Requests and checks microphone (and optionally camera) permissions for a call.
  ///
  /// - Always requests microphone permission.
  /// - If [requestCamera] is true, also requests camera permission.
  /// - Returns `true` only if all required permissions are granted.
  Future<bool> _forcePermissionsForCallIsGranted({
    bool requestCamera = true,
  }) async {
    final microGranted =
        await PermissionUtils.requestMicrophonePermission(context);

    if (!microGranted) return false;

    if (requestCamera) {
      return await PermissionUtils.requestCameraPermission(context);
    }

    return true;
  }

  /// Displays a dialog indicating that the user cannot make the call.
  void _showCannotMakeThisCallDialog() {
    ui.DialogUtils.showCannotMakeThisCallDialog(
      context,
      onClickOk: (context) {
        Navigator.pop(context);
      },
    );
  }

  /// Displays a dialog indicating that an error occurred.
  void _showErrorOccurredDialog() {
    ui.DialogUtils.showErrorOccurredTranslateDialog(
      context,
      onOkClicked: () {
        Navigator.of(context).pop();
      },
    );
  }

  Future<bool> _checkPermissionsForCallDM() async {
    final isMicroGranted = await PermissionUtils.requestMicrophonePermission(
      context,
    );
    if (isMicroGranted != true) return false;

    final isCameraGranted = await PermissionUtils.requestCameraPermission(
      context,
    );
    if (isCameraGranted != true) return false;

    return true;
  }

  @override
  void onCallClicked() async {
    final outputMe = await getIt.get<GetMeUseCase>().execute(GetMeInput());

    meInfo = outputMe.user;
    final hasPermission = await _checkPermissionsForCallDM();
    final callerId = Config.getInstance().activeSessionKey;
    final calleeUsername = _recipientUser != null
        ? NameUtils.parseNameOfUser(_recipientUser!) ?? ''
        : '';
    final calleeAvatar = _channel.value!.avatar ?? '';
    if (hasPermission) {
      AppEventBus.publish(
        CallCreatedEvent(
          callee: {
            'user_id': widget.userId,
            'username': calleeUsername,
            'avatar': calleeAvatar,
            'displayName': widget.initUserData?.displayName ?? '',
          },
          caller: {
            'user_id': callerId,
            'username': meInfo!.username ?? '',
            'avatar': meInfo!.profile!.avatar ?? '',
            'displayName': meInfo!.profile!.displayName ?? '',
          },
        ),
      );
    }
  }

  @override
  void onCallGroupClicked() {
    _onJoinToGroupCall(isVideoCall: false);
  }

  @override
  void onCallVideoGroupClicked() {
    _onJoinToGroupCall(isVideoCall: true);
  }
//endregion Join to meeting room
}
