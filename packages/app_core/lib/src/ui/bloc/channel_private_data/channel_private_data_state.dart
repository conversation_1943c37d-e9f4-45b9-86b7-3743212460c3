part of 'channel_private_data_bloc.dart';

@freezed
sealed class ChannelPrivateDataState extends BaseBlocState
    with _$ChannelPrivateDataState {
  const ChannelPrivateDataState._();

  factory ChannelPrivateDataState.initial() = ChannelPrivateDataInitial;

  factory ChannelPrivateDataState.listChannelPrivateData({
    @Default([]) List<ChannelPrivateData> listChannelPrivateData,
  }) = ChannelPrivateDataStateListChannelPrivateData;
}

extension ChannelPrivateDataStateX on ChannelPrivateDataState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(List<ChannelPrivateData> list)? listChannelPrivateData,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is ChannelPrivateDataInitial && initial != null) {
      return initial();
    }
    if (state is ChannelPrivateDataStateListChannelPrivateData &&
        listChannelPrivateData != null) {
      return listChannelPrivateData(state.listChannelPrivateData);
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(List<ChannelPrivateData> list) listChannelPrivateData,
  }) {
    final state = this;

    if (state is ChannelPrivateDataInitial) {
      return initial();
    }
    if (state is ChannelPrivateDataStateListChannelPrivateData) {
      return listChannelPrivateData(state.listChannelPrivateData);
    }

    throw StateError('Unhandled ChannelPrivateDataState: $state');
  }
}
