import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import '../../common/di/di.dart';
import '../../data/repositories/database/enums/presence_state.dart';
import '../../data/repositories/database/enums/user_badge_enum.dart';
import '../../utils/name_utils.dart';
import '../channel/widgets/friend_list_skeleton.dart';
import 'bloc/friend_list_bloc.dart';

class FriendListPage extends StatefulWidget {
  const FriendListPage({
    required this.friendListInterface,
    super.key,
  });

  final FriendListInterface friendListInterface;

  @override
  State<FriendListPage> createState() => _FriendListPageState();
}

const _firstPageToken = '';

class _FriendListPageState extends BasePageState<FriendListPage, FriendListBloc>
    implements ui.FriendListInterface {
  final PagingController<String, ChatUser> _pagingController =
      PagingController(firstPageKey: _firstPageToken);
  List<ChatUser> _friends = [];
  var _noMoreItems = true;
  var _nextPageToken = _firstPageToken;
  int _totalFriendRequest = 0;
  late final UserPrivateDataBloc _userPrivateDataBloc;
  Map<String, UserPrivateData> _mapUserPrivateData = {};
  int badgeEnum = 0;
  ui.UserBadgeType? userBadgeType;

  @override
  void initState() {
    super.initState();
    _pagingController.addPageRequestListener(
      (pageKey) {
        if (!_noMoreItems)
          bloc.add(
            LoadMoreFriendsEvent(
              nextPageToken: _nextPageToken,
              friends: _friends,
            ),
          );
      },
    );
    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    bloc.add(InitiateFriendListEvent());
  }

  @override
  void dispose() {
    _pagingController.dispose();
    super.dispose();
  }

  void _handleLoaded(
    List<ChatUser> chatUsers,
    bool noMoreItems,
  ) {
    _noMoreItems = noMoreItems;
    if (!_noMoreItems && chatUsers.isEmpty) {
      bloc.add(
        LoadMoreFriendsEvent(
          nextPageToken: _nextPageToken,
          friends: chatUsers,
        ),
      );
      return;
    }
    if (noMoreItems) {
      _noMoreItems = true;
      _pagingController.appendLastPage([]);
    }
    if (chatUsers.isEmpty) {
      return;
    }
    _nextPageToken = chatUsers.last.userId;
    _pagingController.itemList = chatUsers;
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        listUserPrivateData.forEach((item) {
          _mapUserPrivateData[item.userId] = item;
        });
        setState(() {});
      },
    );
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      var user = _mapUserPrivateData[userId];
      return user?.aliasName != null && user!.aliasName!.isNotEmpty
          ? user.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<UserPrivateDataBloc>.value(value: _userPrivateDataBloc),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocUserPrivateListener,
          ),
        ],
        child: BlocBuilder<FriendListBloc, FriendListState>(
          buildWhen: (previous, current) {
            return previous != current;
          },
          builder: (BuildContext context, state) {
            return state.maybeWhen(
              initial: _buildFriendListPage,
              loaded: (friends, totalFriendRequest, noMoreItems) {
                _totalFriendRequest = totalFriendRequest;

                _friends = _sortAlphaNameFriend(friends);
                _handleLoaded(_friends, noMoreItems);
                return _buildFriendListPage();
              },
              orElse: _buildFriendListPage,
            );
          },
        ),
      ),
    );
  }

  List<ChatUser> _sortAlphaNameFriend(List<ChatUser> friends) {
    return friends
        .map(
          (friend) {
            friend.aliasName = getAliasName(friend.userId);
            return friend;
          },
        )
        .whereType<ChatUser>()
        .toList()
      ..sort((a, b) {
        return NameUtils.parseNameOfUser(a)!
            .toLowerCase()
            .compareTo(NameUtils.parseNameOfUser(b)!.toLowerCase());
      });
  }

  Widget _buildFriendListPage() {
    return ui.FriendListPage(interface: this);
  }

  @override
  Widget friendListWidget() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
        child: CustomScrollView(
          slivers: [
            PagedSliverList<String, ChatUser>(
              pagingController: _pagingController,
              builderDelegate: PagedChildBuilderDelegate<ChatUser>(
                animateTransitions: true,
                itemBuilder: (context, chatUser, index) {
                  badgeEnum = chatUser.profile?.userBadgeType ?? 0;
                  userBadgeType =
                      UserBadgeEnumExtension.getEnumByValue(badgeEnum)
                          .toUserBadgeType();
                  var friendItem = ui.FriendItem(
                    badgeType: userBadgeType,
                    userId: chatUser.userId,
                    avatarPath: UrlUtils.parseAvatar(chatUser.profile?.avatar),
                    isOnline: chatUser.presenceData?.presenceState ==
                        PresenceStateEnum.ONLINE,
                    displayName: NameUtils.parseNameOfUser(chatUser)!,
                    statusEmoji: chatUser.statusData?.status ?? '',
                    statusText: chatUser.statusData?.content ?? '',
                  );
                  return ui.FriendWidget(
                    friendItem: friendItem,
                    onClickFriendItem: () {
                      onClickFriendItem(friendItem);
                    },
                  );
                },
                firstPageProgressIndicatorBuilder: (_) {
                  return FriendListSkeleton();
                },
                noItemsFoundIndicatorBuilder: (_) {
                  return FriendListSkeleton();
                },
                newPageProgressIndicatorBuilder: (context) {
                  return FriendListSkeleton();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  int friendRequestCount() {
    return _totalFriendRequest;
  }

  @override
  void onClickFindNearBy() {
    Log.d('onClickFindNearBy');
  }

  @override
  void onClickFriendRequest() {
    widget.friendListInterface.onClickFriendRequest();
  }

  void onClickInviteToZiiChat() {
    Log.d('onClickInviteToZiiChat');
  }

  void onClickFriendItem(ui.FriendItem friendItem) {
    widget.friendListInterface.onClickFriendItem(friendItem);
  }

  @override
  bool allowFindNearBy() {
    // TODO: implement allowFindNearBy
    return true;
  }

  @override
  void onClickInviteFriends() {
    // TODO: implement onClickInviteFriends
  }

  void onClickZiiChat(BuildContext context) {
    // TODO: implement onClickZiiChat
  }

  @override
  bool displaySkeleton() {
    return false;
  }

  @override
  List<ui.FriendItem> friendList() {
    return List.generate(((_friends.length)), (index) {
      return ui.FriendItem(
        userId: _friends[index].userId,
        avatarPath: _friends[index].profile?.avatar ?? '',
        isOnline: _friends[index].presenceData?.isOnline ?? false,
        displayName: _friends[index].username ?? '',
        statusEmoji: _friends[index].statusData?.status ?? '',
        statusText: _friends[index].statusData?.content ?? '',
      );
    });
  }

  @override
  bool isFriendList() {
    return true;
  }
}
