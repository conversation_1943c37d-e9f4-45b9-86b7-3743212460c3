<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools">
    <uses-permission
            android:name="android.permission.INTERNET"/>
    <uses-permission
            android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission
            android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission
            android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission
            android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION"/>
    <uses-permission
            android:name="android.permission.FOREGROUND_SERVICE_CAMERA"/>
    <uses-permission
            android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE"/>
    <uses-permission
            android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission
            android:name="android.permission.USE_BIOMETRIC"/>
    <uses-permission
            android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission
            android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
    <uses-permission
            android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30"/>
    <uses-permission
            android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission
            android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30"/>
    <!-- To handle the reselection within the app on devices running Android 14
     or higher if your app targets Android 14 (API level 34) or higher.  -->
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED"/>
    <uses-permission
            android:name="android.permission.VIBRATE"/>
    <uses-permission
            android:name="android.permission.WRITE_EXTERNAL_STORAGE"
            android:maxSdkVersion="32"
            tools:ignore="ScopedStorage"
            tools:replace="android:maxSdkVersion"/>
    <uses-permission
            android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission
            android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission
            android:name="android.permission.READ_MEDIA_AUDIO"/>
    <uses-permission
            android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission
            android:name="android.permission.WAKE_LOCK"/>
    <uses-permission
            android:name="android.permission.TURN_SCREEN_ON"/>
    <uses-permission
            android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK"/>

    <uses-permission
            android:name="android.permission.MANAGE_OWN_CALLS"/>
    <uses-permission
            android:name="android.permission.CALL_PHONE"/>
    <uses-permission
            android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission
            android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission
            android:name="android.permission.READ_EXTERNAL_STORAGE"
            android:maxSdkVersion="32"/>
    <uses-permission
            android:name="android.permission.CAMERA"/>

    <uses-feature
            android:name="android.hardware.camera"/>
    <uses-feature
            android:name="android.hardware.camera.autofocus"/>
    <uses-feature
            android:name="android.hardware.camera.flash"/>
    <uses-feature
            android:name="android.hardware.camera.front"/>
    <uses-feature
            android:name="android.hardware.telephony"
            android:required="false"/>
    <application
            android:name="${applicationName}"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name">
        <activity
                android:name=".MainActivity"
                android:allowBackup="false"
                android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
                android:enableOnBackInvokedCallback="true"
                android:exported="true"
                android:hardwareAccelerated="true"
                android:launchMode="singleTask"
                android:showWhenLocked="false"
                android:taskAffinity="${applicationId}"
                android:theme="@style/LaunchTheme"
                android:turnScreenOn="true"
                android:windowSoftInputMode="adjustResize"
                tools:ignore="DiscouragedApi,LockedOrientationActivity,UnusedAttribute">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                    android:name="io.flutter.embedding.android.NormalTheme"
                    android:resource="@style/NormalTheme"/>
            <intent-filter>
                <action
                        android:name="FLUTTER_NOTIFICATION_CLICK"/>
                <category
                        android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            <meta-data
                    android:name="flutter_deeplinking_enabled"
                    android:value="false"/>
            <intent-filter
                    android:autoVerify="true">
                <action
                        android:name="android.intent.action.VIEW"/>
                <category
                        android:name="android.intent.category.DEFAULT"/>
                <category
                        android:name="android.intent.category.BROWSABLE"/>
                <data
                        android:host="zii.chat"
                        android:pathPrefix="/i/"
                        android:scheme="https"/>
            </intent-filter>
            <intent-filter
                    android:autoVerify="true">
                <action
                        android:name="android.intent.action.VIEW"/>
                <category
                        android:name="android.intent.category.DEFAULT"/>
                <category
                        android:name="android.intent.category.BROWSABLE"/>
                <data
                        android:host="zii.chat"
                        android:pathPrefix="/connect/"
                        android:scheme="https"/>
            </intent-filter>
            <intent-filter
                    android:autoVerify="true">
                <action
                        android:name="android.intent.action.VIEW"/>
                <category
                        android:name="android.intent.category.DEFAULT"/>
                <category
                        android:name="android.intent.category.BROWSABLE"/>
                <data
                        android:host="zii.chat"
                        android:pathPrefix="/send"
                        android:scheme="https"/>
            </intent-filter>
            <intent-filter
                    android:autoVerify="true">
                <action
                        android:name="android.intent.action.VIEW"/>
                <category
                        android:name="android.intent.category.DEFAULT"/>
                <category
                        android:name="android.intent.category.BROWSABLE"/>
                <data
                        android:host="zii.chat"
                        android:pathPrefix="/qrauth/"
                        android:scheme="https"/>
            </intent-filter>
            <meta-data
                    android:name="android.app.shortcuts"
                    android:resource="@xml/shortcuts"/>
            <intent-filter>
                <action
                        android:name="android.intent.action.SEND"/>
                <action
                        android:name="android.intent.action.SEND_MULTIPLE"/>
                <category
                        android:name="android.intent.category.DEFAULT"/>
                <data
                        android:mimeType="*/*"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
                android:name="flutterEmbedding"
                android:value="2"/>
        <receiver
                android:name="com.dexterous.flutterlocalnotifications.ActionBroadcastReceiver"
                android:exported="false"/>
        <receiver
                android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver"
                android:exported="false"/>
        <receiver
                android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
                android:exported="false">
            <intent-filter>
                <action
                        android:name="android.intent.action.MY_PACKAGE_REPLACED"/>
                <action
                        android:name="android.intent.action.QUICKBOOT_POWERON"/>
                <action
                        android:name="com.htc.intent.action.QUICKBOOT_POWERON"/>
            </intent-filter>
        </receiver>
        <service
                android:name="com.google.firebase.messaging.FirebaseMessagingService"
                android:exported="false"
                android:stopWithTask="false">
            <intent-filter>
                <action
                        android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>
        <service
                android:name="com.ziichat.NotificationService"
                android:exported="false"
                android:stopWithTask="false">
            <intent-filter>
                <action
                        android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>
        <meta-data
                android:name="com.google.firebase.messaging.default_notification_channel_id"
                android:value="channel_id_bg"/>
        <meta-data
                android:name="com.google.android.geo.API_KEY"
                android:value="AIzaSyDPZjtmxaoeTvSEon3o8m2nFXyawC1_bYM"/>
        <service
                android:name="de.julianassmann.flutter_background.IsolateHolderService"
                android:enabled="true"
                android:exported="false"
                android:foregroundServiceType="mediaProjection"/>
        <service
                android:name="com.pravera.flutter_foreground_task.service.ForegroundService"
                android:foregroundServiceType="microphone"
                android:exported="false"/>
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action
                    android:name="android.intent.action.PROCESS_TEXT"/>
            <data
                    android:mimeType="text/plain"/>
        </intent>
    </queries>
    <queries>
        <intent>
            <action
                    android:name="android.intent.action.VIEW"/>
            <data
                    android:scheme="http"/>
        </intent>
    </queries>
    <queries>
        <intent>
            <action
                    android:name="android.speech.RecognitionService"/>
        </intent>
    </queries>
</manifest>