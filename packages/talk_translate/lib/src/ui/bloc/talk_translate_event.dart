part of 'talk_translate_bloc.dart';

@freezed
sealed class TalkTranslateEvent extends BaseBlocEvent
    with _$TalkTranslateEvent {
  const TalkTranslateEvent._();

  factory TalkTranslateEvent.initialize({
    required Locale myLocale,
  }) = TalkTranslateInitializeEvent;

  factory TalkTranslateEvent.startListening({
    required String localeId,
    required bool isSender,
  }) = TalkTranslateStartListeningEvent;

  factory TalkTranslateEvent.stopListening({
    required String content,
    required bool isSender,
  }) = TalkTranslateStopListeningEvent;

  factory TalkTranslateEvent.textRecognized(
    String textRecognized,
  ) = TalkTranslateTextRecognizedEvent;

  factory TalkTranslateEvent.translateText({
    required String messageId,
    required bool isSender,
    required bool isRetry,
  }) = TalkTranslateTranslateTextEvent;

  factory TalkTranslateEvent.addMessage({
    required String messageId,
    required List<TATMessage> messages,
    required bool isSender,
  }) = TalkTranslateAddMessageEvent;

  factory TalkTranslateEvent.speakText({
    required String messageId,
    required String text,
    required bool isSender,
  }) = TalkTranslateSpeakTextEvent;

  factory TalkTranslateEvent.completeSpeaking({
    required String messageId,
    required bool isSender,
  }) = TalkTranslateCompleteSpeakingEvent;

  factory TalkTranslateEvent.stopSpeaking({
    required String messageId,
    required bool isSender,
  }) = TalkTranslateStopSpeakingEvent;

  factory TalkTranslateEvent.selectLanguage({
    required Locale language,
    required bool isSender,
  }) = TalkTranslateSelectLanguageEvent;

  factory TalkTranslateEvent.clearTextRegconized() =
      TalkTranslateClearTextRegconizedEvent;

  factory TalkTranslateEvent.swapLanguages() = TalkTranslateSwapLanguagesEvent;
}
