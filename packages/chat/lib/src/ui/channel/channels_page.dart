import 'dart:async';
import 'dart:io';

import 'package:app_core/core.dart' hide Config;
import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart' as chat;
import '../../../chat.dart';
import '../../common/di/di.dart';
import '../../data/repositories/database/enums/user_badge_enum.dart';
import '../../domain/event_bus/channel/channel_events_handler.dart';
import '../create_channel/create_channel_bottom_sheet.dart';
import '../message_request/bloc/message_request_bloc.dart';
import '../user_status/bloc/user_status_bloc.dart';
import 'widgets/channel_circle_status_skeleton.dart';
import 'widgets/channel_item.dart';
import 'widgets/channel_list_skeleton.dart';

part './widgets/channel_list_view.dart';
part './widgets/message_request_view.dart';
part './widgets/user_status_list_view.dart';

class ChannelsPage extends StatefulWidget {
  const ChannelsPage({
    required this.interface,
    super.key,
  });

  final ChannelsInterface interface;

  @override
  State<ChannelsPage> createState() => _ChannelsPageState();
}

class _ChannelsPageState extends State<ChannelsPage>
    implements ui.ChannelAppBarInterface {
  GlobalKey<ChannelListViewState> _channelListKey = GlobalKey();

  late final ChannelsBloc _channelsBloc;
  late final MessageRequestBloc _messageRequestBloc;
  late final UserPrivateDataBloc _userPrivateDataBloc;
  late final ChannelPrivateDataBloc _channelPrivateDataBloc;

  late final UserReportBloc _userReportBloc;
  late BlockUserBloc _blockUserBloc;
  late SettingNotificationBloc _settingNotificationBloc;
  late UserStatusBloc _userStatusBloc;
  final ScrollController _scrollController = ScrollController();

  ValueNotifier<ui.MinimizeCallData?> _minimizeCallData = ValueNotifier(null);
  MeetingRoomInfo? _roomInfo;

  @override
  void initState() {
    super.initState();
    _channelsBloc = getIt<ChannelsBloc>();
    _channelsBloc.add(InitiateChannelsEvent());
    _messageRequestBloc = getIt<MessageRequestBloc>();
    _messageRequestBloc.add(InitMessageRequestEvent());
    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    _channelPrivateDataBloc = getIt<ChannelPrivateDataBloc>();
    _channelPrivateDataBloc.add(InitChannelPrivateDataEvent());
    _userReportBloc = getIt<UserReportBloc>();
    _blockUserBloc = getIt<BlockUserBloc>();
    _blockUserBloc.add(OnLoadListBlockUserEvent());
    _settingNotificationBloc = getIt<SettingNotificationBloc>();
    _userStatusBloc = getIt<UserStatusBloc>();
    _userStatusBloc.add(UserStatusEvent.initiate());
    getIt<MeetingHandler>().addRoomListener(_meetingRoomListener);
  }

  @override
  void dispose() {
    getIt<MeetingHandler>().removeRoomListener(_meetingRoomListener);
    super.dispose();
  }

  void _meetingRoomListener(MeetingRoomInfo? roomInfo) {
    if (roomInfo != null) {
      _roomInfo = roomInfo;
      _minimizeCallData.value = ui.MinimizeCallData(
        channelName: roomInfo.channelName,
        numberParticipant: roomInfo.numberParticipants,
        timeValue: TimeUtils.formatCallDuration(roomInfo.duration),
        callJoined: true,
      );
    } else {
      _minimizeCallData.value = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final appBarHeight =
        Platform.isIOS ? kMinInteractiveDimensionCupertino : kToolbarHeight;
    final paddingTop = MediaQuery.of(context).padding.top + appBarHeight;
    final paddingBot = MediaQuery.of(context).padding.bottom;
    return ui.AppScaffold(
      hasSafeArea: false,
      systemUIColor: Colors.transparent,
      systemUIBottomColor: Colors.transparent,
      appBar: ui.ChannelAppBarWidget(interface: this),
      body: MultiBlocProvider(
        providers: [
          BlocProvider<ChannelsBloc>.value(value: _channelsBloc),
          BlocProvider<MessageRequestBloc>.value(value: _messageRequestBloc),
          BlocProvider<UserPrivateDataBloc>.value(value: _userPrivateDataBloc),
          BlocProvider<ChannelPrivateDataBloc>.value(
            value: _channelPrivateDataBloc,
          ),
          BlocProvider<UserReportBloc>.value(
            value: _userReportBloc,
          ),
          BlocProvider<BlockUserBloc>.value(
            value: _blockUserBloc,
          ),
          BlocProvider<SettingNotificationBloc>.value(
            value: _settingNotificationBloc,
          ),
          BlocProvider<UserStatusBloc>.value(
            value: _userStatusBloc,
          ),
        ],
        child: Column(
          children: [
            _buildMinimizeCallWidget(context, paddingTop),
            Expanded(
              child: ui.AppOnlyOnePointerUtilWidget(
                child: ui.AppRawScrollBar(
                  controller: _scrollController,
                  timeToFade: Duration(milliseconds: 1000),
                  padding: EdgeInsets.only(
                    right: 4.w,
                    top: paddingTop,
                    bottom: paddingBot,
                  ),
                  thickness: 3.w,
                  fadeDuration: Duration(milliseconds: 1000),
                  child: CustomScrollView(
                    controller: _scrollController,
                    slivers: [
                      SliverToBoxAdapter(
                        child: ValueListenableBuilder<ui.MinimizeCallData?>(
                          valueListenable: _minimizeCallData,
                          builder: (context, data, _) {
                            return SizedBox(
                              height: data == null ? paddingTop : 0,
                            );
                          },
                        ),
                      ),
                      UserStatusListView(
                        onTapFriendStatus: widget.interface.onTapFriendStatus,
                      ),
                      MessageRequestView(
                        onTap: widget.interface.onTapMessageRequest,
                      ),
                      ChannelListView(
                        key: _channelListKey,
                        onTapChannel: widget.interface.onTapChannel,
                      ),
                      SliverToBoxAdapter(
                        child: SizedBox(height: paddingBot),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMinimizeCallWidget(
    BuildContext context,
    double paddingTop,
  ) {
    return ValueListenableBuilder<ui.MinimizeCallData?>(
      valueListenable: _minimizeCallData,
      builder: (context, data, _) {
        if (data == null) {
          return const SizedBox.shrink();
        }
        return Padding(
          padding: EdgeInsets.only(top: paddingTop),
          child: ui.MinimizeCallWidget(
            onClickToFullScreen: () {
              _onOpenMeetingRoom();
            },
            onClickToJoin: () {},
            numberParticipant: data.numberParticipant,
            channelName: data.channelName,
            timeValue: data.timeValue,
            callJoined: true,
          ),
        );
      },
    );
  }

  void _onOpenMeetingRoom() async {
    try {
      final callHandler = getIt<MeetingHandler>();
      final currentRoom = callHandler.getCurrentRoom(
        channelId: _roomInfo!.channelId,
        workspaceId: _roomInfo!.workspaceId,
      );
      if (currentRoom != null) {
        widget.interface.goToMeetingRoom(
          channelId: _roomInfo!.channelId,
          workspaceId: _roomInfo!.workspaceId,
          channelName: _roomInfo!.channelName,
          room: currentRoom,
          isVideoCall: true,
        );
        return;
      }
    } on InOtherRoomException {
      Log.e(
        name: '_ChannelsPageState._onOpenMeetingRoom',
        'InOtherRoomException',
      );
    }
  }

  @override
  void onScanQrIconClicked() {
    widget.interface.onTapScanQR();
  }

  @override
  void onTranslateIconClicked() {
    widget.interface.onTapTranslate();
  }

  @override
  void onPlusSquareIconClicked() {
    ui.BottomSheetUtil.showDefaultBottomSheet(
      context: context,
      child: CreateChannelBottomSheet(
        onCreatedChannel: (channel) {
          widget.interface.onTapChannel(
            channel.workspaceId,
            channel.channelId,
            null,
          );
        },
        onTapTakePhoto: widget.interface.opTapTakePhotoAvatar,
        onTapOpenGallery: widget.interface.onTapOpenGalleryAvatar,
        openImageViewPage: widget.interface.openImageViewPage,
      ),
    );
  }
}
