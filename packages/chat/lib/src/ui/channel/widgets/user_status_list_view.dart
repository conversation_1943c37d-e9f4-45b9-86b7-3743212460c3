part of '../channels_page.dart';

class UserStatusListView extends StatefulWidget {
  const UserStatusListView({super.key, this.onTapFriendStatus});

  final void Function(
    String userId,
  )? onTapFriendStatus;

  @override
  State<UserStatusListView> createState() => _UserStatusListViewState();
}

class _UserStatusListViewState extends State<UserStatusListView>
    with AutoRouteAwareStateMixin {
  List<ChatUser> _users = [];

  bool isStatusBadgeClick = false;
  int badgeEnum = 0;
  ui.UserBadgeType? userBadgeType;
  late UserStatusBloc _bloc;

  String get _myUserId => Config.getInstance().activeSessionKey!;

  @override
  void didPopNext() {
    super.didPopNext();
    _bloc.add(UserStatusEvent.reLoadStatus());
  }

  @override
  void initState() {
    _bloc = context.read<UserStatusBloc>();
    super.initState();
  }

  @override
  void dispose() {
    _bloc.add(UserStatusEvent.UnSubscription());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SliverToBoxAdapter(
      child: Container(
        height: 100.h,
        width: screenWidth,
        color: isDarkMode
            ? ui.AppColors.bgChannelCircleAvatarDark
            : ui.AppColors.statusListLight,
        child: BlocBuilder<UserStatusBloc, UserStatusState>(
          builder: (context, state) {
            Widget child = SizedBox.shrink();

            state.when(
              initial: () {
                child = ChannelCircleStatusSkeleton();
              },
              loaded: (users) {
                _users = List.from(users);
                child = _buildListUserStatus(users);
              },
              updatedStatus: (List<ChatUser> users) {
                _users = List.from(users);
                child = _buildListUserStatus(users);
              },
            );
            return child;
          },
        ),
      ),
    );
  }

  ListView _buildListUserStatus(List<chat.ChatUser> users) {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: users.length,
      itemExtent: 85,
      itemBuilder: (context, index) {
        final user = users[index];
        final ui.ChannelCircleAvatar channelCircleAvatar =
            ui.ChannelCircleAvatar(
          id: user.userId,
          avatarUrl: UrlUtils.parseAvatar(
            user.profile?.originalAvatar ?? '',
          ),
          isOnline: user.presenceData?.isOnline,
          channelStatusBadge: ui.ChannelStatusBadge(
            emoji: user.statusData?.status ?? '',
            content: user.statusData?.content ?? '',
          ),
          isMyStatusBadge: _myUserId == user.userId,
        );
        return ui.ChannelCircleAvatarWidget(
          width: 100.w,
          height: 100.w,
          channelCircleAvatar: channelCircleAvatar,
          onCircleAvatarClicked: _onChannelCircleAvatarClicked,
          onSeflCircleAvatarClicked: () => _onSelfCircleAvatarClicked(context),
          onStatusBadgeClicked: (channelStatusBadge) async {
            await _onChannelStatusBadgeClicked(
              context,
              channelStatusBadge,
              user,
              _myUserId,
            );
          },
        );
      },
    );
  }

  void _onChannelCircleAvatarClicked(
    ui.ChannelCircleAvatar channelCircleAvatar,
  ) {
    widget.onTapFriendStatus!(channelCircleAvatar.id);
  }

  void _onSelfCircleAvatarClicked(
    BuildContext context,
  ) {
    getIt<UserStatusHandler>().showAddStatusBottomSheet(
      context,
      onCancel: () => AppEventBus.publish(PopToHomeEvent()),
      onAddStatusSuccessful: (content, emoji, statusDuration) =>
          _onUpdateUserStatus(
        ui.Status(
          emoji: emoji,
          statusText: content,
          hourDuration: statusDuration,
        ),
      ),
    );
  }

  void _onUpdateUserStatus(ui.Status status) {
    LoadingOverlayHelper.showLoading(context);

    setState(() {
      _users[0].statusData = ChatUserStatus(
        content: status.statusText,
        status: status.emoji,
        expireAfterTime: ChatUserStatusExpireAfterTimeEnum.values.firstWhere(
          (e) => e.value == status.hourDuration,
          orElse: () => ChatUserStatusExpireAfterTimeEnum.UNSPECIFIED,
        ),
      );
    });
    context
        .read<UserStatusBloc>()
        .add(UserStatusEvent.updatedStatus(users: _users));
    LoadingOverlayHelper.hideLoading(context);
    AppEventBus.publish(PopToHomeEvent());
  }

  Future<void> _onChannelStatusBadgeClicked(
    BuildContext context,
    ui.ChannelStatusBadge channelStatusBadge,
    ChatUser user,
    String sessionKey,
  ) async {
    String? aliasName;

    final outputUser = await getIt
        .get<GetChatUserUseCase>()
        .execute(GetChatUserInput(userId: user.userId));

    if (outputUser.user == null) return;

    badgeEnum = user.profile?.userBadgeType ?? 0;
    userBadgeType =
        UserBadgeEnumExtension.getEnumByValue(badgeEnum).toUserBadgeType();

    final outputAlias =
        await GetIt.instance.get<GetUserPrivateDataByListUserUseCase>().execute(
              GetUserPrivateDataByListUserUseCaseInput(
                listUserId: [outputUser.user!.userId],
              ),
            );
    aliasName = outputAlias.data?.isNotEmpty == true
        ? outputAlias.data!.first.aliasName
        : null;
    if (outputUser.user?.statusData == null) {
      ui.DialogUtils.showStatusUnavailable(
        context,
        onOKClicked: (dialogContext) {
          AppEventBus.publish(PopToHomeEvent());
        },
      );
      _users.removeWhere((e) => e.userId == user.userId);
      _users = [..._users];
      context
          .read<UserStatusBloc>()
          .add(UserStatusEvent.updatedStatus(users: _users));
      return;
    }

    if (!isStatusBadgeClick) {
      isStatusBadgeClick = true;
      ui.BottomSheetUtil.showBottomSheetUserStatus(
        context,
        onClose: () {
          isStatusBadgeClick = false;
          context.read<UserStatusBloc>().add(UserStatusEvent.initiate());
        },
        bottomSheetUserStatus: ui.BottomSheetUserStatus(
          isMyStatus: outputUser.user?.userId == sessionKey,
          username: '@${outputUser.user?.username ?? ''}',
          name: (aliasName?.isNotEmpty == true
                  ? aliasName
                  : (outputUser.user?.profile?.displayName?.isNotEmpty == true
                      ? outputUser.user?.profile?.displayName
                      : '')) ??
              '',
          content: outputUser.user?.statusData?.content ?? '',
          emoji: outputUser.user?.statusData?.status ?? '',
          avatarUrl: UrlUtils.parseAvatar(
            outputUser.user?.profile?.originalAvatar ?? '',
          ),
          badgeType: userBadgeType,
        ),
        onEditStatusButtonClicked: (_) {
          _onEditStatusButtonClicked(context, _);
        },
        onDeleteButtonClicked: (_) {
          _onDeleteButtonClicked(context);
        },
      );
    }
  }

  void _onEditStatusButtonClicked(
    BuildContext context,
    ui.BottomSheetUserStatus bottomSheetUserStatus,
  ) {
    final statusDuration = _users.first.statusData!.expireAfterTime!.value;
    getIt<UserStatusHandler>().showUpdateStatusBottomSheet(
      context,
      onCancel: () => AppEventBus.publish(PopToHomeEvent()),
      initContent: bottomSheetUserStatus.content ?? '',
      initEmoji: bottomSheetUserStatus.emoji ?? '',
      statusDuration: statusDuration,
      onUpdateStatusSuccessful: (content, emoji, statusDuration) =>
          _onUpdateUserStatus(
        ui.Status(
          emoji: emoji,
          statusText: content,
          hourDuration: statusDuration,
        ),
      ),
    );
  }

  void _onDeleteButtonClicked(
    BuildContext context,
  ) {
    getIt<UserStatusHandler>().showDeleteStatusActionSheet(
      context,
      onCancel: () => Navigator.of(context).pop(),
      onDeleteStatusSuccessful: (content, emoji, statusDuration) =>
          _onUpdateUserStatus(
        ui.Status(
          emoji: emoji,
          statusText: content,
          hourDuration: statusDuration,
        ),
      ),
    );
  }
}

class ChannelBottomSheetImplementation
    implements ui.BottomSheetUserStatusInterface {
  @override
  void onDeleteButtonClicked(bottomSheetUserStatus) {}

  @override
  void onEditStatusButtonClicked(bottomSheetUserStatus) {}
}
