import 'dart:async';

import 'package:dartx/dartx.dart';
import 'package:injectable/injectable.dart' hide Order;

import '../../../chat.dart';
import 'cached/message_modification_cache.dart';
import 'channel_metadata_repository.dart';
import 'database/database.dart';
import 'database/generated/objectbox.g.dart';
import 'source/api/models/channel_identification.dart';

@LazySingleton(as: MessageRepository)
class MessageRepositoryImpl implements MessageRepository {
  MessageRepositoryImpl(
    this._chatStore,
    this._cache,
    this._channelMetaDataRepository,
  );

  final ChatStore _chatStore;
  final MessageModificationCache _cache;
  final ChannelMetaDataRepository _channelMetaDataRepository;

  Box<Message> get _messageBox => _chatStore.box<Message>();

  Box<Attachment> get _attachmentBox => _chatStore.box<Attachment>();

  String get _sessionKey => Config.getInstance().activeSessionKey ?? '';

  /// Preserves local metadata from an existing message.
  void _handleKeepLocalMetadata(Message newMessage, Message oldMessage) {
    newMessage.id = oldMessage.id;
    newMessage.isFirstMessage = oldMessage.isFirstMessage;

    // Keep media attachments
    if (oldMessage.mediaAttachments.isNotEmpty &&
        newMessage.attachmentType == AttachmentType.PHOTO) {
      // Clone the old attachments and filter invalid ones (must have ref).
      final oldAttachments = List<Attachment>.from(oldMessage.mediaAttachments)
          .where((a) => a.isValid)
          .toList();
      // Create a map for quick lookup of existing Attachments by ref.
      final newAttachmentRefs = {
        for (var att in newMessage.mediaAttachments) att.ref: att,
      };

      for (int index = 0; index < oldAttachments.length; index++) {
        if (!newAttachmentRefs.containsKey(oldAttachments[index].ref)) {
          oldAttachments[index].message.target = newMessage;
          // Add the attachment back to the message.
          newMessage.mediaAttachments.insert(index, oldAttachments[index]);
        }
      }
    }
  }

  /// Private method to handle batch insertion or updating of messages.
  /// Returns a list of message IDs after insertion/updating.
  List<int> _insertOrUpdateMessages(
    List<Message> messages, {
    bool isInsertOne = false,
  }) {
    final messagesToUpdate =
        messages.where(shouldInsertOrUpdateMessage).toList();

    if (messagesToUpdate.isEmpty) {
      // No need to update/insert, return current IDs.
      return messages.map((m) => m.id).toList();
    }

    // Update local metadata if the message already exists.
    _existsAndUpdate(messagesToUpdate);

    // Update the cache for each message.
    for (var message in messagesToUpdate) {
      updateCacheForMessage(message);
    }

    // Collect all non-null attachmentIds from the messages to update.
    final allAttachmentIds = messagesToUpdate
        .expand(
          (message) => message.mediaAttachments.map((a) => a.attachmentId),
        )
        .whereType<String>() // Remove null values.
        .toSet()
        .toList();

    // Collect all non-null attachmentRefs from the messages to update.
    final allAttachmentRefs = messages
        .expand(
          (message) => message.mediaAttachments.map((a) => a.ref),
        )
        .whereType<String>() // Remove null values.
        .toSet()
        .toList();

    // Query all existing Attachments based on the collected attachmentIds.
    final existingAttachments = _attachmentBox
        .query(
          Attachment_.attachmentId
              .oneOf(allAttachmentIds)
              .or(Attachment_.ref.oneOf(allAttachmentRefs)),
        )
        .build()
        .find();

    // Create a map for quick lookup of existing Attachments by attachmentId.
    final attachmentIdToExisting = {
      for (var att in existingAttachments) att.attachmentId: att,
    };
    // Create a map for quick lookup of existing Attachments by ref.
    final attachmentRefToExisting = {
      for (var att in existingAttachments) att.ref: att,
    };

    final newMessageCount =
        isInsertOne ? messagesToUpdate.count((msg) => msg.id <= 0) : 0;

    // Perform operations within a transaction to ensure data consistency.
    _chatStore.runInTransaction(TxMode.write, () {
      // **Step 1: Remove all existing attachments with these attachmentIds globally.**
      for (var att in existingAttachments) {
        _attachmentBox.remove(att.id);
      }

      // **Step 2: Iterate through each message and handle attachments.**
      for (var message in messagesToUpdate) {
        // Clone the new attachments from the message and filter invalid ones (must have ref).
        final newAttachments = List<Attachment>.from(message.mediaAttachments)
            .where((a) => a.isValid)
            .toList();
        // Clear the current attachments in the message.
        message.mediaAttachments.clear();

        for (var attachment in newAttachments) {
          _updateAttachmentId(
            attachmentIdToExisting,
            attachment,
            attachmentRefToExisting,
            message,
          );

          // Establish the relationship with the message.
          attachment.message.target = message;
          // Add the attachment back to the message.
          message.mediaAttachments.add(attachment);
        }
        _attachmentBox.putMany(newAttachments);

        // Update message status when all attachments have been sent
        if (message.attachmentType == AttachmentType.PHOTO &&
            message.messageStatus != MessageStatus.FAILURE) {
          if (!message.mediaAttachments.any(
            (attachment) =>
                attachment.attachmentStatus == AttachmentStatusEnum.UPLOADING,
          )) {
            message.messageStatusRaw = MessageStatus.UNRECOGNIZED.value;
          } else
            message.messageStatusRaw = MessageStatus.PENDING.value;
        }
        // Insert or update the message along with its attachments.
        _messageBox.put(message);
      }

      // **Step 3: Update channel metadata for all channels that have updated messages.**
      final channelsToUpdate = messagesToUpdate
          .map(
            (message) => ChannelIdentification(
              workspaceId: message.workspaceId,
              channelId: message.channelId,
            ),
          )
          .toSet();

      for (final channel in channelsToUpdate) {
        _channelMetaDataRepository.updateChannelMetadata(
          workspaceId: channel.workspaceId,
          channelId: channel.channelId,
          newMessageCount: newMessageCount,
        );
      }
    });

    // Collect and return the IDs of the inserted/updated messages.
    final insertedIds = messagesToUpdate.map((m) => m.id).toList();

    return insertedIds;
  }

  /// Update message
  List<int> _forceUpdateMessages(List<Message> messages) {
    _existsAndUpdate(messages);

    // Update the cache for each message.
    for (var message in messages) {
      updateCacheForMessage(message);
    }

    // Collect all non-null attachmentIds from the messages to update.
    final allAttachmentIds = messages
        .expand(
          (message) => message.mediaAttachments.map((a) => a.attachmentId),
        )
        .whereType<String>() // Remove null values.
        .toSet()
        .toList();

    // Collect all non-null attachmentRefs from the messages to update.
    final allAttachmentRefs = messages
        .expand(
          (message) => message.mediaAttachments.map((a) => a.ref),
        )
        .whereType<String>() // Remove null values.
        .toSet()
        .toList();

    // Query all existing Attachments based on the collected attachmentIds.
    final existingAttachments = _attachmentBox
        .query(
          Attachment_.attachmentId
              .oneOf(allAttachmentIds)
              .or(Attachment_.ref.oneOf(allAttachmentRefs)),
        )
        .build()
        .find();

    // Create a map for quick lookup of existing Attachments by attachmentId.
    final attachmentIdToExisting = {
      for (var att in existingAttachments) att.attachmentId: att,
    };

    // Create a map for quick lookup of existing Attachments by ref.
    final attachmentRefToExisting = {
      for (var att in existingAttachments) att.ref: att,
    };

    // Perform operations within a transaction to ensure data consistency.
    _chatStore.runInTransaction(TxMode.write, () {
      // **Step 1: Remove all existing attachments with these attachmentIds globally.**
      for (var att in existingAttachments) {
        _attachmentBox.remove(att.id);
      }

      // **Step 2: Iterate through each message and handle attachments.**
      for (var message in messages) {
        // Clone the new attachments from the message and filter invalid ones (must have ref).
        final newAttachments = List<Attachment>.from(message.mediaAttachments)
            .where((a) => a.isValid)
            .toList();
        // Clear the current attachments in the message.
        message.mediaAttachments.clear();

        for (var attachment in newAttachments) {
          _updateAttachmentId(
            attachmentIdToExisting,
            attachment,
            attachmentRefToExisting,
            message,
          );
          // Establish the relationship with the message.
          attachment.message.target = message;
          // Add the attachment back to the message.
          message.mediaAttachments.add(attachment);
        }
        _attachmentBox.putMany(newAttachments);

        // Update message status when all attachments have been sent
        if (message.attachmentType == AttachmentType.PHOTO &&
            message.messageStatus != MessageStatus.FAILURE) {
          if (!message.mediaAttachments.any(
            (attachment) =>
                attachment.attachmentStatus == AttachmentStatusEnum.UPLOADING,
          )) {
            message.messageStatusRaw = MessageStatus.UNRECOGNIZED.value;
          } else
            message.messageStatusRaw = MessageStatus.PENDING.value;
        }

        // Insert or update the message along with its attachments.
        _messageBox.put(message);
      }

      // **Step 3: Update channel metadata for all channels that have updated messages.**
      final channelsToUpdate = messages
          .map(
            (message) => ChannelIdentification(
              workspaceId: message.workspaceId,
              channelId: message.channelId,
            ),
          )
          .toSet();

      for (final channel in channelsToUpdate) {
        _channelMetaDataRepository.updateChannelMetadata(
          workspaceId: channel.workspaceId,
          channelId: channel.channelId,
        );
      }
    });

    // Collect and return the IDs of the inserted/updated messages.
    final updatedIds = messages.map((m) => m.id).toList();

    return updatedIds;
  }

  /// Updates the [attachment]'s internal ID to match an existing one if found.
  ///
  /// - Checks for existing attachment by [attachmentId] or [ref].
  /// - Prevents duplicate attachments by reusing the ID of an existing upload.
  /// - Ensures the update applies only if the existing attachment is still uploading
  ///   or belongs to the same [message].
  void _updateAttachmentId(
    Map<String?, Attachment> attachmentIdToExisting,
    Attachment attachment,
    Map<String?, Attachment> attachmentRefToExisting,
    Message message,
  ) {
    if (attachmentIdToExisting.containsKey(attachment.attachmentId)) {
      // Assign the existing Attachment's ID to update it instead of creating a new one.
      attachment.id = attachmentIdToExisting[attachment.attachmentId]!.id;
    } else {
      if (attachmentRefToExisting.containsKey(attachment.ref) &&
          (attachmentRefToExisting[attachment.ref]!.attachmentStatus ==
                  AttachmentStatusEnum.UPLOADING ||
              attachmentRefToExisting[attachment.ref]!
                      .message
                      .target
                      ?.messageId ==
                  message.messageId)) {
        // Assign the existing Attachment's ID to update it instead of creating a new one.
        attachment.id = attachmentRefToExisting[attachment.ref]!.id;
      }
    }
  }

  @override
  int insert(Message message) {
    if (message.id > 0) {
      if (message.mediaAttachments.isNotEmpty) {
        _attachmentBox.putMany(message.mediaAttachments);
      }
      return _messageBox.put(message);
    }
    // Call the common method with a single message in the list.
    final ids = _insertOrUpdateMessages([message], isInsertOne: true);
    return ids.isNotEmpty ? ids.first : -1;
  }

  @override
  Future<List<int>> insertAll(List<Message> messages) async {
    // Call the common method for all messages.
    return _insertOrUpdateMessages(messages);
  }

  @override
  Future<List<int>> forceUpdateMessageAll(List<Message> messages) async {
    // Call the common method for all messages.
    return _forceUpdateMessages(messages);
  }

  /// Determines whether a message should be inserted or updated based on cache.
  bool shouldInsertOrUpdateMessage(Message message) {
    final cachedTime = _cache.peekCached(
      workspaceId: message.workspaceId,
      channelId: message.channelId,
      messageId: message.messageId,
      sessionKey: Config.getInstance().activeSessionKey ?? '',
    );
    return cachedTime != message.updateTime;
  }

  /// Updates the cache for a given message.
  void updateCacheForMessage(Message message) {
    if (message.updateTime != null) {
      _cache.setCache(
        workspaceId: message.workspaceId,
        channelId: message.channelId,
        messageId: message.messageId,
        updateTime: message.updateTime!,
        sessionKey: Config.getInstance().activeSessionKey ?? '',
      );
    }
  }

  /// Updates existing messages with new metadata.
  void _existsAndUpdate(List<Message> messages) {
    if (messages.isEmpty) return;

    final sessionKeyQuery = Message_.sessionKey.equals(_sessionKey);

    final workspaceIds = messages.map((m) => m.workspaceId).toSet().toList();
    final channelIds = messages.map((m) => m.channelId).toSet().toList();
    final messageIds = messages.map((m) => m.messageId).toSet().toList();
    final messageRefs = messages.map((m) => m.ref ?? '').toSet().toList();

    final existingQuery = _messageBox
        .query(
          sessionKeyQuery.andAll([
            Message_.workspaceId.oneOf(workspaceIds),
            Message_.channelId.oneOf(channelIds),
            Message_.messageId.oneOf(messageIds),
          ]).or(
            sessionKeyQuery.and(Message_.ref.oneOf(messageRefs)),
          ),
        )
        .build();

    final existingMessages = existingQuery.find();
    existingQuery.close();

    for (final existingMessage in existingMessages) {
      final message = messages.firstWhere(
        (m) =>
            (m.workspaceId == existingMessage.workspaceId &&
                m.channelId == existingMessage.channelId &&
                m.messageId == existingMessage.messageId) ||
            (m.ref == existingMessage.ref),
        orElse: () => existingMessage,
      );
      _handleKeepLocalMetadata(message, existingMessage);
    }
  }

  @override
  Message? getMessage({
    required String workspaceId,
    required String channelId,
    required String messageId,
  }) {
    final query = _messageBox
        .query(
          Message_.workspaceId
              .equals(workspaceId)
              .and(Message_.channelId.equals(channelId))
              .and(Message_.messageId.equals(messageId))
              .and(Message_.sessionKey.equals(_sessionKey)),
        )
        .build();

    final message = query.findFirst();
    query.close();
    return message;
  }

  @override
  List<Message>? getMessagesByIds({
    required String workspaceId,
    required String channelId,
    required List<String> messageIds,
  }) {
    final query = _messageBox
        .query(
          Message_.workspaceId
              .equals(workspaceId)
              .and(Message_.channelId.equals(channelId))
              .and(Message_.messageId.oneOf(messageIds))
              .and(Message_.sessionKey.equals(_sessionKey)),
        )
        .build();
    final messages = query.find();
    query.close();
    return messages;
  }

  @override
  List<Message> getMessages({
    required String workspaceId,
    required String channelId,
    int limit = 100,
    String? nextPageToken,
  }) {
    final queryBuilder = (nextPageToken != null && nextPageToken.isNotEmpty)
        ? _messageBox
            .query(
              Message_.workspaceId
                  .equals(workspaceId)
                  .and(Message_.channelId.equals(channelId))
                  .and(Message_.sessionKey.equals(_sessionKey))
                  .and(Message_.messageId.lessThan(nextPageToken)),
            )
            .order(Message_.createTime, flags: Order.descending)
        : _messageBox
            .query(
              Message_.workspaceId
                  .equals(workspaceId)
                  .and(Message_.channelId.equals(channelId))
                  .and(Message_.sessionKey.equals(_sessionKey)),
            )
            .order(Message_.createTime, flags: Order.descending);

    final query = queryBuilder.build()..limit = limit;
    final messages = query.find();
    query.close();
    return messages;
  }

  @override
  Stream<List<Message>> getStreamMessagesFullScreen({
    required String workspaceId,
    required String channelId,
  }) {
    final filterMessageTypeRaw = [
      MessageViewType.system.rawValue(),
    ];

    final queryBuilder = _messageBox
        .query(
          Message_.workspaceId
              .equals(workspaceId)
              .and(Message_.channelId.equals(channelId))
              .and(Message_.sessionKey.equals(_sessionKey))
              .and(Message_.messageViewTypeRaw.notOneOf(filterMessageTypeRaw)),
        )
        .order(Message_.createTime, flags: Order.descending);

    return queryBuilder
        .watch(triggerImmediately: true)
        .map((query) => query.find());
  }

  @override
  Message getLastTextMessage({
    required String workspaceId,
    required String channelId,
  }) {
    final textMessageType = MessageViewType.text.value;
    final queryBuilder = _messageBox
        .query(
          Message_.workspaceId
              .equals(workspaceId)
              .and(Message_.channelId.equals(channelId))
              .and(Message_.sessionKey.equals(_sessionKey))
              .and(Message_.messageViewTypeRaw.equals(textMessageType))
              .and(Message_.isTemp.equals(false)),
        )
        .order(Message_.createTime, flags: Order.descending);

    final query = queryBuilder.build();
    final message = query.find().first;
    query.close();
    return message;
  }

  @override
  bool deleteMessageById({
    required String workspaceId,
    required String channelId,
    required String messageId,
  }) {
    final message = getMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
    );
    return message != null && _messageBox.remove(message.id);
  }

  @override
  int deleteMessageByIds({
    required String workspaceId,
    required String channelId,
    required List<String> messageIds,
  }) {
    final messages = getMessagesByIds(
      workspaceId: workspaceId,
      channelId: channelId,
      messageIds: messageIds,
    );
    List<int>? ids = messages?.map((item) => item.id).toList();
    int result = _messageBox.removeMany(ids!);
    _channelMetaDataRepository.updateChannelMetadata(
      workspaceId: workspaceId,
      channelId: channelId,
    );
    return result;
  }

  @override
  bool deleteAllMessageOnChannel({
    required String workspaceId,
    required String channelId,
  }) {
    final query = _messageBox
        .query(
          Message_.workspaceId
              .equals(workspaceId)
              .and(Message_.channelId.equals(channelId))
              .and(Message_.sessionKey.equals(_sessionKey)),
        )
        .build();
    _channelMetaDataRepository.removeLastMessage(
      workspaceId: workspaceId,
      channelId: channelId,
    );
    final deletedCount = query.remove();
    query.close();
    _cache.cleanCache();

    return deletedCount > 0;
  }

  @override
  int deleteAllMessage() {
    final query =
        _messageBox.query(Message_.sessionKey.equals(_sessionKey)).build();
    final deletedCount = query.remove();
    query.close();
    return deletedCount;
  }

  @override
  bool isEmpty() {
    final query =
        _messageBox.query(Message_.sessionKey.equals(_sessionKey)).build();
    final countMessages = query.count();
    query.close();
    return countMessages == 0;
  }

  @override
  Future<int> checkAndInsertOrUpdate(Message message) async {
    return insert(message);
  }

  @override
  Message? getMessageByIdOrRef({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String ref,
  }) {
    final query = _messageBox
        .query(
          Message_.workspaceId
              .equals(workspaceId)
              .and(Message_.channelId.equals(channelId))
              .and(
                Message_.messageId
                    .equals(messageId)
                    .or(Message_.ref.equals(ref)),
              )
              .and(Message_.sessionKey.equals(_sessionKey)),
        )
        .build();

    final message = query.findFirst();
    query.close();
    return message;
  }

  @override
  DateTime? getLastMessageCreated() {
    final query = _messageBox
        .query(
          Message_.sessionKey
              .equals(_sessionKey)
              .and(Message_.messageStatusRaw.equals(1)),
        )
        .order(Message_.createTime, flags: Order.descending)
        .build();

    final message = query.findFirst();
    query.close();
    return message?.createTime;
  }

  @override
  void tagFirstMessage({
    required String workspaceId,
    required String channelId,
    required String messageId,
  }) {
    final message = getMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
    );

    if (message == null) return;

    message.isFirstMessage = true;

    _messageBox.put(message);
  }

  @override
  Message? updateReactions({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String? reactionsRaw,
  }) {
    final message = getMessageByIdOrRef(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
      ref: '',
    );
    if (message != null) {
      message.reactionsRaw = reactionsRaw;
      _messageBox.put(message);
      return message;
    }
    return null;
  }

  @override
  Message? getMessageByRef({required String ref}) {
    final query = _messageBox.query(Message_.ref.equals(ref)).build();
    final message = query.findFirst();
    query.close();
    return message;
  }

  @override
  List<Message> getMessagesByRefs({required List<String> refs}) {
    final query = _messageBox.query(Message_.ref.oneOf(refs)).build();

    final messages = query.find();
    query.close();
    return messages;
  }

  /// Updates an attachment by its reference within a message.
  bool updateAttachmentByRef({
    required String workspaceId,
    required String channelId,
    String? messageId,
    String? messageRef,
    required String attachmentRef,
    required Attachment updatedAttachment,
  }) {
    Message? message;

    if (messageId != null) {
      message = getMessage(
        workspaceId: workspaceId,
        channelId: channelId,
        messageId: messageId,
      );
    } else if (messageRef != null) {
      message = getMessageByRef(ref: messageRef);
    }

    if (message == null) {
      return false;
    }

    final attachmentIndex = message.mediaAttachments.indexWhere(
      (attachment) => attachment.ref == attachmentRef,
    );

    if (attachmentIndex == -1) {
      return false;
    }

    final existingAttachment = message.mediaAttachments[attachmentIndex];
    existingAttachment.updateWith(updatedAttachment);

    _messageBox.put(message);

    return true;
  }

  @override
  bool deleteSession(String sessionKey) {
    final query =
        _messageBox.query(Message_.sessionKey.equals(sessionKey)).build();

    final removedCount = query.remove();

    query.close();
    return removedCount > 0;
  }

  @override
  Future<bool> insertOrUpdateAttachment({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required Attachment attachment,
  }) async {
    return _chatStore.runInTransaction(TxMode.write, () {
      final message = getMessage(
        workspaceId: workspaceId,
        channelId: channelId,
        messageId: messageId,
      );

      if (message == null) {
        return false;
      }

      final existingAttachment = message.mediaAttachments.firstOrNullWhere(
        (att) =>
            (attachment.attachmentId != null &&
                att.attachmentId == attachment.attachmentId) ||
            (attachment.ref != null && att.ref == attachment.ref),
      );

      if (existingAttachment != null) {
        attachment.id = existingAttachment.id;
        existingAttachment.updateWith(attachment);
      }

      attachment.message.target = message;

      final success = _attachmentBox.put(attachment) > 0;

      _messageBox.put(message);

      return success;
    });
  }

  @override
  Future<bool> updateAttachment({
    required Attachment attachment,
  }) async {
    return _chatStore.runInTransaction(TxMode.write, () {
      final query = _attachmentBox
          .query(
            Attachment_.attachmentId
                .equals(attachment.attachmentId!)
                .or(Attachment_.ref.equals(attachment.ref!)),
          )
          .build();

      final dbAttachment = query.findFirst();
      query.close();

      if (dbAttachment != null) {
        attachment.id = dbAttachment.id;
      }

      final success = _attachmentBox.put(attachment) > 0;

      return success;
    });
  }

  @override
  Future<Message?> updateAttachmentAndGetMessage({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required Attachment attachment,
  }) async {
    insertOrUpdateAttachment(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
      attachment: attachment,
    );

    return getMessage(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
    )!;
  }

  @override
  bool deleteAttachmentByDBId(int attachmentId) {
    final attachment = _attachmentBox.get(attachmentId);
    if (attachment == null) {
      return false;
    }

    return _attachmentBox.remove(attachmentId);
  }

  @override
  Future<void> updateMessageStatus({
    required String workspaceId,
    required String channelId,
    required String messageId,
    required String ref,
    required MessageStatus messageStatus,
  }) async {
    var message = getMessageByIdOrRef(
      workspaceId: workspaceId,
      channelId: channelId,
      messageId: messageId,
      ref: ref,
    );

    if (message == null) return;

    message..messageStatusRaw = messageStatus.rawValue();

    _messageBox.put(message);
  }

  @override
  List<Message> getMessagesByOriginMessageIds({
    required String workspaceId,
    required String channelId,
    required String originalMessageId,
  }) {
    final query = _messageBox
        .query(
          Message_.workspaceId
              .equals(workspaceId)
              .and(Message_.channelId.equals(channelId))
              .and(
                Message_.originalMessageRaw
                    .contains('"messageId":"$originalMessageId"'),
              )
              .and(Message_.sessionKey.equals(_sessionKey)),
        )
        .build();
    final messages = query.find();
    query.close();
    return messages;
  }

  @override
  List<Message>? getMessagesMentionOrContentArguments() {
    final query = _messageBox
        .query(
          Message_.sessionKey.equals(_sessionKey).and(
                Message_.mentionsRaw
                    .notNull()
                    .or(Message_.contentArguments.notNull()),
              ),
        )
        .build();
    final messages = query.find();
    query.close();
    return messages;
  }

  @override
  Message? updateAttachmentStatusAndGetMessage({
    required String messageRef,
    required String attachmentRef,
    required AttachmentStatusEnum attachmentStatus,
  }) {
    final message = getMessageByRef(ref: messageRef);
    if (message == null) return null;

    try {
      final attachment = message.mediaAttachments.firstWhere(
        (mediaAttachment) => mediaAttachment.ref == attachmentRef,
      );

      attachment.attachmentStatusRaw = attachmentStatus.rawValue();
      _attachmentBox.put(attachment);

      bool hasUploading = message.mediaAttachments.any(
        (attachment) =>
            attachment.attachmentStatus == AttachmentStatusEnum.UPLOADING,
      );

      if (!hasUploading) {
        bool hasSuccessOrUnspecified = message.mediaAttachments.any(
          (attachment) =>
              attachment.attachmentStatus == AttachmentStatusEnum.SUCCESS ||
              attachment.attachmentStatus == AttachmentStatusEnum.UNSPECIFIED,
        );

        message.messageStatusRaw = hasSuccessOrUnspecified
            ? MessageStatus.UNRECOGNIZED.value
            : MessageStatus.FAILURE.value;
      }

      _messageBox.put(message);
      return message;
    } catch (_) {
      return null;
    }
  }

  @override
  Message? deleteAttachmentByRefOrId({
    String? attachmentId,
    String? attachmentRef,
  }) {
    if (attachmentId == null && attachmentRef == null) return null;
    final query = attachmentId != null
        ? _attachmentBox
            .query(Attachment_.attachmentId.equals(attachmentId))
            .build()
        : _attachmentBox.query(Attachment_.ref.equals(attachmentRef!)).build();
    final attachment = query.findFirst();
    query.close();
    if (attachment == null) return null;
    final msg = attachment.message.target;
    _attachmentBox.remove(attachment.id);
    if (msg == null) return null;
    return getMessageByIdOrRef(
      channelId: msg.channelId,
      workspaceId: msg.workspaceId,
      messageId: msg.messageId,
      ref: msg.ref ?? '',
    );
  }

  @override
  Stream<List<Message>> streamPinMessages({
    required String workspaceId,
    required String channelId,
  }) {
    final filterMessageTypeRaw = [
      MessageViewType.system.rawValue(),
    ];

    final queryBuilder = _messageBox
        .query(
          Message_.workspaceId
              .equals(workspaceId)
              .and(Message_.channelId.equals(channelId))
              .and(Message_.sessionKey.equals(_sessionKey))
              .and(Message_.messageViewTypeRaw.notOneOf(filterMessageTypeRaw))
              .and(Message_.isPinned.equals(true)),
        )
        .order(Message_.createTime, flags: Order.descending);

    return queryBuilder
        .watch(triggerImmediately: true)
        .map((query) => query.find());
  }

  @override
  List<Message> getPinMessages({
    required String workspaceId,
    required String channelId,
  }) {
    final filterMessageTypeRaw = [
      MessageViewType.system.rawValue(),
    ];
    final query = _messageBox
        .query(
          Message_.workspaceId
              .equals(workspaceId)
              .and(Message_.channelId.equals(channelId))
              .and(Message_.sessionKey.equals(_sessionKey))
              .and(Message_.messageViewTypeRaw.notOneOf(filterMessageTypeRaw))
              .and(Message_.isPinned.equals(true)),
        )
        .build();
    final messages = query.find();
    query.close();
    return messages;
  }

  @override
  List<Message> getMessagesByStatus(MessageStatus status) {
    final query = _messageBox
        .query(
          Message_.sessionKey
              .equals(_sessionKey)
              .and(Message_.messageStatusRaw.equals(status.rawValue())),
        )
        .order(Message_.createTime, flags: Order.descending)
        .build();

    final messages = query.find();
    query.close();
    return messages;
  }
}
