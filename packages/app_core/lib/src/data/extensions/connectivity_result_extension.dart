import 'package:connectivity_plus/connectivity_plus.dart';

extension ConnectivityResultExtension on ConnectivityResult {
  bool get isWifi => this == ConnectivityResult.wifi;

  bool get isBluetooth => this == ConnectivityResult.bluetooth;

  bool get isEthernet => this == ConnectivityResult.ethernet;

  bool get isMobile => this == ConnectivityResult.mobile;

  bool get isNone => this == ConnectivityResult.none;

  bool get isVpn => this == ConnectivityResult.vpn;

  bool get isOther => this == ConnectivityResult.other;
}
