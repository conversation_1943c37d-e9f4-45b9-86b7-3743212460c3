import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import '../../../../core.dart';

part 'get_user_private_data_use_case.freezed.dart';

@Injectable()
class GetUserPrivateDataUseCase extends BaseFutureUseCase<
    GetUserPrivateDataUseCaseInput, GetUserPrivateDataUseCaseOutput> {
  const GetUserPrivateDataUseCase(this._repository);

  final PrivateDataRepository _repository;

  @protected
  @override
  Future<GetUserPrivateDataUseCaseOutput> buildUseCase(
    GetUserPrivateDataUseCaseInput input,
  ) async {
    try {
      final newPrivateData = _repository.getPrivateData();

      if (newPrivateData != null) {
        return GetUserPrivateDataUseCaseOutput(data: newPrivateData);
      }
      return GetUserPrivateDataUseCaseOutput(data: null);
    } on Exception catch (_) {
      return GetUserPrivateDataUseCaseOutput(data: null);
    }
  }
}

@freezed
sealed class GetUserPrivateDataUseCaseInput extends BaseInput
    with _$GetUserPrivateDataUseCaseInput {
  const GetUserPrivateDataUseCaseInput._();
  factory GetUserPrivateDataUseCaseInput() = _GetUserPrivateDataUseCaseInput;
}

@freezed
sealed class GetUserPrivateDataUseCaseOutput extends BaseOutput
    with _$GetUserPrivateDataUseCaseOutput {
  const GetUserPrivateDataUseCaseOutput._();
  factory GetUserPrivateDataUseCaseOutput({
    @Default(null) PrivateData? data,
  }) = _GetUserPrivateDataUseCaseOutput;
}
