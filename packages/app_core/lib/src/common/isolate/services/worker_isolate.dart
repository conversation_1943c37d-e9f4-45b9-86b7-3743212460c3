import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:isolate';
import 'dart:math' as math;
import 'dart:ui';

import 'package:chat/chat.dart';
import 'package:dartx/dartx.dart';
import 'package:dio/dio.dart';
import 'package:filestore_sdk/core/implementations/filestore_client_v2.dart';
import 'package:flutter/widgets.dart' show WidgetsFlutterBinding;
import 'package:shared/shared.dart';
import 'package:upload_manager/upload_manager.dart';
import 'package:workmanager/workmanager.dart';

import '../../../../core.dart';
import '../../../data/source/api/client/isolate_api_client.dart';
import '../data/stores/shared_preferences_store.dart';
import '../exceptions/file_not_found_exception.dart';
import '../exceptions/send_message_timeout_exception.dart';
import '../handlers/compress_and_upload_image_handler.dart';
import '../handlers/compress_video_message_handler.dart';
import '../handlers/edit_message_handler.dart';
import '../handlers/forward_message_handler.dart';
import '../handlers/location_message_handler.dart';
import '../handlers/media_message_handler.dart';
import '../handlers/poke_message_handler.dart';
import '../handlers/quote_message_handler.dart';
import '../handlers/sticker_message_handler.dart';
import '../handlers/text_message_handler.dart';
import '../handlers/update_media_attachments_handler.dart';
import '../handlers/upload_file_handler.dart' as fileHandler;
import '../handlers/worker_send_result_handler.dart';
import '../input/worker_send_media_input.dart';
import '../input/worker_send_message_base.dart';
import '../models/attachment_error.dart';
import '../models/worker_file_metadata.dart';
import '../models/worker_media_object.dart';
import '../models/worker_size.dart';
import '../models/worker_upload_file.dart';
import '../output/worker_compress_video_output.dart';
import '../output/worker_upload_file_output.dart' as fileHandler;
import '../output/worker_upload_file_output.dart';
import '../resilient_isolate.dart';

/**
 * Entry point for Workmanager background tasks.
 *
 * This function is called by Workmanager when a background task is triggered.
 * It initializes necessary services, processes the task, and handles retries if needed.
 *
 * The function flow:
 * 1. Initialize Flutter binding
 * 2. Initialize services (API client, FileStore client, etc.)
 * 3. Convert Workmanager data to format compatible with worker isolate
 * 4. Process the task using shared logic
 * 5. Handle retries for failed tasks on iOS
 */
@pragma('vm:entry-point')
void callbackDispatcher() async {
  try {
    Workmanager().executeTask((task, inputData) async {
      WidgetsFlutterBinding.ensureInitialized();
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        'callbackDispatcher',
        'callbackDispatcher: ${inputData}',
      );

      // Initialize necessary services
      await _initializeServicesForWorkManager(ExecutorType.worker);

      if (task == "process_pending_tasks") {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          'callbackDispatcher',
          'Processing composite task: process_pending_tasks',
        );

        final tasks = await SharedPreferencesStore.loadAllTasks();

        if (tasks.isEmpty) {
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            'callbackDispatcher',
            'No pending tasks found',
          );
          return true;
        }

        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          'callbackDispatcher',
          'Processing ${tasks.length} pending tasks',
        );

        // Sắp xếp task theo ưu tiên
        tasks.sort((a, b) {
          try {
            // Sắp xếp theo ưu tiên (high -> medium -> low)
            if (a.priority != b.priority) {
              return a.priority.index.compareTo(b.priority.index);
            }

            // Nếu cùng ưu tiên, sắp xếp theo thời gian tạo (cũ hơn được ưu tiên)
            return a.createdAt.compareTo(b.createdAt);
          } catch (e) {
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'callbackDispatcher',
              'Error sorting tasks: $e',
            );
          }

          return 0;
        });

        bool overallSuccess = true;
        int successCount = 0;
        int failureCount = 0;

        for (final task in tasks) {
          try {
            final taskData = {
              'taskId': task.id,
              'name': task.name,
              'inputData': task.inputData,
              'timeout': task.timeout,
            };

            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'callbackDispatcher',
              'Processing task: ${task.name} (ID: ${task.id})',
            );

            // Xử lý task với logic chung
            final success = await _processTaskWithSharedLogic(taskData);

            if (success) {
              successCount++;

              // Xóa task đã hoàn thành khỏi SharedPreferencesStore
              await SharedPreferencesStore.deleteTask(task.id);

              RILogger.printClassMethodDebug(
                'WorkerIsolate',
                'callbackDispatcher',
                'Task completed successfully: ${task.name} (ID: ${task.id})',
              );
            } else {
              failureCount++;
              overallSuccess = false;

              RILogger.printClassMethodDebug(
                'WorkerIsolate',
                'callbackDispatcher',
                'Task failed: ${task.name} (ID: ${task.id})',
              );
            }
          } catch (e, stackTrace) {
            failureCount++;
            overallSuccess = false;

            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'callbackDispatcher',
              'Error processing task: ${task.name} (ID: ${task.id}), error: $e',
            );
            RILogger.printError(
              'Error processing task: ${task.name} (ID: ${task.id})',
              e,
              stackTrace,
            );
          }
        }

        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          'callbackDispatcher',
          'Processed ${tasks.length} tasks: $successCount succeeded, $failureCount failed',
        );

        if (!overallSuccess) {
          final remainingTasks = await SharedPreferencesStore.loadAllTasks();

          if (remainingTasks.isNotEmpty) {
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'callbackDispatcher',
              'There are still ${remainingTasks.length} pending tasks, but not registering composite task again',
            );

            if (Platform.isIOS) {
              await Workmanager().registerOneOffTask(
                "process_pending_tasks",
                "process_pending_tasks",
                existingWorkPolicy: ExistingWorkPolicy.replace,
                constraints: Constraints(networkType: NetworkType.connected),
                initialDelay: Duration(seconds: 5),
              );
            }
            return false;
          }
        }

        return overallSuccess;
      } else {
        final taskData = _convertWorkManagerDataToTaskData(task, inputData);

        // Process task with the same logic as in isolate
        final success = await _processTaskWithSharedLogic(taskData);

        // Retry a failed task on iOS
        if (!success && Platform.isIOS) {
          await Future.delayed(DurationUtils.ms250, () {
            Workmanager().registerOneOffTask(
              task,
              inputData!['taskName'],
              constraints: Constraints(networkType: NetworkType.connected),
              inputData: inputData,
            );
          });
        }

        return success;
      }
    });
  } catch (e, stackTrace) {
    RILogger.printError('Error in callbackDispatcher', e, stackTrace);
  }
}

/**
 * Main entry point for worker isolate.
 *
 * This function is called when a new worker isolate is created.
 * It sets up communication ports, initializes shared variables, and listens for task requests.
 *
 * The function flow:
 * 1. Set up receive port and send it back to main isolate
 * 2. Initialize shared variables (token, metadata, clients)
 * 3. Listen for task requests and process them
 * 4. Set up heartbeat timer to keep isolate alive
 *
 * @param args Arguments passed from main isolate:
 *             - args[0]: SendPort to communicate back to main isolate
 *             - args[1]: SendPort for acknowledgments
 *             - args[2]: SendPort for task registration (optional)
 */
@pragma('vm:entry-point')
void workerIsolateEntryPoint(List<dynamic> args) async {
  // Log entry point start
  RILogger.printDebug('Worker isolate entry point started');

  try {
    final responsePort = args[0] as SendPort;
    final ackPort = args[1] as SendPort;
    final taskRegistrationPort = args.length > 2 ? args[2] as SendPort? : null;

    // Create a set to track registered tasks
    final Set<String> _registeredTaskIds = {};

    // Create a set to track tasks that are currently being processed
    final Set<String> _processingTaskIds = {};

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      'workerIsolateEntryPoint',
      'Worker isolate started with ${args.length} arguments, taskRegistrationPort: ${taskRegistrationPort != null}',
    );

    // Create a receive port for incoming requests
    final receivePort = ReceivePort();

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      'workerIsolateEntryPoint',
      'Created receive port, sending send port back to main isolate',
    );

    // Send the send port back to the main isolate
    responsePort.send(receivePort.sendPort);

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      'workerIsolateEntryPoint',
      'Send port sent to main isolate',
    );

    // Map to store pending acknowledgments
    final pendingAcks = <String, Timer>{};

    // Initialize shared variables
    String? token;
    WorkerMetadata? metadata;
    FilestoreClientV2? fileStoreClient;
    IsolateApiClient? apiClient;
    RetryManager? retryManager;

    // Initialize shared variables
    try {
      // Listen for initialization info from main isolate
      final initCompleter = Completer<void>();

      receivePort.listen((message) async {
        if (message is Map<String, dynamic> && message.containsKey('init')) {
          // Process initialization info from main isolate
          if (!initCompleter.isCompleted) {
            final initData = message['init'] as Map<String, dynamic>;
            token = initData['token'] as String;
            metadata = WorkerMetadata.fromJson(
              initData['metadata'] as Map<String, dynamic>,
            );

            apiClient = IsolateApiClient(
              baseUrl: metadata!.apiHost,
              header: metadata!.header,
              apiAuthToken: token!,
            );

            fileStoreClient = FileStoreSDKClient.instanceForWorker(
              host: metadata!.fileStoreHost,
            );

            retryManager = RetryManager();

            TaskHandlerRegistry.setSharedClients(
              fileStoreClient!,
              apiClient!,
              retryManager!,
              ExecutorType.isolate,
            );

            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'workerIsolateEntryPoint',
              'Shared variables initialized successfully from main isolate',
            );

            initCompleter.complete();
          }
          return;
        }

        if (!initCompleter.isCompleted) {
          try {
            final isolateTaskService = IsolateTaskService();
            token = (await isolateTaskService.getDecryptToken())!;
            metadata = (await isolateTaskService.getDecryptMetadata())!;

            apiClient = IsolateApiClient(
              baseUrl: metadata!.apiHost,
              header: metadata!.header,
              apiAuthToken: token!,
            );

            fileStoreClient = FileStoreSDKClient.instanceForWorker(
              host: metadata!.fileStoreHost,
            );

            retryManager = RetryManager();

            TaskHandlerRegistry.setSharedClients(
              fileStoreClient!,
              apiClient!,
              retryManager!,
              ExecutorType.isolate,
            );

            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'workerIsolateEntryPoint',
              'Shared variables initialized successfully from IsolateTaskService',
            );

            initCompleter.complete();
          } catch (e, stackTrace) {
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'workerIsolateEntryPoint',
              'Error initializing shared variables: $e',
            );
            RILogger.printError(
              'Error initializing shared variables',
              e,
              stackTrace,
            );
          }
        }

        if (message is Map<String, dynamic>) {
          if (message.containsKey('ack')) {
            final taskId = message['ack'] as String;
            pendingAcks[taskId]?.cancel();
            pendingAcks.remove(taskId);
            return;
          }

          // Process result from task with removeFromRegistry field
          if (message.containsKey('taskId') &&
              message.containsKey('removeFromRegistry')) {
            final taskId = message['taskId'] as String;
            if (_registeredTaskIds.contains(taskId)) {
              _registeredTaskIds.remove(taskId);
              RILogger.printClassMethodDebug(
                'WorkerIsolate',
                'workerIsolateEntryPoint',
                'Removed failed task $taskId from _registeredTaskIds to allow resending',
              );
            }
            return;
          }

          // Process clear tasks command from main isolate
          if (message.containsKey('clearTasks')) {
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'workerIsolateEntryPoint',
              'Received clearTasks command from main isolate',
            );

            // Clear all tasks in RetryManager
            retryManager?.clearAllTasks();

            // Clear the list of registered tasks
            _registeredTaskIds.clear();

            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'workerIsolateEntryPoint',
              'Cleared all tasks from RetryManager and registered task list',
            );

            // Send acknowledgment back to main isolate
            responsePort.send({'clearedTasks': true});
            return;
          }

          // Handle task registration request from main isolate
          if (message.containsKey('registerTask')) {
            await initCompleter.future;

            final taskData = message['registerTask'] as Map<String, dynamic>;
            final taskId = taskData['taskId'] as String;
            final name = taskData['name'] as String;
            final inputData = taskData['inputData'] as Map<String, dynamic>;
            final priorityIndex =
                taskData['priority'] as int? ?? TaskPriority.medium.index;
            final priority = TaskPriority.values.firstWhere(
              (p) => p.index == priorityIndex,
              orElse: () => TaskPriority.medium,
            );
            final isReferenceTask =
                taskData['isReferenceTask'] as bool? ?? false;
            final networkRequired =
                taskData['networkRequired'] as bool? ?? true;

            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'workerIsolateEntryPoint',
              'Received task registration request: $taskId, name: $name',
            );

            // Register task with IsolateTaskService through taskRegistrationPort
            try {
              // Ensure taskRegistrationPort is available
              if (taskRegistrationPort == null) {
                throw Exception(
                  'Cannot register task: taskRegistrationPort is null',
                );
              }

              RILogger.printClassMethodDebug(
                'WorkerIsolate',
                'workerIsolateEntryPoint',
                'Forwarding task registration to main isolate via taskRegistrationPort: $taskId',
              );

              taskRegistrationPort.send({
                'registerTask': {
                  'taskName': name,
                  'taskId': taskId,
                  'inputData': inputData,
                  'priority': priority.index,
                  'isReferenceTask': isReferenceTask,
                  'networkRequired': networkRequired,
                },
              });

              RILogger.printClassMethodDebug(
                'WorkerIsolate',
                'workerIsolateEntryPoint',
                'Task registration successful: $taskId',
              );
            } catch (e) {
              RILogger.printClassMethodDebug(
                'WorkerIsolate',
                'workerIsolateEntryPoint',
                'Error registering task: $e',
              );
            }

            return;
          }

          await initCompleter.future;

          // This is a task request
          final taskId = message['taskId'] as String;
          final name = message['name'] as String;
          final inputData = message['inputData'] as Map<String, dynamic>;
          final timeout = message['timeout'] as int;

          // Log detailed information about the task request
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            'workerIsolateEntryPoint',
            '[TASK_DEBUG] Received task request: taskId=$taskId, name=$name, timeout=$timeout',
          );

          // Log detailed information about inputData
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            'workerIsolateEntryPoint',
            '[TASK_DEBUG] Task inputData: ${inputData.toString()}',
          );

          // For uploadFile tasks, log specific details
          if (name == TaskNameEnum.uploadFile.value) {
            final uploadType = inputData['uploadType'];
            final filePath = inputData['filePath'] as String?;
            final fileSize = inputData['fileSize'];
            final fileRef = inputData['fileRef'];

            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'workerIsolateEntryPoint',
              '[TASK_DEBUG] uploadFile details: uploadType=$uploadType, fileRef=$fileRef, fileSize=$fileSize',
            );

            // Check if file exists
            if (filePath != null) {
              final file = File(filePath);
              final fileExists = file.existsSync();
              RILogger.printClassMethodDebug(
                'WorkerIsolate',
                'workerIsolateEntryPoint',
                '[TASK_DEBUG] File exists: $fileExists, path: $filePath',
              );
            }
          }

          // Check if task has already been registered
          if (_registeredTaskIds.contains(taskId)) {
            // Check if task is already being processed
            if (_processingTaskIds.contains(taskId)) {
              RILogger.printClassMethodDebug(
                'WorkerIsolate',
                'workerIsolateEntryPoint',
                '[TASK_DEBUG] Task $taskId is already being processed, sending duplicate notification',
              );

              // Send a notification back to main isolate that this is a duplicate task
              if (responsePort != null) {
                final result = {
                  'taskId': taskId,
                  'success': true,
                  // Mark as success to avoid retries
                  'isDuplicate': true,
                  // Add flag to indicate this is a duplicate
                  'message': 'Task is already being processed',
                };

                RILogger.printClassMethodDebug(
                  'WorkerIsolate',
                  'workerIsolateEntryPoint',
                  '[TASK_DEBUG] Sending duplicate notification for task $taskId',
                );

                responsePort.send(result);
              }

              return;
            }

            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'workerIsolateEntryPoint',
              '[TASK_DEBUG] Task $taskId already registered but not being processed, will process it',
            );
          }

          // Add task to the registered list
          _registeredTaskIds.add(taskId);

          // Mark task as being processed
          _processingTaskIds.add(taskId);

          // Execute the task in a non-blocking way
          // Only log when task is not compressVideoMessage
          if (name != TaskNameEnum.compressVideoMessage.value) {
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'workerIsolateEntryPoint',
              'Worker received task: $taskId',
            );
          }
          Future(() async {
            try {
              await executeTask(
                taskId: taskId,
                name: name,
                sessionKey: metadata!.activeSessionKey,
                inputData: inputData,
                timeout: timeout,
                responsePort: responsePort,
                ackPort: ackPort,
                pendingAcks: pendingAcks,
                taskRegistrationPort: taskRegistrationPort,
              );
            } finally {
              // Remove task from processing list when done
              _processingTaskIds.remove(taskId);

              RILogger.printClassMethodDebug(
                'WorkerIsolate',
                'workerIsolateEntryPoint',
                '[TASK_DEBUG] Task $taskId removed from processing list',
              );
            }
          });
        }
      });
    } catch (e, stackTrace) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        'workerIsolateEntryPoint',
        'Error setting up worker isolate: $e',
      );
      RILogger.printError('Error setting up worker isolate', e, stackTrace);
    }

    // Set up heartbeat timer to send periodic heartbeats to main isolate
    // Reduced heartbeat frequency to 30 seconds to minimize overhead
    Timer.periodic(Duration(seconds: 30), (_) {
      responsePort.send({'heartbeat': true});
      // Only log at low debug level, no need to log every heartbeat
      // Logger.printClassMethodDebug('WorkerIsolate', 'heartbeat', 'Sent heartbeat to main isolate');
    });
  } catch (outerError, outerStackTrace) {
    // Catch any errors in the outer try block, including argument parsing
    RILogger.printDebug(
      'Critical error in worker isolate entry point: $outerError\n$outerStackTrace',
    );
  }

  // No need to call _loopTestIsolate() anymore because we have heartbeat mechanism
}

/**
 * Initialize necessary services for Workmanager.
 *
 * This function is called by callbackDispatcher to set up required services:
 * - API client for communication with server
 * - FileStore client for file operations
 * - RetryManager for handling retries
 *
 * @throws Exception if initialization fails
 */
@pragma('vm:entry-point')
Future<void> _initializeServicesForWorkManager(ExecutorType executor) async {
  try {
    // Initialize necessary services
    final token = (await SharedPreferencesStore.getDecryptToken())!;
    final metadata = (await SharedPreferencesStore.getDecryptMetadata())!;

    final apiClient = IsolateApiClient(
      baseUrl: metadata.apiHost,
      header: metadata.header,
      apiAuthToken: token,
    );

    final fileStoreClient = FileStoreSDKClient.instanceForWorker(
      host: metadata.fileStoreHost,
    );
    final retryManager = RetryManager();
    TaskHandlerRegistry.setSharedClients(
      fileStoreClient,
      apiClient,
      retryManager,
      executor,
    );

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_initializeServicesForWorkManager',
      'Shared variables initialized successfully for WorkManager',
    );
    return;
  } catch (e, stackTrace) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_initializeServicesForWorkManager',
      'Error initializing shared variables for WorkManager: $e',
    );
    RILogger.printError(
      'Error initializing shared variables for WorkManager',
      e,
      stackTrace,
    );
    rethrow;
  }
}

/**
 * Convert data from Workmanager to format compatible with workerIsolateEntryPoint.
 *
 * This function transforms the task data received from Workmanager into the format
 * expected by the worker isolate's task execution logic.
 *
 * @param task Task name from Workmanager
 * @param inputData Input data from Workmanager
 * @return Map containing task data in worker isolate format
 */
@pragma('vm:entry-point')
Map<String, dynamic> _convertWorkManagerDataToTaskData(
  String task,
  Map<String, dynamic>? inputData,
) {
  if (inputData == null) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_convertWorkManagerDataToTaskData',
      'Warning: inputData is null, creating empty map',
    );
    return {
      'taskId': task,
      'name': task,
      'inputData': {},
      'timeout': GlobalConfig.sendTimeoutDuration.inMilliseconds,
    };
  }

  // Ensure required fields exist
  final taskId = inputData['taskId'] as String? ?? task;
  final name = inputData['taskName'] as String? ?? task;

  // Add creationTime field if it doesn't exist
  if (!inputData.containsKey('creationTime')) {
    inputData['creationTime'] = DateTime.now().toIso8601String();
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_convertWorkManagerDataToTaskData',
      'Added missing creationTime field to task data',
    );
  }

  // Ensure ref field exists for message sending tasks
  if ((name == TaskNameEnum.sendMessage.value ||
          name == TaskNameEnum.sendQuoteMessage.value ||
          name == TaskNameEnum.editMessage.value ||
          name == TaskNameEnum.sendForwardMessage.value) &&
      !inputData.containsKey('ref')) {
    inputData['ref'] = taskId;
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_convertWorkManagerDataToTaskData',
      'Added missing ref field to message task data',
    );
  }

  // Create data structure compatible with executeTask
  return {
    'taskId': taskId,
    'name': name,
    'inputData': inputData,
    'timeout':
        _getTaskTimeout(name, GlobalConfig.sendTimeoutDuration.inMilliseconds),
  };
}

/**
 * Process task with the same logic as in isolate.
 *
 * This function executes the task using the shared logic from executeTaskLogic,
 * handles errors, and processes the result.
 *
 * @param taskData Task data in worker isolate format
 * @return true if task was processed successfully, false otherwise
 */
@pragma('vm:entry-point')
Future<bool> _processTaskWithSharedLogic(Map<String, dynamic> taskData) async {
  final taskId = taskData['taskId'] as String;
  final name = taskData['name'] as String;
  final inputData = taskData['inputData'] as Map<String, dynamic>;
  final timeout = taskData['timeout'] as int;

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_processTaskWithSharedLogic',
    'Processing task with shared logic: $taskId, name: $name',
  );

  bool success = false;
  String? errorMessage;
  bool isNetworkError = false;

  try {
    // Use the same task processing logic as in isolate
    final metadata = (await SharedPreferencesStore.getDecryptMetadata())!;

    // Set timeout for task
    success = await executeTaskLogic(
      name, metadata.activeSessionKey, inputData,
      null, // No taskRegistrationPort in WorkManager
    ).timeout(
      Duration(milliseconds: timeout),
      onTimeout: () {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_processTaskWithSharedLogic',
          'Task timed out after ${timeout}ms: $taskId, task name: $name',
        );
        return false;
      },
    );

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_processTaskWithSharedLogic',
      'Task processed with result: $success',
    );
  } catch (e, stackTrace) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_processTaskWithSharedLogic',
      'Error processing task: $taskId, error: $e',
    );
    RILogger.printError('Error processing task: $taskId', e, stackTrace);
    success = false;

    // Check if it's a network error
    if (e is DioException && e.isNetworkError) {
      errorMessage = 'Network error: ${e.message}';
      isNetworkError = true;

      // For network errors, we want to retry without showing errors
      String taskName = inputData['taskName'];
      if (taskName == 'sendMessage' ||
          taskName == 'sendQuoteMessage' ||
          taskName == 'editMessage' ||
          taskName == 'sendForwardMessage') {
        // For message-related tasks, pretend success to avoid UI errors
        success = true;
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_processTaskWithSharedLogic',
          'NETWORK DEBUG: Setting success=true for network error in task $taskName to avoid UI error',
        );
      }
    } else {
      errorMessage = e.toString();
    }
  }

  // Process result similar to executeTask
  if (inputData.containsKey('ref')) {
    final msgRef = inputData['ref'];

    // Save result for message sending tasks
    if (name == TaskNameEnum.sendMessage.value ||
        name == TaskNameEnum.sendQuoteMessage.value ||
        name == TaskNameEnum.editMessage.value ||
        name == TaskNameEnum.sendForwardMessage.value) {
      await _saveSendMessageResult(
        msgRef: msgRef,
        workspaceId: inputData['workspaceId'],
        channelId: inputData['channelId'],
        userId: inputData['userId'],
        errorReason: isNetworkError ? null : errorMessage,
        forceStore: true,
      );
    }
  }

  return success;
}

/**
 * Execute a task in the worker isolate.
 *
 * This function is the main entry point for task execution in the worker isolate.
 * It handles task execution, error handling, and result reporting.
 *
 * @param taskId Unique identifier for the task
 * @param name Task name
 * @param sessionKey Active session key
 * @param inputData Task input data
 * @param timeout Timeout in milliseconds
 * @param responsePort SendPort to send results back to main isolate
 * @param ackPort SendPort for acknowledgments
 * @param pendingAcks Map of pending acknowledgments
 * @param taskRegistrationPort SendPort for task registration (optional)
 */
@pragma('vm:entry-point')
Future<void> executeTask({
  required String taskId,
  required String name,
  required String sessionKey,
  required Map<String, dynamic> inputData,
  required int timeout,
  required SendPort responsePort,
  required SendPort ackPort,
  required Map<String, Timer> pendingAcks,
  SendPort? taskRegistrationPort,
}) async {
  bool success = false;

  String? errorMessage;
  bool isNetworkError = false;

  try {
    // Log detailed information about the task execution
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      'executeTask',
      '[TASK_DEBUG] Starting task execution: taskId=$taskId, name=$name',
    );

    // For uploadFile tasks, log specific details
    if (name == TaskNameEnum.uploadFile.value) {
      final uploadType = inputData['uploadType'];
      final filePath = inputData['filePath'] as String?;
      final fileSize = inputData['fileSize'];
      final fileRef = inputData['fileRef'];

      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        'executeTask',
        '[TASK_DEBUG] uploadFile details: uploadType=$uploadType, fileRef=$fileRef, fileSize=$fileSize',
      );

      // Check if file exists
      if (filePath != null) {
        final file = File(filePath);
        final fileExists = file.existsSync();
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          'executeTask',
          '[TASK_DEBUG] File exists: $fileExists, path: $filePath, size: ${fileExists ? file.lengthSync() : 0} bytes',
        );
      }
    }

    final adjustedTimeout = _getTaskTimeout(name, timeout);

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      'executeTask',
      '[TASK_DEBUG] Calling executeTaskLogic for task: $taskId',
    );

    success = await executeTaskLogic(
      name,
      sessionKey,
      inputData,
      taskRegistrationPort,
    ).timeout(
      Duration(milliseconds: adjustedTimeout),
      onTimeout: () {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          'executeTask',
          'Task timed out after ${adjustedTimeout}ms: $taskId, task name: $name',
        );
        // Mark as timeout for special handling later
        errorMessage = 'Task timed out after ${adjustedTimeout}ms';
        return false;
      },
    );
  } catch (e, stackTrace) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      'executeTask',
      'Error executing task: $taskId, error: $e',
    );
    RILogger.printError('Error executing task: $taskId', e, stackTrace);
    success = false;

    // Check if this is a network-related error
    if (e is DioException && e.isNetworkError) {
      errorMessage = 'Network error: ${e.message}';
      isNetworkError = true;

      // For network errors, we want to silently retry without showing error
      // So we'll set success to true for the response
      String taskName = inputData['taskName'];
      if (taskName == 'sendMessage' ||
          taskName == 'sendQuoteMessage' ||
          taskName == 'editMessage' ||
          taskName == 'sendForwardMessage') {
        // For message-related tasks, pretend success to avoid UI error
        success = true;
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          'executeTask',
          'NETWORK DEBUG: Setting success=true for network error in task $taskName to avoid UI error',
        );
      }
    } else {
      errorMessage = e.toString();
    }
  }

  final result = {
    'taskId': taskId,
    'success': success,
  };

  // Add error information if available
  if (errorMessage != null) {
    result['error'] = errorMessage as Object;
  }

  // Add network error flag if applicable
  if (isNetworkError) {
    result['isNetworkError'] = true;
  }

  // Add detailed debug log for network issues
  if (isNetworkError) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      'executeTask',
      'NETWORK DEBUG: Final result for network error task $taskId: $result',
    );
  }

  // If task failed (not network error), add removeFromRegistry field to result
  // to notify workerIsolateEntryPoint to remove taskId from _registeredTaskIds
  if (!success && !isNetworkError) {
    result['removeFromRegistry'] = true;
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      'executeTask',
      'Added removeFromRegistry flag to result for task $taskId to allow resending',
    );
  }

  responsePort.send(result);
  // Only log when task is not compressVideoMessage
  if (name != TaskNameEnum.compressVideoMessage.value) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      'executeTask',
      'Task result sent: $taskId, success: $success',
    );
  }

  // Start a timer to check for acknowledgment
  pendingAcks[taskId] = Timer(Duration(seconds: 5), () {
    // If no acknowledgment received, persist the result
    persistUnacknowledgedResult(taskId, success, ackPort);
    pendingAcks.remove(taskId);
  });
}

/**
 * Core logic for executing tasks.
 *
 * This function contains the actual implementation for different task types.
 * It's shared between the worker isolate and Workmanager.
 *
 * The function handles various task types including:
 * - Send message tasks
 * - Edit message tasks
 * - Quote message tasks
 * - Forward message tasks
 * - Video compression tasks
 * - File upload tasks
 * - Image compression and upload tasks
 *
 * @param name Task name
 * @param sessionKey Active session key
 * @param inputData Task input data
 * @param taskRegistrationPort SendPort for task registration (optional)
 * @return true if task was executed successfully, false otherwise
 * @throws Various exceptions based on task execution
 */
@pragma('vm:entry-point')
Future<bool> executeTaskLogic(
  String name,
  String sessionKey,
  Map<String, dynamic> inputData,
  SendPort? taskRegistrationPort,
) async {
  String taskId = inputData['ref'];
  String taskName = inputData['taskName'];

  // Add log to track hanging tasks
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    'executeTaskLogic',
    '[DEBUG][TaskID:$taskId][TaskName:$taskName] Starting task execution with detailed tracking',
  );

  bool success = true;
  bool isSendMsg = false;
  WorkerSendMessageOutput? sendMessageOutput;
  final IsolateApiClient apiClient = TaskHandlerRegistry.apiClient!;
  final FilestoreClientV2 fileStoreClient =
      TaskHandlerRegistry.fileStoreClient!;
  final retryManager = TaskHandlerRegistry.retryManager!;
  final folderPath = "/${sessionKey}/";

  // Check timeout right from the start for tasks with creationTime
  if (inputData.containsKey('creationTime')) {
    try {
      final creationTimeStr = inputData['creationTime'] as String;
      final creationTime = DateTime.parse(creationTimeStr);

      RILogger.printTaskDebug(
        'WorkerIsolate',
        'executeTaskLogic',
        taskId,
        'Task $taskId ($taskName) creationTime=$creationTimeStr, timeout=${GlobalConfig.sendTimeoutDuration.inSeconds}s',
      );
      RILogger.printTaskDebug(
        'WorkerIsolate',
        'executeTaskLogic',
        taskId,
        'Current time=${DateTime.now()}, timeout time=${creationTime.add(GlobalConfig.sendTimeoutDuration)}',
      );

      if (creationTime
          .add(GlobalConfig.sendTimeoutDuration)
          .isBefore(DateTime.now())) {
        RILogger.printTaskDebug(
          'WorkerIsolate',
          'executeTaskLogic',
          taskId,
          'Task $taskId ($taskName) exceeded timeout limit, completing with error',
        );
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          'executeTaskLogic',
          '[DEBUG][TaskID:$taskId][TaskName:$taskName] Task exceeded timeout limit, completing with error',
        );

        // Special handling for message sending tasks
        if (isSendMsg) {
          sendMessageOutput = WorkerSendMessageOutput(
            success: false,
            channelId: inputData['channelId'],
            workspaceId: inputData['workspaceId'],
            messageId: inputData['messageId'],
            userId: inputData['userId'],
            sendMsgErrorReason: SendMsgErrorEnum.timeout.value,
          );

          // Save result for UI update
          await _saveSendMessageResult(
            msgRef: taskId,
            workspaceId: inputData['workspaceId'],
            channelId: inputData['channelId'],
            userId: inputData['userId'],
            errorReason: "Task exceeded timeout limit",
            forceStore: true,
          );
        }

        return false;
      }

      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        'executeTaskLogic',
        'Task $taskId ($taskName) is within timeout limit, continuing execution',
      );
    } catch (e) {
      // If there's an error parsing creationTime, continue processing task normally
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        'executeTaskLogic',
        'Task $taskId ($taskName) error parsing creationTime: $e',
      );
    }
  }

  try {
    // Only log when task is not compressVideoMessage
    if (name != TaskNameEnum.compressVideoMessage.value) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        'executeTaskLogic',
        'Executing task $name with handler ${taskName}',
      );
    }

    try {
      switch (TaskNameEnum.getEnumByValue(taskName)) {
        case TaskNameEnum.sendMessage:
          isSendMsg = true;
          sendMessageOutput =
              await _handleSendMessage(inputData, taskRegistrationPort);
          success = sendMessageOutput.success;
        case TaskNameEnum.editMessage:
          isSendMsg = true;
          sendMessageOutput = await EditMessageHandler(
            apiClient: apiClient,
            retryManager: retryManager,
          ).handleEditMessage(inputData);
          success = sendMessageOutput.success;
          break;
        case TaskNameEnum.sendQuoteMessage:
          isSendMsg = true;
          sendMessageOutput = await QuoteMessageHandler(
            apiClient: apiClient,
            retryManager: retryManager,
          ).handleQuoteMessage(inputData);
          success = sendMessageOutput.success;
          break;
        case TaskNameEnum.sendForwardMessage:
          final output = await ForwardMessageHandler(
            apiClient: apiClient,
            retryManager: retryManager,
          ).handleForwardMessage(inputData);
          success = output.success;
          break;
        case TaskNameEnum.compressVideoMessage:
          await _handleCompressVideoMessage(inputData, taskRegistrationPort);
          success = true;
          break;
        case TaskNameEnum.compressAndUploadVideoMessage:
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            'executeTaskLogic',
            '[DEBUG][TaskID:$taskId][TaskName:$taskName] About to call _handleCompressAndUploadVideoMessage',
          );
          await _handleCompressAndUploadVideoMessage(
            inputData,
            apiClient,
            fileStoreClient,
            folderPath,
          );
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            'executeTaskLogic',
            '[DEBUG][TaskID:$taskId][TaskName:$taskName] _handleCompressAndUploadVideoMessage completed successfully',
          );
          success = true;
          break;
        case TaskNameEnum.uploadFile:
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            'executeTaskLogic',
            '[DEBUG][TaskID:$taskId][TaskName:$taskName] Starting uploadFile task with uploadType: ${inputData['uploadType']}, inputData: $inputData',
          );

          // Kiểm tra file có tồn tại không
          final filePath = inputData['filePath'] as String?;
          if (filePath != null) {
            final file = File(filePath);
            final fileExists = file.existsSync();
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'executeTaskLogic',
              '[DEBUG][TaskID:$taskId][TaskName:$taskName] File exists: $fileExists, path: $filePath',
            );
          }

          try {
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'executeTaskLogic',
              '[DEBUG][TaskID:$taskId][TaskName:$taskName] Calling _handleUploadAndSendFileMessage',
            );

            await _handleUploadAndSendFileMessage(
              inputData,
              apiClient,
              fileStoreClient,
              folderPath,
            );

            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'executeTaskLogic',
              '[DEBUG][TaskID:$taskId][TaskName:$taskName] uploadFile task completed successfully',
            );

            success = true;
          } catch (e, stackTrace) {
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'executeTaskLogic',
              '[DEBUG][TaskID:$taskId][TaskName:$taskName] Error in uploadFile task: $e',
            );
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              'executeTaskLogic',
              '[DEBUG][TaskID:$taskId][TaskName:$taskName] Stack trace: $stackTrace',
            );

            success = false;
          }
          break;
        case TaskNameEnum.compressAndUploadImages:
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            'executeTaskLogic',
            'Starting compressAndUploadImages task $taskId',
          );
          success = await _handleCompressAndUploadImages(
            inputData,
            apiClient,
            fileStoreClient,
            folderPath,
            taskRegistrationPort,
          );
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            'executeTaskLogic',
            'Completed compressAndUploadImages task $taskId with result: $success',
          );
          break;
        default:
          success = false;
      }
    } on FileNotFoundException catch (e) {
      success = _handleFileNotFoundException(e, taskName, inputData);
    } on SendMessageTimeoutException catch (e) {
      success = await _handleTimeoutException(
        taskName,
        e,
        inputData,
      );
      if (taskName == TaskNameEnum.sendMessage.value) {
        sendMessageOutput = WorkerSendMessageOutput(
          success: success,
          channelId: inputData['channelId'],
          workspaceId: inputData['workspaceId'],
          messageId: inputData['messageId'],
          userId: inputData['userId'],
          sendMsgErrorReason: SendMsgErrorEnum.timeout.value,
        );
      }
    } on DioException catch (e) {
      // Handle network-related errors
      if (e.isNetworkError) {
        // Silently fail but return false to trigger retry mechanism
        // Don't send any notification to UI
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          'executeTaskLogic',
          'Network error detected for task $taskName. Will retry silently when network is available.',
        );
        success = false;

        // For send message tasks, create a proper output WITHOUT error reason
        // This will keep the message in PENDING state without showing error
        if (isSendMsg) {
          sendMessageOutput = WorkerSendMessageOutput(
            success: true,
            // Return true to avoid showing error in UI
            channelId: inputData['channelId'],
            workspaceId: inputData['workspaceId'],
            messageId: inputData['messageId'],
            userId: inputData['userId'],
            // Do NOT set sendMsgErrorReason to avoid showing error in UI
          );

          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            'executeTaskLogic',
            'NETWORK DEBUG: Created WorkerSendMessageOutput with success=true for network error, ref=${inputData["ref"]}',
          );
        }
      } else {
        // Handle other Dio errors
        throw e;
      }
    } catch (e, stackTrace) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        'executeTaskLogic',
        'Task $name encountered an error: $e',
      );
      RILogger.printError('Task execution error', e, stackTrace);

      // Check if this is a network-related error
      final isNetworkError = e.toString().toLowerCase().contains('network') ||
          e.toString().toLowerCase().contains('socket') ||
          e.toString().toLowerCase().contains('connection') ||
          e.toString().toLowerCase().contains('timeout') ||
          e.toString().toLowerCase().contains('unreachable') ||
          e.toString().toLowerCase().contains('internet') ||
          e.toString().toLowerCase().contains('dio');

      // For send message tasks, create a proper error response
      if (isSendMsg) {
        final ref = inputData['ref'];

        // Only send failure response if it's NOT a network error
        if (!isNetworkError) {
          final sendPort = IsolateNameServer.lookupPortByName(ref);
          if (sendPort != null) {
            sendPort.send(
              SendMessageFailureResponse(
                ref: ref,
                errorMessage: 'Unexpected error: $e',
              ).toJson(),
            );
          }
        } else {
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            'executeTaskLogic',
            'NETWORK DEBUG: Suppressing error notification for network error in task $name',
          );
        }

        // Create a generic error output for send message tasks
        sendMessageOutput = WorkerSendMessageOutput(
          // For network errors, pretend success to avoid UI error
          success: isNetworkError ? true : false,
          channelId: inputData['channelId'],
          workspaceId: inputData['workspaceId'],
          messageId: inputData['messageId'],
          userId: inputData['userId'],
          // Only set error reason for non-network errors
          sendMsgErrorReason:
              isNetworkError ? null : SendMsgErrorEnum.unknown.value,
        );
      }

      success = false;
    }

    await _updateTask(
      success,
      isSendMsg,
      taskId,
      sendMessageOutput,
    );

    return success;
  } catch (e) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      'executeTaskLogic',
      'Error executing task $name: $e',
    );
    return false;
  }
}

//region Send Images
/**
 * Handle compress and upload images task.
 *
 * This function processes image compression and upload, handling both
 * first message and subsequent batches. It manages:
 * - Compressing images
 * - Uploading images to server
 * - Creating media objects
 * - Sending or updating messages with images
 * - Handling errors for individual images
 *
 * @param inputData Task input data
 * @param apiClient API client for server communication
 * @param fileStoreClient FileStore client for file operations
 * @param folderPath Folder path for file storage
 * @param taskRegistrationPort SendPort for task registration (optional)
 * @return true if successful, false otherwise
 */
Future<bool> _handleCompressAndUploadImages(
  Map<String, dynamic> inputData,
  IsolateApiClient apiClient,
  FilestoreClientV2 fileStoreClient,
  String folderPath,
  SendPort? taskRegistrationPort,
) async {
  // Get RetryManager from TaskHandlerRegistry
  final retryManager = TaskHandlerRegistry.retryManager!;
  final taskId = inputData['ref'] as String;
  RILogger.printTaskDebug(
    'WorkerIsolate',
    '_handleCompressAndUploadImages',
    taskId,
    'Starting _handleCompressAndUploadImages for task $taskId',
  );

  RILogger.printTaskDebug(
    'WorkerIsolate',
    '_handleCompressAndUploadImages',
    taskId,
    jsonEncode(inputData),
  );

  final input = WorkerCompressAndUploadImagesInput.fromJson(inputData);
  RILogger.printTaskDebug(
    'WorkerIsolate',
    '_handleCompressAndUploadImages',
    taskId,
    'Input parsed, messageRef: ${input.messageRef}, currentBash: ${input.currentBash}, files count: ${input.files.length}',
  );

  //region Handle first message
  if (input.currentBash == 0) {
    RILogger.printTaskDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      taskId,
      'Processing first batch (currentBash=0)',
    );
    final uploadFile = input.firstFileToCompress;
    if (uploadFile == null) {
      RILogger.printTaskDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadImages',
        taskId,
        'No files to compress in first batch, returning true',
      );
      return true;
    }

    RILogger.printTaskDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      taskId,
      'Processing first file: ${uploadFile.fileRef}, path: ${uploadFile.path}',
    );

    final output = await CompressAndUploadImageHandler(
      apiClient: apiClient,
      fileStoreClient: fileStoreClient,
      folderPath: folderPath,
      retryManager: retryManager,
    ).processing(uploadFile, input.creationTime, input.uploadKey);

    RILogger.printTaskDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      taskId,
      'First file processing result: status=${output.attachmentStatus}, url=${output.uploadOutput?.url}',
    );

    // Attachments upload failed but can be retried
    if (output.attachmentStatus == AttachmentStatusEnum.FAILURE &&
        (output.error?.canRetry ?? false)) {
      return false;
    }

    // Attachment download failed but cannot be retried
    if (output.attachmentStatus == AttachmentStatusEnum.FAILURE) {
      await _handleAttachmentsUploadError(
        input,
        [output.error!.attachmentRef],
        input.currentBash,
        taskRegistrationPort,
      );
      return true;
    }

    // Attachment uploaded successfully and can send message directly
    final messageRef = uploadFile.ref;

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      'Directly handling sendMessage for messageRef: $messageRef instead of registering new task',
    );

    // Create media object list
    final mediaList = [
      WorkerMediaObject(
        attachmentType: input.attachmentType,
        fileUrl: output.uploadOutput!.url,
        fileRef: uploadFile.fileRef,
        fileMetadataEmbed: jsonEncode(
          WorkerFileMetadata(
            mimetype: output.uploadOutput!.metadata?.mimetype,
            filename: output.uploadOutput!.metadata?.filename,
            extension: output.uploadOutput!.metadata?.extension,
            filesize: output.uploadOutput!.metadata?.filesize,
            dimensionsEmbed: jsonEncode(
              WorkerSize(
                width: output.imageSize!.width.toInt(),
                height: output.imageSize!.height.toInt(),
              ),
            ),
          ).toJson(),
        ),
      ),
    ];

    // Create input for MediaMessageHandler or UpdateMediaAttachmentsHandler
    // Create mediaListEmbed directly from list of JSON objects
    final mediaListJson = mediaList.map((media) => media.toJson()).toList();
    final mediaListEmbed = jsonEncode(mediaListJson);

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      'Created mediaListEmbed with ${mediaListJson.length} items',
    );

    // Create input for MediaMessageHandler or UpdateMediaAttachmentsHandler
    var workerSendMediaInput = WorkerSendMediaInput(
      mediaListEmbed: mediaListEmbed,
      workspaceId: input.workspaceId,
      channelId: input.channelId,
      userId: input.userId,
      ref: messageRef,
      taskName: TaskNameEnum.sendMessage.value,
      attachmentType: input.attachmentType,
      creationTime: input.creationTime,
      uploadConcurrency: input.uploadConcurrency,
      uploadFilesEmbed: input.uploadFilesEmbed,
      currentBash: input.currentBash,
      messageId: input.messageId,
      isRefTask: true,
    );

    // Log request details for debugging
    final requestData = workerSendMediaInput.toRequest();
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      'Request data: mediaObjects count=${requestData["mediaObjects"]?.length ?? 0}, currentBash=${input.currentBash}',
    );

    if (requestData["mediaObjects"] != null) {
      final mediaObjects = requestData["mediaObjects"] as List<dynamic>;
      for (int i = 0; i < mediaObjects.length; i++) {
        final obj = mediaObjects[i] as Map<String, dynamic>;
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleCompressAndUploadImages',
          'Request mediaObject ${i + 1}/${mediaObjects.length}: fileRef=${obj["fileRef"]}, url=${obj["fileUrl"]}',
        );
      }
    }

    // Call MediaMessageHandler or UpdateMediaAttachmentsHandler directly depending on currentBash
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      'Using ${input.currentBash == 0 ? "MediaMessageHandler" : "UpdateMediaAttachmentsHandler"} for currentBash=${input.currentBash}',
    );

    // Ensure messageId is set for subsequent batches
    if (input.currentBash > 0 &&
        (input.messageId == null || input.messageId!.isEmpty)) {
      // Find messageId from SharedPreferencesStore
      final msgResultKey = '${input.messageRef}';
      final savedResult =
          await SharedPreferencesStore.loadTaskResult(msgResultKey);

      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadImages',
        'Looking for saved message result with key: $msgResultKey, found: ${savedResult != null}',
      );

      if (savedResult != null) {
        final resultData = jsonDecode(savedResult);
        final messageId = resultData['messageId'];

        if (messageId != null && messageId.isNotEmpty) {
          // Create new input with updated messageId
          final updatedInput = WorkerSendMediaInput(
            mediaListEmbed: workerSendMediaInput.mediaListEmbed,
            workspaceId: workerSendMediaInput.workspaceId,
            channelId: workerSendMediaInput.channelId,
            userId: workerSendMediaInput.userId,
            ref: workerSendMediaInput.ref,
            taskName: workerSendMediaInput.taskName,
            attachmentType: workerSendMediaInput.attachmentType,
            creationTime: workerSendMediaInput.creationTime,
            uploadFilesEmbed: workerSendMediaInput.uploadFilesEmbed,
            uploadConcurrency: workerSendMediaInput.uploadConcurrency,
            currentBash: workerSendMediaInput.currentBash,
            messageId: messageId,
          );

          // Use new input instead of reassigning to final variable
          workerSendMediaInput = updatedInput;

          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_handleCompressAndUploadImages',
            'Updated messageId from saved result: $messageId',
          );
        }
      }
    }

    final result = input.currentBash == 0
        ? await MediaMessageHandler(
            apiClient: apiClient,
            retryManager: retryManager,
          ).handleMediaMessage(
            messageInput: workerSendMediaInput,
            ref: messageRef,
          )
        : await UpdateMediaAttachmentsHandler(
            apiClient: apiClient,
            retryManager: retryManager,
          ).handleMediaMessage(
            messageInput: workerSendMediaInput,
            ref: messageRef,
          );

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      'Message ${input.currentBash == 0 ? "sent" : "updated"} with result: ${result.success}, messageId: ${result.messageId}',
    );

    // Save result to SharedPreferencesStore for later retrieval
    await SharedPreferencesStore.saveTaskResult(
      '$messageRef',
      jsonEncode(result.toJson()),
    );

    // Send event to notify result
    _sendSuccessResponse(messageRef, result);

    // Check if there are more images to process
    if (!result.success) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadImages',
        'First image send failed, skipping remaining images',
      );
      return false;
    }

    // Get messageId from result to use for subsequent images
    final messageId = result.messageId;
    if (messageId == null || messageId.isEmpty) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadImages',
        'No messageId returned from first image send, skipping remaining images',
      );
      return result.success;
    }

    // Get all remaining images to process
    final allFiles = input.files;
    final processedFileRefs = [
      mediaList[0].fileRef,
    ]; // Already processed first image
    final remainingFiles = allFiles
        .where(
          (file) =>
              !processedFileRefs.contains(file.fileRef) &&
              file.status == UploadFileStatus.pending,
        )
        .toList();

    if (remainingFiles.isEmpty) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadImages',
        'No remaining images to process',
      );
      return result.success;
    }

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      'Processing ${remainingFiles.length} remaining images with messageId: $messageId',
    );

    // Create new input for all remaining images with existing messageId
    final nextBatchInput = input.copyWith(
      currentBash: 1,
      messageId: messageId,
      uploadFilesEmbed: jsonEncode(remainingFiles),
    );

    // Call recursively to process next images
    final nextBatchResult = await _processNextBatchImages(
      nextBatchInput,
      apiClient,
      fileStoreClient,
      folderPath,
      retryManager,
    );

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      'Completed processing all images with final result: $nextBatchResult',
    );

    // Return final result
    return nextBatchResult;
  }
  //endregion Handle first message

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_handleCompressAndUploadImages',
    'Handle update attachments',
  );

  //region Handle update attachments
  final uploadFiles = input.nextBashFilesToCompress;
  RILogger.printTaskDebug(
    'WorkerIsolate',
    '_handleCompressAndUploadImages',
    taskId,
    'Processing next batch, found ${uploadFiles.length} files',
  );

  // No more files to send
  if (uploadFiles.isEmpty) {
    RILogger.printTaskDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      taskId,
      'No more files to send, returning true',
    );
    return true;
  }

  RILogger.printTaskDebug(
    'WorkerIsolate',
    '_handleCompressAndUploadImages',
    taskId,
    'Starting processingMultiFiles for ${uploadFiles.length} files',
  );

  for (int i = 0; i < uploadFiles.length; i++) {
    RILogger.printTaskDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      taskId,
      'File ${i + 1}/${uploadFiles.length}: ${uploadFiles[i].fileRef}, path: ${uploadFiles[i].path}',
    );
  }

  final uploadResults = await CompressAndUploadImageHandler(
    apiClient: apiClient,
    fileStoreClient: fileStoreClient,
    folderPath: folderPath,
    retryManager: retryManager,
  ).processingMultiFiles(uploadFiles, input.creationTime, input.uploadKey);

  RILogger.printTaskDebug(
    'WorkerIsolate',
    '_handleCompressAndUploadImages',
    taskId,
    'processingMultiFiles completed with ${uploadResults.length} results',
  );

  // Media attachments to send message
  List<WorkerMediaObject> mediaList = [];

  List<AttachmentError> attachmentsError = [];

  for (int i = 0; i < uploadFiles.length; i++) {
    if (uploadResults[i].attachmentStatus == AttachmentStatusEnum.SUCCESS) {
      mediaList.add(
        WorkerMediaObject(
          attachmentType: input.attachmentType,
          fileUrl: uploadResults[i].uploadOutput!.url,
          fileRef: uploadFiles[i].fileRef,
          fileMetadataEmbed: jsonEncode(
            WorkerFileMetadata(
              mimetype: uploadResults[i].uploadOutput!.metadata?.mimetype,
              filename: uploadResults[i].uploadOutput!.metadata?.filename,
              extension: uploadResults[i].uploadOutput!.metadata?.extension,
              filesize: uploadResults[i].uploadOutput!.metadata?.filesize,
              dimensionsEmbed: jsonEncode(
                WorkerSize(
                  width: uploadResults[i].imageSize!.width.toInt(),
                  height: uploadResults[i].imageSize!.height.toInt(),
                ),
              ),
            ).toJson(),
          ),
        ),
      );
    } else {
      attachmentsError.add(uploadResults[i].error!);
    }
  }

  final attachmentRefsError = {
    for (final attachment in attachmentsError)
      attachment.attachmentRef: attachment.canRetry,
  };
  if (mediaList.isNotEmpty) {
    var newInput = input;
    if (attachmentsError.isNotEmpty) {
      // Update upload file status
      final filesUpload = List<WorkerUploadFile>.from(input.files);
      for (final file in filesUpload) {
        if (attachmentRefsError[file.fileRef] ?? true) continue;
        file.status = UploadFileStatus.failed;
      }
      await _saveSendMessageResult(
        msgRef: input.messageRef,
        mapAttachmentStatus: {
          for (final file in input.files)
            file.fileRef!: switch (file.status) {
              UploadFileStatus.pending => AttachmentStatusEnum.UPLOADING,
              UploadFileStatus.sending => AttachmentStatusEnum.UPLOADING,
              UploadFileStatus.done => AttachmentStatusEnum.SUCCESS,
              UploadFileStatus.failed => AttachmentStatusEnum.FAILURE,
            },
        },
      );
      newInput = input.copyWith(
        uploadFilesEmbed: jsonEncode(filesUpload.toList()),
      );
    }
    // Process directly instead of registering new task
    final messageRef = uploadFiles.first.ref;

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      'Directly handling sendMessage for messageRef: $messageRef instead of registering new task with ${mediaList.length} images',
    );

    // Log image list for debugging
    for (int i = 0; i < mediaList.length; i++) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadImages',
        'Media ${i + 1}/${mediaList.length}: fileRef=${mediaList[i].fileRef}, url=${mediaList[i].fileUrl}',
      );
    }

    // Create input for MediaMessageHandler or UpdateMediaAttachmentsHandler
    // Create mediaListEmbed directly from list of JSON objects
    final mediaListJson = mediaList.map((media) => media.toJson()).toList();
    final mediaListEmbed = jsonEncode(mediaListJson);

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      'Created mediaListEmbed with ${mediaListJson.length} items',
    );

    // Create input for MediaMessageHandler or UpdateMediaAttachmentsHandler
    var workerSendMediaInput = WorkerSendMediaInput(
      mediaListEmbed: mediaListEmbed,
      workspaceId: newInput.workspaceId,
      channelId: newInput.channelId,
      userId: newInput.userId,
      ref: messageRef,
      taskName: TaskNameEnum.sendMessage.value,
      attachmentType: newInput.attachmentType,
      creationTime: newInput.creationTime,
      uploadConcurrency: newInput.uploadConcurrency,
      uploadFilesEmbed: newInput.uploadFilesEmbed,
      currentBash: newInput.currentBash,
      messageId: newInput.messageId,
      isRefTask: true,
    );

    // Log request details for debugging
    final requestData = workerSendMediaInput.toRequest();
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      'Request data: mediaObjects count=${requestData["mediaObjects"]?.length ?? 0}, currentBash=${newInput.currentBash}',
    );

    if (requestData["mediaObjects"] != null) {
      final mediaObjects = requestData["mediaObjects"] as List<dynamic>;
      for (int i = 0; i < mediaObjects.length; i++) {
        final obj = mediaObjects[i] as Map<String, dynamic>;
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleCompressAndUploadImages',
          'Request mediaObject ${i + 1}/${mediaObjects.length}: fileRef=${obj["fileRef"]}, url=${obj["fileUrl"]}',
        );
      }
    }

    // Call MediaMessageHandler or UpdateMediaAttachmentsHandler directly depending on currentBash
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      'Using ${newInput.currentBash == 0 ? "MediaMessageHandler" : "UpdateMediaAttachmentsHandler"} for currentBash=${newInput.currentBash}',
    );

    // Ensure messageId is set for subsequent batches
    if (newInput.currentBash > 0 &&
        (newInput.messageId == null || newInput.messageId!.isEmpty)) {
      // Find messageId from SharedPreferencesStore
      final msgResultKey = '${newInput.messageRef}';
      final savedResult =
          await SharedPreferencesStore.loadTaskResult(msgResultKey);

      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadImages',
        'Looking for saved message result with key: $msgResultKey, found: ${savedResult != null}',
      );

      if (savedResult != null) {
        final resultData = jsonDecode(savedResult);
        final messageId = resultData['messageId'];

        if (messageId != null && messageId.isNotEmpty) {
          // Create new input with updated messageId
          final updatedInput = WorkerSendMediaInput(
            mediaListEmbed: workerSendMediaInput.mediaListEmbed,
            workspaceId: workerSendMediaInput.workspaceId,
            channelId: workerSendMediaInput.channelId,
            userId: workerSendMediaInput.userId,
            ref: workerSendMediaInput.ref,
            taskName: workerSendMediaInput.taskName,
            attachmentType: workerSendMediaInput.attachmentType,
            creationTime: workerSendMediaInput.creationTime,
            uploadFilesEmbed: workerSendMediaInput.uploadFilesEmbed,
            uploadConcurrency: workerSendMediaInput.uploadConcurrency,
            currentBash: workerSendMediaInput.currentBash,
            messageId: messageId,
          );

          // Use new input instead of reassigning to final variable
          workerSendMediaInput = updatedInput;

          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_handleCompressAndUploadImages',
            'Updated messageId from saved result: $messageId',
          );
        }
      }
    }

    final result = newInput.currentBash == 0
        ? await MediaMessageHandler(
            apiClient: apiClient,
            retryManager: retryManager,
          ).handleMediaMessage(
            messageInput: workerSendMediaInput,
            ref: messageRef,
          )
        : await UpdateMediaAttachmentsHandler(
            apiClient: apiClient,
            retryManager: retryManager,
          ).handleMediaMessage(
            messageInput: workerSendMediaInput,
            ref: messageRef,
          );

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadImages',
      'Message ${newInput.currentBash == 0 ? "sent" : "updated"} with result: ${result.success}, messageId: ${result.messageId}',
    );

    // Save result to SharedPreferencesStore for later retrieval
    await SharedPreferencesStore.saveTaskResult(
      '$messageRef',
      jsonEncode(result.toJson()),
    );

    // Send event to notify result
    _sendSuccessResponse(messageRef, result);

    // Return result of sending message
    return result.success;
  }
  // Attachments upload failed but can be retried
  if (attachmentRefsError.any((_, canRetry) => canRetry == false)) {
    attachmentRefsError.removeWhere((_, canRetry) => canRetry);
    await _handleAttachmentsUploadError(
      input,
      attachmentRefsError.keys.toList(),
      input.currentBash + 1,
      taskRegistrationPort,
    );
    return true;
  }
  // Attachment download failed but cannot be retried
  return false;
  //endregion Handle update attachments
}

/**
 * Save message sending result to SharedPreferencesStore.
 *
 * This function is called after sending a message or when there's an error
 * to ensure UI is updated correctly. It handles special cases like network errors
 * to keep messages in PENDING state.
 *
 * @param msgRef Message reference
 * @param msgId Message ID on server (if sent successfully)
 * @param workspaceId Workspace ID
 * @param channelId Channel ID
 * @param userId User ID
 * @param mapAttachmentStatus Status of attachments
 * @param errorReason Error reason (if any)
 * @param forceStore Force storing result even if message was sent successfully
 */
Future<void> _saveSendMessageResult({
  required String msgRef,
  String? msgId,
  String? workspaceId,
  String? channelId,
  String? userId,
  Map<String, AttachmentStatusEnum>? mapAttachmentStatus,
  String? errorReason,
  bool forceStore = false,
}) async {
  // Only save to SharedPreferencesStore in specific cases:
  // 1. When message is sent successfully but we need to track it (forceStore = true)
  // 2. When we published an event but didn't receive ack (handled by persistUnacknowledgedResult)
  if (!forceStore && msgId != null) {
    // Message was sent successfully and we have a msgId, no need to store unless forced
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_saveSendMessageResult',
      'Message $msgRef sent successfully with ID $msgId, skipping storage',
    );
    return;
  }

  // Store the result in SharedPreferencesStore for recovery purposes
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_saveSendMessageResult',
    'Storing result for $msgRef in SharedPreferencesStore for recovery: msgId=$msgId, workspaceId=$workspaceId, channelId=$channelId, errorReason=$errorReason, forceStore=$forceStore',
  );

  // Check if this is a network-related error
  final isNetworkError = errorReason == SendMsgErrorEnum.network.value ||
      (errorReason?.toLowerCase().contains('network') ?? false);

  // Create a task result model
  final result = WorkerSendMessageResult(
    messageRef: msgRef,
    messageId: msgId,
    channelId: channelId,
    workspaceId: workspaceId,
    userId: userId,
    mapAttachmentStatusRaw: mapAttachmentStatus == null
        ? null
        : jsonEncode(
            mapAttachmentStatus.map(
              (key, value) => MapEntry(key, value.value),
            ),
          ),
    // Don't set error reason for network errors to keep message in PENDING state
    sendMsgErrorReason: isNetworkError ? null : errorReason,
  );

  if (isNetworkError) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_saveSendMessageResult',
      'NETWORK DEBUG: Suppressing error reason for network error to keep message in PENDING state',
    );
  }

  // Store directly in SharedPreferencesStore instead of using WorkManagerService
  try {
    await SharedPreferencesStore.saveTaskResult(
      '$msgRef',
      jsonEncode(result.toJson()),
    );

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_saveSendMessageResult',
      'Successfully saved result for $msgRef in SharedPreferencesStore',
    );
  } catch (e, stackTrace) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_saveSendMessageResult',
      'Error saving result for $msgRef: $e',
    );

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_saveSendMessageResult',
      'Stack trace: $stackTrace',
    );
  }
}

/**
 * Register a task to send an image message.
 *
 * This function registers a task to send a message with image attachments.
 *
 * @param input Compress and upload images input data
 * @param mediaList List of media objects with image information
 * @param msgRef Message reference
 * @param taskRegistrationPort SendPort for task registration (optional)
 */
Future<void> _registerImageMessageSendTask(
  WorkerCompressAndUploadImagesInput input,
  List<WorkerMediaObject> mediaList,
  String msgRef,
  SendPort? taskRegistrationPort,
) async {
  final messageRef = msgRef;

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_registerImageMessageSendTask',
    'Registering sendMessage task with messageRef: $messageRef',
  );

  final workerSendMediaInput = WorkerSendMediaInput(
    mediaListEmbed: jsonEncode(mediaList),
    workspaceId: input.workspaceId,
    channelId: input.channelId,
    userId: input.userId,
    ref: messageRef,
    taskName: TaskNameEnum.sendMessage.value,
    attachmentType: input.attachmentType,
    creationTime: input.creationTime,
    uploadConcurrency: input.uploadConcurrency,
    uploadFilesEmbed: input.uploadFilesEmbed,
    currentBash: input.currentBash,
    messageId: input.messageId,
  );

  // messageRef is required for message sending task
  if (messageRef.isEmpty) {
    throw Exception('messageRef is required for sendMessage tasks');
  }

  await _registerTaskWithResilientIsolate(
    taskRegistrationPort: taskRegistrationPort,
    taskName: TaskNameEnum.sendMessage.value,
    taskId: messageRef,
    inputData: workerSendMediaInput.toJson(),
    constraints: Constraints(networkType: NetworkType.connected),
    refTask: true,
  );
}

/**
 * Handle attachments upload error.
 *
 * This function processes errors during attachment upload and registers
 * a task for the next batch. It updates attachment status and creates
 * a new task for remaining attachments.
 *
 * @param input Task input data
 * @param attachmentRefs List of attachment references that failed
 * @param nextBash Next batch number
 * @param taskRegistrationPort SendPort for task registration (optional)
 */
Future<void> _handleAttachmentsUploadError(
  WorkerCompressAndUploadImagesInput input,
  List<String> attachmentRefs,
  int nextBash,
  SendPort? taskRegistrationPort,
) async {
  final filesUpload = List<WorkerUploadFile>.from(input.files);
  for (final file in filesUpload) {
    if (attachmentRefs.contains(file.fileRef)) {
      file.status = UploadFileStatus.failed;
    }
  }

  await _saveSendMessageResult(
    msgRef: input.messageRef,
    mapAttachmentStatus: {
      for (final file in filesUpload)
        file.fileRef!: switch (file.status) {
          UploadFileStatus.pending => AttachmentStatusEnum.UPLOADING,
          UploadFileStatus.sending => AttachmentStatusEnum.UPLOADING,
          UploadFileStatus.done => AttachmentStatusEnum.SUCCESS,
          UploadFileStatus.failed => AttachmentStatusEnum.FAILURE,
        },
    },
  );

  final taskRef = UUIDUtils.random();
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_handleAttachmentsUploadError',
    'Creating next batch task with ID: $taskRef, nextBash: $nextBash, filesUpload: ${filesUpload.length}',
  );

  final inputData = WorkerCompressAndUploadImagesInput(
    workspaceId: input.workspaceId,
    channelId: input.channelId,
    userId: input.userId,
    ref: taskRef,
    taskName: TaskNameEnum.compressAndUploadImages.value,
    attachmentType: AttachmentType.PHOTO.rawValue(),
    messageRef: input.messageRef,
    uploadFilesEmbed: jsonEncode(filesUpload),
    uploadConcurrency: input.uploadConcurrency,
    currentBash: nextBash,
    creationTime: DateTime.now(),
  ).toJson();

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_handleAttachmentsUploadError',
    'Registering next batch task with ResilientIsolate',
  );
  await _registerTaskWithResilientIsolate(
    taskRegistrationPort: taskRegistrationPort,
    taskName: TaskNameEnum.compressAndUploadImages.value,
    taskId: taskRef,
    inputData: inputData,
    constraints: Constraints(networkType: NetworkType.connected),
    refTask: true,
  );
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_handleAttachmentsUploadError',
    'Next batch task registered successfully',
  );
}

/**
 * Compress video with retry mechanism and timeout.
 *
 * This function compresses a video file with retries on failure.
 * It includes detailed logging and verification of the compressed output.
 *
 * @param input Compression input data
 * @param taskId Task identifier
 * @param messageRef Message reference
 * @param fileRef File reference
 * @param maxRetries Maximum number of retry attempts
 * @return WorkerCompressVideoOutput or null if compression failed
 */
Future<WorkerCompressVideoOutput?> _compressVideo({
  required WorkerCompressVideoInput input,
  required String taskId,
  required String messageRef,
  required String fileRef,
  int maxRetries = 3,
}) async {
  // No need for RetryManager because video compression is a local task, not related to network
  int retryCount = 0;
  final stopwatch = Stopwatch()..start();

  // Timeout for video compression process (5 minutes)
  final compressTimeout = Duration(minutes: 5);

  final file = File(input.file.path);

  // Check original file
  if (!file.existsSync()) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_compressVideo',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] ERROR: Source file does not exist at path: ${input.file.path}',
    );

    WorkerSendResultHandler.handleError(
      ref: taskId,
      error:
          FileNotFoundException(message: 'File not exists: ${input.file.path}'),
    );
    return null;
  }

  final fileSize = file.lengthSync();
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_compressVideo',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Starting video compression for file: ${input.file.path} (${fileSize ~/ 1024} KB), timeout: ${compressTimeout.inMinutes} minutes',
  );

  while (retryCount <= maxRetries) {
    try {
      // Compress video with timeout
      final compressCompleter = Completer<WorkerCompressVideoOutput>();

      // Create timer for timeout
      final timer = Timer(compressTimeout, () {
        if (!compressCompleter.isCompleted) {
          compressCompleter.completeError(
            TimeoutException(
              'Video compression timeout after ${compressTimeout.inMinutes} minutes',
            ),
          );
        }
      });

      // Start video compression in a separate Future
      Future(() async {
        try {
          final result = await CompressVideoHandler().compressVideo(input);
          if (!compressCompleter.isCompleted) {
            compressCompleter.complete(result);
          }
        } catch (e, stackTrace) {
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_compressVideo',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Caught exception in compression: ${e.runtimeType} - $e',
          );

          // Errors are handled as before
          if (!compressCompleter.isCompleted) {
            compressCompleter.completeError(e, stackTrace);
          }
        }
      });

      // Wait for result or timeout
      final compressOutput =
          await compressCompleter.future.whenComplete(() => timer.cancel());

      // Check video compression result
      if (compressOutput.videoPath.isEmpty) {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_compressVideo',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Video compression failed: empty videoPath, retry ${retryCount + 1}/${maxRetries + 1}',
        );
        retryCount++;
        if (retryCount > maxRetries) return null;
        await Future.delayed(
          Duration(seconds: 2 * retryCount),
        ); // Exponential backoff
        continue;
      }

      // Check compressed file
      final compressedVideoFile = File(compressOutput.videoPath);
      if (!compressedVideoFile.existsSync()) {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_compressVideo',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] WARNING: Compressed video file does not exist at path: ${compressOutput.videoPath}, retry ${retryCount + 1}/${maxRetries + 1}',
        );
        retryCount++;
        if (retryCount > maxRetries) return null;
        await Future.delayed(Duration(seconds: 2 * retryCount));
        continue;
      }

      // Compression successful
      final compressedSize = compressedVideoFile.lengthSync();
      final compressionRatio = fileSize > 0
          ? (compressedSize / fileSize * 100).toStringAsFixed(1)
          : "N/A";
      final elapsedMs = stopwatch.elapsedMilliseconds;

      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_compressVideo',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Video compression successful in ${elapsedMs}ms: Original=${fileSize ~/ 1024} KB, Compressed=${compressedSize ~/ 1024} KB (${compressionRatio}%)',
      );

      return compressOutput;
    } catch (e, stackTrace) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_compressVideo',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Error compressing video (attempt ${retryCount + 1}/${maxRetries + 1}): $e',
      );
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_compressVideo',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
      );

      retryCount++;
      if (retryCount > maxRetries) return null;
      await Future.delayed(Duration(seconds: 2 * retryCount));
    }
  }

  return null;
}

/**
 * Handle upload and send file message in a single flow.
 *
 * This function uploads a file and sends a message with it in a single task.
 * It includes detailed logging and timeout handling for each step.
 *
 * @param inputData Task input data
 * @param apiClient API client for server communication
 * @param fileStoreClient FileStore client for file operations
 * @param folderPath Folder path for file storage
 */
Future<void> _handleUploadAndSendFileMessage(
  Map<String, dynamic> inputData,
  IsolateApiClient apiClient,
  FilestoreClientV2 fileStoreClient,
  String folderPath,
) async {
  final stopwatch = Stopwatch()..start();
  final taskId = inputData['ref'];
  final input = WorkerUploadFileInput.fromJson(inputData);
  final messageRef =
      input.messageRef.isEmpty ? input.fileRef : input.messageRef;

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_handleUploadAndSendFileMessage',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Starting to handle upload and send message, uploadType: ${input.uploadType}, filePath: ${input.filePath}, fileSize: ${input.fileSize}',
  );

  // Log task details for debugging
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_handleUploadAndSendFileMessage',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Task details: fileRef=${input.fileRef}, fileName=${input.fileName}',
  );

  // Log chi tiết về file
  final filePath = input.filePath;
  if (filePath.isNotEmpty) {
    final file = File(filePath);
    final fileExists = file.existsSync();
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[TASK_DEBUG][TaskID:$taskId][MessageRef:$messageRef] File details: exists=$fileExists, path=$filePath, size=${fileExists ? file.lengthSync() : 0} bytes',
    );

    // Kiểm tra quyền truy cập file
    if (fileExists) {
      try {
        final randomAccess = file.openSync(mode: FileMode.read);
        randomAccess.closeSync();
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleUploadAndSendFileMessage',
          '[TASK_DEBUG][TaskID:$taskId][MessageRef:$messageRef] File is readable',
        );
      } catch (e) {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleUploadAndSendFileMessage',
          '[TASK_DEBUG][TaskID:$taskId][MessageRef:$messageRef] File is not readable: $e',
        );
      }
    }
  }

  // Thiết lập timeout tổng thể (10 phút)
  final totalTimeout = Duration(minutes: 10);
  final timeoutCompleter = Completer<void>();
  final processingCompleter = Completer<void>();

  // Khai báo timer ở phạm vi rộng hơn để có thể sử dụng trong toàn bộ phương thức
  Timer? taskTimer;

  // Tạo timer cho timeout
  taskTimer = Timer(totalTimeout, () {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Total timeout reached after ${totalTimeout.inMinutes} minutes',
    );

    if (!timeoutCompleter.isCompleted) {
      timeoutCompleter.complete();
    }

    if (!processingCompleter.isCompleted) {
      processingCompleter.complete();
    }
  });

  try {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Starting processing with timeout of ${totalTimeout.inMinutes} minutes',
    );

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 1: Before processing file',
    );

    // Thay đổi cơ chế xử lý song song, không sử dụng Future.any nữa
    // Thay vào đó, sử dụng timeout cho từng bước cụ thể

    // Tiếp tục xử lý bình thường, không chờ Future.any
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 2: Continuing with normal processing',
    );
  } catch (e, stackTrace) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Error processing file: $e',
    );
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
    );
    return;
  }

  try {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Starting file upload process',
    );

    // Bước 1: Upload file
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 3: Before starting file upload process with _uploadFile',
    );

    final stopwatch = Stopwatch()..start();

    // Khai báo uploadOutput ở phạm vi rộng hơn để có thể sử dụng trong toàn bộ phương thức
    WorkerUploadFileOutput? uploadOutput;

    try {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 4: Calling _uploadFile',
      );

      // Đặt timeout cho _uploadFile
      uploadOutput = await _uploadFile(
        uploadFileInput: input,
        apiClient: apiClient,
        fileStoreClient: fileStoreClient,
        folderPath: folderPath,
        taskId: taskId,
        messageRef: messageRef,
      ).timeout(
        Duration(minutes: 5),
        onTimeout: () {
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_handleUploadAndSendFileMessage',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] _uploadFile timed out after 5 minutes',
          );
          return null;
        },
      );

      final uploadDuration = stopwatch.elapsedMilliseconds;
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 5: After _uploadFile, duration=${uploadDuration}ms, result=${uploadOutput != null ? "success" : "failed"}',
      );

      if (uploadOutput != null) {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleUploadAndSendFileMessage',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Upload details: url=${uploadOutput.url}',
        );
      } else {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleUploadAndSendFileMessage',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 5.1: Upload failed, uploadOutput is null',
        );
        //TODO: Add WorkerSendResultHandler.handleError call here
        // Should save error result and call WorkerSendResultHandler.handleError
        // to notify UI about upload failure before returning
        // Hoàn thành quá trình xử lý vì upload thất bại
        taskTimer.cancel(); // Hủy timer tổng thể
        if (!processingCompleter.isCompleted) {
          processingCompleter.complete();
        }
        return;
      }
    } catch (e, stackTrace) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 5.2: Exception in _uploadFile: $e',
      );
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
      );
      //TODO: Add WorkerSendResultHandler.handleError call here
      // Should save error result and call WorkerSendResultHandler.handleError
      // to notify UI about upload exception before returning
      // Hoàn thành quá trình xử lý vì có lỗi
      taskTimer.cancel(); // Hủy timer tổng thể
      if (!processingCompleter.isCompleted) {
        processingCompleter.complete();
      }
      return;
    }

    // Removed redundant check since we already handle null uploadOutput in the try-catch block

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Continue send message',
    );

    // Xác định loại attachment dựa trên uploadType
    final int attachmentTypeValue;
    switch (input.uploadType) {
      case 4: // UploadFileTypeEnum.voice
        attachmentTypeValue = AttachmentType.VOICE_MESSAGE.rawValue();
        break;
      case 5: // UploadFileTypeEnum.file
        attachmentTypeValue = AttachmentType.FILE.rawValue();
        break;
      default:
        attachmentTypeValue = AttachmentType.FILE.rawValue();
    }

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Creating media object with attachmentType: $attachmentTypeValue, uploadType: ${input.uploadType}',
    );

    // Tạo media object từ kết quả upload
    final mediaObject = WorkerMediaObject(
      attachmentType: attachmentTypeValue,
      fileUrl: uploadOutput.url,
      fileRef: input.fileRef,
      fileMetadataEmbed: jsonEncode(
        WorkerFileMetadata(
          mimetype: uploadOutput.metadata?.mimetype,
          filename: uploadOutput.metadata?.filename,
          extension: uploadOutput.metadata?.extension,
          filesize: uploadOutput.metadata?.filesize,
        ).toJson(),
      ),
    );

    // Bước 2: Gửi tin nhắn file hoặc voice
    final sendMediaInput = WorkerSendMediaInput(
      mediaListEmbed: jsonEncode([mediaObject]),
      workspaceId: input.mediaMessageBody?.workspaceId ?? '',
      channelId: input.mediaMessageBody?.channelId ?? '',
      userId: input.mediaMessageBody?.userId ?? '',
      ref: messageRef,
      taskName: TaskNameEnum.sendMessage.value,
      attachmentType: attachmentTypeValue,
      creationTime: input.mediaMessageBody?.creationTime ?? DateTime.now(),
    );

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Preparing to send message with attachmentType: $attachmentTypeValue',
    );

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 10: Before calling _sendFileMessage',
    );

    final sendStopwatch = Stopwatch()..start();
    WorkerSendMessageOutput? sendOutput;
    try {
      // Đặt timeout cho _sendFileMessage
      sendOutput = await _sendFileMessage(
        sendMediaInput: sendMediaInput,
        apiClient: apiClient,
        taskId: taskId,
        messageRef: messageRef,
      ).timeout(
        Duration(minutes: 5),
        onTimeout: () {
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_handleUploadAndSendFileMessage',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] _sendFileMessage timed out after 5 minutes',
          );
          return null;
        },
      );

      final sendDuration = sendStopwatch.elapsedMilliseconds;
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 11: After _sendFileMessage, duration=${sendDuration}ms, result=${sendOutput != null ? "success=${sendOutput.success}, messageId=${sendOutput.messageId}" : "failed"}',
      );

      if (sendOutput == null) {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleUploadAndSendFileMessage',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 11.2: _sendFileMessage returned null',
        );
        //TODO: Add WorkerSendResultHandler.handleError call here
        // Should save error result and call WorkerSendResultHandler.handleError
        // to notify UI about send message failure before returning
        // Hoàn thành quá trình xử lý vì gửi tin nhắn thất bại
        taskTimer.cancel(); // Hủy timer tổng thể
        if (!processingCompleter.isCompleted) {
          processingCompleter.complete();
        }
        return;
      }
    } catch (e, stackTrace) {
      final sendDuration = sendStopwatch.elapsedMilliseconds;
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 11.1: Exception in _sendFileMessage after ${sendDuration}ms: $e',
      );
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
      );
      //TODO: Add WorkerSendResultHandler.handleError call here
      // Should save error result and call WorkerSendResultHandler.handleError
      // to notify UI about send message exception before returning
      // Hoàn thành quá trình xử lý vì có lỗi
      taskTimer.cancel(); // Hủy timer tổng thể
      if (!processingCompleter.isCompleted) {
        processingCompleter.complete();
      }
      return;
    }

    // Đã kiểm tra sendOutput == null trong try-catch ở trên, không cần kiểm tra lại ở đây

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 13: Send message success, messageId=${sendOutput.messageId}',
    );

    // Hoàn thành quá trình xử lý
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 14: Before completing processingCompleter',
    );

    // Hủy timer tổng thể vì task đã hoàn thành
    taskTimer.cancel();

    if (!processingCompleter.isCompleted) {
      processingCompleter.complete();
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 14.1: processingCompleter completed successfully',
      );
    } else {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 14.2: processingCompleter was already completed',
      );
    }

    final totalDuration = stopwatch.elapsedMilliseconds;
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 15: File message sent successfully in ${totalDuration}ms',
    );

    // Log chi tiết kết quả để debug
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 16: Final result - fileUrl=${uploadOutput.url}, messageId=${sendOutput.messageId}',
    );

    // Thông báo thành công
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 17: TASK COMPLETED SUCCESSFULLY',
    );
  } catch (e, stackTrace) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 18: Error in main try-catch block: $e',
    );
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
    );

    // Hủy timer tổng thể vì có lỗi
    taskTimer.cancel();

    // Hoàn thành quá trình xử lý với lỗi
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 19: Before completing processingCompleter with error',
    );

    if (!processingCompleter.isCompleted) {
      // Thay vì completeError, sử dụng complete để tránh lỗi không xử lý được
      processingCompleter.complete();
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 19.1: processingCompleter completed despite error',
      );
    } else {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 19.2: processingCompleter was already completed',
      );
    }

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadAndSendFileMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 20: End of catch block',
    );
  } finally {
    // Đảm bảo timer luôn bị hủy và processingCompleter luôn được hoàn thành
    if (taskTimer.isActive) {
      taskTimer.cancel();
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 21: Timer cancelled in finally block',
      );
    }

    if (!processingCompleter.isCompleted) {
      processingCompleter.complete();
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadAndSendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] CHECKPOINT 22: processingCompleter completed in finally block',
      );
    }
  }
}

/**
 * Upload file with retry mechanism and timeout.
 *
 * This function uploads a file with retries on failure.
 * It includes detailed logging and verification of the uploaded file.
 *
 * @param uploadFileInput Upload file input data
 * @param apiClient API client for server communication
 * @param fileStoreClient FileStore client for file operations
 * @param folderPath Folder path for file storage
 * @param taskId Task identifier
 * @param messageRef Message reference
 * @param maxRetries Maximum number of retry attempts
 * @return WorkerUploadFileOutput or null if upload failed
 */
Future<WorkerUploadFileOutput?> _uploadFile({
  required WorkerUploadFileInput uploadFileInput,
  required IsolateApiClient apiClient,
  required FilestoreClientV2 fileStoreClient,
  required String folderPath,
  required String taskId,
  required String messageRef,
  int maxRetries = 3,
}) async {
  // Lấy RetryManager từ TaskHandlerRegistry
  final retryManager = TaskHandlerRegistry.retryManager!;
  int retryCount = 0;
  final stopwatch = Stopwatch()..start();

  // Timeout cho quá trình upload file (5 phút)
  final uploadTimeout = Duration(minutes: 5);

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_uploadFile',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 1: Starting file upload with path: ${uploadFileInput.filePath}, size: ${uploadFileInput.fileSize} bytes, uploadType: ${uploadFileInput.uploadType}',
  );

  // Kiểm tra file có tồn tại không
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_uploadFile',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 2: Checking if file exists',
  );

  final file = File(uploadFileInput.filePath);
  if (!file.existsSync()) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_uploadFile',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 2.1: ERROR - File does not exist at path: ${uploadFileInput.filePath}',
    );
    //TODO: Throw FileNotFoundException instead of returning null
    // Should throw FileNotFoundException(message: 'File not exists: ${uploadFileInput.filePath}')
    // to be consistent with other file validation patterns in the codebase
    return null;
  }

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_uploadFile',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 3: File exists, actual size: ${file.lengthSync()} bytes',
  );

  while (retryCount <= maxRetries) {
    try {
      final uploadCompleter = Completer<WorkerUploadFileOutput>();
      final timer = Timer(uploadTimeout, () {
        if (!uploadCompleter.isCompleted) {
          uploadCompleter.completeError(
            TimeoutException(
              'File upload timed out after ${uploadTimeout.inMinutes} minutes',
            ),
          );
        }
      });

      // Bắt đầu upload file trong một Future riêng biệt
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_uploadFile',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 4: Starting file upload in separate Future',
      );

      Future(() async {
        try {
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_uploadFile',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 5: Calling UploadFileHandler.uploadFile',
          );

          final uploadStartTime = DateTime.now();
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_uploadFile',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 6: Creating UploadFileHandler instance',
          );

          final handler = fileHandler.UploadFileHandler(
            apiClient: apiClient,
            fileStoreClient: fileStoreClient,
            folderPath: folderPath,
            retryManager: retryManager,
          );

          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_uploadFile',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 7: Calling handler.uploadFile',
          );

          final result = await handler.uploadFile(uploadFileInput);

          final uploadDuration =
              DateTime.now().difference(uploadStartTime).inMilliseconds;
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_uploadFile',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 8: UploadFileHandler.uploadFile completed in ${uploadDuration}ms, url: ${result?.url}',
          );

          if (!uploadCompleter.isCompleted) {
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              '_uploadFile',
              '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 9: Completing uploadCompleter with result',
            );
            uploadCompleter.complete(result);
          } else {
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              '_uploadFile',
              '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 9.1: uploadCompleter already completed',
            );
          }
        } catch (e, stackTrace) {
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_uploadFile',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 10: Error in UploadFileHandler.uploadFile: $e',
          );

          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_uploadFile',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 10.1: Stack trace: $stackTrace',
          );

          if (!uploadCompleter.isCompleted) {
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              '_uploadFile',
              '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 11: Completing uploadCompleter with error',
            );
            uploadCompleter.completeError(e, stackTrace);
          } else {
            RILogger.printClassMethodDebug(
              'WorkerIsolate',
              '_uploadFile',
              '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 11.1: uploadCompleter already completed',
            );
          }
        }
      });

      // Đợi kết quả hoặc timeout
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_uploadFile',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 12: Waiting for uploadCompleter.future',
      );

      WorkerUploadFileOutput? uploadOutput;
      try {
        uploadOutput =
            await uploadCompleter.future.whenComplete(() => timer.cancel());

        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_uploadFile',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 13: uploadCompleter.future completed successfully',
        );
      } catch (e, stackTrace) {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_uploadFile',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 13.1: Error in uploadCompleter.future: $e',
        );
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_uploadFile',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
        );
        throw e; // Re-throw to be caught by outer try-catch
      }

      final uploadDuration = stopwatch.elapsedMilliseconds;
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_uploadFile',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 14: File uploaded successfully in ${uploadDuration}ms, url: ${uploadOutput.url}',
      );

      return uploadOutput;
    } catch (e, stackTrace) {
      final errorDuration = stopwatch.elapsedMilliseconds;
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_uploadFile',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 15: Error uploading file (attempt ${retryCount + 1}/${maxRetries + 1}) after ${errorDuration}ms: $e',
      );
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_uploadFile',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
      );

      retryCount++;
      if (retryCount > maxRetries) {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_uploadFile',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 16: Max retries reached, returning null',
        );
        //TODO: Throw specific exception instead of returning null
        // Should throw UploadException with retry count and last error details
        // to provide better error context to caller
        return null;
      }

      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_uploadFile',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] UPLOAD CHECKPOINT 17: Retrying after delay (${2 * retryCount} seconds)',
      );
      await Future.delayed(Duration(seconds: 2 * retryCount));
    }
  }

  //TODO: Throw specific exception instead of returning null
  // Should throw UploadException with retry count and last error details
  // to provide better error context to caller
  return null;
}

/**
 * Send file or voice message with retry mechanism and timeout.
 *
 * This function sends a file or voice message with retries on failure.
 * It's the final step in the file sending process.
 *
 * @param sendMediaInput Send media input data
 * @param apiClient API client for server communication
 * @param taskId Task identifier
 * @param messageRef Message reference
 * @param maxRetries Maximum number of retry attempts
 * @return WorkerSendMessageOutput or null if sending failed
 */
Future<WorkerSendMessageOutput?> _sendFileMessage({
  required WorkerSendMediaInput sendMediaInput,
  required IsolateApiClient apiClient,
  required String taskId,
  required String messageRef,
  int maxRetries = 3,
}) async {
  int retryCount = 0;
  final stopwatch = Stopwatch()..start();
  final retryManager = TaskHandlerRegistry.retryManager!;

  // Timeout cho quá trình gửi tin nhắn (3 phút)
  final sendTimeout = Duration(minutes: 3);

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_sendFileMessage',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Starting to send message with attachmentType: ${sendMediaInput.attachmentType}',
  );

  while (retryCount <= maxRetries) {
    try {
      final sendCompleter = Completer<WorkerSendMessageOutput>();
      final timer = Timer(sendTimeout, () {
        if (!sendCompleter.isCompleted) {
          sendCompleter.completeError(
            TimeoutException(
              'Send message timed out after ${sendTimeout.inMinutes} minutes',
            ),
          );
        }
      });

      // Bắt đầu gửi tin nhắn trong một Future riêng biệt
      Future(() async {
        try {
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_sendFileMessage',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Calling MediaMessageHandler.handleMediaMessage with mediaListEmbed: ${sendMediaInput.mediaListEmbed}',
          );

          final result = await MediaMessageHandler(
            apiClient: apiClient,
            retryManager: retryManager,
          ).handleMediaMessage(
            messageInput: sendMediaInput,
            ref: messageRef,
          );

          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_sendFileMessage',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] MediaMessageHandler.handleMediaMessage completed with result: success=${result.success}, messageId=${result.messageId}',
          );

          if (!sendCompleter.isCompleted) {
            sendCompleter.complete(result);
          }
        } catch (e, stackTrace) {
          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_sendFileMessage',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Error in MediaMessageHandler.handleMediaMessage: $e',
          );

          RILogger.printClassMethodDebug(
            'WorkerIsolate',
            '_sendFileMessage',
            '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
          );

          if (!sendCompleter.isCompleted) {
            sendCompleter.completeError(e, stackTrace);
          }
        }
      });

      // Đợi kết quả hoặc timeout
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_sendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Waiting for sendCompleter.future result',
      );

      final sendOutput =
          await sendCompleter.future.whenComplete(() => timer.cancel());

      final sendDuration = stopwatch.elapsedMilliseconds;
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_sendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] File message sent successfully in ${sendDuration}ms, messageId: ${sendOutput.messageId}',
      );

      // Lưu kết quả gửi tin nhắn
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_sendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Saving send message result with _saveSendMessageResult',
      );

      await _saveSendMessageResult(
        msgRef: messageRef,
        workspaceId: sendMediaInput.workspaceId,
        channelId: sendMediaInput.channelId,
        userId: sendMediaInput.userId,
        msgId: sendOutput.messageId,
      );

      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_sendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Send message result saved successfully',
      );

      return sendOutput;
    } catch (e, stackTrace) {
      final errorDuration = stopwatch.elapsedMilliseconds;
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_sendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Error sending file message (attempt ${retryCount + 1}/${maxRetries + 1}) after ${errorDuration}ms: $e',
      );
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_sendFileMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
      );

      retryCount++;
      if (retryCount > maxRetries) {
        //TODO: Throw SendMessageException instead of returning null
        // Should throw exception with details about send failure and retry attempts
        // to provide proper error context to caller
        return null;
      }
      await Future.delayed(Duration(seconds: 2 * retryCount));
    }
  }

  //TODO: Throw SendMessageException instead of returning null
  // Should throw exception with details about send failure and retry attempts
  // to provide proper error context to caller
  return null;
}

/**
 * Upload video with retry mechanism and timeout.
 *
 * This function uploads a video file with retries on failure.
 * It includes timeout handling and detailed logging.
 *
 * @param uploadFileInput Upload file input data
 * @param apiClient API client for server communication
 * @param fileStoreClient FileStore client for file operations
 * @param folderPath Folder path for file storage
 * @param taskId Task identifier
 * @param messageRef Message reference
 * @param maxRetries Maximum number of retry attempts
 * @return WorkerUploadFileOutput or null if upload failed
 */
Future<fileHandler.WorkerUploadFileOutput?> _uploadVideo({
  required WorkerUploadFileInput uploadFileInput,
  required IsolateApiClient apiClient,
  required FilestoreClientV2 fileStoreClient,
  required String folderPath,
  required String taskId,
  required String messageRef,
  int maxRetries = 3,
}) async {
  // Get RetryManager from TaskHandlerRegistry
  final retryManager = TaskHandlerRegistry.retryManager!;
  int retryCount = 0;
  final stopwatch = Stopwatch()..start();

  // Timeout for video upload process (8 minutes)
  final uploadTimeout = Duration(minutes: 8);
  final file = File(uploadFileInput.filePath);

  // Check file to upload
  if (!file.existsSync()) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_uploadVideo',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] ERROR: Video file does not exist at path: ${uploadFileInput.filePath}',
    );
    WorkerSendResultHandler.handleError(
      ref: taskId,
      error: FileNotFoundException(message: 'File not exists: ${file.path}'),
    );
    return null;
  }

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_uploadVideo',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Starting video upload: ${uploadFileInput.filePath}, timeout: ${uploadTimeout.inMinutes} minutes',
  );

  while (retryCount <= maxRetries) {
    try {
      // Upload video with timeout
      final uploadCompleter = Completer<fileHandler.WorkerUploadFileOutput?>();

      // Create timer for timeout
      final timer = Timer(uploadTimeout, () {
        if (!uploadCompleter.isCompleted) {
          uploadCompleter.completeError(
            TimeoutException(
              'Video upload timeout after ${uploadTimeout.inMinutes} minutes',
            ),
          );
        }
      });

      // Start video upload in a separate Future
      Future(() async {
        try {
          final result = await fileHandler.UploadFileHandler(
            apiClient: apiClient,
            fileStoreClient: fileStoreClient,
            folderPath: folderPath,
            retryManager: retryManager,
          ).uploadFile(uploadFileInput);

          if (!uploadCompleter.isCompleted) {
            uploadCompleter.complete(result);
          }
        } catch (e, stackTrace) {
          if (!uploadCompleter.isCompleted) {
            uploadCompleter.completeError(e, stackTrace);
          }
        }
      });

      // Wait for result or timeout
      final uploadOutput =
          await uploadCompleter.future.whenComplete(() => timer.cancel());

      // Check upload result
      if (uploadOutput?.url == null || uploadOutput!.url.isEmpty) {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_uploadVideo',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Video upload failed: empty URL, retry ${retryCount + 1}/${maxRetries + 1}',
        );
        retryCount++;
        if (retryCount > maxRetries) return null;
        await Future.delayed(
          Duration(seconds: 3 * retryCount),
        ); // Exponential backoff
        continue;
      }

      // Upload successful
      final elapsedMs = stopwatch.elapsedMilliseconds;
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_uploadVideo',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Video uploaded successfully in ${elapsedMs}ms: ${uploadOutput.url}',
      );

      return uploadOutput;
    } catch (e, stackTrace) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_uploadVideo',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Error uploading video (attempt ${retryCount + 1}/${maxRetries + 1}): $e',
      );
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_uploadVideo',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
      );

      retryCount++;
      if (retryCount > maxRetries) {
        WorkerSendResultHandler.handleError(
          ref: taskId,
          error: SendMessageTimeoutException(msgRef: taskId),
        );
        return null;
      }
      await Future.delayed(Duration(seconds: 3 * retryCount));
    }
  }

  WorkerSendResultHandler.handleError(
    ref: taskId,
    error: SendMessageTimeoutException(msgRef: taskId),
  );
  return null;
}

/**
 * Upload thumbnail with retry mechanism.
 *
 * This function uploads a video thumbnail with retries on failure.
 * Thumbnail upload is less critical than video upload, so it has fewer retries.
 *
 * @param thumbnailInput Upload thumbnail input data
 * @param apiClient API client for server communication
 * @param fileStoreClient FileStore client for file operations
 * @param folderPath Folder path for file storage
 * @param taskId Task identifier
 * @param messageRef Message reference
 * @param maxRetries Maximum number of retry attempts (default: 2, fewer than video)
 * @return WorkerUploadFileOutput or null if upload failed
 */
Future<fileHandler.WorkerUploadFileOutput?> _uploadThumbnail({
  required WorkerUploadFileInput thumbnailInput,
  required IsolateApiClient apiClient,
  required FilestoreClientV2 fileStoreClient,
  required String folderPath,
  required String taskId,
  required String messageRef,
  int maxRetries =
      2, // Fewer retries because thumbnail is less important than video
}) async {
  // Get RetryManager from TaskHandlerRegistry
  final retryManager = TaskHandlerRegistry.retryManager!;
  int retryCount = 0;
  final stopwatch = Stopwatch()..start();

  final file = File(thumbnailInput.thumbnailPath ?? '');

  // Check thumbnail file
  if (thumbnailInput.thumbnailPath!.isEmpty || !file.existsSync()) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_uploadThumbnail',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] ERROR: Thumbnail file does not exist at path: ${thumbnailInput.thumbnailPath}',
    );
    WorkerSendResultHandler.handleError(
      ref: taskId,
      error: FileNotFoundException(
        message: 'File thumbnail not exists: ${thumbnailInput.thumbnailPath}',
      ),
    );
    return null;
  }

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_uploadThumbnail',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Starting thumbnail upload: ${thumbnailInput.thumbnailPath}',
  );

  while (retryCount <= maxRetries) {
    try {
      // Upload thumbnail
      final thumbnailOutput = await fileHandler.UploadFileHandler(
        apiClient: apiClient,
        fileStoreClient: fileStoreClient,
        folderPath: folderPath,
        retryManager: retryManager,
      ).uploadFile(thumbnailInput);

      // Check upload result
      if (thumbnailOutput?.url == null || thumbnailOutput!.url.isEmpty) {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_uploadThumbnail',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Thumbnail upload failed: empty URL, retry ${retryCount + 1}/${maxRetries + 1}',
        );
        retryCount++;
        if (retryCount > maxRetries) {
          WorkerSendResultHandler.handleError(
            ref: taskId,
            error: SendMessageTimeoutException(
                msgRef: taskId, message: 'Send thumbnail timeout'),
          );
          return null;
        }
        await Future.delayed(
          Duration(seconds: 2 * retryCount),
        ); // Exponential backoff
        continue;
      }

      // Upload successful
      final elapsedMs = stopwatch.elapsedMilliseconds;
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_uploadThumbnail',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Thumbnail uploaded successfully in ${elapsedMs}ms: ${thumbnailOutput.url}',
      );

      return thumbnailOutput;
    } catch (e, stackTrace) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_uploadThumbnail',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Error uploading thumbnail (attempt ${retryCount + 1}/${maxRetries + 1}): $e',
      );
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_uploadThumbnail',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
      );

      retryCount++;
      if (retryCount > maxRetries) {
        // Thumbnail is less important than video, so we can continue even if there's an error
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_uploadThumbnail',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Failed to upload thumbnail after retries, but will continue without it',
        );
        return null;
      }
      await Future.delayed(Duration(seconds: 2 * retryCount));
    }
  }

  return null;
}

/**
 * Send video message with retry mechanism and timeout.
 *
 * This function sends a video message with retries on failure.
 * It's the final step in the video sending process.
 *
 * @param sendMediaInput Send media input data
 * @param apiClient API client for server communication
 * @param taskId Task identifier
 * @param messageRef Message reference
 * @param maxRetries Maximum number of retry attempts
 * @return WorkerSendMessageOutput or null if sending failed
 */
Future<WorkerSendMessageOutput?> _sendVideoMessage({
  required WorkerSendMediaInput sendMediaInput,
  required IsolateApiClient apiClient,
  required String taskId,
  required String messageRef,
  int maxRetries = 3,
}) async {
  // Get RetryManager from TaskHandlerRegistry
  final retryManager = TaskHandlerRegistry.retryManager!;
  int retryCount = 0;
  final stopwatch = Stopwatch()..start();

  // Timeout for message sending process (3 minutes)
  final sendTimeout = Duration(minutes: 3);

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_sendVideoMessage',
    '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Starting to send video message, timeout: ${sendTimeout.inMinutes} minutes',
  );

  while (retryCount <= maxRetries) {
    try {
      // Send message with timeout
      final sendCompleter = Completer<WorkerSendMessageOutput>();

      // Create timer for timeout
      final timer = Timer(sendTimeout, () {
        if (!sendCompleter.isCompleted) {
          sendCompleter.completeError(
            TimeoutException(
              'Send video message timeout after ${sendTimeout.inMinutes} minutes',
            ),
          );
        }
      });

      // Start sending message in a separate Future
      Future(() async {
        try {
          final result = await MediaMessageHandler(
            apiClient: apiClient,
            retryManager: retryManager,
          ).handleMediaMessage(
            messageInput: sendMediaInput,
            ref: messageRef,
          );

          if (!sendCompleter.isCompleted) {
            sendCompleter.complete(result);
          }
        } catch (e, stackTrace) {
          if (!sendCompleter.isCompleted) {
            sendCompleter.completeError(e, stackTrace);
          }
        }
      });

      // Wait for result or timeout
      final sendOutput =
          await sendCompleter.future.whenComplete(() => timer.cancel());

      // Check message sending result
      if (!sendOutput.success || sendOutput.messageId!.isEmpty) {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_sendVideoMessage',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Send video message failed: success=${sendOutput.success}, messageId=${sendOutput.messageId}, retry ${retryCount + 1}/${maxRetries + 1}',
        );
        retryCount++;
        if (retryCount > maxRetries) {
          WorkerSendResultHandler.handleError(
            ref: taskId,
            error: SendMessageTimeoutException(
              msgRef: taskId,
              message: 'Send video message timeout',
            ),
          );
          return null;
        }
        await Future.delayed(
          Duration(seconds: 2 * retryCount),
        ); // Exponential backoff
        continue;
      }

      // Message sent successfully
      final elapsedMs = stopwatch.elapsedMilliseconds;
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_sendVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Video message sent successfully in ${elapsedMs}ms, messageId: ${sendOutput.messageId}',
      );

      return sendOutput;
    } catch (e, stackTrace) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_sendVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Error sending video message (attempt ${retryCount + 1}/${maxRetries + 1}): $e',
      );
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_sendVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
      );

      retryCount++;
      if (retryCount > maxRetries) {
        WorkerSendResultHandler.handleError(
          ref: taskId,
          error: SendMessageTimeoutException(
            msgRef: taskId,
            message: 'Send video message timeout',
          ),
        );
        return null;
      }
      await Future.delayed(Duration(seconds: 2 * retryCount));
    }
  }

  WorkerSendResultHandler.handleError(
    ref: taskId,
    error: 'Unknown error when sending video message',
  );
  return null;
}

/**
 * Handle compress and upload video message task.
 *
 * This function performs the complete video sending process:
 * 1. Compress video
 * 2. Upload video
 * 3. Upload thumbnail (if available)
 * 4. Send message with video
 *
 * It includes error handling and timeout management for each step.
 *
 * @param inputData Task input data
 * @param apiClient API client for server communication
 * @param fileStoreClient FileStore client for file operations
 * @param folderPath Folder path for file storage
 */
Future<void> _handleCompressAndUploadVideoMessage(
  Map<String, dynamic> inputData,
  IsolateApiClient apiClient,
  FilestoreClientV2 fileStoreClient,
  String folderPath,
) async {
  final stopwatch = Stopwatch()..start();
  final taskId = inputData['ref'];
  final input = WorkerCompressVideoInput.fromJson(inputData);
  final messageRef = input.messageRef ?? input.file.ref;

  // Overall timeout for the entire process (15 minutes)
  final totalTimeout = Duration(minutes: 15);
  final timeoutCompleter = Completer<void>();
  final processingCompleter = Completer<void>();

  // Create timer for overall timeout
  final timeoutTimer = Timer(totalTimeout, () {
    if (!timeoutCompleter.isCompleted) {
      timeoutCompleter.completeError(
        TimeoutException(
          'Total processing timeout after ${totalTimeout.inMinutes} minutes',
        ),
      );
    }
  });

  final file = File(input.file.path);

  if (!file.existsSync()) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadVideoMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] File does not exist at path: ${input.file.path}',
    );

    WorkerSendResultHandler.handleError(
      ref: taskId,
      error: FileNotFoundException(),
    );

    return;
  }

  try {
    final fileRef = input.file.fileRef ?? input.file.ref;

    // Only log basic information when starting video compression
    final fileSize = file.lengthSync();
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadVideoMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Starting video compression and upload for file: ${input.file.path} (${fileSize ~/ 1024} KB)',
    );

    // Step 1: Compress video using separate method
    final compressOutput = await _compressVideo(
      input: input,
      taskId: taskId,
      messageRef: messageRef,
      fileRef: fileRef,
    );

    // Check video compression result
    if (compressOutput == null) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Video compression failed after retries, aborting task',
      );

      // Save error result to ensure UI is updated correctly
      await _saveSendMessageResult(
        msgRef: messageRef,
        workspaceId: input.workspaceId,
        channelId: input.channelId,
        userId: input.userId,
        errorReason: "Video compression failed after retries",
        forceStore: true, // Ensure result is always saved
      );

      WorkerSendResultHandler.handleError(
        ref: taskId,
        error: 'compression video failed',
      );
      return;
    }

    // Step 2: Create input data for file upload
    final mediaMessageBodyEmbed = jsonEncode(
      WorkerSendMediaInput(
        mediaListEmbed: jsonEncode([
          WorkerMediaObject(
            attachmentType: input.attachmentType,
            fileMetadataEmbed: jsonEncode(
              WorkerFileMetadata(
                duration:
                    Duration(milliseconds: compressOutput.duration).inSeconds,
              ).toJson(),
            ),
          ),
        ]),
        workspaceId: input.workspaceId,
        channelId: input.channelId,
        userId: input.userId,
        ref: input.file.ref,
        taskName: TaskNameEnum.sendMessage.value,
        attachmentType: input.attachmentType,
        creationTime: input.creationTime,
      ).toJson(),
    );

    final uploadFileInput = WorkerUploadFileInput(
      ref: input.uploadKey,
      taskName: TaskNameEnum.uploadFile.value,
      filePath: compressOutput.videoPath,
      thumbnailPath: compressOutput.thumbnailPath,
      fileName: input.file.name,
      fileSize: input.file.size,
      fileRef: fileRef,
      uploadType: UploadFileTypeEnum.video.value,
      mediaMessageBodyEmbed: mediaMessageBodyEmbed,
      messageRef: messageRef,
      messageId: '',
    );

    // Step 3: Upload video using separate method
    final uploadOutput = await _uploadVideo(
      uploadFileInput: uploadFileInput,
      apiClient: apiClient,
      fileStoreClient: fileStoreClient,
      folderPath: folderPath,
      taskId: taskId,
      messageRef: messageRef,
    );

    // Check video upload result
    if (uploadOutput == null) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Video upload failed after retries, aborting task',
      );
      WorkerSendResultHandler.handleError(
        ref: taskId,
        error: 'Video upload failed after retries, aborting task',
      );
      return;
    }

    // Step 4: Upload thumbnail if available using separate method
    String thumbnailUrl = '';
    if (compressOutput.thumbnailPath.isNotEmpty &&
        File(compressOutput.thumbnailPath).existsSync()) {
      final thumbnailTaskId = "${messageRef}_${fileRef}_thumbnail";
      final thumbnailInput = WorkerUploadFileInput(
        ref: thumbnailTaskId,
        taskName: TaskNameEnum.uploadFile.value,
        filePath: uploadFileInput.filePath,
        fileName: uploadFileInput.fileName,
        fileSize: uploadFileInput.fileSize,
        fileRef: uploadFileInput.fileRef,
        thumbnailPath: compressOutput.thumbnailPath,
        uploadType: UploadFileTypeEnum.videoThumbnail.value,
        mediaMessageBodyEmbed: mediaMessageBodyEmbed,
        messageRef: messageRef,
        messageId: '',
      );

      final thumbnailOutput = await _uploadThumbnail(
        thumbnailInput: thumbnailInput,
        apiClient: apiClient,
        fileStoreClient: fileStoreClient,
        folderPath: folderPath,
        taskId: taskId,
        messageRef: messageRef,
      );

      if (thumbnailOutput != null && thumbnailOutput.url.isNotEmpty) {
        thumbnailUrl = thumbnailOutput.url;
      }
    }

    // Step 5: Send message using separate method
    final mediaObject = WorkerMediaObject(
      attachmentType: input.attachmentType,
      fileUrl: uploadOutput.url,
      thumbnailUrl: thumbnailUrl,
      fileRef: fileRef,
      fileMetadataEmbed: jsonEncode(
        WorkerFileMetadata(
          mimetype: uploadOutput.metadata?.mimetype,
          filename: uploadOutput.metadata?.filename,
          extension: uploadOutput.metadata?.extension,
          filesize: uploadOutput.metadata?.filesize,
          duration: Duration(milliseconds: compressOutput.duration).inSeconds,
        ).toJson(),
      ),
    );

    final sendMediaInput = WorkerSendMediaInput(
      mediaListEmbed: jsonEncode([mediaObject]),
      workspaceId: input.workspaceId,
      channelId: input.channelId,
      userId: input.userId,
      ref: messageRef,
      taskName: TaskNameEnum.sendMessage.value,
      attachmentType: input.attachmentType,
      creationTime: input.creationTime,
    );

    final sendOutput = await _sendVideoMessage(
      sendMediaInput: sendMediaInput,
      apiClient: apiClient,
      taskId: taskId,
      messageRef: messageRef,
    );

    if (sendOutput == null) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Failed to send video message after retries, aborting task',
      );
      WorkerSendResultHandler.handleError(
        ref: taskId,
        error: 'Failed to send video message after retries, aborting tasks',
      );
      return;
    }

    final totalElapsedMs = stopwatch.elapsedMilliseconds;
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadVideoMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Video message process completed in ${totalElapsedMs}ms, success: ${sendOutput.success}',
    );

    // Step 6: Save message sending result to ensure UI is updated correctly
    try {
      await _saveSendMessageResult(
        msgRef: messageRef,
        msgId: sendOutput.messageId,
        workspaceId: input.workspaceId,
        channelId: input.channelId,
        userId: input.userId,
        forceStore: true, // Ensure result is always saved
      );

      // Add log to confirm task completion
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Task completed successfully, returning from handler',
      );
    } catch (saveError) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Error saving send message result: $saveError',
      );
    }
    // Mark processing as completed
    if (!processingCompleter.isCompleted) {
      processingCompleter.complete();
    }
  } catch (e, stackTrace) {
    final messageRef = input.messageRef ?? input.file.ref ?? "unknown";

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadVideoMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Error processing video: $e',
    );
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadVideoMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Stack trace: $stackTrace',
    );

    // Mark processing as completed (with error)
    if (!processingCompleter.isCompleted) {
      processingCompleter.completeError(e, stackTrace);
    }

    // Determine which step caused the error
    String errorStep = "unknown";
    if (e.toString().contains("compress")) {
      errorStep = "video_compression";
    } else if (e.toString().contains("upload") ||
        e.toString().contains("network")) {
      errorStep = "video_upload";
    } else if (e.toString().contains("thumbnail")) {
      errorStep = "thumbnail_upload";
    } else if (e.toString().contains("message") ||
        e.toString().contains("send")) {
      errorStep = "send_message";
    }

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadVideoMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Error occurred during step: $errorStep',
    );

    // Log additional information about the error
    final totalElapsedMs = stopwatch.elapsedMilliseconds;
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadVideoMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Total time before error: ${totalElapsedMs}ms',
    );

    // Save error result to ensure UI is updated correctly
    try {
      await _saveSendMessageResult(
        msgRef: messageRef,
        workspaceId: input.workspaceId,
        channelId: input.channelId,
        userId: input.userId,
        errorReason: "Error during $errorStep: ${e.toString()}",
        forceStore: true, // Ensure result is always saved
      );

      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Saved error result to ensure UI update',
      );
    } catch (saveError) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressAndUploadVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Failed to save error result: $saveError',
      );
    }
  }

  // Cancel timer and wait for processing to complete or timeout
  try {
    await Future.any([timeoutCompleter.future, processingCompleter.future]);
  } catch (e) {
    final messageRef = input.messageRef ?? input.file.ref ?? "unknown";
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressAndUploadVideoMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Process terminated due to timeout or error: $e',
    );

    // Save error result to ensure UI is updated correctly
    try {
      await _saveSendMessageResult(
        msgRef: messageRef,
        workspaceId: input.workspaceId,
        channelId: input.channelId,
        userId: input.userId,
        errorReason: "Process timeout or error: ${e.toString()}",
        forceStore: true, // Ensure result is always saved
      );
    } catch (_) {}
  } finally {
    // Cancel timer if not expired
    timeoutTimer.cancel();
  }
}

/**
 * Handle compress video message task.
 *
 * This function compresses a video and registers a task to upload it.
 * It's the first step in the video sending process.
 *
 * @param inputData Task input data
 * @param taskRegistrationPort SendPort for task registration (optional)
 */
Future<void> _handleCompressVideoMessage(
  Map<String, dynamic> inputData,
  SendPort? taskRegistrationPort,
) async {
  final stopwatch = Stopwatch()..start();
  final taskId = UUIDUtils.random();

  final input = WorkerCompressVideoInput.fromJson(inputData);
  try {
    final messageRef = input.messageRef ?? input.file.ref;
    final fileRef = input.file.fileRef ?? input.file.ref;

    // Only log basic information when starting video compression
    final fileSize = File(input.file.path).lengthSync();
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressVideoMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Starting video compression for file: ${input.file.path} (${fileSize ~/ 1024} KB)',
    );

    // Check original file, only log when there's an issue
    if (!File(input.file.path).existsSync()) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] WARNING: Source file does not exist at path: ${input.file.path}',
      );
      return;
    }

    final compressOutput = await CompressVideoHandler().compressVideo(input);

    // Check if the file was compressed successfully
    if (compressOutput.videoPath.isEmpty) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Video compression failed: empty videoPath',
      );
      return;
    }

    // Only log brief information about video compression result
    final compressedVideoFile = File(compressOutput.videoPath);
    if (compressedVideoFile.existsSync()) {
      final compressedSize = compressedVideoFile.lengthSync();
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] Video compression successful: ${compressedSize ~/ 1024} KB',
      );
    } else {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleCompressVideoMessage',
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] WARNING: Compressed video file does not exist at path: ${compressOutput.videoPath}',
      );
    }

    // Check thumbnail, only log when there's an issue
    if (compressOutput.thumbnailPath.isNotEmpty) {
      final thumbnailFile = File(compressOutput.thumbnailPath);
      if (thumbnailFile.existsSync()) {
        // Thumbnail exists, no need to log
      } else {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleCompressVideoMessage',
          '[DEBUG][TaskID:$taskId][MessageRef:$messageRef] WARNING: Thumbnail file does not exist at path: ${compressOutput.thumbnailPath}',
        );
      }
    }

    //region Register for video upload task
    // Forward message data through upload tasks
    final mediaMessageBodyEmbed = jsonEncode(
      WorkerSendMediaInput(
        mediaListEmbed: jsonEncode([
          WorkerMediaObject(
            attachmentType: input.attachmentType,
            fileMetadataEmbed: jsonEncode(
              WorkerFileMetadata(
                duration:
                    Duration(milliseconds: compressOutput.duration).inSeconds,
              ).toJson(),
            ),
          ),
        ]),
        workspaceId: input.workspaceId,
        channelId: input.channelId,
        userId: input.userId,
        ref: input.file.ref,
        taskName: TaskNameEnum.sendMessage.value,
        attachmentType: input.attachmentType,
        creationTime: input.creationTime,
      ).toJson(),
    );

    final uploadTaskId = "${messageRef}_${fileRef}";

    // Reduce unnecessary log information
    final uploadFileInputData = WorkerUploadFileInput(
      ref: uploadTaskId,
      taskName: TaskNameEnum.uploadFile.value,
      filePath: compressOutput.videoPath,
      thumbnailPath: compressOutput.thumbnailPath,
      fileName: input.file.name,
      fileSize: input.file.size,
      fileRef: input.file.fileRef ?? input.file.ref,
      uploadType: UploadFileTypeEnum.video.value,
      mediaMessageBodyEmbed: mediaMessageBodyEmbed,
      messageRef: input.file.ref,
      messageId: '',
    ).toJson();

    // uploadTaskId is required for file upload task
    if (uploadTaskId.isEmpty) {
      throw Exception('uploadTaskId is required for uploadFile tasks');
    }

    await _registerTaskWithResilientIsolate(
      taskRegistrationPort: taskRegistrationPort,
      taskName: TaskNameEnum.uploadFile.value,
      taskId: uploadTaskId,
      inputData: uploadFileInputData,
      constraints: Constraints(networkType: NetworkType.connected),
      refTask: true,
    );

    // No need to log this information

    //endregion Register for video upload task
  } catch (e, stackTrace) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressVideoMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:${input.messageRef ?? "unknown"}] Error processing video: $e',
    );
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressVideoMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:${input.messageRef ?? "unknown"}] Stack trace: $stackTrace',
    );
    RILogger.printError('Error processing video', e, stackTrace);

    // Log additional information about the error
    final totalElapsedMs = stopwatch.elapsedMilliseconds;
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleCompressVideoMessage',
      '[DEBUG][TaskID:$taskId][MessageRef:${input.messageRef ?? "unknown"}] Total time before error: ${totalElapsedMs}ms',
    );
  }
}

/**
 * Handle upload file task.
 *
 * This function uploads a file and registers appropriate follow-up tasks
 * based on the file type (video, thumbnail, voice, file).
 *
 * @param inputData Task input data
 * @param apiClient API client for server communication
 * @param fileStoreClient FileStore client for file operations
 * @param folderPath Folder path for file storage
 * @param taskRegistrationPort SendPort for task registration (optional)
 * @return true if successful, false otherwise
 */
Future<bool> _handleUploadFile(
  Map<String, dynamic> inputData,
  IsolateApiClient apiClient,
  FilestoreClientV2 fileStoreClient,
  String folderPath,
  SendPort? taskRegistrationPort,
) async {
  // Get RetryManager from TaskHandlerRegistry
  final retryManager = TaskHandlerRegistry.retryManager!;
  final input = WorkerUploadFileInput.fromJson(inputData);
  final messageRef = input.messageRef;
  final fileRef = input.fileRef;
  final uploadType = UploadFileTypeEnum.getEnumByValue(input.uploadType);
  final stopwatch = Stopwatch()..start();

  // Log details about file upload task
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_handleUploadFile',
    '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Starting upload file task: type=${uploadType?.value}, path=${input.filePath}',
  );

  try {
    // Check file existence before uploading
    final filePath = input.filePath;
    final file = File(filePath);

    if (!file.existsSync()) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadFile',
        '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] File not found: $filePath',
      );

      // If this is a thumbnail upload task and file doesn't exist, still continue to register sendMessage task
      if (uploadType == UploadFileTypeEnum.videoThumbnail) {
        final mediaMessageBody = input.mediaMessageBody;
        final mediaObject = mediaMessageBody!.mediaList.first;

        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleUploadFile',
          '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Thumbnail file not found, skipping thumbnail upload and registering sendMessage task directly',
        );

        await _registerMediaMessageSendTask(
          input,
          mediaObject,
          taskRegistrationPort,
        );

        return true;
      }

      throw FileNotFoundException(message: 'File not exists: $filePath');
    }

    // Log information about file
    final fileSize = file.lengthSync();
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadFile',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] File exists, size: ${fileSize ~/ 1024} KB',
    );

    // Upload file
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadFile',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Starting file upload to server',
    );

    final uploadStartTime = DateTime.now();

    // Add log before calling uploadFile
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadFile',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Calling UploadFileHandler.uploadFile with type=${uploadType?.value}',
    );

    WorkerUploadFileOutput? uploadOutput;
    try {
      uploadOutput = await fileHandler.UploadFileHandler(
        apiClient: apiClient,
        fileStoreClient: fileStoreClient,
        folderPath: folderPath,
        retryManager: retryManager,
      ).uploadFile(input);

      final uploadDuration =
          DateTime.now().difference(uploadStartTime).inMilliseconds;

      // Add log after uploadFile completes successfully
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadFile',
        '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] UploadFileHandler.uploadFile completed successfully in ${uploadDuration}ms, url: ${uploadOutput?.url}',
      );
    } catch (e, stackTrace) {
      final errorDuration =
          DateTime.now().difference(uploadStartTime).inMilliseconds;
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadFile',
        '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Error in UploadFileHandler.uploadFile after ${errorDuration}ms: $e',
      );
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadFile',
        '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Stack trace: $stackTrace',
      );
      rethrow;
    }

    if (uploadOutput == null) {
      final totalDuration =
          DateTime.now().difference(uploadStartTime).inMilliseconds;
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleUploadFile',
        '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Upload failed: null output after ${totalDuration}ms',
      );
      //TODO: Add WorkerSendResultHandler.handleError call here
      // Should call WorkerSendResultHandler.handleError and save error result
      // before returning false to ensure UI is notified about upload failure
      return false;
    }

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadFile',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Upload completed successfully, url: ${uploadOutput.url}',
    );

    final mediaMessageBody = input.mediaMessageBody;
    final mediaObject = mediaMessageBody!.mediaList.first;

    switch (uploadType) {
      case UploadFileTypeEnum.video:
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleUploadFile',
          '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Video uploaded successfully, registering thumbnail upload task',
        );
        await _registerVideoThumbnailTask(
          input,
          uploadOutput,
          mediaObject,
          taskRegistrationPort,
        );
        break;

      case UploadFileTypeEnum.videoThumbnail:
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleUploadFile',
          '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Thumbnail uploaded successfully, registering send message task',
        );
        await _registerMediaMessageSendTask(
          input,
          mediaObject.copyWith(thumbnailUrl: uploadOutput.url),
          taskRegistrationPort,
        );
        break;

      case UploadFileTypeEnum.voice:
      case UploadFileTypeEnum.file:
        await _registerMediaMessageSendTask(
          input,
          mediaObject.copyWith(
            fileUrl: uploadOutput.url,
            fileRef: input.fileRef,
            fileMetadataEmbed: jsonEncode(
              WorkerFileMetadata(
                mimetype: uploadOutput.metadata?.mimetype,
                filename: uploadOutput.metadata?.filename,
                extension: uploadOutput.metadata?.extension,
                filesize: uploadOutput.metadata?.filesize,
              ).toJson(),
            ),
          ),
          taskRegistrationPort,
        );
        break;
      default:
    }

    final totalDuration = stopwatch.elapsedMilliseconds;
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadFile',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Upload task completed successfully in ${totalDuration}ms',
    );
    return true;
  } catch (e, stackTrace) {
    final errorDuration = stopwatch.elapsedMilliseconds;
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadFile',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Error uploading file after ${errorDuration}ms: $e',
    );
    RILogger.printError('Error uploading file', e, stackTrace);

    // If this is a thumbnail upload task and there's an error, still continue to register sendMessage task
    if (uploadType == UploadFileTypeEnum.videoThumbnail) {
      try {
        final mediaMessageBody = input.mediaMessageBody;
        final mediaObject = mediaMessageBody!.mediaList.first;

        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleUploadFile',
          '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Error uploading thumbnail, registering sendMessage task directly',
        );

        await _registerMediaMessageSendTask(
          input,
          mediaObject,
          taskRegistrationPort,
        );

        return true;
      } catch (innerError) {
        RILogger.printClassMethodDebug(
          'WorkerIsolate',
          '_handleUploadFile',
          '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Error registering sendMessage task after thumbnail upload failed: $innerError',
        );
      }
    }

    // Log detailed error information
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleUploadFile',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Upload task failed: $e\n$stackTrace',
    );

    return false;
  }
}

/**
 * Register a task to send a media message.
 *
 * This function registers a task to send a message with media attachments
 * (file, voice, etc.). It's called after successful file upload.
 *
 * @param input Upload file input data
 * @param updatedMediaObject Media object with updated information
 * @param taskRegistrationPort SendPort for task registration (optional)
 */
Future<void> _registerMediaMessageSendTask(
  WorkerUploadFileInput input,
  WorkerMediaObject updatedMediaObject,
  SendPort? taskRegistrationPort,
) async {
  // Use messageRef as uniqueName for sendMessage task
  final messageRef = input.messageRef;
  final fileRef = input.fileRef;
  final stopwatch = Stopwatch()..start();

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_registerMediaMessageSendTask',
    '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Registering sendMessage task',
  );

  final messageData = input.mediaMessageBody!
      .copyWith(
        mediaListEmbed: jsonEncode([updatedMediaObject]),
      )
      .toJson();

  // messageRef is required for message sending task
  if (messageRef.isEmpty) {
    throw Exception('messageRef is required for sendMessage tasks');
  }

  await _registerTaskWithResilientIsolate(
    taskRegistrationPort: taskRegistrationPort,
    taskName: TaskNameEnum.sendMessage.value,
    taskId: messageRef,
    inputData: messageData,
  );

  final duration = stopwatch.elapsedMilliseconds;
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_registerMediaMessageSendTask',
    '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] sendMessage task registered in ${duration}ms',
  );
}

/**
 * Register a task for uploading a video thumbnail.
 *
 * This function registers a task to upload a video thumbnail and then send a message.
 * If thumbnail upload fails, it still continues with sending the video message.
 *
 * @param input Upload file input data
 * @param uploadOutput Result of video upload
 * @param mediaObject Media object with video information
 * @param taskRegistrationPort SendPort for task registration (optional)
 */
Future<void> _registerVideoThumbnailTask(
  WorkerUploadFileInput input,
  WorkerUploadFileOutput uploadOutput,
  WorkerMediaObject mediaObject,
  SendPort? taskRegistrationPort,
) async {
  final messageRef = input.messageRef;
  final fileRef = input.fileRef;
  final stopwatch = Stopwatch()..start();

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_registerVideoThumbnailTask',
    '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Preparing to register thumbnail upload task',
  );

  try {
    // Check thumbnail file existence before registering task
    if (input.thumbnailPath == null ||
        input.thumbnailPath!.isEmpty ||
        !File(input.thumbnailPath!).existsSync()) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_registerVideoThumbnailTask',
        '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Thumbnail file not found or empty path: ${input.thumbnailPath}. Skipping thumbnail upload.',
      );

      // Still continue to register sendMessage task with mediaObject that already has fileUrl
      final updatedMediaObject = mediaObject.copyWith(
        fileUrl: uploadOutput.url,
        fileRef: input.fileRef,
        fileMetadataEmbed: jsonEncode(
          WorkerFileMetadata(
            mimetype: uploadOutput.metadata?.mimetype,
            filename: uploadOutput.metadata?.filename,
            extension: uploadOutput.metadata?.extension,
            filesize: uploadOutput.metadata?.filesize,
            duration: mediaObject.fileMetadata?.duration ?? 0,
          ).toJson(),
        ),
      );

      await _registerMediaMessageSendTask(
        input,
        updatedMediaObject,
        taskRegistrationPort,
      );

      return;
    }

    final updatedMediaObject = mediaObject.copyWith(
      fileUrl: uploadOutput.url,
      fileRef: input.fileRef,
      fileMetadataEmbed: jsonEncode(
        WorkerFileMetadata(
          mimetype: uploadOutput.metadata?.mimetype,
          filename: uploadOutput.metadata?.filename,
          extension: uploadOutput.metadata?.extension,
          filesize: uploadOutput.metadata?.filesize,
          duration: mediaObject.fileMetadata?.duration ?? 0,
        ).toJson(),
      ),
    );

    final mediaMessageBodyEmbed = jsonEncode(
      input.mediaMessageBody!.copyWith(
        mediaListEmbed: jsonEncode([updatedMediaObject]),
      ),
    );

    // Create unique taskId for video thumbnail upload task based on messageRef and fileRef
    final taskId = "${input.messageRef}_${input.fileRef}_thumbnail";

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerVideoThumbnailTask',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Registering thumbnail upload task with ID: $taskId',
    );

    final uploadFileInputData = WorkerUploadFileInput(
      ref: taskId,
      taskName: TaskNameEnum.uploadFile.value,
      filePath: input.filePath,
      fileName: input.fileName,
      fileSize: input.fileSize,
      fileRef: input.fileRef,
      thumbnailPath: input.thumbnailPath,
      uploadType: UploadFileTypeEnum.videoThumbnail.value,
      mediaMessageBodyEmbed: mediaMessageBodyEmbed,
      messageRef: input.messageRef,
      messageId: input.messageId,
    ).toJson();

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerVideoThumbnailTask',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Calling _registerTaskWithResilientIsolate for thumbnail upload',
    );

    // taskId is required for thumbnail upload task
    if (taskId.isEmpty) {
      throw Exception('taskId is required for video thumbnail upload tasks');
    }

    await _registerTaskWithResilientIsolate(
      taskRegistrationPort: taskRegistrationPort,
      taskName: TaskNameEnum.uploadFile.value,
      taskId: taskId,
      inputData: uploadFileInputData,
      constraints: Constraints(networkType: NetworkType.connected),
      refTask: true,
      timeout: GlobalConfig.sendTimeoutDuration.inMilliseconds,
    );

    final duration = stopwatch.elapsedMilliseconds;
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerVideoThumbnailTask',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Thumbnail upload task registered in ${duration}ms',
    );
  } catch (e, stackTrace) {
    final errorDuration = stopwatch.elapsedMilliseconds;
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerVideoThumbnailTask',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Error registering video thumbnail task after ${errorDuration}ms: $e',
    );
    RILogger.printError(
      'Error registering video thumbnail task',
      e,
      stackTrace,
    );

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerVideoThumbnailTask',
      '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Error details: $e\n$stackTrace',
    );

    // If there's an error when registering thumbnail task, still continue to register sendMessage task
    try {
      final updatedMediaObject = mediaObject.copyWith(
        fileUrl: uploadOutput.url,
        fileRef: input.fileRef,
        fileMetadataEmbed: jsonEncode(
          WorkerFileMetadata(
            mimetype: uploadOutput.metadata?.mimetype,
            filename: uploadOutput.metadata?.filename,
            extension: uploadOutput.metadata?.extension,
            filesize: uploadOutput.metadata?.filesize,
            duration: mediaObject.fileMetadata?.duration ?? 0,
          ).toJson(),
        ),
      );

      await _registerMediaMessageSendTask(
        input,
        updatedMediaObject,
        taskRegistrationPort,
      );
    } catch (innerError) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_registerVideoThumbnailTask',
        '[DEBUG][MessageRef:$messageRef][FileRef:$fileRef] Error registering sendMessage task after thumbnail upload failed: $innerError',
      );
    }
  }
}

/**
 * Handle send message task.
 *
 * This function routes the message to the appropriate handler based on attachment type:
 * - Text messages
 * - Sticker messages
 * - Poke messages
 * - Location messages
 * - Media messages (video, photo, file, etc.)
 *
 * @param inputData Task input data
 * @param taskRegistrationPort SendPort for task registration (optional)
 * @return WorkerSendMessageOutput containing the result
 */
Future<WorkerSendMessageOutput> _handleSendMessage(
  Map<String, dynamic> inputData,
  SendPort? taskRegistrationPort,
) async {
  final stopwatch = Stopwatch()..start();
  final messageRef = inputData['ref'] as String?;

  // messageRef is required for message sending task
  if (messageRef == null || messageRef.isEmpty) {
    throw Exception('messageRef is required for sendMessage tasks');
  }

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_handleSendMessage',
    '[DEBUG][MessageRef:$messageRef] Starting to handle send message task',
  );

  IsolateApiClient apiClient = TaskHandlerRegistry.apiClient!;
  RetryManager retryManager = TaskHandlerRegistry.retryManager!;
  final baseInput = WorkerSendMessageInputBase.fromJson(inputData);

  switch (AttachmentType.getEnumByValue(baseInput.attachmentType)) {
    case AttachmentType.UNSPECIFIED:
      return await TextMessageHandler(
        apiClient: apiClient,
        retryManager: retryManager,
      ).handleTextMessage(inputData);

    case AttachmentType.STICKER:
      if (inputData["isPoked"] == true) {
        return await PokeMessageHandler(
          apiClient: apiClient,
          retryManager: retryManager,
        ).handlePokeMessage(inputData);
      }
      return await StickerMessageHandler(
        apiClient: apiClient,
        retryManager: retryManager,
      ).handleStickerMessage(inputData);
    case AttachmentType.LOCATION:
      return await LocationMessageHandler(
        apiClient: apiClient,
        retryManager: retryManager,
      ).handleLocationMessage(inputData);
    case AttachmentType.VIDEO: // Video
      final ref = inputData['ref'];

      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleSendMessage',
        '[DEBUG][MessageRef:$ref] Handling video message',
      );
      final messageInput = WorkerSendMediaInput.fromJson(inputData);
      return await MediaMessageHandler(
        apiClient: apiClient,
        retryManager: retryManager,
      ).handleMediaMessage(messageInput: messageInput, ref: ref);
    case AttachmentType.VIDEO_MESSAGE: // ZiiShort
      final ref = inputData['ref'];
      final messageInput = WorkerSendMediaInput.fromJson(inputData);
      return await MediaMessageHandler(
        apiClient: apiClient,
        retryManager: retryManager,
      ).handleMediaMessage(messageInput: messageInput, ref: ref);
    case AttachmentType.VOICE_MESSAGE: // ZiiVoice
      final ref = inputData['ref'];
      final messageInput = WorkerSendMediaInput.fromJson(inputData);
      return await MediaMessageHandler(
        apiClient: apiClient,
        retryManager: retryManager,
      ).handleMediaMessage(messageInput: messageInput, ref: ref);
    case AttachmentType.FILE:
      final ref = inputData['ref'];
      final messageInput = WorkerSendMediaInput.fromJson(inputData);
      return await MediaMessageHandler(
        apiClient: apiClient,
        retryManager: retryManager,
      ).handleMediaMessage(messageInput: messageInput, ref: ref);
    case AttachmentType.PHOTO:
      return await _handleSendPhoto(apiClient, inputData, taskRegistrationPort);
    case AttachmentType.AUDIO:
    case AttachmentType.LINKS:
    case AttachmentType.MEDIA:
    case AttachmentType.MENTION:
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleSendMessage',
        '[DEBUG][MessageRef:$messageRef] Unsupported attachment type: ${baseInput.attachmentType}',
      );
      return WorkerSendMessageOutput(success: true);
  }
}

/**
 * Handle send photo message task.
 *
 * This function processes photo messages, which may involve multiple images.
 * It handles both initial sending and updating with additional images.
 *
 * @param apiClient API client for server communication
 * @param inputData Task input data
 * @param taskRegistrationPort SendPort for task registration (optional)
 * @return WorkerSendMessageOutput containing the result
 */
Future<WorkerSendMessageOutput> _handleSendPhoto(
  IsolateApiClient apiClient,
  Map<String, dynamic> inputData,
  SendPort? taskRegistrationPort,
) async {
  final ref = inputData['ref'];
  if (ref == null || (ref is String && ref.isEmpty)) {
    final errorMessage = 'Missing or empty ref in inputData for sendPhoto task';
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleSendPhoto',
      errorMessage,
    );
    throw Exception(errorMessage);
  }

  final messageInput = WorkerSendMediaInput.fromJson(inputData);
  final retryManager = TaskHandlerRegistry.retryManager!;
  final result = messageInput.currentBash == 0
      ? await MediaMessageHandler(
          apiClient: apiClient,
          retryManager: retryManager,
        ).handleMediaMessage(messageInput: messageInput, ref: ref)
      : await UpdateMediaAttachmentsHandler(
          apiClient: apiClient,
          retryManager: retryManager,
        ).handleMediaMessage(
          messageInput: messageInput.copyWith(isRefTask: true),
          ref: ref,
        );

  // In case of error, cannot continue sending
  if (result.sendMsgErrorReason == SendMsgErrorEnum.blockedUser.value ||
      result.sendMsgErrorReason == SendMsgErrorEnum.reachedMessageLimit.value) {
    return result;
  }

  // Update file upload status
  final filesUpload =
      List<WorkerUploadFile>.from(messageInput.uploadFiles ?? []);

  // In case there is an error in some files, you can continue sending.
  if (result.sendMsgErrorReason != null) {
    for (final file in filesUpload) {
      if (messageInput.mediaList.map((e) => e.fileRef).contains(file.fileRef)) {
        file.status = UploadFileStatus.failed;
      }
    }
  } else {
    for (final file in filesUpload) {
      if (messageInput.mediaList.map((e) => e.fileRef).contains(file.fileRef)) {
        file.status = UploadFileStatus.done;
      }
    }
  }

  await _saveSendMessageResult(
    msgRef: messageInput.ref,
    msgId: result.messageId,
    workspaceId: result.workspaceId,
    channelId: result.channelId,
    userId: result.userId,
    mapAttachmentStatus: {
      for (final file in filesUpload)
        file.fileRef!: switch (file.status) {
          UploadFileStatus.pending => AttachmentStatusEnum.UPLOADING,
          UploadFileStatus.sending => AttachmentStatusEnum.UPLOADING,
          UploadFileStatus.done => AttachmentStatusEnum.SUCCESS,
          UploadFileStatus.failed => AttachmentStatusEnum.FAILURE,
        },
    },
  );

  // Register upload task for pending files
  if (filesUpload
          .indexWhere((file) => file.status == UploadFileStatus.pending) >=
      0) {
    if (result.messageId == null) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleSendPhoto',
        'Cannot register upload task: messageId is null',
      );
      return result;
    }
    final taskRef = UUIDUtils.random();
    final inputData = WorkerCompressAndUploadImagesInput(
      workspaceId: result.workspaceId,
      channelId: result.channelId,
      userId: messageInput.userId,
      messageId: result.messageId!,
      // Safe to use ! now since we checked above
      ref: taskRef,
      taskName: TaskNameEnum.compressAndUploadImages.value,
      attachmentType: AttachmentType.PHOTO.rawValue(),
      messageRef: messageInput.ref,
      uploadFilesEmbed: jsonEncode(filesUpload.toList()),
      uploadConcurrency: messageInput.uploadConcurrency,
      currentBash: messageInput.currentBash + 1,
      creationTime: DateTime.now(),
    ).toJson();

    // taskRef is required for image compression and upload task
    if (taskRef.isEmpty) {
      throw Exception('taskRef is required for compressAndUploadImages tasks');
    }

    await _registerTaskWithResilientIsolate(
      taskRegistrationPort: taskRegistrationPort,
      taskName: TaskNameEnum.compressAndUploadImages.value,
      taskId: taskRef,
      inputData: inputData,
      constraints: Constraints(networkType: NetworkType.connected),
      refTask: true,
    );
  }

  return result;
}

/**
 * Handle timeout exception for message sending.
 *
 * This function processes timeout exceptions, saves error state,
 * and notifies the main isolate. It handles network-related timeouts differently
 * to allow silent retries.
 *
 * @param taskName Task name
 * @param exception The timeout exception
 * @param inputData Task input data
 * @return true if handled successfully
 */
Future<bool> _handleTimeoutException(
  String taskName,
  SendMessageTimeoutException exception,
  Map<String, dynamic> inputData,
) async {
  final msgRef = exception.msgRef;
  final sendPort = IsolateNameServer.lookupPortByName(msgRef);

  // Log detailed information about the timeout
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_handleTimeoutException',
    'Handling timeout for task $taskName, msgRef: $msgRef',
  );

  if (sendPort == null) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleTimeoutException',
      'SendPort not found for msgRef: $msgRef, saving error state',
    );
    // Even if sendPort is null, we should still save the error state
    await _saveSendMessageResult(
      msgRef: msgRef,
      errorReason: SendMsgErrorEnum.timeout.value,
    );
    return true;
  }

  // Check if this is a network-related timeout
  final isNetworkTimeout =
      exception.message?.toLowerCase().contains('network') ?? false;

  // Only send failure response if it's NOT a network-related timeout
  if (!isNetworkTimeout) {
    // Send failure response to main isolate
    sendPort.send(
      SendMessageFailureResponse(
        ref: msgRef,
        errorMessage: exception.message ?? 'Send message timeout',
      ).toJson(),
    );
  } else {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleTimeoutException',
      'NETWORK DEBUG: Suppressing error notification for network-related timeout',
    );
  }

  // Save error task for all message types to ensure consistent error handling
  await _saveSendMessageResult(
    msgRef: msgRef,
    errorReason: SendMsgErrorEnum.timeout.value,
  );

  return true;
}

/**
 * Handle file not found exception.
 *
 * This function processes file not found exceptions for various task types
 * including video compression, video upload, and file upload tasks.
 *
 * @param exception The file not found exception
 * @param taskName Task name
 * @param inputData Task input data
 * @return true if handled successfully, false to trigger retry
 */
bool _handleFileNotFoundException(
  FileNotFoundException exception,
  String taskName,
  Map<String, dynamic> inputData,
) {
  if (taskName == TaskNameEnum.compressVideoMessage.value ||
      taskName == TaskNameEnum.compressAndUploadVideoMessage.value ||
      taskName == TaskNameEnum.uploadFile.value) {
    final msgRef = inputData['messageRef'];
    final sendPort = IsolateNameServer.lookupPortByName(msgRef);

    // Log detailed information about the file not found exception
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_handleFileNotFoundException',
      'File not found for task $taskName, msgRef: $msgRef, path: ${exception.message}',
    );

    if (sendPort == null) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleFileNotFoundException',
        'SendPort not found for msgRef: $msgRef, saving error state',
      );
      // Even if sendPort is null, we should still save the error state
      _saveSendMessageResult(
        msgRef: msgRef,
        errorReason: SendMsgErrorEnum.unknown.value,
      );
      return false; // retry on app restart
    }

    // Check if this is a network-related file not found error
    final isNetworkError =
        exception.message.toLowerCase().contains('network') ||
            exception.message.toLowerCase().contains('connection') ||
            exception.message.toLowerCase().contains('internet');

    // Only send failure response if it's NOT a network-related error
    if (!isNetworkError) {
      // Send failure response to main isolate
      sendPort.send(
        SendMessageFailureResponse(
          ref: msgRef,
          errorMessage: exception.message,
        ).toJson(),
      );
    } else {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_handleFileNotFoundException',
        'NETWORK DEBUG: Suppressing error notification for network-related file not found error',
      );
    }

    // Save error state
    _saveSendMessageResult(
      msgRef: msgRef,
      errorReason: SendMsgErrorEnum.unknown.value,
    );
  }
  return true;
}

/**
 * Update task status after execution.
 *
 * This function updates the task status in SharedPreferencesStore and
 * saves message results for message-related tasks.
 *
 * @param success Whether the task was successful
 * @param isSendMsg Whether this is a message sending task
 * @param taskId Unique identifier for the task
 * @param sendMessageOutput Output from message sending (optional)
 */
Future<void> _updateTask(
  bool success,
  bool isSendMsg,
  String taskId,
  WorkerSendMessageOutput? sendMessageOutput,
) async {
  try {
    // Add more detailed logs to track hung tasks
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_updateTask',
      '[DEBUG][TaskID:$taskId] Starting to update task status, success=$success, isSendMsg=$isSendMsg',
    );

    // Check if task exists in storage
    final taskExists = await SharedPreferencesStore.taskExists(taskId);
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_updateTask',
      '[DEBUG][TaskID:$taskId] Task exists in storage: $taskExists',
    );

    if (!success) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_updateTask',
        'Task $taskId failed, updating status to failed',
      );
      final updateResult = await SharedPreferencesStore.updateTaskStatus(
        taskId,
        TaskStatus.failed,
      );
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_updateTask',
        'Task $taskId status update result: $updateResult',
      );
      return;
    }

    // Task completed successfully, delete it
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_updateTask',
      'Task $taskId completed successfully, deleting task',
    );
    final deleteResult = await SharedPreferencesStore.deleteTask(taskId);
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_updateTask',
      'Task $taskId deletion result: $deleteResult',
    );

    if (!isSendMsg) return;

    // Check if this is a network-related error
    final isNetworkError = sendMessageOutput?.sendMsgErrorReason ==
            SendMsgErrorEnum.network.value ||
        (sendMessageOutput?.sendMsgErrorReason
                ?.toLowerCase()
                .contains('network') ??
            false);

    // Save message result for send message tasks
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_updateTask',
      'Saving message result for task $taskId, messageId: ${sendMessageOutput?.messageId}',
    );

    await _saveSendMessageResult(
      msgRef: taskId,
      msgId: sendMessageOutput?.messageId,
      channelId: sendMessageOutput?.channelId,
      workspaceId: sendMessageOutput?.workspaceId,
      userId: sendMessageOutput?.userId,
      // Don't set error reason for network errors to keep message in PENDING state
      errorReason:
          isNetworkError ? null : sendMessageOutput?.sendMsgErrorReason,
    );

    if (isNetworkError) {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_updateTask',
        'NETWORK DEBUG: Suppressing error reason for network error to keep message in PENDING state',
      );
    }

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_updateTask',
      'Successfully saved message result for task $taskId',
    );
  } catch (e, stackTrace) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_updateTask',
      'Error updating task $taskId: $e\n$stackTrace',
    );
  }
}

/**
 * Persist result for tasks that didn't receive acknowledgment.
 *
 * This function is called when a task result wasn't acknowledged by the main isolate.
 * It ensures the result is saved for recovery.
 *
 * @param taskId Unique identifier for the task
 * @param success Whether the task was successful
 * @param ackPort SendPort to send persistence notification
 */
@pragma('vm:entry-point')
Future<void> persistUnacknowledgedResult(
  String taskId,
  bool success,
  SendPort ackPort,
) async {
  try {
    // In the isolate, we can't directly use SharedPreferences
    // So we send a message back to the main isolate to handle persistence
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      'persistUnacknowledgedResult',
      'No acknowledgment received for task: $taskId, persisting result',
    );

    // For message tasks, also save the result directly
    if (taskId.startsWith('MSG_') ||
        taskId.contains('message') ||
        taskId.contains('Message')) {
      // Force store the result since we didn't receive an ack
      await _saveSendMessageResult(
        msgRef: taskId,
        forceStore: true,
      );

      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        'persistUnacknowledgedResult',
        'Forced storage of message result for recovery: $taskId',
      );
    }

    // Send a special message to the ack port to persist the result
    ackPort.send('persist:$taskId:$success');
  } catch (e) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      'persistUnacknowledgedResult',
      'Error persisting unacknowledged result: $e',
    );
  }
}

/**
 * Adjust timeout based on task type.
 *
 * This function returns an appropriate timeout value based on the task type.
 * Video-related tasks get longer timeouts.
 *
 * @param taskName Task name
 * @param defaultTimeout Default timeout in milliseconds
 * @return Adjusted timeout in milliseconds
 */
int _getTaskTimeout(String taskName, int defaultTimeout) {
  // Use GlobalConfig.sendTimeoutDuration for all tasks
  final globalTimeout = GlobalConfig.sendTimeoutDuration.inMilliseconds;

  // Increase timeout for video-related tasks
  if (taskName == TaskNameEnum.compressVideoMessage.value ||
      taskName == TaskNameEnum.compressAndUploadVideoMessage.value) {
    // 10 minutes for video compression task or GlobalConfig.sendTimeoutDuration, whichever is longer
    return math.max(globalTimeout, 10 * 60 * 1000);
  } else if (taskName == TaskNameEnum.uploadFile.value) {
    // 5 minutes for file upload task or GlobalConfig.sendTimeoutDuration, whichever is longer
    return math.max(globalTimeout, 5 * 60 * 1000);
  }

  // Return GlobalConfig.sendTimeoutDuration for other tasks
  return globalTimeout;
}

/**
 * Register task through IsolateTaskService.
 *
 * This function sends task information to IsolateTaskService through taskRegistrationPort.
 * All tasks must be registered through the main isolate to ensure proper task recovery.
 *
 * @param taskId Unique identifier for the task
 * @param taskName Task name
 * @param taskRegistrationPort SendPort for task registration
 * @param inputData Task input data
 * @param various other parameters for task configuration
 */
@pragma('vm:entry-point')
Future<void> _registerTaskWithResilientIsolate({
  required String taskId,
  required String taskName,
  String? tag,
  required SendPort? taskRegistrationPort,
  required Map<String, dynamic> inputData,
  ExistingWorkPolicy? existingWorkPolicy,
  Duration initialDelay = Duration.zero,
  Constraints? constraints,
  BackoffPolicy? backoffPolicy,
  Duration backoffPolicyDelay = Duration.zero,
  OutOfQuotaPolicy? outOfQuotaPolicy,
  bool refTask = false,
  int priority = 1, // Medium priority by default
  int timeout = 15000, // Default timeout: 15 seconds
}) async {
  timeout = _getTaskTimeout(taskName, timeout);

  // Log for all tasks to help with debugging
  final messageRef = inputData['messageRef'] as String?;
  final fileRef = inputData['fileRef'] as String?;

  // If taskRegistrationPort is null, we're running in WorkManager and need to register directly
  if (taskRegistrationPort == null) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerTaskWithResilientIsolate',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef][FileRef:$fileRef] taskRegistrationPort is null, registering directly with Workmanager',
    );

    return await _registerDirectlyWithWorkmanager(
      taskId: taskId,
      taskName: taskName,
      inputData: inputData,
      priority: priority,
      refTask: refTask,
      networkRequired: constraints?.networkType == NetworkType.connected,
    );
  }

  try {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerTaskWithResilientIsolate',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef][FileRef:$fileRef] Registering task via taskRegistrationPort: $taskName, timeout: ${timeout}ms',
    );

    final message = {
      'registerTask': {
        'taskName': taskName,
        'inputData': inputData,
        'taskId': taskId,
        'tag': tag,
        'existingWorkPolicy': existingWorkPolicy?.toString(),
        'initialDelay': initialDelay.inMilliseconds,
        'constraints': constraints != null
            ? {
                'networkType': constraints.networkType.index,
                'requiresBatteryNotLow': constraints.requiresBatteryNotLow,
                'requiresCharging': constraints.requiresCharging,
                'requiresDeviceIdle': constraints.requiresDeviceIdle,
                'requiresStorageNotLow': constraints.requiresStorageNotLow,
              }
            : null,
        'backoffPolicy': backoffPolicy?.toString(),
        'backoffPolicyDelay': backoffPolicyDelay.inMilliseconds,
        'outOfQuotaPolicy': outOfQuotaPolicy?.toString(),
        'refTask': refTask,
        'priority': priority,
      },
    };

    // Send the task registration message to the main isolate
    taskRegistrationPort.send(message);

    // Add a small delay to ensure the message is processed
    await Future.delayed(Duration(milliseconds: 50));
  } catch (e, stackTrace) {
    final errorMessage =
        '[DEBUG][TaskID:$taskId][MessageRef:$messageRef][FileRef:$fileRef] Error registering task: $taskName, error: $e';
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerTaskWithResilientIsolate',
      errorMessage,
    );
    RILogger.printError('Error registering task', e, stackTrace);

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerTaskWithResilientIsolate',
      '[DEBUG][TaskID:$taskId][MessageRef:$messageRef][FileRef:$fileRef] Error details: $e\n$stackTrace',
    );

    // Rethrow the exception to be handled by the caller
    throw Exception(errorMessage);
  }
}

/**
 * Register task directly with Workmanager when running in callbackDispatcher.
 *
 * This method is called when taskRegistrationPort is null.
 *
 * @param taskId Unique identifier for the task
 * @param taskName Task name
 * @param inputData Task input data
 * @param priority Task priority
 * @param refTask Whether this is a reference task
 * @param networkRequired Whether network is required
 */
@pragma('vm:entry-point')
Future<void> _registerDirectlyWithWorkmanager({
  required String taskId,
  required String taskName,
  required Map<String, dynamic> inputData,
  int priority = 1,
  bool refTask = false,
  bool networkRequired = true,
}) async {
  try {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerDirectlyWithWorkmanager',
      '[DEBUG][TaskID:$taskId] Registering task directly with Workmanager: $taskName',
    );

    // There's no way to check if Workmanager has been initialized
    // So we'll call initialize and catch the error if it's already initialized
    try {
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_registerDirectlyWithWorkmanager',
        '[DEBUG][TaskID:$taskId] Initializing Workmanager',
      );

      await Workmanager().initialize(
        callbackDispatcher,
        isInDebugMode: false,
      );
    } catch (e) {
      // Ignore error if Workmanager is already initialized
      RILogger.printClassMethodDebug(
        'WorkerIsolate',
        '_registerDirectlyWithWorkmanager',
        '[DEBUG][TaskID:$taskId] Workmanager may already be initialized: $e',
      );
    }

    // Add taskName and taskId fields to inputData for use in callbackDispatcher
    final workerInputData = Map<String, dynamic>.from(inputData);
    workerInputData['taskName'] = taskName;
    workerInputData['taskId'] = taskId;
    workerInputData['priority'] = priority;
    workerInputData['isReferenceTask'] = refTask;
    workerInputData['networkRequired'] = networkRequired;

    // Update creationTime to current time to avoid timeout
    workerInputData['creationTime'] = DateTime.now().toIso8601String();

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerDirectlyWithWorkmanager',
      '[DEBUG][TaskID:$taskId] Updated creationTime to current time',
    );

    // Save task to SharedPreferences for recovery after app restart
    final task = TaskModel(
      id: taskId,
      name: taskName,
      inputData: workerInputData,
      status: TaskStatus.pending,
      priority: TaskPriority.values[priority],
      isReferenceTask: refTask,
      networkRequired: networkRequired,
      maxRetries: 3,
      // Maximum retry attempts
      retryDelay: 5000,
      // Wait time between retry attempts (ms)
      timeout: GlobalConfig
          .sendTimeoutDuration.inMilliseconds, // Timeout duration (ms)
    );

    await SharedPreferencesStore.saveTask(task);

    // Register task with Workmanager
    await Workmanager().registerOneOffTask(
      taskId,
      taskName,
      inputData: workerInputData,
      existingWorkPolicy: ExistingWorkPolicy.replace,
      constraints: Constraints(
        networkType:
            networkRequired ? NetworkType.connected : NetworkType.not_required,
      ),
    );

    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerDirectlyWithWorkmanager',
      '[DEBUG][TaskID:$taskId] Task registered with Workmanager successfully',
    );
  } catch (e, stackTrace) {
    final errorMessage =
        '[DEBUG][TaskID:$taskId] Error registering task directly with Workmanager: $e';
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_registerDirectlyWithWorkmanager',
      errorMessage,
    );
    RILogger.printError(
      'Error registering task with Workmanager',
      e,
      stackTrace,
    );

    // Don't throw exceptions to avoid crashing the app
    // Instead, just log the error and continue
  }
}

// Helper function to send result notification
void _sendSuccessResponse(String ref, dynamic data) {
  try {
    final port = IsolateNameServer.lookupPortByName('send_message_$ref');
    if (port != null) {
      port.send({'type': 'Success', 'data': data});
    }
  } catch (e) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_sendSuccessResponse',
      'Error sending success response: $e',
    );
  }
}

// Function to process subsequent images after successfully sending the first image
Future<bool> _processNextBatchImages(
  WorkerCompressAndUploadImagesInput input,
  IsolateApiClient apiClient,
  FilestoreClientV2 fileStoreClient,
  String folderPath,
  RetryManager retryManager,
) async {
  final taskId = input.ref;
  final messageRef = input.messageRef;
  final messageId = input.messageId;

  // Get all files to process, not limited by uploadConcurrency
  final filesToProcess = input.files
      .where((file) => file.status == UploadFileStatus.pending)
      .toList();

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_processNextBatchImages',
    'Processing next batch images for messageRef: $messageRef, messageId: $messageId, files count: ${filesToProcess.length}',
  );

  if (filesToProcess.isEmpty) {
    return true;
  }

  // Compress and upload images
  final uploadResults = await CompressAndUploadImageHandler(
    apiClient: apiClient,
    fileStoreClient: fileStoreClient,
    folderPath: folderPath,
    retryManager: retryManager,
  ).processingMultiFiles(filesToProcess, input.creationTime, input.uploadKey);

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_processNextBatchImages',
    'Completed processing ${uploadResults.length} files',
  );

  // Create list of media objects from successfully uploaded images
  List<WorkerMediaObject> mediaList = [];
  List<AttachmentError> attachmentsError = [];

  for (int i = 0; i < filesToProcess.length; i++) {
    if (uploadResults[i].attachmentStatus == AttachmentStatusEnum.SUCCESS) {
      mediaList.add(
        WorkerMediaObject(
          attachmentType: input.attachmentType,
          fileUrl: uploadResults[i].uploadOutput!.url,
          fileRef: filesToProcess[i].fileRef,
          fileMetadataEmbed: jsonEncode(
            WorkerFileMetadata(
              mimetype: uploadResults[i].uploadOutput!.metadata?.mimetype,
              filename: uploadResults[i].uploadOutput!.metadata?.filename,
              extension: uploadResults[i].uploadOutput!.metadata?.extension,
              filesize: uploadResults[i].uploadOutput!.metadata?.filesize,
              dimensionsEmbed: jsonEncode(
                WorkerSize(
                  width: uploadResults[i].imageSize!.width.toInt(),
                  height: uploadResults[i].imageSize!.height.toInt(),
                ),
              ),
            ).toJson(),
          ),
        ),
      );
    } else {
      attachmentsError.add(uploadResults[i].error!);
    }
  }

  // If no images were uploaded successfully, return false
  if (mediaList.isEmpty) {
    RILogger.printClassMethodDebug(
      'WorkerIsolate',
      '_processNextBatchImages',
      'No images uploaded successfully',
    );
    return false;
  }

  // Create input for UpdateMediaAttachmentsHandler
  final mediaListJson = mediaList.map((media) => media.toJson()).toList();
  final mediaListEmbed = jsonEncode(mediaListJson);

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_processNextBatchImages',
    'Created mediaListEmbed with ${mediaListJson.length} items',
  );

  final workerSendMediaInput = WorkerSendMediaInput(
    mediaListEmbed: mediaListEmbed,
    workspaceId: input.workspaceId,
    channelId: input.channelId,
    userId: input.userId,
    ref: messageRef,
    taskName: TaskNameEnum.sendMessage.value,
    attachmentType: input.attachmentType,
    creationTime: input.creationTime,
    uploadConcurrency: input.uploadConcurrency,
    uploadFilesEmbed: input.uploadFilesEmbed,
    currentBash: input.currentBash,
    messageId: messageId,
    isRefTask: true,
  );

  // Call UpdateMediaAttachmentsHandler to update message
  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_processNextBatchImages',
    'Calling UpdateMediaAttachmentsHandler to update message with ${mediaList.length} images',
  );

  final result = await UpdateMediaAttachmentsHandler(
    apiClient: apiClient,
    retryManager: retryManager,
  ).handleMediaMessage(
    messageInput: workerSendMediaInput,
    ref: messageRef,
  );

  RILogger.printClassMethodDebug(
    'WorkerIsolate',
    '_processNextBatchImages',
    'Message updated with result: ${result.success}, messageId: ${result.messageId}',
  );

  // Save result to SharedPreferencesStore
  await SharedPreferencesStore.saveTaskResult(
    '$messageRef',
    jsonEncode(result.toJson()),
  );

  // Send event to notify result
  _sendSuccessResponse(messageRef, result);

  return result.success;
}
