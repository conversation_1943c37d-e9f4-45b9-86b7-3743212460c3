import 'dart:async';
import 'dart:io';

import 'package:apns_notification/apns_notification.dart';
import 'package:app_core/core.dart' as core;
import 'package:app_links/app_links.dart';
import 'package:auth/auth.dart' as auth;
import 'package:auto_route/auto_route.dart';
import 'package:call/call.dart' as call;
import 'package:call/call.dart';
import 'package:chat/chat.dart' as chat;
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:search/search.dart' as search;
import 'package:share_to/share_to.dart' as shareTo;
import 'package:shared/shared.dart';
import 'package:sticker/sticker.dart' as sticker;
import 'package:upload_manager/upload_manager.dart' as upload;
import 'package:user_manager/user_manager.dart' as user;

import '../../../domain/handler/copy_handler.dart';
import '../../../domain/handler/invitation_handler.dart';
import '../../../domain/handler/login_qr_handler.dart';
import '../../../domain/handler/me_info_handler.dart';
import '../../../domain/handler/pop_to_handler.dart';
import '../../../domain/handler/private_data_handler.dart';
import '../../../domain/handler/valid_token_handler.dart';
import '../../../navigation/routes/app_router.dart';
import '../../common/di/di.dart';
import '../../navigation/routes/app_router.gr.dart';
import '../../navigation/routes/app_tabs_router.dart';
import '../../ui/page/home/<USER>';
import 'api_error_dialog_handler.dart';
import 'improve_passkey_handler.dart';

@LazySingleton()
class AppInitializer {
  late InvitationHandler _invitationHandler;
  late PrivateDataHandler _privateDataHandler;
  StreamSubscription? _deepLinkSubscription;
  late LoginQRHandler _loginQRHandler = getIt<LoginQRHandler>();
  late StreamSubscription _loginQRSubscription;
  late ValidTokenHandler _validTokenHandler;
  late MeInfoHandler _meInfoHandler;
  late CopyHandler _copyHandler;
  late ApiErrorDialogHandler _apiErrorDialogHandler;

  final core.SendMessageListener _sendMessageListener =
      getIt<core.SendMessageListener>();
  final core.IsolateTaskService _isolateTaskService = core.IsolateTaskService();

  bool isInitialize = false;

  AppInitializer();

  Future<void> initialize() async {
    if (isInitialize) return;
    isInitialize = true;
    _runSyncTasks();

    _runAsyncTasks();

    _schedulePostFrameTasks();
  }

  void _runSyncTasks() {
    _setupDeepLinks();
    _listenEventValidToken();
    _setupInvitationHandler();
    _setupLoginQRHandler();
    _setupPopToHandler();
    _setupPrivateDataHandler();
    _setupMeInfoHandler();
    _setupCopyHandler();
    _setupUserInfoHandler();
    _listenEventShowBlockedUserDialog();
    _listenUsersPrivateData();
  }

  Future<void> _runAsyncTasks() async {
    _initWebSocketAndListeners();
    _initStickers();
    _setupCallHandler();
    _syncUserPrivateData();
    _addSuggestionShareTo();
    _checkMigratePasskeyStatus();
    _registerNotification();
    _syncUsers();
  }

  void _schedulePostFrameTasks() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _listenFriendRequest();
      _listenUnreadMessages();
    });
  }

  Future<String> _getFsCallAddressFromRemoteConfig() async {
    String fallbackAddress = EnvConfig.getFsAddressCall;

    try {
      final remoteConfig = FirebaseRemoteConfigService();

      for (int i = 0; i < 10; i++) {
        if (await remoteConfig.fetch()) {
          break;
        }
        await Future.delayed(Duration(seconds: 1));
      }

      final fsCallAddress = remoteConfig.getString('fs_call_address');
      GlobalConfig.callDmEnable = remoteConfig.getBool('call_dm_enable');

      // Return fallback if empty or null
      if (fsCallAddress.isEmpty) {
        return fallbackAddress;
      }

      return fsCallAddress;
    } catch (e) {
      return fallbackAddress;
    }
  }

  Future<Map<String, dynamic>> _getMeInfo() async {
    final meOutput =
        await GetIt.I.get<user.LoadMeUseCase>().execute(user.LoadMeInput());
    final currentUser = meOutput.user;
    if (currentUser == null) {
      return {};
    }
    final sipCredential = currentUser.sipCredentials;
    final sipAddress = currentUser.sipAddress;
    final userId = currentUser.userId;

    // Get fs_call_address from Firebase Remote Config with fallback
    final fsCallAddress = await _getFsCallAddressFromRemoteConfig();

    return {
      'sipCredential': sipCredential,
      'sipAddress': sipAddress,
      'userId': userId,
      'fsCallAddress': fsCallAddress,
    };
  }

  void _setupDeepLinks() {
    _deepLinkSubscription = AppLinks().uriLinkStream.listen((uri) {
      GetIt.instance
          .get<AppTabsRouter>()
          .setActiveIndex(HomePage.CHANNEL_LIST_INDEX);
      HomePage.CURRENT_INDEX = HomePage.CHANNEL_LIST_INDEX;
    });
  }

  BuildContext? getContext() => getIt<AppRouter>().navigatorKey.currentContext;

  void _listenEventValidToken() {
    final context = getContext();
    if (context == null) {
      Future.delayed(DurationUtils.ms100, () {
        _listenEventValidToken();
      });
      return;
    }
    _validTokenHandler = ValidTokenHandler();
    _validTokenHandler.register(context);
  }

  void _setupCallHandler() async {
    final initUserInfo = await _getMeInfo();
    CallHandler().init(
      userId: initUserInfo['userId'],
      sipCredential: initUserInfo['sipCredential'],
      sipAddress: initUserInfo['fsCallAddress'],
      fsCallAddress: initUserInfo['fsCallAddress'],
    );
  }

  void _setupInvitationHandler() {
    final context = getContext();
    if (context == null) {
      Future.delayed(DurationUtils.ms100, () {
        _setupInvitationHandler();
      });
      return;
    }
    _invitationHandler = InvitationHandler(context: context);
    _invitationHandler.setupGlobalInvitationClicked();
  }

  void _listenFriendRequest() {
    getIt<core.StreamFriendRequest>().listenFriendRequest();
  }

  void _listenUnreadMessages() {
    getIt<core.StreamUnreadMessages>().listenUnReadMessage();
  }

  void _setupLoginQRHandler() {
    final context = getContext();
    if (context == null) {
      Future.delayed(DurationUtils.ms100, () {
        _setupLoginQRHandler();
      });
      return;
    }
    _loginQRHandler.init();
    _loginQRSubscription =
        getIt<AppEventBus>().on<HandleLoginQREvent>().listen((event) {
      _loginQRHandler.handleQrLoginDetected(event.qrValue);
    });
  }

  void _setupPopToHandler() {
    getIt<PopToHandler>().setupPopToHandler();
  }

  void _setupPrivateDataHandler() {
    _privateDataHandler = PrivateDataHandler();
    _privateDataHandler.setupPrivateDataHandler();
  }

  void _setupMeInfoHandler() {
    _meInfoHandler = MeInfoHandler();
    _meInfoHandler.setupMeInfoHandler();
  }

  void _setupCopyHandler() {
    _copyHandler = CopyHandler();
    _copyHandler.setupCopyHandler();
  }

  void _setupUserInfoHandler() {
    getIt<core.UserInfoHandler>().setupUserInfoHandler();
  }

  Future<void> _initWebSocketAndListeners() async {
    getIt<core.InitWebsocketUseCase>().execute(core.InitWebsocketInput());
    getIt<core.ReceiveMessageListener>().observer();
    getIt<chat.ChannelEventsListener>().observer();
  }

  Future<void> _initStickers() async {
    getIt<sticker.StickerBloc>().add(sticker.GetListStickerCollectionsEvent());
  }

  Future<void> _syncUserPrivateData() async {
    getIt<core.SyncUserPrivateDataUseCase>()
        .execute(core.SyncUserPrivateDataUseCaseInput());
  }

  Future<void> _addSuggestionShareTo() async {
    getIt<shareTo.AddSuggestionUseCase>().execute(shareTo.AddSuggestionInput());
  }

  Future<void> _checkMigratePasskeyStatus() async {
    final context = getContext();
    if (context == null) {
      Future.delayed(DurationUtils.ms100, () {
        _checkMigratePasskeyStatus();
      });
      return;
    }
    final migratePasskeyHandler = auth.MigratePasskeyHandler(
      context: context,
      onHideBottomSheet: _onHideMigrationPasskeyBts,
    );
    await migratePasskeyHandler.passkeyMigrated();

    ///Todo hide improve passkey temp to release date 08/05/2025 https://discord.com/channels/720906494442471504/1245311539872010310/1369878077349560382
    if (GlobalConfig.enableImprovePassKey)
      getIt<ImprovePasskeyHandler>().listenCallImprovePasskey((callPasskey) {
        if (!callPasskey) return;
        Future.delayed(DurationUtils.ms3000).then((a) {
          migratePasskeyHandler.startMigrate();
        });
      });
  }

  void _onHideMigrationPasskeyBts() {
    getIt<AppRouter>().popUntilRouteWithName(HomeRoute.name);
  }

  Future<void> _registerNotification() async {
    if (core.Config.getInstance().notificationToken == null) {
      await Future.delayed(DurationUtils.ms3000);
      _registerNotification();
      return;
    }

    GetIt.instance<core.RegisterNotificationUseCase>()
        .execute(core.RegisterNotificationInput());
  }

  Future<void> _syncUsers() async {
    getIt<core.SyncUsersUseCase>().execute(core.SyncUsersInput());
  }

  void _listenEventShowBlockedUserDialog() {
    final context = getContext();
    if (context == null) {
      Future.delayed(DurationUtils.ms100, () {
        _listenEventShowBlockedUserDialog();
      });
      return;
    }
    _apiErrorDialogHandler = ApiErrorDialogHandler();
    _apiErrorDialogHandler.register(context);
  }

  void _listenUsersPrivateData() {
    getIt<core.StreamUsersPrivateData>().listenUserPrivateData();
  }

  //region Initialize application module after successful login
  void initModules(String userId, String token) async {
    final authData = AuthData(authToken: token, sessionKey: userId);
    core.Config.getInstance().authData = authData;
    chat.Config.getInstance().authData = authData;
    user.Config.getInstance().authData = authData;
    auth.Config.getInstance().authData = authData;
    search.Config.getInstance().authData = authData;
    sticker.Config.getInstance().authData = authData;
    shareTo.Config.getInstance().authData = authData;
    call.Config.getInstance().authData = authData;
    upload.Config.getInstance().authData = authData;

    Future.delayed(DurationUtils.ms3000, () async {
      final count = await core.PresenceHandler().sendPresenceEvent();

      if (Platform.isIOS && count != null) {
        ApnsNotification().setBadgeNotification(count);
      }
    });

    Log.e(name: 'authData', [authData.authToken, userId]);
    FirebaseCrashlytics.instance.setUserIdentifier(userId);

    unawaited(_setupWorker(authData));
    _sendMessageListener.isCurrentlyInChannelView =
        _handleCheckIsCurrentInChannelView;
  }

  bool _handleCheckIsCurrentInChannelView({
    required String workspaceId,
    required String channelId,
    String? userId,
  }) {
    final AppRouter _router = GetIt.I.get<AppRouter>();
    final routerLength = _router.stack.length;

    if (routerLength > 0) {
      final routePage = _router.stack.last;
      if (routePage.routeData.name != ChannelViewRoute.name) return false;
      final args = routePage.routeData.args as ChannelViewRouteArgs;
      if ((args.channelId == channelId && args.workspaceId == workspaceId) ||
          args.userId == userId) {
        return true;
      }
    }
    return false;
  }

  Future<void> _setupWorker(AuthData authData) async {
    await _isolateTaskService.encryptToken(authData.authToken);
    await _isolateTaskService.encryptMetadata(
      core.WorkerMetadata(
        apiHost: EnvConfig.getApiHost,
        fileStoreHost: EnvConfig.getFileStoreHost,
        activeSessionKey: authData.sessionKey,
        header: <String, String>{
          EnvConfig.getClientIdEnv: EnvConfig.getClientId,
        },
      ),
    );

    await _isolateTaskService.doubleCheckMessageStatus();

    unawaited(_setupIsolateTaskService(authData));
  }

  Future<void> _setupIsolateTaskService(AuthData authData) async {
    // Delay to wait for system resources to be unloaded during initialization before initializing (immediate initialization may cause initialization errors)
    await Future.delayed(DurationUtils.ms1000);
    try {
      // Initialize IsolateTaskService directly with low heartbeatInterval (5 seconds) for early problem detection
      // After successful initialization, heartbeatInterval will be increased to 60 seconds
      Log.d(
        name: 'AppInitializer',
        'Initializing IsolateTaskService with low heartbeatInterval (5000ms) for faster error detection',
      );

      // Initialize IsolateTaskService with static configuration from SendMessageConfig
      await _isolateTaskService.initialize();

      Log.d(
        name: 'AppInitializer',
        'IsolateTaskService initialized successfully',
      );
    } catch (e, stackTrace) {
      Log.e(
        name: 'AppInitializer',
        ['Error initializing IsolateTaskService', e, stackTrace],
      );
      // Don't throw exception to avoid affecting app startup process

      // Retry after 5 seconds
      await Future.delayed(Duration(seconds: 5));
      unawaited(_setupIsolateTaskService(authData));
    }
  }

  //endregion Initialize application module after successful login

  void dispose() {
    isInitialize = false;
    _invitationHandler.dispose();
    _privateDataHandler.dispose();
    _deepLinkSubscription?.cancel();
    _loginQRHandler.dispose();
    _loginQRSubscription.cancel();
    _validTokenHandler.cancel();
    _apiErrorDialogHandler.cancel();
    _meInfoHandler.dispose();
    _copyHandler.dispose();
    getIt<PopToHandler>().dispose();
    getIt<core.StreamUsersPrivateData>().close();
    getIt<core.StreamFriendRequest>().close();
    getIt<core.StreamUnreadMessages>().close();

    // Disconnect meeting room when user logout
    getIt<call.RoomBloc>().add(call.DisconnectEvent());

    _disposeIsolateTaskService();
  }

  Future<void> _disposeIsolateTaskService() async {
    try {
      await _isolateTaskService.dispose();
      Log.d(name: 'AppInitializer', 'IsolateTaskService disposed successfully');
    } catch (e) {
      Log.e(name: 'AppInitializer', ['Error disposing IsolateTaskService', e]);
    }
  }
}
