part of 'reactions_list_bloc.dart';

@freezed
sealed class ReactionsListState with _$ReactionsListState {
  const ReactionsListState._();
  factory ReactionsListState.initial() = ReactionsListStateInitial;

  factory ReactionsListState.loaded({
    String? nextPageKey,
    @Default([]) List<Member> members,
    @Default([]) List<ChatUser> users,
    @Default(false) bool hasNext,
  }) = ReactionsListStateLoaded;
}

extension ReactionsListStateExtension on ReactionsListState {
  bool isSameFactory(ReactionsListState other) {
    if (other is ReactionsListStateInitial &&
        this is ReactionsListStateInitial) {
      return true;
    }
    if (other is ReactionsListStateLoaded && this is ReactionsListStateLoaded) {
      return true;
    }
    return false;
  }
}

extension ReactionsListStateX on ReactionsListState {
  T maybeWhen<T>({
    T Function()? initial,
    T Function(
      String? nextPageKey,
      List<Member> members,
      List<ChatUser> users,
      bool hasNext,
    )? loaded,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is ReactionsListStateInitial && initial != null) return initial();
    if (state is ReactionsListStateLoaded && loaded != null) {
      return loaded(
        state.nextPageKey,
        state.members,
        state.users,
        state.hasNext,
      );
    }

    return orElse();
  }

  T when<T>({
    required T Function() initial,
    required T Function(
      String? nextPageKey,
      List<Member> members,
      List<ChatUser> users,
      bool hasNext,
    ) loaded,
  }) {
    final state = this;

    if (state is ReactionsListStateInitial) return initial();
    if (state is ReactionsListStateLoaded) {
      return loaded(
        state.nextPageKey,
        state.members,
        state.users,
        state.hasNext,
      );
    }

    throw StateError('Unhandled state: $state');
  }
}
