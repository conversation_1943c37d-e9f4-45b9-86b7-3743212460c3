import 'dart:async';

import 'package:app_core/core.dart';
import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../data/models/invitable_user.dart';
import '../../../../domain/usecase/invitation/fetch_invitable_users_use_case.dart';

part 'invitable_users_bloc.freezed.dart';
part 'invitable_users_event.dart';
part 'invitable_users_state.dart';

@injectable
class InvitableUsersBloc
    extends BaseBloc<InvitableUsersEvent, InviableUsersState> {
  InvitableUsersBloc(this._fetchInvitableUsersUseCase)
      : super(InviableUsersState.initial()) {
    on<InitiateInvitableUsersEvent>(_onInit);
  }

  final FetchInvitableUsersUseCase _fetchInvitableUsersUseCase;
  List<UserPrivateData>? listUserPrivateData;

  Future<void> _onInit(
    InitiateInvitableUsersEvent event,
    Emitter<InviableUsersState> emit,
  ) async {
    emit(InviableUsersState.loading());
    final output =
        await _fetchInvitableUsersUseCase.execute(FetchInvitableUsersInput());
    output.searchResults.forEach((item) {
      var listUserPrivateDataFiltered =
          listUserPrivateData?.firstWhere((user) => user.userId == item.userId);
      if (listUserPrivateDataFiltered != null) {
        String? aliasName = listUserPrivateDataFiltered.aliasName;
        item.aliasName = aliasName;
      }
    });

    emit(InviableUsersState.loadedUsers(users: output.searchResults));
  }
}
