part of 'forward_bloc.dart';

@freezed
sealed class ForwardState extends BaseBlocState with _$ForwardState {
  const ForwardState._();

  factory ForwardState.initial() = ForwardStateInitial;

  factory ForwardState.channelLoaded(List<UserItem> listUser) =
      ForwardStateChannelLoaded;

  factory ForwardState.search(List<UserItem> listSearch) = ForwardStateSearch;

  factory ForwardState.share(List<UserItem> selectedAccounts) =
      ForwardStateShare;

  factory ForwardState.waiting(List<UserItem> listUser) = ForwardStateWaiting;
}

extension ForwardStateX on ForwardState {
  T when<T>({
    required T Function() initial,
    required T Function(List<UserItem> listUser) channelLoaded,
    required T Function(List<UserItem> listSearch) search,
    required T Function(List<UserItem> selectedAccounts) share,
    required T Function(List<UserItem> listUser) waiting,
  }) {
    final state = this;

    if (state is ForwardStateInitial) return initial();
    if (state is ForwardStateChannelLoaded)
      return channelLoaded(state.listUser);
    if (state is ForwardStateSearch) return search(state.listSearch);
    if (state is ForwardStateShare) return share(state.selectedAccounts);
    if (state is ForwardStateWaiting) return waiting(state.listUser);

    throw StateError('Unhandled ForwardState: $state');
  }

  T maybeWhen<T>({
    T Function()? initial,
    T Function(List<UserItem> listUser)? channelLoaded,
    T Function(List<UserItem> listSearch)? search,
    T Function(List<UserItem> selectedAccounts)? share,
    T Function(List<UserItem> listUser)? waiting,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is ForwardStateInitial && initial != null) return initial();
    if (state is ForwardStateChannelLoaded && channelLoaded != null) {
      return channelLoaded(state.listUser);
    }
    if (state is ForwardStateSearch && search != null) {
      return search(state.listSearch);
    }
    if (state is ForwardStateShare && share != null) {
      return share(state.selectedAccounts);
    }
    if (state is ForwardStateWaiting && waiting != null) {
      return waiting(state.listUser);
    }

    return orElse();
  }
}
