part of 'user_private_data_bloc.dart';

sealed class UserPrivateDataEvent extends BaseBlocEvent {
  const UserPrivateDataEvent();
}

@freezed
sealed class InitUserPrivateDataEvent extends UserPrivateDataEvent
    with _$InitUserPrivateDataEvent {
  const InitUserPrivateDataEvent._();
  factory InitUserPrivateDataEvent() = _InitUserPrivateDataEvent;
}

@freezed
sealed class ListUserPrivateDataEvent extends UserPrivateDataEvent
    with _$ListUserPrivateDataEvent {
  const ListUserPrivateDataEvent._();
  factory ListUserPrivateDataEvent({
    @Default([]) List<UserPrivateData> listUserPrivateData,
  }) = _ListUserPrivateDataEvent;
}

@freezed
sealed class GetPrivateDataUnSubscriptionEvent extends UserPrivateDataEvent
    with _$GetPrivateDataUnSubscriptionEvent {
  const GetPrivateDataUnSubscriptionEvent._();
  factory GetPrivateDataUnSubscriptionEvent() =
      _GetPrivateDataUnSubscriptionEvent;
}
