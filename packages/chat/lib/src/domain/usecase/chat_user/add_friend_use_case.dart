import 'dart:convert';

import 'package:app_core/core.dart';
import 'package:dio/dio.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:friend_api/friend_api.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';
import '../../../data/repositories/database/enums/chat_friend_status.dart';

part 'add_friend_use_case.freezed.dart';

@Injectable()
class AddFriendUseCase
    extends BaseFutureUseCase<AddFriendInput, AddFriendOutput> {
  @protected
  @override
  Future<AddFriendOutput> buildUseCase(
    AddFriendInput input,
  ) async {
    try {
      var body = V3AddFriendRequestBuilder()..userId = input.user?.userId;
      final result =
          await FriendClient().instance.addFriend(body: body.build());

      if (result.data?.ok ?? false) {
        final jsonFriend = jsonDecode(
          standardSerializers.toJson(
            V3FriendData.serializer,
            result.data!.data,
          ),
        );

        final friend = ChatFriend.fromJson(jsonFriend['friend']);
        final requestedToUserId = friend.requestedToUserId;
        final jsonIncludes = jsonDecode(
          standardSerializers.toJson(
            V3DataInclude.serializer,
            result.data!.includes,
          ),
        );
        ChatUser? chatUser;
        var responseIncludes = ResponseIncludes.fromJson(jsonIncludes);
        final usersIncludes = responseIncludes.users ?? [];
        for (final userInclude in usersIncludes) {
          final userSer =
              ChatUserSerializer.serializeFromJson(data: userInclude.toJson());
          if (userSer != null && userSer.userId == requestedToUserId) {
            userSer
              ..chatFriendDataRaw = jsonEncode(
                ChatFriendData(
                  status: ChatFriendStatusEnumExtension.getEnumByValue(
                    friend.status,
                  ),
                ).toJson(),
              );
            chatUser = userSer;
          }
        }

        await GetIt.instance.get<UpsertChatUserUseCase>().execute(
              UpsertChatUserInput(user: chatUser!),
            );
        await GetIt.instance.get<ConvertChatUserAndUpsertUserUseCase>().execute(
              ConvertChatUserAndUpsertUserInput(chatUser: chatUser),
            );
        return AddFriendOutput(user: chatUser);
      }
      return AddFriendOutput(
        user: null,
        code: result.data?.error?.code,
        message: result.data?.error?.message,
      );
    }on DioException catch (e) {
      final statusCode = e.response?.statusCode;
      final message = e.message;
      return AddFriendOutput(
        user: null,
        code: statusCode,
        message: message,
      );
    }
    on Exception catch (ex) {
      if (ex is AppUncaughtException && ex.rootError is DioException) {
        final rootEx = ex.rootError as DioException;
        final statusCode = rootEx.response?.statusCode;
        final message = rootEx.message;
        return AddFriendOutput(
          user: null,
          code: statusCode,
          message: message,
        );
      }
      return AddFriendOutput(user: null);
    }
  }
}

@freezed
sealed class AddFriendInput extends BaseInput with _$AddFriendInput {
  const AddFriendInput._();
  factory AddFriendInput({@Default(null) ChatUser? user}) = _AddFriendInput;
}

@freezed
sealed class AddFriendOutput extends BaseOutput with _$AddFriendOutput {
  const AddFriendOutput._();
  factory AddFriendOutput({
    @Default(null) ChatUser? user,
    @Default(null) int? code,
    @Default(null) String? message,
  }) = _AddFriendOutput;
}
