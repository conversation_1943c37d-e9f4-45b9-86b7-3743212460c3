import 'package:freezed_annotation/freezed_annotation.dart';

enum AttachmentType {
  @JsonValue(0)
  UNSPECIFIED(0),
  @JsonValue(1)
  PHOTO(1),
  @JsonValue(2)
  VOICE_MESSAGE(2),
  @JsonValue(3)
  VIDEO_MESSAGE(3),
  @JsonValue(4)
  AUDIO(4),
  @JsonValue(5)
  VIDEO(5),
  @JsonValue(6)
  LINKS(6),
  @JsonValue(7)
  STICKER(7),
  @JsonValue(8)
  MEDIA(8),
  @JsonValue(9)
  MENTION(9),
  @JsonValue(10)
  LOCATION(10),
  @JsonValue(11)
  FILE(11);

  final int value;

  const AttachmentType(this.value);

  static AttachmentType getEnumByValue(int? value) {
    if (value == null) return AttachmentType.UNSPECIFIED;
    return AttachmentType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => AttachmentType.UNSPECIFIED,
    );
  }

  int rawValue() => value;
}
