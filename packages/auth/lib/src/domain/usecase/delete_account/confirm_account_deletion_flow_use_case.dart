import 'package:auth_api/auth_api.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';

@Injectable()
class ConfirmAccountDeletionUseCase extends BaseFutureUseCase<
    ConfirmAccountDeletionInput, ConfirmAccountDeletionOutput> {
  ConfirmAccountDeletionUseCase();

  @override
  Future<ConfirmAccountDeletionOutput> buildUseCase(
    ConfirmAccountDeletionInput input,
  ) async {
    final response = await AuthClient().instance.confirmAccountDeletion(
          body: input.request,
        );

    return ConfirmAccountDeletionOutput(ok: response.data!.ok ?? false);
  }
}

class ConfirmAccountDeletionInput extends BaseInput {
  ConfirmAccountDeletionInput({
    required this.request,
  });

  final V3ConfirmAccountDeletionRequest request;
}

class ConfirmAccountDeletionOutput extends BaseOutput {
  ConfirmAccountDeletionOutput({required this.ok});

  final bool ok;
}
