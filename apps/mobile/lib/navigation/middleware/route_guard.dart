import 'package:auth/auth.dart';
import 'package:auto_route/auto_route.dart';
import '../routes/app_router.gr.dart';
import 'package:shared/shared.dart';

class RouteGuard extends AutoRouteGuard {
  RouteGuard(this._isAuthenticatedUseCase);

  final IsAuthenticatedUseCase _isAuthenticatedUseCase;

  bool get _isAuthenticated => runCatching(
        action: () => _isAuthenticatedUseCase.execute(IsAuthenticatedInput()),
      ).when(
        success: (output) => output.isAuthenticated,
        failure: (e) => false,
      );

  @override
  void onNavigation(NavigationResolver resolver, StackRouter router) {
    if (_isAuthenticated) {
      resolver.next(true);
    } else {
      router.replaceAll([const AuthRoute()]);
      resolver.next(false);
    }
  }
}
