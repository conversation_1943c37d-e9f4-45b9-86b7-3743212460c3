import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../websockets/websocket_manager.dart';

@Injectable()
class InitWebsocketUseCase
    extends BaseFutureUseCase<InitWebsocketInput, InitWebsocketOutput> {
  InitWebsocketUseCase(this._webSocketManager);

  final WebSocketManager _webSocketManager;

  @override
  Future<InitWebsocketOutput> buildUseCase(InitWebsocketInput input) async {
    _webSocketManager.connect();

    return InitWebsocketOutput();
  }
}

class InitWebsocketInput extends BaseInput {
  InitWebsocketInput();
}

class InitWebsocketOutput extends BaseOutput {
  InitWebsocketOutput();
}
