import 'package:auth_api/auth_api.dart' as auth;
import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../upload_manager.dart';

@LazySingleton()
class FileStoreSDKClient {
  static FilestoreClientV2? _instance;
  static const openConnectURL = "api/uploads/open-connection";

  static FilestoreClientV2 instanceForWorker({String? host}) {
    _instance ??= FilestoreClientV2(
      baseUrl: host ?? EnvConfig.getFileStoreHost,
      openConnectURL: openConnectURL,
    );
    return _instance!;
  }

  FileStoreSDKClient() {
    _instance = _initialize();
  }

  FilestoreClientV2 _initialize() {
    return FilestoreClientV2(
      baseUrl: EnvConfig.getFileStoreHost,
      openConnectURL: openConnectURL,
    );
  }

  FilestoreClientV2 get instance => _instance ?? _initialize();
}

@LazySingleton()
class AuthClient {
  AuthClient() {
    if (Config.getInstance().apiAuthToken.isNotEmpty) {
      BaseClient.addAuthToken(
        BaseClient.dio,
        Config.getInstance().apiAuthToken,
      );
    }
    _instance = auth.AuthApi(
      dio: BaseClient.dio,
      serializers: auth.standardSerializers,
    ).getAuthServiceApi();
  }

  late final auth.AuthServiceApi _instance;

  auth.AuthServiceApi get instance => _instance;
}
