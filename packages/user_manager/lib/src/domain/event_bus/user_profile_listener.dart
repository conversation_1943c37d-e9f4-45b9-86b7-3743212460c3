import 'package:shared/shared.dart';

class UserProfileEventListener extends BaseEventListener {
  UserProfileEventListener(super.eventBus);

  @override
  void handleLocalEvent(LocalEvent event) {
    Log.d(name: 'UserProfileEventListener', event.toJson());
  }

  @override
  void handleCloudEvent(CloudEvent event) {
    Log.d(name: 'UserProfileEventListener', event.toJson());
  }

  @override
  void handleUnknownEvent(BaseEvent event) {}
}
