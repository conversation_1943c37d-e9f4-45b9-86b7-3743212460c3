import 'package:json_annotation/json_annotation.dart';

enum MessageType {
  @JsonValue(0)
  DEFAULT(0),
  @JsonValue(1)
  AUDIT_LOG(1);

  final int value;

  const MessageType(this.value);

  static MessageType getEnumByValue(int? value) {
    if (value == null) return MessageType.DEFAULT;
    return MessageType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => MessageType.DEFAULT,
    );
  }

  int rawValue() => value;
}

extension MessageTypeExtension on MessageType {
  static MessageType fromRawValue(int rawValue) {
    return MessageType.values.firstWhere(
      (type) => type.value == rawValue,
      orElse: () => MessageType.DEFAULT,
    );
  }

  static int toRawValue(MessageType type) {
    return type.rawValue();
  }
}
