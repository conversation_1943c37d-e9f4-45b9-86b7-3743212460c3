import 'package:json_annotation/json_annotation.dart';

part 'worker_send_message_output.g.dart';

@JsonSerializable(explicitToJson: true)
class WorkerSendMessageOutput {
  WorkerSendMessageOutput({
    required this.success,
    this.messageId,
    this.workspaceId,
    this.channelId,
    this.userId,
    this.sendMsgErrorReason,
    this.skip = false,
    this.skipReason = '',
  });

  factory WorkerSendMessageOutput.success({
    String? messageId,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? sendMsgErrorReason,
  }) {
    return WorkerSendMessageOutput(
      success: true,
      messageId: messageId,
      workspaceId: workspaceId,
      channelId: channelId,
      userId: userId,
      sendMsgErrorReason: sendMsgErrorReason,
    );
  }

  factory WorkerSendMessageOutput.fail() {
    return WorkerSendMessageOutput(success: false);
  }

  factory WorkerSendMessageOutput.skip({String? skipReason}) {
    return WorkerSendMessageOutput(
      success: true,
      skip: true,
      skipReason: skipReason,
    );
  }

  final String? messageId;
  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final String? sendMsgErrorReason;
  final bool success;
  final bool skip;
  final String? skipReason;

  factory WorkerSendMessageOutput.fromJson(Map<String, dynamic> json) =>
      _$WorkerSendMessageOutputFromJson(json);

  Map<String, dynamic> toJson() => _$WorkerSendMessageOutputToJson(this);
}

enum SendMsgErrorEnum {
  blockedUser('blockedUser'),
  fileNotFound('fileNotFound'),
  timeout('timeout'),
  reachedMessageLimit('reachedMessageLimit'),
  network('network'),
  serverUnavailable('serverUnavailable'),
  serverError('serverError'),
  unknown('unknown');

  final String value;

  const SendMsgErrorEnum(this.value);

  factory SendMsgErrorEnum.getEnumByValue(String? value) {
    if (value == null) return SendMsgErrorEnum.unknown;
    return SendMsgErrorEnum.values.firstWhere(
      (status) => status.value == value,
      orElse: () => SendMsgErrorEnum.unknown,
    );
  }
}
