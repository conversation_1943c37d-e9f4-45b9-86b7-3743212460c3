name: auth
description: ZiiChat auth package
version: 0.1.0+1
publish_to: none
environment:
  sdk: ^3.4.0
  flutter: ^3.22.0
dependencies:
  bloc: ^9.0.0
  dartx: ^1.2.0
  dio: ^5.8.0+1
  encrypted_shared_preferences: ^3.0.1
  equatable: ^2.0.7
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.0
  get_it: ^8.0.3
  injectable: ^2.5.0
  rxdart: ^0.28.0
  shared:
    path: ../shared
  app_core:
    path: ../app_core
  passkeys:
    git:
      url: **************:ziichatlabs/flutter-passkeys.git
      ref: feat/upgrade-dependencies
      path: packages/passkeys/passkeys
  auth_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.26
      path: apis/auth_api
  hash_cash_api:
    git:
      url: **************:ziichatlabs/ziichat-flutter-apis-client.git
      ref: v0.1.20
      path: apis/hash_cash_api
  shared_preferences: ^2.5.3
  localization_client:
    git:
      url: **************:ziichatlabs/ziichat-flutter-i18n.git
      ref: main
  ziichat_ui:
    git:
      url: **************:ziichatlabs/ziichat-flutter-ui.git
      ref: release/0.56.0
  path_provider: ^2.1.5
  objectbox:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: objectbox
      ref: v4.1.0
  objectbox_flutter_libs:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: flutter_libs
      ref: v4.1.0
  freezed_annotation: ^3.0.0
  webauthn:
    git:
      url: **************:ziichatlabs/ziichat-flutter-sdks.git
      ref: 0.0.1
      path: packages/webauthn
  crypto: ^3.0.6
  built_value: ^8.9.5
  json_annotation: ^4.9.0
  flutter_cloud_kit: ^0.0.3
  url_launcher: ^6.3.1
  firebase_crashlytics: ^4.3.5
dev_dependencies:
  build_runner: ^2.4.15
  flutter_test:
    sdk: flutter
  freezed: ^3.0.6
  injectable_generator: ^2.7.0
  mocktail: ^1.0.4
  very_good_analysis: ^7.0.0
  objectbox_generator:
    git:
      url: **************:ziichatlabs/objectbox-dart.git
      path: generator
      ref: v4.1.0
  json_serializable: ^6.9.1
objectbox:
  output_dir: src/data/repositories/database/generated
dependency_overrides:
  analyzer: 7.3.0
