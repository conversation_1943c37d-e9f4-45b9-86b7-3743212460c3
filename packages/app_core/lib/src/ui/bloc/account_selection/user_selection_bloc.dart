import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';

import '../../../domain/usecase/delete_session_use_case.dart';

part 'user_selection_bloc.freezed.dart';
part 'user_selection_event.dart';
part 'user_selection_state.dart';

@injectable
class UserSelectionBloc
    extends BaseBloc<UserSelectionEvent, UserSelectionState> {
  UserSelectionBloc(
    this._getUserBySessionKeyUseCase,
    this._deleteAccountSelectionUseCase,
  ) : super(UserSelectionState.initial()) {
    on<LoadUsersEvent>(_onLoadUsers);
    on<DeleteUserEvent>(_onDeleteUser);
  }

  final GetUserBySessionKeyUseCase _getUserBySessionKeyUseCase;
  final DeleteSessionUseCase _deleteAccountSelectionUseCase;

  FutureOr<void> _onLoadUsers(
    LoadUsersEvent event,
    Emitter<UserSelectionState> emit,
  ) async {
    try {
      final output = await _getUserBySessionKeyUseCase.execute(
        GetUserBySessionKeyInput(sessionKeys: event.sessionKeys),
      );

      emit(UserSelectionState.loaded(users: output.users));
    } catch (e) {
      emit(UserSelectionState.error(error: e.toString()));
    }
  }

  FutureOr<void> _onDeleteUser(
    DeleteUserEvent event,
    Emitter<UserSelectionState> emit,
  ) async {
    if (state is Loaded) {
      final currentState = state as Loaded;
      final updatedUsers = currentState.users
          .where((user) => user.userId != event.userId)
          .toList();
      emit(UserSelectionState.loaded(users: updatedUsers));
      _deleteAccountSelectionUseCase
          .execute(DeleteAccountSelectionInput(event.userId));
    }
  }
}
