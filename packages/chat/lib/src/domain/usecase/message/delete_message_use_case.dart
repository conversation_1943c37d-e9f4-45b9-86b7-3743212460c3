import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class DeleteMessageUseCase
    extends BaseFutureUseCase<DeleteMessageInput, DeleteMessageOutput> {
  DeleteMessageUseCase();

  @override
  Future<DeleteMessageOutput> buildUseCase(
    DeleteMessageInput input,
  ) async {
    final response = await MessageClient().instance.deleteMessageOnlyMe(
          workspaceId: input.workspaceId,
          channelId: input.channelId,
          messageId: input.messageId,
        );
    return DeleteMessageOutput(ok: response.data?.ok ?? false);
  }
}

class DeleteMessageInput extends BaseInput {
  DeleteMessageInput({
    required this.workspaceId,
    required this.channelId,
    required this.messageId,
  });

  final String workspaceId;
  final String channelId;
  final String messageId;
}

class DeleteMessageOutput extends BaseOutput {
  DeleteMessageOutput({
    required this.ok,
  });

  final bool ok;
}
