import 'dart:io';

import 'package:app_core/core.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';

import '../../../data/notification_data.dart';
import '../../../navigation/routes/app_router.dart';
import '../../../navigation/routes/app_router.gr.dart';
import '../../../ui/page/channel_view/channel_view_page.dart' as c;
import '../../../ui/page/home/<USER>';
import '../../../ui/page/user_profile/user_profile_page.dart';

class NotificationHandler {
  static final NotificationHandler _instance = NotificationHandler._internal();

  factory NotificationHandler() => _instance;

  NotificationHandler._internal();

  final AppRouter _router = GetIt.I.get<AppRouter>();

  Future<void> onNotificationClicked(NotificationData notificationData) async {
    if (notificationData.hasChannelViewAction()) {
      await _popToChannelView(notificationData);
      return;
    }

    if (notificationData.hasUserViewViewAction()) {
      await _popToUserProfile(notificationData);
      return;
    }

    if (Platform.isIOS && notificationData.isDownload()) {
      var path = GetIt.instance
          .get<AppPreferences>()
          .getDownloadPath(notificationData.taskId!);
      if (path == null) return;
      var result = await OpenFileUtils.openFile(filePath: path);
      if (!result) {
        OpenLauncherUrl.onOpenLauncherURL(
          OpenLauncherUrl.pathOpenPhotoGalleyIOS,
        );
      }
      return;
    }
  }

  Future<void> _popToChannelView(NotificationData notificationData) async {
    if (isChannelViewFocused()) {
      if (isSameChannel(notificationData)) {
        _sendEventClickNotification(notificationData);
        return;
      }
      _replaceWithChannelView(notificationData);
      return;
    }

    if (_hasChannelViewInStack()) {
      _replaceWithChannelView(notificationData);
      return;
    }
    _pushChannelView(notificationData, fromNotification: true);
  }

  /// Send event to channel view to handle click notification in case open focused channel on background
  void _sendEventClickNotification(NotificationData notificationData) {
    AppEventBus.publish(
      OnClickMessageNotificationEvent(
        workspaceId: notificationData.workspaceId,
        channelId: notificationData.channelId,
        userId: notificationData.isDm() ? notificationData.userId : '',
        messageId: notificationData.messageId,
      ),
    );
  }

  void _replaceWithChannelView(NotificationData notificationData) {
    _router.popUntil((route) => route.settings.name == ChannelViewRoute.name);
    _router.replace(createChannelViewRoute(notificationData));

    //Use when the transition effect in ChannelView is black screen
    // _router.popUntil((route) => route.settings.name == HomeRoute.name);
    // _router.push(createChannelViewRoute(notificationData));
  }

  void _pushChannelView(
    NotificationData notificationData, {
    bool? fromNotification,
  }) {
    _router.push(
      createChannelViewRoute(
        notificationData,
        fromNotification: fromNotification,
      ),
    );
  }

  bool _hasChannelViewInStack() {
    return _router.stack
        .any((route) => route.routeData.name == ChannelViewRoute.name);
  }

  ChannelViewRoute createChannelViewRoute(
    NotificationData notificationData, {
    bool? fromNotification = false,
    bool? onOpenApp = false,
  }) {
    return ChannelViewRoute(
      workspaceId:
          notificationData.isDm() ? null : notificationData.workspaceId,
      channelId: notificationData.isDm() ? null : notificationData.channelId,
      userId: notificationData.isDm() ? notificationData.userId : null,
      focusableMessageId: notificationData.messageId ?? '',
      fromNotification: fromNotification ?? false,
      onOpenApp: onOpenApp ?? false,
    );
  }

  Future<void> _popToUserProfile(NotificationData notificationData) async {
    if (isCurrentlyInUserProfile(notificationData)) {
      return;
    }
    _router.push(createUserProfileRoute(notificationData));
  }

  UserProfileRoute createUserProfileRoute(
    NotificationData notificationData, {
    bool? onOpenApp = false,
  }) {
    return UserProfileRoute(
      workspaceId: notificationData.workspaceId,
      channelId: notificationData.channelId,
      userId: notificationData.userId,
      callVisitedProfile: !notificationData.hasUserViewProfile(),
      onOpenApp: onOpenApp ?? false,
    );
  }

  bool needShowNotification(NotificationData notificationData) {
    try {
      if (notificationData.hasChannelViewAction()) {
        if (isCurrentlyInChannelListView()) {
          return false;
        }

        return !isCurrentlyInChannelView(notificationData);
      }

      if (notificationData.hasUserViewProfile()) {
        return !isCurrentlyNotificationPage(notificationData);
      }

      if (notificationData.hasUserViewViewAction()) {
        return !isCurrentlyInUserProfile(notificationData);
      }
    } catch (e) {
      print('Error when check needShowNotification: ${e.toString()}');
    }

    return true;
  }

  bool isCurrentlyNotificationPage(NotificationData notificationData) {
    final routerLength = _router.stack.length;
    if (routerLength > 0) {
      final routePage = _router.stack.last;
      return routePage.routeData.name == NotificationRoute.name;
    }
    return false;
  }

  bool isCurrentlyInUserProfile(NotificationData notificationData) {
    final routerLength = _router.stack.length;
    if (routerLength > 0) {
      final routePage = _router.stack.last;
      final isUserProfileRoute =
          routePage.routeData.name == UserProfileRoute.name;
      final isFriendRequestRoute =
          routePage.routeData.name == FriendRequestRoute.name;
      if(isFriendRequestRoute) return true;

      if (isUserProfileRoute) {
        final page = routePage.child as UserProfilePage;
        final isSamePage = page.userId == notificationData.userId ||
            page.username == notificationData.channelName;
        return isSamePage;
      }
    }
    return false;
  }

  bool isCurrentlyInChannelView(NotificationData notificationData) {
    if (!isChannelViewFocused()) return false;
    return isSameChannel(notificationData);
  }

  bool isChannelViewFocused() {
    if (_router.stack.isEmpty) return false;
    return _router.stack.last.routeData.name == ChannelViewRoute.name;
  }

  bool isSameChannel(NotificationData notificationData) {
    final routePage = _router.stack.last;
    final channelViewPage = routePage.child as c.ChannelViewPage;

    return (notificationData.isDm()
        ? channelViewPage.userId == notificationData.userId
        : channelViewPage.workspaceId == notificationData.workspaceId &&
            channelViewPage.channelId == notificationData.channelId);
  }

  bool isCurrentlyInChannelListView() {
    final routerLength = _router.stack.length;

    if (routerLength > 0) {
      final routePage = _router.stack.last;
      return routePage.routeData.name == HomeRoute.name &&
          HomePage.CURRENT_INDEX == HomePage.CHANNEL_LIST_INDEX;
    }
    return false;
  }

  static Future<void> updateAndroidBadge(int count) async {
    if (!await FlutterAppBadger.isAppBadgeSupported()) return;
    FlutterAppBadger.updateBadgeCount(count);
  }
}
