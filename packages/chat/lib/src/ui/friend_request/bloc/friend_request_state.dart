part of 'friend_request_bloc.dart';

@freezed
sealed class FriendRequestState extends BaseBlocState
    with _$FriendRequestState {
  const FriendRequestState._();

  factory FriendRequestState.initial() = FriendRequestStateInitial;

  factory FriendRequestState.loaded({
    @Default([]) List<ChatUser> friends,
    @Default(false) bool noMoreItems,
  }) = FriendRequestStateLoaded;

  factory FriendRequestState.refresh() = FriendRequestStateRefresh;

  factory FriendRequestState.onError({
    @Default(null) int? code,
    @Default(null) String? message,
  }) = FriendRequestStateOnError;
}



extension FriendRequestStateX on FriendRequestState {
  T when<T>({
    required T Function() initial,
    required T Function(List<ChatUser> friends, bool noMoreItems) loaded,
  }) {
    final state = this;

    if (state is FriendRequestStateInitial) return initial();
    if (state is FriendRequestStateLoaded) {
      return loaded(state.friends, state.noMoreItems);
    }

    throw StateError('Unhandled FriendRequestState: $state');
  }

  T maybeWhen<T>({
    T Function()? initial,
    T Function(List<ChatUser> friends, bool noMoreItems)? loaded,
    T Function()? refresh,
    T Function(int? code, String? message)? onError,
    required T Function() orElse,
  }) {
    final state = this;

    if (state is FriendRequestStateInitial && initial != null) {
      return initial();
    }
    if (state is FriendRequestStateLoaded && loaded != null) {
      return loaded(state.friends, state.noMoreItems);
    }
    if (state is FriendRequestStateRefresh && refresh != null) {
      return refresh();
    }
    if (state is FriendRequestStateOnError && onError !=null) {
      return onError(state.code, state.message);
    }

    return orElse();
  }
}
