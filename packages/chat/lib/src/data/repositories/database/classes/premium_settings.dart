import 'package:json_annotation/json_annotation.dart';

import 'boosted.dart';

part 'premium_settings.g.dart';

@JsonSerializable(explicitToJson: true)
class PremiumSettings {
  final Boosted boosted;

  PremiumSettings({required this.boosted});

  factory PremiumSettings.fromJson(Map<String, dynamic> json) =>
      _$PremiumSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$PremiumSettingsToJson(this);
}
