import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

class ModelMemberSettingProfile {
  ModelMemberSettingProfile({
    required this.userId,
    required this.username,
    required this.role,
    required this.isOnline,
    this.channelId,
    this.workspaceId,
    this.nickname,
    this.displayName,
    this.aliasName,
    this.avatarUrl,
    this.userBadgeType,
  });

  final String userId;
  final String? channelId;
  final String? workspaceId;
  final String username;
  final String? nickname;
  final String? displayName;
  final String? aliasName;
  final Roles? role;
  final String? avatarUrl;
  final bool isOnline;
  final UserBadgeType? userBadgeType;

  String get memberName {
    if (!StringUtils.isNullOrEmpty(aliasName)) {
      return aliasName!;
    }
    if (!StringUtils.isNullOrEmpty(displayName)) {
      return displayName!;
    }
    if (!StringUtils.isNullOrEmpty(username)) {
      return username;
    }
    return '';
  }

  String get name {
    if (!StringUtils.isNullOrEmpty(aliasName)) {
      return aliasName!;
    }
    if (!StringUtils.isNullOrEmpty(nickname)) {
      return nickname!;
    }
    if (!StringUtils.isNullOrEmpty(displayName)) {
      return displayName!;
    }
    if (!StringUtils.isNullOrEmpty(username)) {
      return username;
    }
    return '';
  }

  ItemMemberSettingProfile toItemMemberSettingProfile() {
    return ItemMemberSettingProfile(
      userId: userId,
      workspaceId: workspaceId,
      channelId: channelId,
      name: memberName,
      role: role,
      nickName: nickname ?? '',
      url: avatarUrl,
      isOnline: isOnline,
      badgeType: userBadgeType,
    );
  }
}
