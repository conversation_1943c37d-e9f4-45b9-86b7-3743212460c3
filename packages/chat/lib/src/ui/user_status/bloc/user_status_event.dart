part of 'user_status_bloc.dart';

@freezed
sealed class UserStatusEvent with _$UserStatusEvent {
  const UserStatusEvent._();

  factory UserStatusEvent.initiate() = Initiate;

  factory UserStatusEvent.reLoadStatus() = ReLoadStatusEvent;

  factory UserStatusEvent.loaded({required List<ChatUser> users}) = LoadedEvent;

  factory UserStatusEvent.updatedStatus({required List<ChatUser> users}) =
      UpdatedStatusEvent;

  factory UserStatusEvent.UnSubscription() = UnSubscriptionEvent;
}
