import 'package:dio/dio.dart';

import '../../../../../shared.dart';

/// Interceptor to log requests, responses, and errors
///
/// This interceptor only logs information and does not modify requests, responses, or errors
class LoggingInterceptor extends Interceptor {
  static const tag = 'LoggingInterceptor';

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final fullUrl = _getFullUrl(options);
    options.extra['startTime'] = DateTime.now();

    if (GlobalConfig.enableLogRequestInfo)
      Log.api(
        '-----> Request:\nUrl: $fullUrl',
        name: tag,
        requestData: options.data,
        time: DateTime.now(),
      );

    handler.next(options);
  }

  @override
  void onResponse(
    Response<dynamic> response,
    ResponseInterceptorHandler handler,
  ) {
    final fullUrl = _getFullUrl(response.requestOptions);
    final startTime = response.requestOptions.extra['startTime'] as DateTime?;
    final endTime = DateTime.now();
    final duration =
        startTime != null ? endTime.difference(startTime).inMilliseconds : null;

    if (GlobalConfig.enableLogSuccessResponse)
      Log.api(
        '<----- Response:\nUrl: $fullUrl \nDuration: $duration ms ',
        name: tag,
        responseData: response.data,
        statusCode: response.statusCode,
        time: endTime,
      );

    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final fullUrl = _getFullUrl(err.requestOptions);
    final startTime = err.requestOptions.extra['startTime'] as DateTime?;
    final endTime = DateTime.now();
    final duration =
        startTime != null ? endTime.difference(startTime).inMilliseconds : null;

    if (GlobalConfig.enableLogErrorResponse)
      Log.api(
        'Error:\nUrl: $fullUrl',
        name: tag,
        requestData: err.requestOptions.data,
        responseData: err.response?.data,
        statusCode: err.response?.statusCode,
        time: endTime,
      );

    if (duration != null && GlobalConfig.enableLogResponseDuration)
      Log.d(
        'Request duration: $duration ms',
        name: tag,
      );

    handler.next(err);
  }

  String _getFullUrl(RequestOptions options) {
    return options.baseUrl +
        options.path +
        (options.queryParameters.isNotEmpty
            ? '?${Transformer.urlEncodeMap(options.queryParameters)}'
            : '');
  }
}
