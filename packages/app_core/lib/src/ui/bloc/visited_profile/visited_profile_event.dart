part of 'visited_profile_bloc.dart';

sealed class VisitedProfileEvent extends BaseBlocEvent {
  const VisitedProfileEvent();
}

@freezed
sealed class InitVisitedProfileEvent extends VisitedProfileEvent
    with _$InitVisitedProfileEvent {
  const InitVisitedProfileEvent._();
  factory InitVisitedProfileEvent() = _InitVisitedProfileEvent;
}

@freezed
sealed class ListVisitedProfileEvent extends VisitedProfileEvent
    with _$ListVisitedProfileEvent {
  const ListVisitedProfileEvent._();
  factory ListVisitedProfileEvent({
    List<VisitedProfile>? listVisitedProfile,
    List<User>? listUser,
  }) = _ListVisitedProfileEvent;
}

@freezed
sealed class DeleteVisitedProfileByUserIdEvent extends VisitedProfileEvent
    with _$DeleteVisitedProfileByUserIdEvent {
  const DeleteVisitedProfileByUserIdEvent._();
  factory DeleteVisitedProfileByUserIdEvent({String? userId}) =
      _DeleteVisitedProfileByUserIdEvent;
}

@freezed
sealed class ClearVisitedProfileNotificationEvent extends VisitedProfileEvent
    with _$ClearVisitedProfileNotificationEvent {
  const ClearVisitedProfileNotificationEvent._();
  factory ClearVisitedProfileNotificationEvent() =
      _ClearVisitedProfileNotificationEvent;
}

@freezed
sealed class UpdateIsReadVisitedProfileEvent extends VisitedProfileEvent
    with _$UpdateIsReadVisitedProfileEvent {
  const UpdateIsReadVisitedProfileEvent._();
  factory UpdateIsReadVisitedProfileEvent({required bool? isAllRead}) =
      _UpdateIsReadVisitedProfileEvent;
}

@freezed
sealed class AddFriendVisitedProfileEvent extends VisitedProfileEvent
    with _$AddFriendVisitedProfileEvent {
  const AddFriendVisitedProfileEvent._();
  factory AddFriendVisitedProfileEvent({
    required String userId,
  }) = _AddFriendVisitedProfileEvent;
}

@freezed
sealed class CancelRequestVisitedProfileEvent extends VisitedProfileEvent
    with _$CancelRequestVisitedProfileEvent {
  const CancelRequestVisitedProfileEvent._();
  factory CancelRequestVisitedProfileEvent({
    required String userId,
  }) = _CancelRequestVisitedProfileEvent;
}

@freezed
sealed class AcceptRequestVisitedProfileEvent extends VisitedProfileEvent
    with _$AcceptRequestVisitedProfileEvent {
  const AcceptRequestVisitedProfileEvent._();
  factory AcceptRequestVisitedProfileEvent({
    required String userId,
  }) = _AcceptRequestVisitedProfileEvent;
}

@freezed
sealed class UnfriendVisitedProfileEvent extends VisitedProfileEvent
    with _$UnfriendVisitedProfileEvent {
  const UnfriendVisitedProfileEvent._();
  factory UnfriendVisitedProfileEvent({
    required String userId,
  }) = _UnfriendVisitedProfileEvent;
}

@freezed
sealed class ErrorVisitedProfileEvent extends VisitedProfileEvent
    with _$ErrorVisitedProfileEvent {
  const ErrorVisitedProfileEvent._();
  factory ErrorVisitedProfileEvent({int? code, String? message}) =
      _ErrorVisitedProfileEvent;
}
