import 'package:injectable/injectable.dart';
import 'package:notification_api/notification_api.dart' as api;
import 'package:shared/shared.dart';

import '../../../../../core.dart';

@LazySingleton()
class NotificationClient {
  NotificationClient() {
    if (Config.getInstance().apiAuthToken.isNotEmpty) {
      BaseClient.addAuthToken(
        BaseClient.dio,
        Config.getInstance().apiAuthToken,
      );
    }
    _instance = api.NotificationApi(
      dio: BaseClient.dio,
      serializers: api.standardSerializers,
    ).getNotificationServiceApi();
  }

  late final api.NotificationServiceApi _instance;

  api.NotificationServiceApi get instance => _instance;
}
