import 'package:json_annotation/json_annotation.dart';

part 'worker_upload_file.g.dart';

@JsonSerializable(explicitToJson: true)
class WorkerUploadFile {
  WorkerUploadFile({
    required this.path,
    required this.name,
    required this.size,
    this.fileRef,
    this.messageRef,
    this.messageId,
    this.status = UploadFileStatus.pending,
  }) : assert(messageRef != null || messageId != null);

  final String? messageRef;
  final String? messageId;
  final String? fileRef;
  final String path;
  final String name;
  final int size;
  UploadFileStatus status;

  @JsonKey(includeFromJson: false, includeToJson: false)
  String get ref => messageRef ?? messageId!;

  factory WorkerUploadFile.fromJson(Map<String, dynamic> json) =>
      _$WorkerUploadFileFromJson(json);

  Map<String, dynamic> toJson() => _$WorkerUploadFileToJson(this);

  WorkerUploadFile copyWith({
    String? path,
    String? name,
    int? size,
    String? fileRef,
    String? messageRef,
    String? messageId,
    UploadFileStatus? status,
    DateTime? creationTime,
  }) {
    return WorkerUploadFile(
      path: path ?? this.path,
      name: name ?? this.name,
      size: size ?? this.size,
      fileRef: fileRef ?? this.fileRef,
      messageRef: messageRef ?? this.messageRef,
      messageId: messageId ?? this.messageId,
      status: status ?? this.status,
    );
  }
}

enum UploadFileStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('sending')
  sending,
  @JsonValue('done')
  done,
  @JsonValue('failed')
  failed
}
