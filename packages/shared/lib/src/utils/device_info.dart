import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';

import '../../shared.dart';

Future<String?> getDeviceId() async {
  final deviceInfo = DeviceInfoPlugin();
  if (Platform.isIOS) {
    final iosDeviceInfo = await deviceInfo.iosInfo;
    return iosDeviceInfo.identifierForVendor;
  } else if (Platform.isAndroid) {
    final androidDeviceInfo = await deviceInfo.androidInfo;
    return androidDeviceInfo.id;
  }
  return null;
}

Future<String?> getOSVersion() async {
  if (Platform.isIOS &&
      await DebugAuthUtils.getDebugAuthEnabled() &&
      await DebugAuthUtils.getSelectedIosVersion() != 'Default') {
    return await DebugAuthUtils.getSelectedIosVersion();
  }

  final deviceInfo = DeviceInfoPlugin();
  if (Platform.isIOS) {
    final iosDeviceInfo = await deviceInfo.iosInfo;
    return iosDeviceInfo.systemVersion;
  } else if (Platform.isAndroid) {
    final androidDeviceInfo = await deviceInfo.androidInfo;
    return androidDeviceInfo.version.release;
  }
  return null;
}

Future<double> getDoubleOSVersion() async {
  if (Platform.isIOS &&
      await DebugAuthUtils.getDebugAuthEnabled() &&
      await DebugAuthUtils.getSelectedIosVersion() != 'Default') {
    return double.parse((await DebugAuthUtils.getSelectedIosVersion())!);
  }

  final deviceInfo = DeviceInfoPlugin();
  if (Platform.isIOS) {
    final iosDeviceInfo = await deviceInfo.iosInfo;
    final mainVersion =
        iosDeviceInfo.systemVersion.split('.').take(2).join('.');
    return double.parse(mainVersion);
  } else if (Platform.isAndroid) {
    final androidDeviceInfo = await deviceInfo.androidInfo;
    return androidDeviceInfo.version.sdkInt.toDouble();
  }
  return 0.0;
}

Future<int?> getSDKVersion() async {
  final deviceInfo = DeviceInfoPlugin();
  if (Platform.isAndroid) {
    final androidDeviceInfo = await deviceInfo.androidInfo;
    return androidDeviceInfo.version.sdkInt;
  }
  return null;
}

Future<String?> getDeviceName() async {
  final deviceInfo = DeviceInfoPlugin();
  if (Platform.isIOS) {
    final iosDeviceInfo = await deviceInfo.iosInfo;
    return iosDeviceInfo.name;
  } else if (Platform.isAndroid) {
    final androidDeviceInfo = await deviceInfo.androidInfo;
    return androidDeviceInfo.model;
  }
  return null;
}

Future<String?> getManufacturer() async {
  final deviceInfo = DeviceInfoPlugin();
  if (Platform.isIOS) {
    return "Apple";
  } else if (Platform.isAndroid) {
    final androidDeviceInfo = await deviceInfo.androidInfo;
    return androidDeviceInfo.manufacturer;
  }
  return null;
}

Future<Map<String, String>> getDeviceDetails() async {
  final osVersion = await getOSVersion();
  final deviceId = await getDeviceId();
  final deviceName = await getDeviceName();
  final manufacturer = await getManufacturer();
  final sdkVersion = await getSDKVersion();

  return {
    "platform": Platform.operatingSystem,
    "osVersion": osVersion ?? "Unknown",
    "deviceId": deviceId ?? "Unknown",
    "deviceName": deviceName ?? "Unknown",
    "manufacturer": manufacturer ?? "Unknown",
    "sdkVersion": sdkVersion?.toString() ?? "Unknown",
  };
}
