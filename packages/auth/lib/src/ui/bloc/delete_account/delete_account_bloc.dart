import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:auth_api/auth_api.dart';
import 'package:bloc/bloc.dart';
import 'package:crypto/crypto.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../auth.dart';
import '../../../domain/usecase/delete_account/confirm_account_deletion_flow_use_case.dart';
import '../../../domain/usecase/delete_account/initiate_account_deletion_flow_use_case.dart';
import '../../../domain/usecase/improving_passkey/check_migrate_passkey_status_use_case.dart';

part 'delete_account_bloc.freezed.dart';
part 'delete_account_event.dart';
part 'delete_account_state.dart';

@Injectable()
class DeleteAccountBloc
    extends BaseBloc<DeleteAccountEvent, DeleteAccountState> {
  DeleteAccountBloc(
    this._InitiateAccountDeletionFlow,
    this._getCredentialUseCase,
    this._checkMigratePasskeyStatusUseCase,
    this._confirmAccountDeletionUseCase,
  ) : super(DeleteAccountState.initial()) {
    on<DeleteAccountRequestedEvent>(_handleDeleteAccountFlow);
    on<ResetDeleteAccountStateEvent>(_handleResetDeleteAccountStateEvent);
  }

  final InitiateAccountDeletionFlow _InitiateAccountDeletionFlow;
  final GetCredentialUseCase _getCredentialUseCase;
  final CheckMigratePasskeyStatusUseCase _checkMigratePasskeyStatusUseCase;
  final ConfirmAccountDeletionUseCase _confirmAccountDeletionUseCase;
  late String _reqChallenge = RandomUtils.generateRandomString(16);

  Future<void> _handleDeleteAccountFlow(
    DeleteAccountRequestedEvent event,
    Emitter<DeleteAccountState> emit,
  ) async {
    final reqChallengeHash =
        sha256.convert(utf8.encode(_reqChallenge)).toString();

    bool migrated = await _checkMigrationStatus();

    final initOutput = await _InitiateAccountDeletionFlow.execute(
      InitiateAccountDeletionInput(reqChallengeHash: reqChallengeHash),
    );

    final getCredentialOutput = await _getCredentialUseCase.execute(
      GetCredentialInput(
        credentialRequestOptions:
            initOutput.response.confirmationPayload!.credentialRequestOptions!,
        reqId: initOutput.response.confirmationPayload!.reqId!,
        reqVerifier: _reqChallenge,
        passkeyMigrated: migrated,
      ),
    );

    if (getCredentialOutput.exception != null) {
      emit(DeleteAccountState.deleteAccountFailure());
      return;
    }

    if (!getCredentialOutput.ok) return;

    final confirmOutput = await _confirmAccountDeletionUseCase.execute(
      ConfirmAccountDeletionInput(
        request: (V3ConfirmAccountDeletionRequestBuilder()
              ..reqId = getCredentialOutput.reqId
              ..reqVerifier = getCredentialOutput.reqVerifier
              ..assertion = getCredentialOutput.assertion!.toBuilder())
            .build(),
      ),
    );
    if (confirmOutput.ok) {
      emit(DeleteAccountState.deleteAccountSuccess());
      return;
    }

    emit(DeleteAccountState.deleteAccountFailure());
  }

  Future<bool> _checkMigrationStatus() async {
    if (Platform.isAndroid) return true;

    final checkMigratedOutput = await _checkMigratePasskeyStatusUseCase.execute(
      CheckMigratePasskeyStatusInput(),
    );
    return checkMigratedOutput.ok;
  }

  FutureOr<void> _handleResetDeleteAccountStateEvent(
    ResetDeleteAccountStateEvent event,
    Emitter<DeleteAccountState> emit,
  ) {
    emit(DeleteAccountState.initial());
  }
}
