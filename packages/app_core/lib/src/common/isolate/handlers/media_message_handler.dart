import 'package:shared/shared.dart';

import '../../../data/source/api/client/isolate_api_client.dart';
import '../../network/retry_manager.dart';
import '../input/worker_send_media_input.dart';
import '../output/worker_send_message_output.dart';
import 'base_message_handler.dart';

/// Specialized handler for media messages
class MediaMessageHandler extends BaseMessageHandler<WorkerSendMediaInput> {
  MediaMessageHandler({
    required super.apiClient,
    required super.retryManager,
  });

  @override
  WorkerSendMediaInput parseInput(Map<String, dynamic> inputData) {
    return WorkerSendMediaInput.fromJson(inputData);
  }

  @override
  String getApiEndpoint(WorkerSendMediaInput input) {
    return '/Message/' + (input.isDm() ? 'SendDmMessageMedia' : 'SendMessageMedia');
  }

  @override
  Future<WorkerSendMessageOutput> executeMessageOperation(
    WorkerSendMediaInput input,
    DateTime createTime,
  ) async {
    return await makeRetryableApiCall(
      input: input,
      createTime: createTime,
      apiCall: () => apiClient.post(
        getApiEndpoint(input),
        body: input.toRequest(),
      ),
    );
  }

  /// Legacy method for backward compatibility
  Future<WorkerSendMessageOutput> handleMediaMessage({
    required WorkerSendMediaInput messageInput,
    required String ref,
  }) async {
    // Giữ API cũ cho tương thích, nhưng nên chuyển sang handleMessage nếu dùng input dạng Map
    return await handleMessage(messageInput.toJson()..['ref'] = ref);
  }
}
