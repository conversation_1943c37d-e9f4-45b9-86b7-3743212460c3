import 'dart:async';

import 'package:app_core/core.dart';
import 'package:bloc/bloc.dart';
import 'package:flutter_background/flutter_background.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart' as rtc;
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:shared/shared.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../../../common/di/di.dart';
import '../../../domain/models/call_user.dart';
import '../call_duration_manager.dart';
import '../models/participant_track.dart';
import '../models/room_page_args.dart';
import 'foreground_service_handler.dart';

part 'room_bloc.freezed.dart';
part 'room_event.dart';
part 'room_state.dart';

enum DevicesKind {
  audioinput,
  audiooutput,
  videoinput,
}

@LazySingleton()
class RoomBloc extends Bloc<CallRoomEvent, RoomState> {
  RoomBloc(
    this._privateDataRepository,
    this._getStreamUsersUseCase,
    this._getStreamMembersUseCase,
  ) : super(RoomState.initial()) {
    on<InitiateEvent>(_onInit);
    on<UpdateParticipantListEvent>(_onUpdateParticipantList);
    on<ChangeCameraDirectionEvent>(_onChangeCameraDirection);
    on<ToggleCameraStateEvent>(_onToggleCameraState);
    on<ToggleSpeakerStateEvent>(_onToggleSpeakerState);
    on<ChangeSpeakerEvent>(_onChangeSpeaker);
    on<ShowSpeakerSelectorEvent>(_onShowSpeakerSelector);
    on<ToggleMicroStateEvent>(_onToggleMicroState);
    on<FocusParticipantEvent>(_onFocusParticipant);
    on<SortParticipantsEvent>(_onSortParticipants);
    on<ToggleScreenShareEvent>(_onToggleScreenShare);
    on<DisconnectEvent>(_onDisconnect);
  }

  //region Injectable
  final PrivateDataRepository _privateDataRepository;
  final GetStreamUsersUseCase _getStreamUsersUseCase;
  final GetStreamMembersUseCase _getStreamMembersUseCase;

  //endregion Injectable

  //region Room variables
  Room? _room;
  EventsListener<RoomEvent>? _listener;
  bool _busy = false;
  CameraPosition _currentCameraPosition = CameraPosition.front;
  List<MediaDevice> _audioInputs = [];
  List<MediaDevice> _audioOutputs = [];
  List<MediaDevice> _videoInputs = [];

  final _videoParameters = VideoParametersPresets.h1080_169;
  final _maxFrameRate = 30.0;
  final _screenShareOptions = ScreenShareCaptureOptions(
    useiOSBroadcastExtension: true,
    captureScreenAudio: false,
    maxFrameRate: 30,
    params: VideoParametersPresets.screenShareH1080FPS15,
  );

  StreamSubscription? _hardwareSubscription;
  StreamSubscription? _endRoomEventSubscription;

  //endregion Room variables

  //region User info, channel info
  Map<String, String> _aliasNames = {};
  Map<String, String> _nicknames = {};
  Map<String, CallUser> _users = {};

  StreamSubscription? _usersSubscription;
  StreamSubscription? _usersPrivateDataSubscription;
  StreamSubscription? _membersSubscription;

  //endregion User info, channel info

  //region Meeting room info
  CallDurationManager _callDurationManager = CallDurationManager();
  RoomPageArgs? _currentArgs;
  MeetingRoomInfo? _currentRoomInfo;

  List<Function(Duration)> _callDurationListeners = [];
  List<Function(MeetingRoomInfo?)> _roomListeners = [];

  //endregion Meeting room info

  //region Public properties and methods
  bool get hasJoinedMeetingRoom => _room != null;

  Room? get currentRoom => _room;

  bool isSameRoom({required String channelId, required String workspaceId}) {
    if (_currentArgs?.channelId == channelId &&
        _currentArgs?.workspaceId == workspaceId) {
      return true;
    }
    return false;
  }

  void addDurationCallback(Function(Duration) onTick) {
    _callDurationListeners.add(onTick);
  }

  void removeDurationCallback(Function(Duration) onTick) {
    _callDurationListeners.remove(onTick);
  }

  void addRoomListener(
    Function(MeetingRoomInfo?) callback, {
    bool triggerImmediately = false,
  }) {
    if (triggerImmediately) {
      callback(_currentRoomInfo);
    }
    _roomListeners.add(callback);
  }

  void removeRoomListener(Function(MeetingRoomInfo?) callback) {
    _roomListeners.remove(callback);
  }

  Map<String, rtc.RTCVideoRenderer> _cachedRenderers = {};

  rtc.RTCVideoRenderer getVideoRenderer(String trackId) {
    return _cachedRenderers[trackId]!;
  }

  //endregion Public properties and methods

  //region Bloc Events
  /// Initialize room page
  FutureOr<void> _onInit(InitiateEvent event, Emitter<RoomState> emit) {
    WakelockPlus.enable();
    ForegroundServiceHandler.requestPermissions();
    ForegroundServiceHandler.initService();
    ForegroundServiceHandler.startService();
    // TODO: Load channel info
    _room = event.args.room;
    _currentArgs = event.args;
    _room!.setSpeakerOn(true);
    _currentRoomInfo = MeetingRoomInfo(
      channelId: event.args.channelId,
      workspaceId: event.args.workspaceId,
      channelName: event.args.channelName,
      duration: _callDurationManager.currentDuration,
      numberParticipants: 0,
    );
    _setUpListeners();
    _sortParticipants();
    _room!.addListener(_onRoomDidUpdate);
    _setupMediaDevices();

    _endRoomEventSubscription =
        getIt<AppEventBus>().on<EndMeetingRoomEvent>().listen((event) {
      if (_room == null) return;
      if (event.channelId != _currentArgs?.channelId ||
          event.workspaceId != _currentArgs?.workspaceId) return;
      _room?.disconnect();
    });

    Future.delayed(DurationUtils.ms100, () async {
      if (await PermissionUtils.isGrantedMicrophonePermission()) {
        if (_room!.localParticipant != null) {
          add(ToggleMicroStateEvent(true));
        }
      }
    });
    if (event.args.isVideoCall) {
      Future.delayed(DurationUtils.ms500, () async {
        if (_room!.localParticipant != null) {
          if (await PermissionUtils.isGrantedCameraPermission()) {
            add(ToggleCameraStateEvent(true));
          }
        }
      });
    }
    emit(RoomState.speakerStateChanged(_room!.speakerOn ?? false));

    //Stream data
    _usersPrivateDataSubscription = _privateDataRepository
        .getStreamUsers()
        .listen(_userPrivateDataListener);
    _initStreamMembers(
      channelId: event.args.channelId,
      workspaceId: event.args.workspaceId,
    );
    _callDurationManager.setOnTick((duration) {
      _callDurationListeners.forEach((action) => action(duration));
      _currentRoomInfo = _currentRoomInfo!.copyWith(duration: duration);
      _roomListeners.forEach((action) => action(_currentRoomInfo));
    });
    _callDurationManager.start();
  }

  /// Show speaker selector
  FutureOr<void> _onShowSpeakerSelector(
    ShowSpeakerSelectorEvent event,
    Emitter<RoomState> emit,
  ) async {
    if (_audioOutputs.isEmpty) return;
    if (_room!.selectedAudioOutputDeviceId == null) return;

    emit(
      RoomState.speakerSelectorShown(
        _audioOutputs,
        _room!.selectedAudioOutputDeviceId!,
      ),
    );
  }

  /// Change audio output device
  FutureOr<void> _onChangeSpeaker(
    ChangeSpeakerEvent event,
    Emitter<RoomState> emit,
  ) async {
    if (_room!.selectedAudioOutputDeviceId == event.device.deviceId) return;
    if (_busy) return;
    _busy = true;

    try {
      if (event.device.groupId == 'speaker') {
        await _room!.setSpeakerOn(true, forceSpeakerOutput: true);
      } else {
        await _room!.setSpeakerOn(false, forceSpeakerOutput: false);
      }
      //TODO: Switching between Internal Speaker and Bluetooth is not correct
      await _room!.setAudioOutputDevice(event.device);
    } catch (ex) {
      Log.e(name: 'RoomBloc._onChangeSpeaker', ex);
    }
    _busy = false;
  }

  /// Update participant list
  FutureOr<void> _onUpdateParticipantList(
    UpdateParticipantListEvent event,
    Emitter<RoomState> emit,
  ) {
    emit(RoomState.participantListChanged(event.participants));
  }

  /// Change camera direction
  FutureOr<void> _onChangeCameraDirection(
    ChangeCameraDirectionEvent event,
    Emitter<RoomState> emit,
  ) async {
    if (_busy) return;
    _busy = true;

    final participant = _room!.localParticipant;
    if (participant == null) {
      _busy = false;
      return;
    }

    if (!participant.isCameraEnabled()) {
      _busy = false;
      return;
    }
    if (participant.videoTrackPublications.isEmpty) {
      _busy = false;
      return;
    }

    try {
      _currentCameraPosition = _currentCameraPosition == CameraPosition.front
          ? CameraPosition.back
          : CameraPosition.front;

      final localVideoTrack = participant.videoTrackPublications
          .firstWhere((e) => !e.isScreenShare)
          .track as LocalVideoTrack;
      await localVideoTrack.restartTrack(
        CameraCaptureOptions(
          cameraPosition: _currentCameraPosition,
          params: _videoParameters,
          maxFrameRate: _maxFrameRate,
        ),
      );
      emit(RoomState.cameraPositionChanged(_currentCameraPosition));
    } catch (ex) {
      Log.e(name: 'RoomBloc._onChangeCameraDirection', ex);
    }
    _busy = false;
  }

  /// Toggle camera state
  FutureOr<void> _onToggleCameraState(
    ToggleCameraStateEvent event,
    Emitter<RoomState> emit,
  ) async {
    final participant = _room!.localParticipant;
    if (participant == null) return;

    if (_busy) return;
    _busy = true;

    try {
      final cameraEnabled = event.isActive != null
          ? event.isActive!
          : !participant.isCameraEnabled();
      await participant.setCameraEnabled(cameraEnabled);
      emit(RoomState.cameraStateChanged(cameraEnabled));
    } catch (ex) {
      Log.e(name: 'RoomBloc._onToggleCameraState', ex);
    }

    _busy = false;
  }

  /// Toggle speaker state
  FutureOr<void> _onToggleSpeakerState(
    ToggleSpeakerStateEvent event,
    Emitter<RoomState> emit,
  ) async {
    if (_room!.speakerOn == null) return;

    if (_busy) return;
    _busy = true;

    try {
      final isSpeakerOn =
          event.isActive != null ? event.isActive! : !_room!.speakerOn!;
      await _room!.setSpeakerOn(isSpeakerOn, forceSpeakerOutput: isSpeakerOn);
      emit(RoomState.speakerStateChanged(isSpeakerOn));
    } catch (ex) {
      Log.e(name: 'RoomBloc._onToggleSpeakerState', ex);
    }
    _busy = false;
  }

  /// Toggle microphone state
  FutureOr<void> _onToggleMicroState(
    ToggleMicroStateEvent event,
    Emitter<RoomState> emit,
  ) async {
    final participant = _room!.localParticipant;
    if (participant == null) return;

    if (_busy) return;
    _busy = true;

    try {
      final microEnabled = event.isMute != null
          ? event.isMute!
          : !participant.isMicrophoneEnabled();
      await participant.setMicrophoneEnabled(microEnabled);
      emit(RoomState.microStateChanged(microEnabled));
    } catch (ex) {
      Log.e(name: 'RoomBloc._onToggleMicroState', ex);
    }

    _busy = false;
  }

  /// Toggle screen sharing for the local participant.
  FutureOr<void> _onToggleScreenShare(
    ToggleScreenShareEvent event,
    Emitter<RoomState> emit,
  ) async {
    final participant = _room!.localParticipant;
    if (participant == null) return;
    final shouldScreenShare = event.isScreenShare != null
        ? event.isScreenShare!
        : !participant.isScreenShareEnabled();

    if (shouldScreenShare) {
      await _enableScreenShare(participant);
    } else {
      await _disableScreenShare(participant);
    }
  }

  /// Change focus participant
  FutureOr<void> _onFocusParticipant(
    FocusParticipantEvent event,
    Emitter<RoomState> emit,
  ) async {
    emit(RoomState.focusParticipantChanged(event.participantId));
  }

  /// Sort participants
  FutureOr<void> _onSortParticipants(
    SortParticipantsEvent event,
    Emitter<RoomState> emit,
  ) {
    _initStreamMembers(
      channelId: _currentArgs!.channelId,
      workspaceId: _currentArgs!.workspaceId,
    );

    emit(RoomState.speakerStateChanged(_room!.speakerOn!));
    _sortParticipants();
  }

  /// Disconnect from room
  FutureOr<void> _onDisconnect(
    DisconnectEvent event,
    Emitter<RoomState> emit,
  ) async {
    if (_room?.connectionState == ConnectionState.disconnected) {
      emit(RoomState.disconnected());
    } else {
      emit(RoomState.disconnected());

      await _room?.localParticipant?.setScreenShareEnabled(
        false,
        screenShareCaptureOptions: _screenShareOptions,
      );
      if (lkPlatformIs(PlatformType.android) &&
          FlutterBackground.isBackgroundExecutionEnabled) {
        await FlutterBackground.disableBackgroundExecution();
      }
      await ForegroundServiceHandler.stopService();
      _room?.disconnect();
    }
  }

  //endregion Bloc Events

  /// Set up media devices
  void _setupMediaDevices() async {
    try {
      _hardwareSubscription?.cancel();
      _hardwareSubscription =
          Hardware.instance.onDeviceChange.stream.listen(_loadDevices);
      Hardware.instance.enumerateDevices().then(_loadDevices);
    } catch (e) {
      Log.e(name: 'RoomBloc._setupMediaDevices', e);
    }
  }

  /// Load video/audio devices
  void _loadDevices(List<MediaDevice> devices) async {
    _audioInputs =
        devices.where((d) => d.kind == DevicesKind.audioinput.name).toList();
    _audioOutputs =
        devices.where((d) => d.kind == DevicesKind.audiooutput.name).toList();
    _videoInputs =
        devices.where((d) => d.kind == DevicesKind.videoinput.name).toList();
  }

  /// Set up listeners for room events
  void _setUpListeners() {
    _listener = _room!.createListener();
    _listener!
      ..on<RoomDisconnectedEvent>((event) {
        add(DisconnectEvent());
        _leaveRoom();
      })
      ..on<ParticipantConnectedEvent>((event) {
        _sortParticipants();
      })
      ..on<ParticipantDisconnectedEvent>((event) {
        _sortParticipants();
      })
      ..on<LocalTrackPublishedEvent>((event) {
        _sortParticipants();
      })
      ..on<LocalTrackUnpublishedEvent>((event) {
        _sortParticipants();
      })
      ..on<TrackSubscribedEvent>((event) {
        _sortParticipants();
      })
      ..on<TrackUnsubscribedEvent>((event) {
        _sortParticipants();
      });
  }

  void _onRoomDidUpdate() {
    _sortParticipants();
  }

  /// Sort participants
  void _sortParticipants() {
    List<ParticipantTrack> tracks = [];

    if (_room!.localParticipant != null) {
      bool noVideoTrack = true;
      if (_room!.localParticipant!.videoTrackPublications.isNotEmpty) {
        for (final trackPub
            in _room!.localParticipant!.videoTrackPublications) {
          if (!trackPub.isScreenShare) {
            noVideoTrack = false;
          }
          tracks.add(
            _createParticipantTrack(
              _room!.localParticipant!,
              trackPub,
              isScreenShare: trackPub.isScreenShare,
              isMe: true,
            ),
          );
        }
      }
      if (noVideoTrack) {
        tracks.add(
          _createParticipantTrack(
            _room!.localParticipant!,
            null,
            isScreenShare: false,
            isMe: true,
          ),
        );
      }
    }

    for (final participant in _room!.remoteParticipants.values) {
      bool noVideoTrack = true;
      if (participant.videoTrackPublications.isNotEmpty) {
        for (final trackPub in participant.videoTrackPublications) {
          if (!trackPub.isScreenShare) {
            noVideoTrack = false;
          }
          tracks.add(
            _createParticipantTrack(
              participant,
              trackPub,
              isScreenShare: trackPub.isScreenShare,
              isMe: false,
            ),
          );
        }
      }
      if (noVideoTrack) {
        tracks.add(
          _createParticipantTrack(
            participant,
            null,
            isScreenShare: false,
            isMe: false,
          ),
        );
      }
    }
    final oldUserIds = _users.keys.toSet();
    final newUserIds = tracks
        .map((track) => track.participant.identity.split(':').first)
        .toSet();
    if (oldUserIds != newUserIds) {
      _onUpdateStreamUsers(newUserIds);
    }
    add(UpdateParticipantListEvent(participants: tracks));
    _currentRoomInfo = _currentRoomInfo!.copyWith(
      numberParticipant:
          tracks.where((track) => track.isScreenShare == false).length,
    );

    // Update cache RTCVideoRenderer
    List<String> participantIds = [];
    for (final participant in tracks) {
      _cachedRenderers[participant.id] ??= rtc.RTCVideoRenderer()..initialize();
      participantIds.add(participant.id);
    }

    final cacheRenderIds = List.from(_cachedRenderers.keys);
    for (final id in cacheRenderIds) {
      if (!participantIds.contains(id)) {
        _disposeRenderer(_cachedRenderers[id]);
        _cachedRenderers.remove(id);
      }
    }
  }

  /// Dispose cache renderer
  void _disposeRenderer(rtc.RTCVideoRenderer? renderer) {
    try {
      renderer?.srcObject = null;
      renderer?.dispose();
      renderer = null;
    } catch (e) {
      logger.warning('Got error disposing renderer: $e');
    }
  }

  /// Create participant track from participant
  ParticipantTrack _createParticipantTrack(
    Participant participant,
    TrackPublication? trackPub, {
    bool isScreenShare = false,
    bool isMe = false,
  }) {
    final userId = participant.identity.split(':').first;
    final user = _users[userId];
    final aliasName = _aliasNames[userId];
    final nickname = _nicknames[userId];
    final userName = participant.name;

    final displayName = !StringUtils.isNullOrEmpty(aliasName)
        ? aliasName!
        : !StringUtils.isNullOrEmpty(nickname)
            ? nickname!
            : user?.name ?? userName;

    final id = '${participant.sid}:${trackPub?.sid}';
    return ParticipantTrack(
      id: id,
      participant: participant,
      videoTrackPublication: trackPub,
      userId: userId,
      avatarUrl: user?.avatar ?? '',
      displayName: displayName,
      isMe: isMe,
      isScreenShare: isScreenShare,
    );
  }

  /// Enables screen sharing for the local participant.
  ///
  /// - On Android, it requests screen capture and background execution permissions.
  /// - Initializes background mode with a notification if required.
  /// - Automatically retries background permission once on failure.
  /// - Activates screen sharing with audio capture enabled.
  Future<void> _enableScreenShare(LocalParticipant participant) async {
    if (lkPlatformIs(PlatformType.android)) {
      // Required for android screen share.
      final hasCapturePermission = await Helper.requestCapturePermission();
      if (!hasCapturePermission) {
        return;
      }

      final backgroundPermissionGranted = await _requestBackgroundPermission();
      if (!backgroundPermissionGranted) return;
    }

    await participant.setScreenShareEnabled(
      true,
      screenShareCaptureOptions: _screenShareOptions,
    );
  }

  /// Requests background execution permission for Android screen sharing.
  ///
  /// - Initializes [FlutterBackground] if not already granted.
  /// - Enables background execution if allowed.
  /// - Automatically retries once on failure.
  /// - Returns `true` if permission and execution are granted.
  Future<bool> _requestBackgroundPermission([bool isRetry = false]) async {
    try {
      bool hasPermissions = await FlutterBackground.hasPermissions;
      if (!isRetry) {
        const androidConfig = FlutterBackgroundAndroidConfig(
          notificationTitle: 'Screen Sharing',
          notificationText: 'ZiiChat is sharing the screen.',
          notificationImportance: AndroidNotificationImportance.normal,
        );
        hasPermissions = await FlutterBackground.initialize(
          androidConfig: androidConfig,
        );
      }
      if (hasPermissions && !FlutterBackground.isBackgroundExecutionEnabled) {
        return await FlutterBackground.enableBackgroundExecution();
      }
    } catch (e) {
      if (!isRetry) {
        await Future<bool>.delayed(const Duration(seconds: 1));
        return await _requestBackgroundPermission(true);
      }
      Log.e(name: 'RoomBloc.requestBackgroundPermission', e);
    }
    return false;
  }

  /// Disables screen sharing for the local participant.
  ///
  /// - Stops active screen sharing session.
  /// - On Android, disables background execution mode.
  Future<void> _disableScreenShare(LocalParticipant participant) async {
    if (lkPlatformIs(PlatformType.android)) {
      // Android specific
      try {
        await participant.setScreenShareEnabled(
          false,
          screenShareCaptureOptions: _screenShareOptions,
        );
        await FlutterBackground.disableBackgroundExecution();
      } catch (error) {
        Log.e(name: 'RoomBloc._disableScreenShare', error);
      }
    }
    if (lkPlatformIs(PlatformType.iOS)) {
      BroadcastManager().requestActivation();
    }
  }

  //region Room info, user info
  /// Update stream users information
  void _onUpdateStreamUsers(
    Set<String> userIds,
  ) {
    final output =
        _getStreamUsersUseCase.execute(GetStreamUsersInput(userIds: userIds));
    if (output.streamUsers == null) return;
    _usersSubscription?.cancel();
    _usersSubscription = output.streamUsers!.listen((mapUsers) {
      _users = {
        for (var map in mapUsers)
          map['userId']: CallUser(
            userId: map['userId'],
            username: map['username'],
            displayName: map['profile']['displayName'],
            avatar: map['profile']['avatar'],
          ),
      };
    });
  }

  /// Updates the alias name map from a list of user private data.
  ///
  /// - Maps each userId to its aliasName.
  /// - Defaults to empty string if aliasName is null.
  void _userPrivateDataListener(
    List<UserPrivateData> listUser,
  ) {
    _aliasNames = {
      for (var user in listUser) user.userId: user.aliasName ?? '',
    };
  }

  /// Initializes and subscribes to stream members for a given channel.
  ///
  /// - Fetches member data from the use case layer.
  /// - Cancels previous subscription if exists.
  /// - Updates local nickname map on data change.
  void _initStreamMembers({
    required String channelId,
    required String workspaceId,
  }) {
    final output = _getStreamMembersUseCase.execute(
      GetStreamMembersInput(channelId: channelId, workspaceId: workspaceId),
    );
    if (output.streamMembers == null) return;
    _membersSubscription?.cancel();
    _membersSubscription = output.streamMembers!.listen((mapMembers) {
      _nicknames = {
        for (var map in mapMembers) map['userId']: map['nickname'],
      };
    });
  }

  //endregion Room info, user info

  /// Cleans up and exits the current room session.
  ///
  /// - Removes listeners and disconnects from the room.
  /// - Cancels all active subscriptions and timers.
  /// - Resets local state and notifies room listeners.
  void _leaveRoom() {
    WakelockPlus.disable();
    _room?.removeListener(_onRoomDidUpdate);
    _listener?.dispose();
    _room = null;
    _currentArgs = null;
    _callDurationManager.stop();
    _callDurationListeners.clear();
    _hardwareSubscription?.cancel();
    _endRoomEventSubscription?.cancel();
    _usersSubscription?.cancel();
    _usersPrivateDataSubscription?.cancel();
    _membersSubscription?.cancel();
    _roomListeners.forEach((action) => action(null));
    _currentRoomInfo = null;
    for (final id in _cachedRenderers.keys) {
      _disposeRenderer(_cachedRenderers[id]);
    }
    _cachedRenderers.clear();
  }
}
