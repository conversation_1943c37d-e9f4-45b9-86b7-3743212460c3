import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../common/di/di.dart';
import '../../navigation/routes/app_router.dart';

class AppPopScope extends StatefulWidget {
  const AppPopScope({
    required this.child,
    super.key,
  });

  final Widget child;

  @override
  State<AppPopScope> createState() => _AppPopScopeState();
}

class _AppPopScopeState extends State<AppPopScope> {
  final timeout = Duration(seconds: 2);
  Timer? _timer;
  ValueNotifier<bool> _showSnackBarNotifier = ValueNotifier(false);

  bool _canPop() {
    return getIt<AppRouter>().stack.length > 1;
  }

  void _onPopInvokedWithResult(bool didPop, _) {
    if (didPop) {
      return;
    }
    if (_timer?.isActive ?? false) {
      _timer?.cancel();
      _timer = null;
      _showSnackBarNotifier.value = false;
      SystemNavigator.pop();
      return;
    }
    if (_timer == null) {
      _showSnackBarNotifier.value = true;
      _timer = Timer(timeout, () {
        _showSnackBarNotifier.value = false;
        _timer = null;
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _timer = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _canPop(),
      onPopInvokedWithResult: _onPopInvokedWithResult,
      child: Stack(
        children: [
          widget.child,
          _buildToast(),
        ],
      ),
    );
  }

  Widget _buildToast() {
    return ValueListenableBuilder(
      valueListenable: _showSnackBarNotifier,
      builder: (_, showSnack, __) {
        Widget child = SizedBox.shrink();
        if (showSnack) {
          child = Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).padding.bottom + 16.h,
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.gray20,
                  borderRadius: BorderRadius.all(
                    Radius.circular(8.r),
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 8.h,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        child: Text(
                          AppLocalizations.of(context)!.tapAgainToExit,
                          textAlign: TextAlign.left,
                          maxLines: 10,
                          overflow: TextOverflow.ellipsis,
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: Colors.white,
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }

        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 250),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(opacity: animation, child: child);
          },
          child: child,
        );
      },
    );
  }
}
