import 'package:flutter/material.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../data/repositories/extensions/message_extension.dart';
import '../base/base_sender_widget.dart';

class LinkOwnerWidgetImpl extends BaseSenderWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final void Function(MessageItem messageItem)? onQuoteMessageClicked;
  final bool isShowCreateTime;
  final bool isHiddenPin;
  final bool isLastMessage;

  LinkOwnerWidgetImpl({
    required this.isOpenCheckBox,
    this.onCheckBoxButtonTap,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    this.quoteMessage,
    this.isShowCreateTime = false,
    this.isLastMessage = false,
    this.isHiddenPin = false,
    this.onQuoteMessageClicked,
    required super.messageItem,
    required super.message,
    super.key,
  });

  final QuoteMessage? quoteMessage;

  @override
  Widget build(BuildContext context) {
    var embed = (message.embed != null && message.embed!.isNotEmpty)
        ? message.embed!.first
        : null;
    var embedData = embed?.embedData;

    var linkMessage = LinkMessage(
      messageContent: message.argsContent()!,
      title: embedData?.title,
      description: embedData?.description,
      imageUrl: embedData?.thumbnailUrl,
    );

    return LinkMessageSenderWidget(
      messageItem: messageItem,
      mentions: message.mentions ?? [],
      onEmojiClicked: onEmojiClicked,
      onQuote: onQuote,
      isShowCreateTime: isShowCreateTime,
      onCopy: (messageItem) => onCopy(context, messageItem),
      onEdit: onEdit,
      isHiddenPin: isHiddenPin,
      onTranslateMessage: onTranslateMessage,
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      emojiList: message.emojiList,
      onMessageItemClicked: onMessageItemClicked,
      onListReactionClicked: onListReactionClicked,
      linkMessage: linkMessage,
      onOpenLink: (MessageItem messageItem) {
        onClickLink(embedData!.url!);
      },
      onUsernameClicked: (username) => onClickMention(context, username),
      onLinkClicked: onClickLink,
      onResend: onResendMessage,
      onDiscard: onDiscardMessage,
      isCheckedMessage: isCheckedMessage,
      isOpenCheckBox: isOpenCheckBox,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      quoteMessage: quoteMessage,
      onQuoteMessageClicked: onQuoteMessageClicked,
      isHideOptionResend: false,
      isHideOptionCopy: false,
      isHighlighted: isHighlighted,
      shouldAnimate: shouldAnimate,
    );
  }
}
