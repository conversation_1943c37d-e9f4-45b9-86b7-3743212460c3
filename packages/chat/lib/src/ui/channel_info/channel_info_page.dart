import 'dart:async';
import 'dart:io';

import 'package:app_core/core.dart' hide Config;
import 'package:async/async.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:upload_manager/upload_manager.dart' hide Config;
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import '../../common/di/di.dart';
import '../../data/repositories/database/entities/channel_local_metadata.dart';
import '../../data/repositories/database/enums/user_badge_enum.dart';
import '../../domain/usecase/member/leave_channel_use_case.dart';
import '../member_settings/member_settings_handler.dart';
import '../member_settings/model_member_setting_profile.dart';
import '../translate_to/bloc/translate_to_bloc.dart';
import 'bloc/channel_info_bloc.dart';

class ChannelInfoPage extends StatefulWidget {
  const ChannelInfoPage({
    required this.interface,
    this.channel,
    this.channelId,
    this.workspaceId,
    this.updateAvatarChannel,
    required this.isLoadingNewAvatar,
    this.initIsLoadingNewAvatar,
    super.key,
  }) : assert(
          channel != null || (channelId != null && workspaceId != null),
          'chanel must not be null or channelId and workspaceId must not be null',
        );

  final Channel? channel;
  final String? channelId;
  final String? workspaceId;
  final ChannelInfoInterface interface;
  final void Function(UploadFile file)? updateAvatarChannel;
  final ValueNotifier<bool> isLoadingNewAvatar;
  final VoidCallback? initIsLoadingNewAvatar;

  @override
  State<ChannelInfoPage> createState() => _ChannelInfoPageState();
}

class _ChannelInfoPageState
    extends BasePageState<ChannelInfoPage, ChannelInfoBloc>
    with AutoRouteAwareStateMixin<ChannelInfoPage>
    implements ui.ChannelSettingPageInterface {
  Channel? _channel;
  String? _workspaceId;
  String? _channelId;
  ChannelLocalMetadata? _metadata;
  Map<String, Member> _members = {};
  Map<String, ChatUser> _users = {};
  late AppLocalizations appLocalizations;
  String _myUserId = Config.getInstance().activeSessionKey ?? '';
  late ModelMemberSettingProfile _myProfile;
  final _memberSettingsHandler = MemberSettingsHandler();
  Iterable<ModelMemberSettingProfile> _memberProfiles = [];
  late final UserPrivateDataBloc _userPrivateDataBloc;
  late final ChannelInfoBloc _channelInfoBloc;
  List<UserPrivateData> _listUserPrivateData = [];

  ui.Roles? _myRoles;

  bool get _isRoleMember => _myRoles == ui.Roles.member;
  final _appEventBus = GetIt.instance.get<AppEventBus>();
  StreamSubscription? _subscription;
  ValueNotifier<String> _name = ValueNotifier('');
  late SettingNotificationBloc _settingNotificationBloc;

  // **TranslateTo** liên quan
  late final TranslateToBloc _translateToBloc;
  late final TranslateToHandler _translateToHandler;
  bool _translateToBlocInitiated = false;

  // Dialog process
  ValueNotifier<ui.ProcessStatus> processStatus =
      ValueNotifier(ui.ProcessStatus.loading);
  ValueNotifier<String> processContent = ValueNotifier("");

  int badgeEnum = 0;
  ui.UserBadgeType? userBadgeType;

  @override
  void initState() {
    super.initState();
    _workspaceId = widget.workspaceId ?? widget.channel!.workspaceId;
    _channelId = widget.channelId ?? widget.channel!.channelId;
    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    widget.initIsLoadingNewAvatar?.call();

    _channelInfoBloc = getIt<ChannelInfoBloc>();
    _channelInfoBloc.add(
      InitiateChannelInfoEvent(
        channel: widget.channel,
        channelId: widget.channelId,
        workspaceId: widget.workspaceId,
      ),
    );

    // **Khởi tạo TranslateToBloc và TranslateToHandler**
    _translateToHandler = getIt<TranslateToHandler>();
    _translateToBloc = _translateToHandler.translateToBloc;
    _initTranslateBlocIfNeeded();

    _subscription = StreamGroup.merge([
      _appEventBus.on<ChooseCoverEvent>(),
      _appEventBus.on<ChooseAvatarEvent>(),
    ]).listen((event) async {
      switch (event.runtimeType) {
        case ChooseAvatarEvent:
          await _handleChooseAvatarEvent(event as ChooseAvatarEvent);
          break;
        default:
          _showErrorSnackBar(context, "Unhandled event: ${event.runtimeType}");
      }
    });
    _settingNotificationBloc = getIt<SettingNotificationBloc>();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    // _channelInfoBloc.add(
    //   InitiateChannelInfoEvent(
    //     channel: widget.channel,
    //     channelId: widget.channelId,
    //     workspaceId: widget.workspaceId,
    //   ),
    // );
  }

  void _initTranslateBlocIfNeeded() {
    _translateToHandler.initTranslateBlocIfNeeded(
      workspaceId: _workspaceId,
      channelId: _channelId,
    );
  }

  void _onTranslateToBlocStateChanged(
    BuildContext context,
    TranslateToState state,
  ) {
    _translateToHandler.handleTranslateToState(
      context: context,
      state: state,
      onLoaded: (metadata, translateResultList) {
        setState(() {
          _metadata = metadata;
        });
      },
    );
  }

  void changeProcessDialog(ui.ProcessStatus status, String content) {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      processContent.value = content;
      processStatus.value = status;
      Future.delayed(const Duration(seconds: 2), () {
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }
      });
    });
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
        setState(() {});
      },
    );
  }

  //=== TranslateTo: onClickTranslateTo ===//
  @override
  void onClickTranslateTo(BuildContext context) {
    _translateToHandler.onClickTranslateTo(
      context: context,
      metadata: _metadata,
      onLanguageUpdated: (ChannelLocalMetadata? newMetadata) {
        setState(() {
          _metadata = newMetadata;
        });
      },
    );
  }

  //=== End ===//

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((user) => user.userId == userId);
      return userPrivateData.aliasName;
    } catch (error) {
      return null;
    }
  }

  void _blocSettingNotification(
    BuildContext context,
    SettingNotificationState state,
  ) {
    state.maybeWhen(
      subscribeChannel: (response) {
        if (response == true) {
          _channel?.notificationStatus = true;
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
      },
      unsubscribeChannel: (response) {
        if (response == true) {
          _channel?.notificationStatus = false;
        } else {
          ui.DialogUtils.showErrorOccurredTranslateDialog(
            context,
            onOkClicked: () {
              Navigator.pop(context);
            },
          );
        }
      },
      orElse: () {},
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    appLocalizations = AppLocalizations.of(context)!;
    return MultiBlocProvider(
      providers: [
        BlocProvider<ChannelInfoBloc>.value(value: _channelInfoBloc),
        BlocProvider<UserPrivateDataBloc>.value(value: _userPrivateDataBloc),
        BlocProvider<TranslateToBloc>.value(value: _translateToBloc),
        BlocProvider<SettingNotificationBloc>.value(
          value: _settingNotificationBloc,
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocUserPrivateListener,
          ),
          // **Lắng nghe TranslateToBloc**
          BlocListener<TranslateToBloc, TranslateToState>(
            listenWhen: (prev, state) => prev != state,
            listener: _onTranslateToBlocStateChanged,
          ),
          BlocListener<SettingNotificationBloc, SettingNotificationState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocSettingNotification,
          ),
        ],
        child: BlocBuilder<ChannelInfoBloc, ChannelInfoState>(
          builder: (context, state) {
            Widget child = const SizedBox.shrink();
            state.when(
              initial: () {
                child = const ui.ChannelInfoSkeletonPage();
              },
              loading: () {
                child = const ui.ChannelInfoSkeletonPage();
              },
              loaded: (channel, members, users) {
                _channel = channel;
                _members = members;
                _users = users;
                _workspaceId = channel.workspaceId;
                _channelId = channel.channelId;
                _initTranslateBlocIfNeeded();
                if (_members[_myUserId] == null) {
                  child = const ui.ChannelInfoSkeletonPage();
                } else {
                  _myRoles = MemberSettingsHandler.getRoleFromName(
                    _members[_myUserId]!.role,
                  );

                  _name.value = _channel?.name ?? '';
                  child = ui.ChannelSettingPage(
                    interface: this,
                    onProfileClicked: _onTapCoverPhoto,
                    name: _name,
                  );
                }
              },
              showProcessDialog: () {
                WidgetsBinding.instance.addPostFrameCallback((_) async {
                  if (!mounted) return;
                  processContent.value = appLocalizations.clearingMessages;
                  processStatus.value = ui.ProcessStatus.loading;
                  ui.DialogUtils.showProcessStatusDialog(
                    context,
                    processStatus: processStatus,
                    processContent: processContent,
                  );
                });
                child = ui.ChannelSettingPage(
                  interface: this,
                  onProfileClicked: _onTapCoverPhoto,
                  name: _name,
                );
              },
              updateProcessDialog: (response) {
                WidgetsBinding.instance.addPostFrameCallback((_) async {
                  if (!mounted) return;
                  if (response) {
                    changeProcessDialog(
                      ui.ProcessStatus.success,
                      appLocalizations.messagesRemoved,
                    );
                    AppEventBus.publish(
                      ClearMessageEvent(
                        workspaceId: _channel?.workspaceId,
                        channelId: _channel?.channelId,
                      ),
                    );
                  } else {
                    changeProcessDialog(
                      ui.ProcessStatus.failed,
                      appLocalizations.clearingProcessFailed,
                    );
                  }
                });
                child = ui.ChannelSettingPage(
                  interface: this,
                  onProfileClicked: _onTapCoverPhoto,
                  name: _name,
                );
              },
              channelAvatarChanged: (String? avatarPath) {
                _channel?.avatar = avatarPath;
                child = ui.ChannelSettingPage(
                  interface: this,
                  onProfileClicked: _onTapCoverPhoto,
                  name: _name,
                );
              },
              refresh: () {
                child = ui.ChannelSettingPage(
                  interface: this,
                  onProfileClicked: _onTapCoverPhoto,
                  name: _name,
                );
              },
            );
            return child;
          },
        ),
      ),
    );
  }

  @override
  String? avatarPath() {
    return UrlUtils.parseAvatar(_channel?.avatar);
  }

  @override
  String channelDescription() {
    return '';
  }

  @override
  String coverPath() {
    return '';
  }

  @override
  double getAvatarWidth() {
    return 95.w;
  }

  @override
  List<ui.ItemMemberSettingProfile> getListMemberChannelProfile() {
    if (_members.isEmpty && _users.isEmpty) {
      return [];
    }

    _memberProfiles = _members.values.map(
      (member) {
        final user = _users[member.userId]!;

        badgeEnum = user.profile?.userBadgeType ?? 0;
        userBadgeType =
            UserBadgeEnumExtension.getEnumByValue(badgeEnum).toUserBadgeType();
        final profile = ModelMemberSettingProfile(
          userId: member.userId,
          userBadgeType: userBadgeType,
          workspaceId: member.workspaceId,
          channelId: member.channelId,
          username: user.username ?? '',
          aliasName: user.username == GlobalConfig.ghost
              ? user.username
              : getAliasName(member.userId),
          displayName: user.profile?.displayName,
          nickname: user.username == GlobalConfig.ghost ? '' : member.nickname,
          role: MemberSettingsHandler.getRoleFromName(member.role),
          avatarUrl: UrlUtils.parseAvatar(user.profile?.avatar),
          isOnline: user.presenceData?.isOnline ?? false,
        );
        if (member.userId == _myUserId) {
          _myProfile = profile;
        }
        return profile;
      },
    );
    return _memberProfiles
        .map((profile) => profile.toItemMemberSettingProfile())
        .toList();
  }

  @override
  bool hasNotification() {
    return true;
  }

  @override
  bool isFinishedInviteToChannel() {
    return _members.length > 1;
  }

  @override
  bool isFinishedOverview() {
    return isFinishedSetChannelAvatar() &&
        isFinishedSetChannelDescription() &&
        isFinishedSetDisplayName() &&
        isFinishedInviteToChannel();
  }

  @override
  bool isFinishedSetChannelAvatar() {
    return _hasAvatar() || _isRoleMember;
  }

  @override
  bool isFinishedSetChannelDescription() {
    return _hasDescription() || _isRoleMember;
  }

  @override
  bool isFinishedSetDisplayName() {
    return _hasDisplayName() || _isRoleMember;
  }

  @override
  ValueNotifier<bool> isInviteToChannel() {
    return ValueNotifier(_members.length > 1);
  }

  @override
  ValueNotifier<bool> isSetChannelAvatar() {
    return ValueNotifier(_hasAvatar());
  }

  @override
  ValueNotifier<bool> isSetChannelDescription() {
    return ValueNotifier(_hasDescription());
  }

  @override
  bool isSetDisplayName() {
    return _hasDisplayName();
  }

  bool _hasAvatar() => ui.StringUtil.stringIsNotEmpty(_channel?.avatar);

  bool _hasDisplayName() => ui.StringUtil.stringIsNotEmpty(_channel?.name);

  bool _hasDescription() => true; // (đoạn code gốc, placeholder)

  @override
  void onBackButtonPressed(BuildContext context) {
    widget.interface.onTapBack();
  }

  @override
  void onCLickAddDisplayName(
    BuildContext context,
    ui.ItemOverviewCards itemOverviewCards,
  ) {
    Log.d("onCLickAddDisplayName");
  }

  @override
  void onCLickInviteToChannel(
    BuildContext context,
    ui.ItemOverviewCards itemOverviewCards,
  ) {
    if (isFinishedInviteToChannel()) return;
    ui.BottomSheetUtil.showDefaultBottomSheet(
      context: context,
      child: InviteToChannelBottomSheet(
        channelId: _channel!.channelId,
        workspaceId: _channel!.workspaceId,
      ),
    );
  }

  @override
  void onCLickCardSetChannelAvatar(
    BuildContext context,
    ui.ItemOverviewCards itemOverviewCards,
  ) {
    if (_hasAvatar()) {
      return;
    }
    onCLickSetChannelAvatar(context);
  }

  @override
  void onCLickSetChannelAvatar(BuildContext context) {
    if (widget.isLoadingNewAvatar.value) return;
    if (_isRoleMember && !_hasAvatar()) {
      return;
    }
    if (_isRoleMember) {
      widget.interface.onGoToViewImagePage(avatarPath()!);
      return;
    }
    ui.ActionSheetUtil.showSetAvatarChannel1NlActionSheet(
      context,
      isRoleMember: _isRoleMember,
      onTapOpenGallery: _onTapOpenGallery,
      onTapTakePhoto: _onTapTakeChannelAvatarPhoto,
      onTapCancel: () {
        Navigator.of(context).pop();
      },
      hasAvatar: _hasAvatar(),
      onTapViewAvatar: _onTapViewAvatar,
      onTapRemove: _onTapRemoveAvatar,
    );
  }

  void _onTapViewAvatar() {
    if (_channel?.avatar?.isEmpty ?? true) return;
    final avatarUrl = UrlUtils.parseAvatar(_channel!.avatar!);
    Navigator.of(context).pop();
    widget.interface.onGoToViewImagePage(avatarUrl);
  }

  void _onTapRemoveAvatar() {
    if (!_hasAvatar()) return;
    Navigator.of(context).pop();
    _channelInfoBloc.add(
      DeleteChannelAvatarEvent(
        workspaceId: _channel!.workspaceId,
        channelId: _channel!.channelId,
      ),
    );
  }

  Future<void> _onTapTakeChannelAvatarPhoto() async {
    // Check if the user is in a meeting room
    if (getIt<MeetingHandler>().hasJoinedMeetingRoom(context: context)) return;

    final isGranted = await PermissionUtils.requestCameraPermission(context);
    if (isGranted) {
      Navigator.of(context).pop();
      widget.interface.onClickTakeChannelAvatarPhoto(_channel!);
    }
  }

  Future<void> _onTapOpenGallery() async {
    final isGranted = Platform.isIOS
        ? await PermissionUtils.requestImagePermission(context)
        : true;
    if (isGranted) {
      Navigator.of(context).pop();
      widget.interface.onClickTapOpenGalleryAvatar(_channel!);
    }
  }

  void _showErrorSnackBar(BuildContext context, String? errorMessage) {
    SnackBarOverlayHelper().showSnackBar(
      widgetBuilder: (T) {
        return ui.SnackBarUtilV2.showFloatingSnackBar(
          context: context,
          snackBarType: ui.SnackBarType.warning,
          content: errorMessage ?? '',
        );
      },
    );
  }

  Future<void> _handleChooseAvatarEvent(ChooseAvatarEvent event) async {
    if (event.avatarType != AvatarType.channel) return;
    final avatar = XFile(event.filePath);
    final avatarUploadFile = UploadFile(
      path: avatar.path,
      name: avatar.name,
      size: await avatar.length(),
    );
    widget.isLoadingNewAvatar.value = true;
    widget.updateAvatarChannel?.call(avatarUploadFile);
  }

  @override
  void onCLickSetChannelDescription(
    BuildContext context,
    ui.ItemOverviewCards itemOverviewCards,
  ) {
    Log.d("onCLickSetChannelDescription");
  }

  @override
  void onClickBlockedUser() {
    Log.d("onClickBlockedUser");
  }

  @override
  void onClickPrivacyAndSecurity() {
    Log.d("onClickPrivacyAndSecurity");
  }

  @override
  void onEditChannelInfoButton() {
    widget.interface.onTapEdit(_channel!, _isRoleMember);
  }

  @override
  void onInviteButton() {
    ui.BottomSheetUtil.showDefaultBottomSheet(
      context: context,
      child: InviteToChannelBottomSheet(
        channelId: _channel!.channelId,
        workspaceId: _channel!.workspaceId,
      ),
    );
  }

  @override
  void onMemberCardPressed(
    BuildContext context,
    ui.ItemMemberSettingProfile member,
    ui.ItemMemberSection section,
  ) {
    if (member.userId != _myUserId) {
      AppEventBus.publish(OnGoToUserProfileEvent(userId: member.userId));
    }
  }

  @override
  void onShareButton() {
    final channelUrl = _channel?.invitationLink ?? '';
    ui.BottomSheetUtil.showChannelQRBottomSheet(
      context,
      channelName: _channel?.name ?? '',
      channelUrl: channelUrl,
      onShared: (_) {},
      onCopyLink: () {
        Clipboard.setData(ClipboardData(text: channelUrl));
        SnackBarOverlayHelper().showSnackBar(
          widgetBuilder: (T) {
            return ui.SnackBarUtilV2.showCopied(
              context,
              appLocalizations: AppLocalizations.of(context)!,
            );
          },
        );
      },
      avatarImageUrl: UrlUtils.parseAvatar(_channel?.avatar),
      qrImage: ui.AppAssets.iconZiichatQR,
    );
  }

  @override
  bool showNotification() {
    return _channel?.notificationStatus ?? true;
  }

  @override
  void updateNotification({required bool notification}) {
    if (notification == true) {
      _settingNotificationBloc.add(
        OnSubscribeChannelEvent(
          workspaceId: _channel?.workspaceId,
          channelId: _channel?.channelId,
        ),
      );
    } else {
      _settingNotificationBloc.add(
        OnUnsubscribeChannelEvent(
          workspaceId: _channel?.workspaceId,
          channelId: _channel?.channelId,
        ),
      );
    }
  }

  void onClickLeaveChannel(BuildContext context) {
    ui.ActionSheetUtil.showLeaveChannelActionSheet(
      context,
      onLeave: () async {
        final output = await GetIt.instance.get<LeaveChannelUseCase>().execute(
              LeaveChannelInput(
                workspaceId: _channel!.workspaceId,
                channelId: _channel!.channelId,
              ),
            );
        if (output.ok) {
          await GetIt.instance.get<RemoveChannelUseCase>().execute(
                RemoveChannelInput(
                  workspaceId: _channel!.workspaceId,
                  channelId: _channel!.channelId,
                ),
              );
          AppEventBus.publish(PopToHomeEvent());

          // Notify the end meeting room event
          AppEventBus.publish(
            EndMeetingRoomEvent(
              workspaceId: _channel!.workspaceId,
              channelId: _channel!.channelId,
            ),
          );
        }
      },
      onCancel: () {
        Navigator.pop(context);
      },
    );
  }

  void onClickDeleteChannel(BuildContext context) {
    widget.interface.onTapDeleteChannel(_channel!);
  }

  void onClickDeleteChat(BuildContext context) {
    ui.ActionSheetUtil.showClearMessagesActionSheet(
      context,
      onRemove: () {
        Navigator.pop(context);
        _channelInfoBloc.add(
          ClearMessageAllForMeEvent(
            workspaceId: _channel?.workspaceId,
            channelId: _channel?.channelId,
            userId: null,
          ),
        );
      },
      onCancel: () {
        Navigator.pop(context);
      },
    );
  }

  @override
  void onClickNotificationCard(BuildContext context) {
    // TODO: implement onClickNotificationCard
  }

  @override
  void onClickTransferOwnership(BuildContext context) {
    if (_myRoles != ui.Roles.owner) return;
    ui.ActionSheetUtil.showTransferOwnershipActionSheet(
      context,
      onTransferTo: () {
        Navigator.of(context).pop();
        widget.interface.onGoToTransferOwnershipPage(
          channel: _channel!,
          isTransferAndLeave: false,
        );
      },
      onTransferAndLeave: () {
        Navigator.of(context).pop();
        widget.interface.onGoToTransferOwnershipPage(
          channel: _channel!,
          isTransferAndLeave: true,
        );
      },
      onCancel: () {
        Navigator.of(context).pop();
      },
    );
  }

  @override
  Uint8List? avatarData() {
    return null;
  }

  @override
  Uint8List? coverData() {
    return null;
  }

  @override
  void onLongPressedMemberCardPressed(
    BuildContext context,
    ui.ItemMemberSettingProfile profile,
    ui.ItemMemberSection section,
  ) {
    final user = _users[profile.userId];
    if (user == null) {
      return;
    }
    final memberProfile =
        _memberProfiles.firstWhere((m) => m.userId == profile.userId);

    _memberSettingsHandler.showMemberSettings(
      context: context,
      myProfile: _myProfile,
      memberProfile: memberProfile,
      goToDMMessage: widget.interface.onGoToDMMessage,
      goToViewAvatar: widget.interface.onGoToViewImagePage,
      sourcePage: MemberSettingSourcePage.channelInfo,
    );
  }

  void _onTapCoverPhoto() {}

  @override
  void onDescriptionClick() {}

  bool isRoleMember() {
    return _isRoleMember;
  }

  @override
  void dispose() {
    _subscription?.cancel();
    _translateToHandler.dispose();
    super.dispose();
  }

  @override
  ui.ItemMemberSettingProfile myProfile() {
    final member = _members[_myUserId];
    return ui.ItemMemberSettingProfile(
      userId: Config.getInstance().activeSessionKey ?? '',
      name: _name.value,
      role: MemberSettingsHandler.getRoleFromName(member?.role),
      nickName: member?.nickname ?? '',
      isOnline: false,
    );
  }

  @override
  String translateToLanguage() {
    if (_metadata == null) return '';

    return _translateToHandler.getLangNameFromLanguageCode(
          context,
          _metadata!.translateToLanguage!,
        ) ??
        '';
  }

  @override
  ValueNotifier<bool> isLoadingNewAvatar() {
    return widget.isLoadingNewAvatar;
  }

  @override
  Future<void> onClickCall() async {
    try {
      final callHandler = getIt<MeetingHandler>();
      final currentRoom = callHandler.getCurrentRoom(
        channelId: _channel!.channelId,
        workspaceId: _channel!.workspaceId,
      );
      if (currentRoom == null) {
        final canCall =
            await _forcePermissionsForCallIsGranted(requestCamera: false);
        if (!canCall) return;
        LoadingOverlayHelper.showLoading(context);
        await getIt<MeetingHandler>().getRoomToken(
          channelId: _channel!.channelId,
          workspaceId: _channel!.workspaceId,
          onSuccess: (token, host) => _connectToRoom(token, host, false),
          onError: (error) {
            LoadingOverlayHelper.hideLoading(context);
            _showErrorOccurredDialog();
          },
        );
        return;
      }

      widget.interface.onGoToCallRoomPage(
        room: currentRoom,
        channel: _channel!,
        isVideoCall: false,
      );
    } on InOtherRoomException {
      _showCannotMakeThisCallDialog();
    }
  }

  @override
  Future<void> onClickCallVideo() async {
    try {
      final callHandler = getIt<MeetingHandler>();
      final currentRoom = callHandler.getCurrentRoom(
        channelId: _channel!.channelId,
        workspaceId: _channel!.workspaceId,
      );
      if (currentRoom == null) {
        final canCall =
            await _forcePermissionsForCallIsGranted(requestCamera: true);
        if (!canCall) return;
        LoadingOverlayHelper.showLoading(context);
        await callHandler.getRoomToken(
          channelId: _channel!.channelId,
          workspaceId: _channel!.workspaceId,
          onSuccess: (token, host) => _connectToRoom(token, host, true),
          onError: (error) {
            LoadingOverlayHelper.hideLoading(context);
            _showErrorOccurredDialog();
          },
        );
        return;
      }

      widget.interface.onGoToCallRoomPage(
        room: currentRoom,
        channel: _channel!,
        isVideoCall: true,
      );
    } on InOtherRoomException {
      _showCannotMakeThisCallDialog();
    }
  }

  /// Connect to room with token and host
  void _connectToRoom(String token, String host, bool isVideoCall) {
    getIt<MeetingHandler>().connectToRoom(
      token: token,
      host: host,
      onConnected: (room) {
        LoadingOverlayHelper.hideLoading(context);
        widget.interface.onGoToCallRoomPage(
          room: room,
          channel: _channel!,
          isVideoCall: isVideoCall,
        );
      },
      onError: (error) {
        LoadingOverlayHelper.hideLoading(context);
        _showErrorOccurredDialog();
      },
    );
  }

  /// Requests microphone and optionally camera permissions.
  Future<bool> _checkPermissionsForCall({bool requestCamera = true}) async {
    final completer = Completer<bool>();
    bool _isOpenSetting = false;
    PermissionUtils.requestMicrophonePermission(
      context,
      onOpenSetting: () {
        _isOpenSetting = true;
      },
    ).then((_) {
      if (_isOpenSetting) {
        return completer.complete(false);
      }
      if (requestCamera) {
        PermissionUtils.requestCameraPermission(
          context,
          onOpenSetting: () {
            _isOpenSetting = true;
          },
        ).then((_) {
          if (_isOpenSetting) {
            return completer.complete(false);
          }
          return completer.complete(true);
        });
      } else {
        return completer.complete(true);
      }
    });
    return completer.future;
  }

  /// Requests and checks microphone (and optionally camera) permissions for a call.
  ///
  /// - Always requests microphone permission.
  /// - If [requestCamera] is true, also requests camera permission.
  /// - Returns `true` only if all required permissions are granted.
  Future<bool> _forcePermissionsForCallIsGranted({
    bool requestCamera = true,
  }) async {
    final microGranted =
        await PermissionUtils.requestMicrophonePermission(context);

    if (!microGranted) return false;

    if (requestCamera) {
      return await PermissionUtils.requestCameraPermission(context);
    }

    return true;
  }

  /// Displays a dialog indicating that the user cannot make the call.
  void _showCannotMakeThisCallDialog() {
    ui.DialogUtils.showCannotMakeThisCallDialog(
      context,
      onClickOk: (context) {
        Navigator.pop(context);
      },
    );
  }

  /// Displays a dialog indicating that an error occurred.
  void _showErrorOccurredDialog() {
    ui.DialogUtils.showErrorOccurredTranslateDialog(
      context,
      onOkClicked: () {
        Navigator.of(context).pop();
      },
    );
  }
}
