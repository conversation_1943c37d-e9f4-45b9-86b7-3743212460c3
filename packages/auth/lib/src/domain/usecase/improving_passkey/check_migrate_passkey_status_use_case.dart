import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../data/repositories/source/api/client/auth_client.dart';

@Injectable()
class CheckMigratePasskeyStatusUseCase extends BaseFutureUseCase<
    CheckMigratePasskeyStatusInput, CheckMigratePasskeyStatusOutput> {
  CheckMigratePasskeyStatusUseCase();

  @override
  Future<CheckMigratePasskeyStatusOutput> buildUseCase(
    CheckMigratePasskeyStatusInput input,
  ) async {
    final response = await AuthClient().instance.checkMigratePasskeyStatus();

    return CheckMigratePasskeyStatusOutput(ok: response.data!.data ?? false);
  }
}

class CheckMigratePasskeyStatusInput extends BaseInput {
  CheckMigratePasskeyStatusInput();
}

class CheckMigratePasskeyStatusOutput extends BaseOutput {
  CheckMigratePasskeyStatusOutput({required this.ok});

  final bool ok;
}
