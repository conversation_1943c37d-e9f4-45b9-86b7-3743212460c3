import 'package:json_annotation/json_annotation.dart';

import 'worker_send_message_base.dart';

part 'worker_send_location_input.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: false)
class WorkerSendLocationInput extends WorkerSendMessageInputBase {
  final double latitude;
  final double longitude;
  final String description;

  WorkerSendLocationInput({
    required this.latitude,
    required this.longitude,
    required this.description,
    required super.workspaceId,
    required super.channelId,
    required super.userId,
    required super.ref,
    required super.taskName,
    required super.attachmentType,
    required super.creationTime,
     super.isRefTask,
  });

  factory WorkerSendLocationInput.fromJson(Map<String, dynamic> json) =>
      _$WorkerSendLocationInputFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$WorkerSendLocationInputToJson(this);

  Map<String, dynamic> toRequest() {
    return isDm()
        ? {
            'userId': userId,
            'latitude': latitude,
            'longitude': longitude,
            'description': description,
            'ref': ref,
          }
        : {
            'workspaceId': workspaceId,
            'channelId': channelId,
            'latitude': latitude,
            'longitude': longitude,
            'description': description,
            'ref': ref,
          };
  }

  @override
  WorkerSendLocationInput copyWith({
    double? latitude,
    double? longitude,
    String? description,
    String? workspaceId,
    String? channelId,
    String? userId,
    String? ref,
    String? taskName,
    int? attachmentType,
    DateTime? creationTime,
    bool? isRefTask,
  }) {
    return WorkerSendLocationInput(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      description: description ?? this.description,
      workspaceId: workspaceId ?? this.workspaceId,
      channelId: channelId ?? this.channelId,
      userId: userId ?? this.userId,
      ref: ref ?? this.ref,
      taskName: taskName ?? this.taskName,
      attachmentType: attachmentType ?? this.attachmentType,
      creationTime: creationTime ?? this.creationTime,
      isRefTask: isRefTask ?? this.isRefTask,
    );
  }
}
