import 'package:shared/shared.dart';

import '../di/di.dart' as di;

class Config extends BaseConfig {
  AuthData? _authData;

  set authData(AuthData? value) {
    _authData = value;
  }

  String get apiAuthToken => _authData?.authToken ?? '';

  String? get activeSessionKey => _authData?.sessionKey;

  factory Config.getInstance() {
    return _instance;
  }

  Config._();

  static final Config _instance = Config._();

  @override
  Future<void> config() async => di.configureInjection();
}
