import 'package:chat/chat.dart';
import 'package:flutter/cupertino.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';
import 'package:user_manager/user_manager.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../usecase/user_status/add_user_status_use_case.dart';
import '../usecase/user_status/delete_user_status_use_case.dart';
import '../usecase/user_status/update_user_status_use_case.dart';

typedef UserStatusUpdatedCallback = void Function(
  String content,
  String emoji,
  int statusDuration,
);

/// Handles user status operations including add, update, and delete.
///
/// This class is registered as a lazy singleton.
@LazySingleton()
class UserStatusHandler {
  const UserStatusHandler(
    this._managerRepository,
    this._userRepository,
    this._chatUserRepository,
    this._addUserStatusUseCase,
    this._updateUserStatusUseCase,
    this._deleteUserStatusUseCase,
  );

  final ManagerRepository _managerRepository;
  final UserRepository _userRepository;
  final ChatUserRepository _chatUserRepository;

  final AddUserStatusUseCase _addUserStatusUseCase;
  final UpdateUserStatusUseCase _updateUserStatusUseCase;
  final DeleteUserStatusUseCase _deleteUserStatusUseCase;

  /// Shows the bottom sheet to add a new user status.
  ///
  /// - Provides emoji picker and text input for status creation.
  /// - Optionally invokes [onAddStatusSuccessful] or [onCancel] callbacks.
  void showAddStatusBottomSheet(
    BuildContext context, {
    VoidCallback? onCancel,
    UserStatusUpdatedCallback? onAddStatusSuccessful,
  }) {
    final emojiList =
        List<String>.from(_managerRepository.getUserStatusEmojis());
    BottomSheetUtil.showAddStatusBottomSheet(
      context: context,
      onPressedCancel: () {
        if (onCancel == null) {
          Navigator.of(context).pop();
        } else {
          onCancel();
        }
      },
      onPressedDone: (status) => _addStatus(
        context,
        status.statusText,
        status.emoji,
        status.hourDuration,
        onAddStatusSuccessful,
      ),
      emojis: emojiList,
      onUpdateEmojiList: _updateEmojiList,
    );
  }

  /// Shows the bottom sheet to update an existing user status.
  ///
  /// - Pre-fills emoji, text, and duration.
  /// - Deletes status if input is empty, otherwise calls [_updateStatus].
  /// - Updates emoji list if the initial emoji is not in the default list.
  void showUpdateStatusBottomSheet(
    BuildContext context, {
    String initContent = '',
    String initEmoji = '',
    int statusDuration = 0,
    VoidCallback? onCancel,
    UserStatusUpdatedCallback? onUpdateStatusSuccessful,
  }) {
    final emojiList =
        List<String>.from(_managerRepository.getUserStatusEmojis());
    if (initEmoji.isNotEmpty && !emojiList.contains(initEmoji)) {
      emojiList.removeLast();
      emojiList.insert(0, initEmoji);
      _updateEmojiList(emojiList);
    }
    BottomSheetUtil.showEditStatusBottomSheet(
      context: context,
      onPressedCancel: () {
        if (onCancel == null) {
          Navigator.of(context).pop();
        } else {
          onCancel();
        }
      },
      onPressedDone: (status) {
        if (StringUtils.isNullOrEmpty(status.emoji) &&
            StringUtils.isNullOrEmpty(status.statusText)) {
          showDeleteStatusActionSheet(
            context,
            onDeleteStatusSuccessful: onUpdateStatusSuccessful,
            onCancel: onCancel,
          );
          return;
        }
        _updateStatus(
          context,
          status.statusText,
          status.emoji,
          status.hourDuration,
          onUpdateStatusSuccessful,
        );
      },
      emoji: initEmoji,
      statusText: initContent,
      emojis: emojiList,
      hour: statusDuration,
      onUpdateEmojiList: _updateEmojiList,
    );
  }

  /// Shows an action sheet to confirm deletion of the user status.
  ///
  /// - Optionally invokes [onDeleteStatusSuccessful] or [onCancel] callbacks.
  void showDeleteStatusActionSheet(
    BuildContext context, {
    VoidCallback? onCancel,
    UserStatusUpdatedCallback? onDeleteStatusSuccessful,
  }) {
    ActionSheetUtil.showDeleteYourStatusActionSheet(
      context,
      onCancel: () {
        if (onCancel == null) {
          Navigator.of(context).pop();
        } else {
          onCancel();
        }
      },
      onDelete: () {
        _deleteStatus(context, onDeleteStatusSuccessful);
      },
    );
  }

  /// Saves or clears the current user status based on the given input.
  ///
  /// - If [content], [emoji], and [keepStatusDuration] are valid, updates status.
  /// - Otherwise, clears the current user status.
  void saveUserStatus({
    String? content,
    String? emoji,
    int? keepStatusDuration,
    String? createTime,
    String? updateTime,
    String? endTime,
  }) {
    if (!StringUtils.isNullOrEmpty(content) &&
        !StringUtils.isNullOrEmpty(emoji) &&
        keepStatusDuration != null) {
      _userRepository.updateMyStatus(
        UserStatus(
          content: content,
          status: emoji,
          expireAfterTime: UserStatusExpireAfterTimeEnum.values.firstWhere(
            (e) => e.value == keepStatusDuration,
            orElse: () => UserStatusExpireAfterTimeEnum.UNSPECIFIED,
          ),
          createTime: createTime,
          endTime: endTime,
          updateTime: updateTime,
        ),
      );
      _chatUserRepository.updateMyStatus(
        ChatUserStatus(
          content: content,
          status: emoji,
          expireAfterTime: ChatUserStatusExpireAfterTimeEnum.values.firstWhere(
            (e) => e.value == keepStatusDuration,
            orElse: () => ChatUserStatusExpireAfterTimeEnum.UNSPECIFIED,
          ),
          createTime: createTime,
          endTime: endTime,
          updateTime: updateTime,
        ),
      );
    } else {
      _userRepository.updateMyStatus(null);
      _chatUserRepository.updateMyStatus(null);
    }
  }

  /// Updates the stored emoji list in the [ManagerRepository].
  ///
  /// - Called when the emoji list is modified via UI.
  void _updateEmojiList(List<String> emojis) {
    _managerRepository.updateUserStatusEmojis(emojis);
  }

  /// Executes use case to add a new status and updates local state.
  Future<void> _addStatus(
    BuildContext context,
    String content,
    String emoji,
    int keepStatusDuration,
    UserStatusUpdatedCallback? onAddStatusSuccessful,
  ) async {
    LoadingOverlayHelper.showLoading(context);
    final output = await _addUserStatusUseCase.execute(
      AddUserStatusInput(
        content: content,
        emoji: emoji,
        statusExpireAfterTime: keepStatusDuration,
      ),
    );
    if (output.ok) {
      onAddStatusSuccessful?.call(content, emoji, keepStatusDuration);
      _userRepository.updateMyStatus(
        UserStatus(
          content: content,
          status: emoji,
          expireAfterTime: UserStatusExpireAfterTimeEnum.values.firstWhere(
            (e) => e.value == keepStatusDuration,
            orElse: () => UserStatusExpireAfterTimeEnum.UNSPECIFIED,
          ),
        ),
      );
      _chatUserRepository.updateMyStatus(
        ChatUserStatus(
          content: content,
          status: emoji,
          expireAfterTime: ChatUserStatusExpireAfterTimeEnum.values.firstWhere(
            (e) => e.value == keepStatusDuration,
            orElse: () => ChatUserStatusExpireAfterTimeEnum.UNSPECIFIED,
          ),
        ),
      );
    }
    LoadingOverlayHelper.hideLoading(context);
  }

  /// Executes use case to update the user status and updates local state.
  Future<void> _updateStatus(
    BuildContext context,
    String content,
    String emoji,
    int keepStatusDuration,
    UserStatusUpdatedCallback? onUpdateStatusSuccessful,
  ) async {
    LoadingOverlayHelper.showLoading(context);
    final output = await _updateUserStatusUseCase.execute(
      UpdateUserStatusInput(
        content: content,
        emoji: emoji,
        statusExpireAfterTime: keepStatusDuration,
      ),
    );
    if (output.ok) {
      onUpdateStatusSuccessful?.call(content, emoji, keepStatusDuration);
      _userRepository.updateMyStatus(
        UserStatus(
          content: content,
          status: emoji,
          expireAfterTime: UserStatusExpireAfterTimeEnum.values.firstWhere(
            (e) => e.value == keepStatusDuration,
            orElse: () => UserStatusExpireAfterTimeEnum.UNSPECIFIED,
          ),
        ),
      );
      _chatUserRepository.updateMyStatus(
        ChatUserStatus(
          content: content,
          status: emoji,
          expireAfterTime: ChatUserStatusExpireAfterTimeEnum.values.firstWhere(
            (e) => e.value == keepStatusDuration,
            orElse: () => ChatUserStatusExpireAfterTimeEnum.UNSPECIFIED,
          ),
        ),
      );
    }
    LoadingOverlayHelper.hideLoading(context);
  }

  /// Executes use case to delete the current user status.
  Future<void> _deleteStatus(
    BuildContext context,
    UserStatusUpdatedCallback? onDeleteStatusSuccessful,
  ) async {
    LoadingOverlayHelper.showLoading(context);
    final output =
        await _deleteUserStatusUseCase.execute(DeleteUserStatusInput());
    if (output.ok) {
      onDeleteStatusSuccessful?.call('', '', 0);
      _userRepository.updateMyStatus(null);
      _chatUserRepository.updateMyStatus(null);
    }
    LoadingOverlayHelper.hideLoading(context);
  }
}
