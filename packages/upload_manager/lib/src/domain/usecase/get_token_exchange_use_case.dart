import 'package:auth_api/auth_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../upload_manager.dart';

part 'get_token_exchange_use_case.freezed.dart';

@Injectable()
class GetTokenExchangeUseCase
    extends BaseFutureUseCase<GetTokenExchangeInput, GetTokenExchangeOutput> {
  const GetTokenExchangeUseCase();

  @protected
  @override
  Future<GetTokenExchangeOutput> buildUseCase(
    GetTokenExchangeInput input,
  ) async {
    final body = V3TokenExchangeRequestBuilder()..tokenType = input.tokenType;
    final response = await AuthClient().instance.tokenExchange(
          body: body.build(),
        );

    if (response.data?.ok ?? false) {
      final data = response.data!.data!;

      return GetTokenExchangeOutput(
        success: true,
        tokenExchange: data,
      );
    }
    return GetTokenExchangeOutput(
      success: false,
      error: response.data?.error as V3Error,
    );
  }
}

@freezed
sealed class GetTokenExchangeInput extends BaseInput
    with _$GetTokenExchangeInput {
  const GetTokenExchangeInput._();
  factory GetTokenExchangeInput({
    required V3TokenType? tokenType,
  }) = _GetTokenExchangeInput;
}

@freezed
sealed class GetTokenExchangeOutput extends BaseOutput
    with _$GetTokenExchangeOutput {
  const GetTokenExchangeOutput._();
  factory GetTokenExchangeOutput({
    required bool success,
    String? tokenExchange,
    final V3Error? error,
  }) = _GetTokenExchangeOutput;
}
