class ApiResponse {
  final bool ok;
  final dynamic data;
  final ApiError? error;

  ApiResponse({
    required this.ok,
    this.data,
    this.error,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json) {
    return ApiResponse(
      ok: json['ok'] as bool,
      data: json['data'],
      error: json['error'] != null ? ApiError.fromJson(json['error']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ok': ok,
      'data': data,
      'error': error?.toJson(),
    };
  }
}

class ApiError {
  final int code;
  final String message;
  final List<String> details;

  ApiError({
    required this.code,
    required this.message,
    required this.details,
  });

  factory ApiError.fromJson(Map<String, dynamic> json) {
    return ApiError(
      code: json['code'] as int,
      message: json['message'] as String,
      details: List<String>.from(json['details'] ?? []),
    );
  }

  Map<String, dynamic> toJ<PERSON>() {
    return {
      'code': code,
      'message': message,
      'details': details,
    };
  }
}
