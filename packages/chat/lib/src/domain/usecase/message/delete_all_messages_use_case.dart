import 'package:injectable/injectable.dart';
import 'package:shared/shared.dart';

import '../../../../chat.dart';

@Injectable()
class DeleteAllMessagesUseCase
    extends BaseFutureUseCase<DeleteAllMessagesInput, DeleteAllMessagesOutput> {
  DeleteAllMessagesUseCase(
    this._messageRepository,
  );

  final MessageRepository _messageRepository;

  @override
  Future<DeleteAllMessagesOutput> buildUseCase(
    DeleteAllMessagesInput input,
  ) async {
    return DeleteAllMessagesOutput(
      ok: await _messageRepository.deleteAllMessageOnChannel(
        workspaceId: input.workspaceId,
        channelId: input.channelId,
      ),
    );
  }
}

class DeleteAllMessagesInput extends BaseInput {
  DeleteAllMessagesInput({
    required this.workspaceId,
    required this.channelId,
  });

  final String workspaceId;
  final String channelId;
}

class DeleteAllMessagesOutput extends BaseOutput {
  DeleteAllMessagesOutput({
    required this.ok,
  });

  final bool ok;
}
