plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id "org.jetbrains.kotlin.android"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader -> localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "1"
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.0"
}

android {
    namespace = "com.ziichat"
    compileSdk = 35
    ndkVersion '28.0.12433566 rc1'
    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    buildFeatures {
        buildConfig true
    }

    defaultConfig {
        applicationId = "com.ziichat.android.media"
        minSdk = 28
        targetSdk = flutter.targetSdkVersion
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
        multiDexEnabled true
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword '8I^f#32olUFs0X'
            keyAlias 'debug-keystore'
            keyPassword '8I^f#32olUFs0X'
        }
        release {
            storeFile file('release.jks')
            storePassword '8I^f#32olUFs0X'
            keyAlias 'upload-key'
            keyPassword '8I^f#32olUFs0X'
        }
    }

    buildTypes {
        release {
            debuggable false
            minifyEnabled true
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            ndk.debugSymbolLevel 'FULL'
            packaging.jniLibs.keepDebugSymbols.add("**")
        }

        debug {
            debuggable true
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
            ndk {
                debugSymbolLevel 'none'
            }
        }
    }

    flavorDimensions = ["env"]
    productFlavors {
        beta {
            dimension "env"
            applicationIdSuffix ".beta"
            resValue "string", "app_name", "ZiiChat-F"
            versionCode 156
            versionName '0.45.0 (1)'

            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
//                abiFilters "arm64-v8a"
            }
        }

        sandboxLive {
            dimension "env"
            versionCode 384
            versionName '0.28.0-f'
            applicationIdSuffix ".sandbox"
            resValue "string", "app_name", "ZiiChat F"

            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
//                abiFilters "arm64-v8a"
            }
        }

        live {
            dimension "env"
            versionCode 465
            versionName '3.0.26 (2)'
            resValue "string", "app_name", "ZiiChat"

            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
            }
        }
    }

    configurations {
        implementation.exclude module: 'protobuf-java'
    }
}


dependencies {
    def objectboxVersion = '4.2.0'

    implementation platform('com.google.firebase:firebase-bom:33.12.0')

    implementation 'com.google.firebase:firebase-perf'
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-messaging'

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.5'
    debugImplementation("io.objectbox:objectbox-android-objectbrowser:$objectboxVersion")
    releaseImplementation("io.objectbox:objectbox-android:$objectboxVersion")

    implementation 'androidx.window:window:1.3.0'
    implementation 'androidx.window:window-java:1.3.0'

    implementation 'androidx.appcompat:appcompat:1.7.0'
}

configurations {
    debugImplementation {
        exclude group: 'io.objectbox', module: 'objectbox-android'
    }
}

flutter {
    source = "../.."
}