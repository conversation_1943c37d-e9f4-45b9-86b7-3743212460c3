import 'dart:async';

import 'package:app_core/core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../chat.dart';
import '../../common/di/di.dart';
import '../../data/repositories/database/enums/user_badge_enum.dart';
import '../../utils/name_utils.dart';
import '../channel/widgets/friend_list_skeleton.dart';
import '../friend_profile/user_profile_handler.dart';
import 'bloc/friend_request_bloc.dart';

class FriendRequestPage extends StatefulWidget {
  const FriendRequestPage({
    required this.friendRequestInterface,
    super.key,
  });

  final FriendRequestInterface friendRequestInterface;

  @override
  State<FriendRequestPage> createState() => _FriendRequestPageState();
}

const _firstPageToken = '';

class _FriendRequestPageState
    extends BasePageState<FriendRequestPage, FriendRequestBloc>
    implements ui.FriendRequestListInterface {
  int selectedIndex = -1;
  List<ChatUser> _listRequest = [];
  final PagingController<String, ChatUser> _pagingController =
      PagingController(firstPageKey: _firstPageToken);
  var _noMoreItems = true;
  var _nextPageToken = _firstPageToken;
  late AppLocalizations appLocalizations;
  Map<String, UserPrivateData> _mapUserPrivateData = {};
  late final UserPrivateDataBloc _userPrivateDataBloc;
  late final StreamSubscription _streamAcceptFriendRequest;
  late final StreamSubscription _streamCancelFriendRequest;
  bool _isShowViewProfileBottomSheet = false;
  int badgeEnum = 0;
  ui.UserBadgeType? userBadgeType;

  @override
  void initState() {
    super.initState();
    _pagingController.addPageRequestListener(
      (pageKey) {
        if (!_noMoreItems)
          bloc.add(
            LoadMoreFriendsEvent(
              nextPageToken: _nextPageToken,
              friends: _listRequest,
            ),
          );
      },
    );
    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    bloc.add(InitiateFriendRequestEvent());

    _streamAcceptFriendRequest =
        GetIt.I.get<AppEventBus>().on<FriendRequestAcceptEvent>().listen(
      (event) {
        if (_isShowViewProfileBottomSheet) {
          _onTapCancel();
        }
      },
    );
    _streamCancelFriendRequest =
        GetIt.I.get<AppEventBus>().on<FriendRequestCancelEvent>().listen(
      (event) {
        if (_isShowViewProfileBottomSheet) {
          _onTapCancel();
        }
      },
    );
  }

  @override
  void dispose() {
    _streamAcceptFriendRequest.cancel();
    _streamCancelFriendRequest.cancel();
    _pagingController.dispose();
    super.dispose();
  }

  void _handleLoaded(
    List<ChatUser> chatUsers,
    bool noMoreItems,
  ) {
    _noMoreItems = noMoreItems;
    if (!_noMoreItems && chatUsers.isEmpty) {
      bloc.add(
        LoadMoreFriendsEvent(
          nextPageToken: _nextPageToken,
          friends: chatUsers,
        ),
      );
      return;
    }
    if (noMoreItems) {
      _noMoreItems = true;
      _pagingController.appendLastPage([]);
    }
    if (chatUsers.isEmpty) {
      return;
    }
    _nextPageToken = chatUsers.last.userId;
    _pagingController.itemList = chatUsers;
  }

  void updateSelectedIndex(int index) {
    setState(() {
      selectedIndex = index;
    });
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        listUserPrivateData.forEach((item) {
          _mapUserPrivateData[item.userId] = item;
        });
        setState(() {});
      },
    );
  }

  String? getAliasName(String? userId) {
    if (userId == null) return null;
    try {
      var user = _mapUserPrivateData[userId];
      return user?.aliasName != null && user!.aliasName!.isNotEmpty
          ? user.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<UserPrivateDataBloc>.value(value: _userPrivateDataBloc),
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
            listenWhen: (prev, state) => prev != state,
            listener: _blocUserPrivateListener,
          ),
        ],
        child: BlocBuilder<FriendRequestBloc, FriendRequestState>(
          buildWhen: (previous, current) {
            return previous != current;
          },
          builder: (BuildContext context, state) {
            Log.e(name: '_FriendRequestPageState.buildPage', state);
            return state.maybeWhen(
              initial: _buildFriendRequestPage,
              loaded: (
                friendRequests,
                noMoreItems,
              ) {
                _listRequest = _updateAliasNameFriendRequest(friendRequests);
                _handleLoaded(_listRequest, noMoreItems);
                return _buildFriendRequestPage();
              },
              refresh: () {
                return _buildFriendRequestPage();
              },
              onError: (code, message) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (code == 403) {
                    ui.DialogUtils.showAccountUnavailableDialog(
                      context,
                      onFirstAction: (context) {
                        popToFriendRequest();
                      },
                    );
                  } else {
                    ui.DialogUtils.showErrorOccurredTranslateDialog(
                      context,
                      onOkClicked: () {
                        popToFriendRequest();
                      },
                    );
                  }
                });
                return _buildFriendRequestPage();
              },
              orElse: _buildFriendRequestPage,
            );
          },
        ),
      ),
    );
  }

  List<ChatUser> _updateAliasNameFriendRequest(List<ChatUser> friends) {
    return friends
        .map(
          (friend) {
            friend.aliasName = getAliasName(friend.userId);
            return friend;
          },
        )
        .whereType<ChatUser>()
        .toList();
  }

  Widget _buildFriendRequestPage() {
    appLocalizations = AppLocalizations.of(context)!;

    return ui.FriendRequestListPage(interface: this);
  }

  @override
  List<ui.FriendItem> friendRequestList() {
    return [];
  }

  @override
  void onClickBack() {
    widget.friendRequestInterface.onClickBack();
  }

  @override
  void onClickGotIt() {}

  @override
  bool isGotIt() {
    return true;
  }

  @override
  void onClickAcceptFriendRequest(ui.FriendItem friendItem) {
    if (friendItem.isBlocked == true) {
      return UserProfileHandler.showDialogUnavailable(context);
    }
    bloc.add(AcceptFriendRequestsEvent(userId: friendItem.userId));
  }

  @override
  void onClickDeleteFriendRequest(ui.FriendItem friendItem) {
    bloc.add(
      DeleteFriendRequestsEvent(
        requests: _listRequest,
        userId: friendItem.userId,
      ),
    );

    bloc.add(DeleteFriendRequestsInLocalEvent(userId: friendItem.userId));
    ui.SnackBarUtil.showItemWasRemovedFromTheList(
      parentContext: context,
      appLocalizations: appLocalizations,
      onUndoDeleteAction: () {
        _onUndoDeleteAction();
      },
    );
  }

  @override
  void onClickFriendItem(ui.FriendItem friendItem) {
    _isShowViewProfileBottomSheet = true;
    ui.ActionSheetUtil.showViewProfileFriend(
      context,
      onTapCancel: _onTapCancel,
      onClickViewProfile: () {
        _isShowViewProfileBottomSheet = false;
        widget.friendRequestInterface.onClickViewProfile(friendItem);
        Navigator.pop(context);
      },
      onClickAccept: () {
        _isShowViewProfileBottomSheet = false;
        onClickAcceptFriendRequest(friendItem);
        Navigator.pop(context);
      },
      onClickDelete: () {
        _isShowViewProfileBottomSheet = false;
        Navigator.pop(context);
        onClickDeleteFriendRequest(friendItem);
      },
    );
  }

  void _onTapCancel() {
    _isShowViewProfileBottomSheet = false;
    Navigator.pop(context);
  }

  void popToFriendRequest() {
    AppEventBus.publish(PopToFriendRequestEvent());
  }

  void _onUndoDeleteAction() async {
    bloc.add(UndoDeleteFriendRequestsEvent());
  }

  @override
  bool displaySkeleton() {
    return false;
  }

  @override
  Widget? friendRequestListWidget() {
    return _listRequest.isEmpty
        ? null
        : Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).padding.bottom,
              ),
              child: CustomScrollView(
                slivers: [
                  PagedSliverList<String, ChatUser>(
                    pagingController: _pagingController,
                    builderDelegate: PagedChildBuilderDelegate<ChatUser>(
                      animateTransitions: true,
                      itemBuilder: (context, chatUser, index) {
                        badgeEnum = chatUser.profile?.userBadgeType ?? 0;
                        userBadgeType =
                            UserBadgeEnumExtension.getEnumByValue(badgeEnum)
                                .toUserBadgeType();
                        var friendItem = ui.FriendItem(
                          badgeType: userBadgeType,
                          userId: _listRequest[index].userId,
                          avatarPath: UrlUtils.parseAvatar(
                            _listRequest[index].profile?.avatar,
                          ),
                          isOnline:
                              _listRequest[index].presenceData?.presenceState ==
                                  'ONLINE',
                          displayName: NameUtils.parseNameOfUser(chatUser)!,
                          statusEmoji:
                              _listRequest[index].statusData?.status ?? '',
                          statusText:
                              _listRequest[index].statusData?.content ?? '',
                        );
                        return ui.FriendRequestWidget(
                          friendItem: friendItem,
                          onClickAccept: () {
                            onClickAcceptFriendRequest(friendItem);
                            setState(() {
                              selectedIndex = -1;
                            });
                          },
                          onClickDelete: () {
                            onClickDeleteFriendRequest(friendItem);
                            setState(() {
                              selectedIndex = -1;
                            });
                          },
                          onClickItem: () {
                            onClickFriendItem(friendItem);
                          },
                          itemIndex: index,
                          selectedIndex: selectedIndex,
                          updateSelectedIndex: updateSelectedIndex,
                        );
                      },
                      firstPageProgressIndicatorBuilder: (_) {
                        return FriendListSkeleton();
                      },
                      newPageProgressIndicatorBuilder: (_) {
                        return SizedBox.shrink();
                      },
                      noItemsFoundIndicatorBuilder: (context) {
                        return SizedBox.shrink();
                      },
                    ),
                  ),
                ],
              ),
            ),
          );
  }
}
