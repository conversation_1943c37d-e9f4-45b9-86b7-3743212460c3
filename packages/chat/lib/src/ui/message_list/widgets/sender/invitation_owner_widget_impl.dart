import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../data/repositories/extensions/message_extension.dart';
import '../base/base_sender_widget.dart';

class InvitationOwnerWidgetImpl extends BaseSenderWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final void Function(MessageItem messageItem)? onQuoteMessageClicked;
  final QuoteMessage? quoteMessage;

  final bool isShowCreateTime;
  final bool isHiddenPin;
  final bool isLastMessage;

  bool _isInvitationExpired() {
    var embed = (message.embed != null && message.embed!.isNotEmpty)
        ? message.embed!.first
        : null;
    final invitationData = embed?.invitationData;
    final past24Hour = invitationData?.updateTime == null
        ? false
        : (invitationData?.expireTime
                ?.toLocalDateTime()
                ?.isBefore(DateTime.now()) ??
            false);
    return (invitationData?.isExpired ?? false) || past24Hour;
  }

  InvitationOwnerWidgetImpl({
    required this.isOpenCheckBox,
    this.onCheckBoxButtonTap,
    this.isHiddenPin = false,
    this.isLastMessage = false,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    this.quoteMessage,
    this.onQuoteMessageClicked,
    required super.messageItem,
    required super.message,
    this.isShowCreateTime = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    var embed = (message.embed != null && message.embed!.isNotEmpty)
        ? message.embed!.first
        : null;
    final invitationData = embed?.invitationData;

    final isExpired = _isInvitationExpired();

    var invitationMessage = InvitationMessage(
      channelName: invitationData?.channel?['name'] ?? '',
      numMembers: invitationData?.channel?['totalMembers'] ?? 0,
      avatarUrl: UrlUtils.parseAvatar(invitationData?.channel?['avatar']),
      qr: invitationData?.invitationLink ?? '',
      hasExpired: isExpired,
    );

    return InvitationMessageSenderWidget(
      isLastMessage: isLastMessage,
      messageItem: messageItem,
      onQuote: onQuote,
      isHiddenPin: isHiddenPin,
      isShowCreateTime: isShowCreateTime,
      onCopy: (messageItem) => onCopy(context, messageItem),
      emojiList: message.emojiList,
      invitationMessage: invitationMessage,
      content: message.argsContent()!,
      onMessageItemClicked: onMessageItemClicked,
      onListReactionClicked: (MessageItem messageItem) {},
      onEmojiClicked: onEmojiClicked,
      mentions: message.mentions ?? [],
      onOpenLink: isExpired
          ? (_) {}
          : (MessageItem messageItem) {
              onClickLink(invitationData?.invitationLink ?? '');
            },
      onUsernameClicked: (username) => onClickMention(context, username),
      onLinkClicked: onClickLink,
      onResend: (MessageItem messageItem) {},
      onDiscard: onDiscardMessage,
      isOpenCheckBox: isOpenCheckBox,
      isCheckedMessage: isCheckedMessage,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      quoteMessage: quoteMessage,
      onQuoteMessageClicked: onQuoteMessageClicked,
      isHideOptionResend: false,
      isHideOptionCopy: false,
      isHighlighted: isHighlighted,
      onEdit: onEdit,
      onForward: onForward,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      onDeleteMessages: onDeleteMessages,
      shouldAnimate: shouldAnimate,
    );
  }

  @override
  void onMessageItemClicked(MessageItem messageItem) {
    if (!message.hasInvitationData || _isInvitationExpired()) return;

    final data = message.firstEmbed!.invitationData!;

    onClickLink(data.invitationLink!);
  }
}
