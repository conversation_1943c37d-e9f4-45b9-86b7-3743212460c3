import 'package:app_core/core.dart' hide Config;
import 'package:channel_view_api/channel_view_api.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:localization_client/l10n/generated/ziichat_localizations.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../chat.dart';
import '../../common/di/di.dart';
import 'bloc/forward_bloc.dart';

class ForwardViewPage extends StatefulWidget {
  ForwardViewPage({
    this.workspaceId,
    this.channelId,
    this.userId,
    required this.mapCheckedMessage,
    required this.messagesBloc,
    this.isRouterFullScreen,
    super.key,
    this.onBack,
  });

  final VoidCallback? onBack;
  final Map<String, Message> mapCheckedMessage;
  late final MessagesBloc messagesBloc;
  final String? workspaceId;
  final String? channelId;
  final String? userId;
  final bool? isRouterFullScreen;

  @override
  State<StatefulWidget> createState() {
    return _ForwardViewPageState();
  }
}

class _ForwardViewPageState extends State<ForwardViewPage> {
  late List<UserItem> allAccounts = [];
  late List<UserItem> selectedAccounts = [];

  late void Function(BuildContext, UserItem)
      onRemoveAccountSelectedButtonPressed;
  late void Function(BuildContext, String) onChangedTextField;
  late List<UserItem> filteredAccounts = [];
  late ForwardBloc _forwardBloc;
  late final UserPrivateDataBloc _userPrivateDataBloc;

  List<UserPrivateData> _listUserPrivateData = [];
  String submitTitle = GetIt.instance.get<AppLocalizations>().send;
  String title = GetIt.instance.get<AppLocalizations>().forward;
  final List<SendMessageHandler> _listSendMessageHandler = [];
  final List<Message> _messages = [];
  final MessagesBloc _messagesBloc = getIt<MessagesBloc>();

  @override
  void initState() {
    super.initState();
    _userPrivateDataBloc = getIt<UserPrivateDataBloc>();
    _userPrivateDataBloc.add(InitUserPrivateDataEvent());
    _forwardBloc = getIt<ForwardBloc>();
    _forwardBloc.add(
      OnInitForwardEvent(
        workspaceId: widget.workspaceId,
        channelId: widget.channelId,
        userId: widget.userId,
      ),
    );
    widget.mapCheckedMessage.values.forEach((item) {
      _messages.add(item);
    });
  }

  @override
  void dispose() {
    super.dispose();
    getIt<UserPrivateDataBloc>().add(GetPrivateDataUnSubscriptionEvent());
  }

  void onBackButtonClicked() {
    widget.onBack?.call();
  }

  String? getUserIdFromListChannel(Channel item) {
    if (item.participantIds?.length == 0) {
      return null;
    }
    final userId = item.participantIds?.where(
      (sessionKey) => sessionKey != Config.getInstance().activeSessionKey,
    );

    return userId?.first;
  }

  void handleMapListUserItem(List<UserItem>? listForward) {
    allAccounts.clear();
    listForward?.forEach((item) {
      item.name = getAliasName(item) ?? item.name;
      allAccounts.add(item);
    });
  }

  void _blocUserPrivateListener(
    BuildContext context,
    UserPrivateDataState state,
  ) {
    state.when(
      initial: () {},
      listUserPrivateData: (List<UserPrivateData> listUserPrivateData) {
        _listUserPrivateData = listUserPrivateData;
      },
    );
  }

  String? getAliasName(UserItem item) {
    if (item.userId == null) return null;
    try {
      UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((user) => user.userId == item.userId);
      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  String? getAliasNameByUserId(String? userId) {
    if (userId == null) return null;
    try {
      UserPrivateData userPrivateData =
          _listUserPrivateData.firstWhere((user) => user.userId == userId);
      return userPrivateData.aliasName != null &&
              userPrivateData.aliasName!.isNotEmpty
          ? userPrivateData.aliasName
          : null;
    } catch (error) {
      return null;
    }
  }

  void handleSendForward() async {
    AppEventBus.publish(
      widget.isRouterFullScreen == true
          ? PopToFullScreenEvent()
          : PopToChannelViewEvent(),
    );
    AppEventBus.publish(
      CancelAppBarChannelViewEvent(isCancel: true, isForward: true),
    );
    selectedAccounts.forEach((item) {
      final handler = SendMessageHandler(
        workspaceId: item.workspaceId,
        channelId: item.channelId,
        userId: item.userId,
      )..messagesBloc = _messagesBloc;
      _listSendMessageHandler.add(handler);
      handler.sendForwardMessage(messages: _messages);
    });
  }

  @override
  Widget build(BuildContext context) {
    return ui.AppScaffold(
      backgroundColor: Colors.transparent,
      body: MultiBlocProvider(
        providers: [
          BlocProvider<ForwardBloc>.value(value: _forwardBloc),
          BlocProvider<UserPrivateDataBloc>.value(
            value: _userPrivateDataBloc,
          ),
        ],
        child: MultiBlocListener(
          listeners: [
            BlocListener<UserPrivateDataBloc, UserPrivateDataState>(
              listenWhen: (prev, state) => prev != state,
              listener: _blocUserPrivateListener,
            ),
          ],
          child: BlocBuilder<ForwardBloc, ForwardState>(
            buildWhen: (prev, state) => prev != state,
            builder: (BuildContext context, ForwardState state) {
              return state.maybeWhen(
                initial: () {
                  return _buildShareToPage(true);
                },
                channelLoaded: (listForward) {
                  handleMapListUserItem(listForward);
                  return _buildShareToPage(false);
                },
                search: (listSearch) {
                  listSearch.forEach((item) {
                    if (item.type ==
                        V3ChannelTypeEnum.CHANNEL_TYPE_ENUM_DM.name) {
                      item.name = getAliasNameByUserId(item.id) ?? item.name;
                    }
                  });

                  filteredAccounts = listSearch;
                  return _buildShareToPage(false);
                },
                waiting: (listForward) {
                  handleMapListUserItem(listForward);
                  return _buildShareToPage(true);
                },
                orElse: () {
                  return _buildShareToPage(false);
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildShareToPage(bool skeleton) {
    return ui.ShareToBottomSheet(
      allAccounts: allAccounts,
      selectedAccounts: selectedAccounts,
      onChangedTextField: (context, string) async {
        _forwardBloc.add(OnSearchEvent(keyword: string));
      },
      filteredAccounts: filteredAccounts,
      parentContext: context,
      skeleton: skeleton,
      onShareToSelectedUsers: (listUserItem) {
        handleSendForward();
      },
      title: title,
      submitTitle: submitTitle,
    );
  }
}
