import 'package:auto_route/auto_route.dart';
import 'package:cross_file/cross_file.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart' as ui;

import '../../../navigation/routes/app_router.dart';
import '../../../navigation/routes/app_router.gr.dart';

@RoutePage()
class TakePhotoAndVideoPage extends StatefulWidget {
  const TakePhotoAndVideoPage({super.key, this.nameRouter});

  final String? nameRouter;

  @override
  State<TakePhotoAndVideoPage> createState() => _TakePhotoAndVideoPageState();
}

class _TakePhotoAndVideoPageState extends State<TakePhotoAndVideoPage> {
  bool enableAudio = false;

  @override
  void initState() {
    super.initState();
    checkMicroPermission();
  }

  Future<void> checkMicroPermission() async {
    enableAudio = await PermissionUtils.isGrantedMicrophonePermission();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: ui.TakePhotoAndVideoPage(
        enableRecordVideo: true,
        maxBytesCountVideo: BytesUtils.megabyteToBytes(100),
        onTakeVideo: (video) => _onTakeVideo(context, video),
        onUploadVideo: (video) => _onUploadVideo(context, video),
        onTakePhoto: (photo) => _onTakePhoto(context, photo),
        onTapBack: () => _onBack(context),
        requestMicroPhonePermission: _onRequestMicroPhonePermission,
        enableAudio: enableAudio,
      ),
    );
  }

  void _onBack(BuildContext context) {
    context.maybePop();
  }

  Future<bool> _onRequestMicroPhonePermission() async {
    return await PermissionUtils.requestMicrophonePermission(context);
  }

  Future<void> _onTakePhoto(BuildContext context, XFile photo) async {
    final isUpload = await context
        .pushRoute(ImagePreviewRoute(imagePath: photo.path)) as bool;
    if (isUpload) {
      context.maybePop();
      if (widget.nameRouter != null && widget.nameRouter!.isNotEmpty) {
        var router = GetIt.instance.get<AppRouter>();
        router.popUtilOrReplace(widget.nameRouter!);
      }
    }
  }

  Future<void> _onTakeVideo(BuildContext context, XFile video) async {
    final isUpload = await context
        .pushRoute(VideoPlaybackRoute(videoPath: video.path)) as bool?;
    if (isUpload ?? false) {
      context.maybePop();
    }
  }

  void _onUploadVideo(BuildContext context, XFile video) {
    context.maybePop();
    AppEventBus.publish(
      TakeVideoMessageEvent(
        id: 'TakeVideoMessageID',
        filePath: video.path,
      ),
    );
    if (widget.nameRouter != null && widget.nameRouter!.isNotEmpty) {
      var router = GetIt.instance.get<AppRouter>();
      router.popUtilOrReplace(widget.nameRouter!);
    }
  }
}
