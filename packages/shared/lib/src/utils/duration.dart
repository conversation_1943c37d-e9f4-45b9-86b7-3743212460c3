extension DurationUtils on Duration {
  static Duration get ms10 => const Duration(milliseconds: 10);

  static Duration get ms50 => const Duration(milliseconds: 50);

  static Duration get ms100 => const Duration(milliseconds: 100);

  static Duration get ms200 => const Duration(milliseconds: 200);

  static Duration get ms300 => const Duration(milliseconds: 300);

  static Duration get ms500 => const Duration(milliseconds: 500);

  static Duration get ms1000 => const Duration(milliseconds: 1000);

  static Duration get ms2000 => const Duration(milliseconds: 2000);

  static Duration get ms3000 => const Duration(milliseconds: 3000);

  static Duration get ms5000 => const Duration(milliseconds: 5000);

  static Duration get s60 => const Duration(seconds: 60);

  static Duration get s45 => const Duration(seconds: 45);

  static Duration get s30 => const Duration(seconds: 30);

  static Duration get s20 => const Duration(seconds: 20);

  static Duration get s5 => const Duration(seconds: 5);

  static const m1 = Duration(minutes: 1);

  static Duration get ms1500 => const Duration(milliseconds: 1500);

  static Duration get ms250 => const Duration(milliseconds: 250);
}
