import 'dart:io';

import '../../shared.dart';

class EnvConfig {
  EnvConfig._();

  static String get getClientIdEnv {
    final result = EnvUtils.getString('CLIENT_ID_ENV');
    return result;
  }

  static String get getClientId {
    final result = EnvUtils.getString(
      Platform.isIOS ? 'CLIENT_ID_IOS' : 'CLIENT_ID_ANDROID',
    );
    return result;
  }

  static String get getApiHost {
    final result = EnvUtils.getString('API_HOST');
    return result;
  }

  static String get getStickerHost {
    final result = EnvUtils.getString('STICKER_HOST');
    return result;
  }

  static String get getAvatarHost {
    final result = EnvUtils.getString('AVATAR_HOST');
    return result;
  }

  static String get getCdnHost {
    final result = EnvUtils.getString('CDN_HOST');
    return result;
  }

  static String get getAuthHost {
    final result = EnvUtils.getString('AUTH_HOST');
    return result;
  }

  static String get getInvitationHost {
    final result = EnvUtils.getString('INVITATION_HOST');
    return result;
  }

  static String get getScanToConnectHost {
    final result = EnvUtils.getString('SCAN_TO_CONNECT_HOST');
    return result;
  }

  static String get getQrAuthHost {
    final result = EnvUtils.getString('QR_AUTH_HOST');
    return result;
  }

  static String get getFileStoreHost {
    final result = EnvUtils.getString('FILESTORE_HOST');
    return result;
  }

  static bool get isDebugApi {
    final result = EnvUtils.getBool('DEBUG_API');
    return result;
  }

  static bool get isUseRemoteConfig {
    final result = EnvUtils.getBool('USE_REMOTE_CONFIG');
    return result;
  }

  static String get getApiVersion {
    final result = EnvUtils.getString('API_VERSION');
    return result;
  }

  static String get getApiHeaderEnv {
    final result = EnvUtils.getString('API_HEADER_ENV');
    return result;
  }

  static String get getApiVersionNumber {
    final result = EnvUtils.getString('API_VERSION_NUMBER');
    return result;
  }

  static String get getApiAuthor {
    final result = EnvUtils.getString('API_AUTHOR');
    return result;
  }

  static String get getIcloudId {
    final result = EnvUtils.getString('ICLOUD_ID');
    return result;
  }

  static String get getSendLink {
    final result = EnvUtils.getString('SEND_LINK');
    return result;
  }

  static String get getBundleId {
    final result = EnvUtils.getString(
      Platform.isIOS ? 'BUNDLE_ID_IOS' : 'BUNDLE_ID_ANDROID',
    );
    return result;
  }

  static String get getFsAddressCall {
    final result = EnvUtils.getString('FS_ADDRESS_CALL');
    return result;
  }
}
