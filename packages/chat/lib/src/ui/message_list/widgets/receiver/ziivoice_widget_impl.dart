import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared/shared.dart';
import 'package:ziichat_ui/ziichat_ui.dart';

import '../../../../../chat.dart';
import '../../../../data/repositories/database/classes/audio_metadata.dart';
import '../../../../data/repositories/extensions/message_extension.dart';
import '../../../../utils/waveform_manager.dart';
import '../base/base_receiver_widget.dart';

class ZiiVoiceWidgetImpl extends StatefulWidget {
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isShowAvatar;
  final bool isShowName;
  final bool isHiddenPin;

  const ZiiVoiceWidgetImpl({
    required this.isOpenCheckBox,
    this.onCheckBoxButtonTap,
    this.isShowCreateTime = false,
    this.isShowAvatar = false,
    this.isShowName = false,
    this.isHiddenPin = false,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    required this.messageItem,
    required this.message,
    super.key,
  });

  final MessageItem messageItem;
  final Message message;

  @override
  State<ZiiVoiceWidgetImpl> createState() => _ZiiVoiceWidgetImplState();
}

class _ZiiVoiceWidgetImplState extends State<ZiiVoiceWidgetImpl>
    with AutomaticKeepAliveClientMixin {
  Message get _message => widget.message;

  MessageItem get _messageItem => widget.messageItem;

  MediaObject? get _file {
    if (_message.mediaAttachments.isEmpty) {
      return null;
    }
    return _message.mediaAttachments.last.voiceMessage!;
  }

  List<double>? _waveFormData;
  WaveformState _state = WaveformState.waiting;

  @override
  void initState() {
    super.initState();
    if (_file != null) {
      _loadAudioAndWaveform();
    }
  }

  Future<void> _loadAudioAndWaveform() async {
    try {
      final waveFormData = _file!.filePath != null
          ? await WaveformManager().getLocalWaveform(_file!.filePath!)
          : await _loadWaveFormFromURL();

      final updatedAudioMetadata = AudioMetadata(samples: waveFormData);
      final updatedFile = _file!.copyWith(audioMetadata: updatedAudioMetadata);

      final attachment = _message.mediaAttachments.last.copyWith(
        voiceMessageRaw: jsonEncode(updatedFile),
      );

      AppEventBus.publish(
        OnUpdateAttachmentEvent(
          workspaceId: _message.workspaceId,
          channelId: _message.channelId,
          messageId: _message.messageId,
          data: attachment.toJson(),
        ),
      );

      if (!mounted) return;

      setState(() {
        _waveFormData = waveFormData;
        _state = WaveformState.success;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _state = WaveformState.error;
      });

      throw Exception(e);
    }
  }

  Future<List<double>> _loadWaveFormFromURL() async {
    return await WaveformManager()
        .getRemoteWaveformRemote(UrlUtils.parseCDNUrl(_file!.fileUrl!));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (_file == null) {
      //TODO: handle file not found view
      return const SizedBox();
    }
    return ZiiVoiceExtendBase(
      isHiddenPin: widget.isHiddenPin,
      message: _message,
      messageItem: _messageItem,
      waveFormData: _waveFormData,
      isShowAvatar: widget.isShowAvatar,
      isShowName: widget.isShowName,
      state: _state,
      isCheckedMessage: widget.isCheckedMessage,
      onCheckBoxButtonTap: widget.onCheckBoxButtonTap,
      isOpenCheckBox: widget.isOpenCheckBox,
      isHighlighted: widget.isHighlighted,
      shouldAnimate: widget.shouldAnimate,
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class ZiiVoiceExtendBase extends BaseReceiverWidget {
  ZiiVoiceExtendBase({
    required this.isOpenCheckBox,
    this.onCheckBoxButtonTap,
    required this.isCheckedMessage,
    required this.isHighlighted,
    required this.shouldAnimate,
    required super.messageItem,
    required super.message,
    this.waveFormData,
    this.state = WaveformState.waiting,
    this.isShowCreateTime = false,
    this.isShowAvatar = false,
    this.isShowName = false,
    this.isHiddenPin = false,
    super.key,
  });

  final List<double>? waveFormData;
  final WaveformState state;
  final bool isOpenCheckBox;
  final bool isCheckedMessage;
  final bool isHighlighted;
  final bool shouldAnimate;
  final void Function(MessageItem messageItem)? onCheckBoxButtonTap;
  final bool isShowCreateTime;
  final bool isShowAvatar;
  final bool isShowName;
  final bool isHiddenPin;

  @override
  Widget build(BuildContext context) {
    return ZiiVoiceMessageReceiverWidget(
      interface: this,
      messageItem: messageItem,
      onQuote: onQuote,
      isShowAvatar: isShowAvatar,
      isShowName: isShowName,
      onDeleteMessages: onDeleteMessages,
      onForward: onForward,
      onPinMessage: onPinMessage,
      onUnPinMessage: onUnPinMessage,
      emojiList: message.emojiList,
      isShowCreateTime: isShowCreateTime,
      isHiddenPin: isHiddenPin,
      onReport: onReport,
      quickReact: hasQuickReaction,
      waveFormData: waveFormData,
      state: state,
      onDownload: (MessageItem messageItem) => onDownload(context),
      onCopy: (MessageItem messageItem) {},
      isCheckedMessage: isCheckedMessage,
      isOpenCheckBox: isOpenCheckBox,
      onCheckBoxButtonTap: onCheckBoxButtonTap,
      isHighlighted: isHighlighted,
      shouldAnimate: shouldAnimate,
    );
  }
}
